---
description: 
globs: frontend/packages/database/**
alwaysApply: false
---
---
description: 当需要了解本项目数据库规范的时候查看此规则
globs: 
alwaysApply: false
---
# 9000AI 数据库设计规范

## 数据库总体架构

9000AI平台采用PostgreSQL关系型数据库，通过Prisma ORM进行数据访问和模型定义。数据库设计采用模块化架构，按业务功能将模型定义拆分到多个.prisma文件中。

### 目录结构

```
frontend/packages/database/
├── prisma/
│   ├── schema/               # 按功能模块拆分的模型定义
│   │   ├── main.prisma       # 数据库连接和生成器配置
│   │   ├── auth.prisma       # 认证相关模型
│   │   ├── api.prisma        # API相关模型
│   │   ├── order.prisma      # 订单相关模型
│   │   ├── subscription.prisma # 订阅相关模型
│   │   ├── creation.prisma   # 创作记录相关模型
│   │   ├── activation.prisma # 激活记录相关模型
│   │   ├── computing_power.prisma # 算力相关模型
│   │   └── referral.prisma   # 推荐相关模型
│   └── src/
│       ├── client.ts         # Prisma客户端单例
│       └── zod/              # 自动生成的Zod验证模式
│           └── index.ts      # 包含所有模型的Zod类型定义
```

## 基本配置

main.prisma文件包含数据库连接和生成器配置:

```prisma
datasource db {
    provider = "postgresql"
    url      = env("DATABASE_URL")
}

generator client {
    provider        = "prisma-client-js"
    previewFeatures = ["prismaSchemaFolder"]
}

generator zod {
    provider         = "zod-prisma-types"
    output           = "../src/zod"
    createInputTypes = false
    addIncludeType   = false
    addSelectType    = false
}
```

## 模型定义规范

### 命名规范

1. **模型名称**：
   - 使用PascalCase(大驼峰)
   - 使用单数形式
   - 例如：`User`, `ApiKey`, `ConsumerOrder`

2. **字段名称**：
   - 使用camelCase(小驼峰)
   - 例如：`userId`, `createdAt`, `phoneNumber`

3. **数据库列名**：
   - 当数据库列名与字段名不同时，使用 `@map` 属性
   - 数据库列名使用蛇形命名法(snake_case)
   - 例如：`userId String @map("user_id")`

4. **枚举类型**：
   - 使用PascalCase(大驼峰)
   - 枚举值使用全大写下划线分隔(SCREAMING_SNAKE_CASE)
   - 例如：`enum OrderStatus { PENDING, COMPLETED }`

5. **关系字段**：
   - 外键字段使用关联表名+Id的格式
   - 例如：`userId`, `planId`, `apiKeyId`

### 必备字段

每个模型必须包含以下标准字段：

1. **id**：
   - 主键字段，使用 `String @id @default(cuid())` 或 `String @id`
   - 某些核心模型如User使用自定义ID

2. **时间戳**：
   - `createdAt DateTime @default(now())`
   - `updatedAt DateTime @updatedAt`
   - 可选的 `deletedAt DateTime?` 用于软删除

3. **备注字段**：
   - 通常加入 `remark String?` 用于管理员备注

### 关系定义

1. **一对一关系**:
   ```prisma
   model User {
     id      String   @id
     agent   Agent?
   }

   model Agent {
     id      String   @id @default(cuid())
     userId  String   @unique
     user    User     @relation(fields: [userId], references: [id])
   }
   ```

2. **一对多关系**:
   ```prisma
   model Agent {
     id        String      @id @default(cuid())
     consumers Consumer[]
   }

   model Consumer {
     id       String   @id @default(cuid())
     agentId  String?
     agent    Agent?   @relation(fields: [agentId], references: [id])
   }
   ```

3. **多对多关系**:
   使用显式中间表模型：
   ```prisma
   model ApiKey {
     id             String         @id @default(cuid())
     apiKeyFeatures ApiKeyFeature[]
   }

   model ApiFeature {
     id             String         @id @default(cuid())
     apiKeyFeatures ApiKeyFeature[]
   }

   model ApiKeyFeature {
     id        String     @id @default(cuid())
     apiKeyId  String     @map("api_key_id")
     apiKey    ApiKey     @relation(fields: [apiKeyId], references: [id], onDelete: Cascade)
     featureId String     @map("feature_id")
     feature   ApiFeature @relation(fields: [featureId], references: [id], onDelete: Cascade)

     @@unique([apiKeyId, featureId])
   }
   ```

4. **自引用关系**:
   ```prisma
   model Agent {
     id       String   @id @default(cuid())
     parentId String?
     parent   Agent?   @relation("AgentHierarchy", fields: [parentId], references: [id])
     children Agent[]  @relation("AgentHierarchy")
   }
   ```

### 索引定义

1. **单字段索引**：
   ```prisma
   model ApiUsageLog {
     id        String @id @default(cuid())
     apiKeyId  String
     createdAt DateTime @default(now())

     @@index([apiKeyId])
     @@index([createdAt])
   }
   ```

2. **唯一索引/约束**：
   ```prisma
   model ApiKey {
     id  String @id @default(cuid())
     key String @unique

     @@index([key])
   }
   ```

3. **多字段唯一约束**：
   ```prisma
   model ApiKeyFeature {
     apiKeyId  String
     featureId String

     @@unique([apiKeyId, featureId])
   }
   ```

### 枚举类型定义

枚举定义时使用 `@map` 确保数据库中存储的值与代码中使用的枚举值一致：

```prisma
enum AgentRole {
    ADMIN    @map("ADMIN")    // 管理员(0)
    BRANCH   @map("BRANCH")   // 分公司(1)
    DIRECTOR @map("DIRECTOR") // 联席董事(2)
    PARTNER  @map("PARTNER")  // 合伙人(3)
    SALES    @map("SALES")    // 销售(4)
}
```

## 数据库操作流程

### 修改数据库模型

1. 在对应的 .prisma 文件中修改或添加模型定义
2. 根据需要修改关系、索引和约束
3. 确保遵循命名规范和字段定义规范

### 应用更改到数据库

使用预定义的npm脚本应用更改：

```bash
# 在项目根目录执行：
pnpm db:push    # 将schema更改推送到数据库
pnpm db:generate # 生成更新的TypeScript类型和Zod验证
```

> **注意**: `db:push` 不会创建迁移记录，仅用于开发环境。生产环境应该使用 `prisma migrate` 创建迁移记录。

相关命令完整列表：

```json
"scripts": {
  "generate": "prisma generate --schema ./prisma/schema",
  "push": "dotenv -c -e ../../.env -- prisma db push --skip-generate --schema ./prisma/schema",
  "migrate": "dotenv -c -e ../../.env -- prisma migrate dev --schema ./prisma/schema",
  "studio": "dotenv -c -e ../../.env -- prisma studio --schema ./prisma/schema",
  "format": "dotenv -c -e ../../.env -- prisma format --schema ./prisma/schema",
  "validate": "dotenv -c -e ../../.env -- prisma validate --schema ./prisma/schema"
}
```

## 最佳实践

1. **正确使用软删除**：
   - 对于重要的业务数据使用软删除 (`deletedAt DateTime?`)
   - 在查询时添加过滤条件 `WHERE deletedAt IS NULL`

2. **使用合适的外键约束**：
   - 使用 `onDelete: Cascade` 在删除父记录时自动删除子记录
   - 使用 `onDelete: SetNull` 在删除父记录时将子记录的外键设为null

3. **使用JSON字段**：
   - 对于结构复杂但不需要单独查询的数据使用JSON字段
   - 例如：`metadata Json?` 用于存储元数据

4. **按业务领域拆分模型**：
   - 继续遵循现有的按业务功能拆分模型到不同.prisma文件的做法
   - 在添加新功能时考虑是扩展现有文件还是创建新文件

5. **避免循环依赖**：
   - 确保不同.prisma文件之间不存在循环依赖
   - 设计关系时确保依赖方向清晰

## 常见字段类型及使用场景

| 类型 | 使用场景 | 示例 |
|------|---------|------|
| String | ID、名称、代码等 | `name String` |
| Int | 计数、金额(分)、排序 | `price Int` |
| Float | 比率、小数值 | `discountRate Float` |
| Boolean | 状态标志、开关 | `isActive Boolean` |
| DateTime | 时间戳、过期时间 | `expiresAt DateTime` |
| Json | 元数据、配置数据 | `metadata Json?` |
| Enum | 状态、类型等有限选项 | `status OrderStatus` |

## 扩展数据库注意事项

在向数据库添加新模型或修改现有模型时，请考虑以下几点：

1. **向后兼容性**：
   - 添加新字段时，尽量设置默认值或允许null
   - 修改现有字段类型时，确保数据兼容

2. **数据迁移**：
   - 重命名字段时使用 `@map` 确保数据不丢失
   - 复杂的数据转换可能需要自定义迁移脚本

3. **性能考虑**：
   - 对频繁查询的字段添加适当的索引
   - 大型表或频繁更新的表考虑分表策略

4. **集成测试**：
   - 数据库更改后执行完整的集成测试
   - 验证现有功能不受影响


## 数据初始化

数据库种子脚本提供了一套完整的测试数据初始化机制，用于快速构建测试环境或开发环境。种子数据支持主要业务实体的初始化，包括用户认证、代理商层级、API功能、订单、套餐、推荐关系和创作记录等。

### 目录结构

```
frontend/tooling/scripts/
├── src/
│   ├── seed.ts                # 入口文件
│   └── seed/
│       ├── index.ts           # 种子数据主流程
│       ├── config/            # 种子数据配置文件
│       │   ├── api.json       # API功能配置
│       │   ├── auth.json      # 用户认证配置
│       │   ├── creation.json  # 创作记录配置
│       │   ├── order.json     # 订单配置
│       │   ├── plan.json      # 套餐配置
│       │   ├── referral.json  # 推荐关系配置
│       │   └── types.ts       # 类型定义
│       └── seeders/           # 各模块种子脚本
│           ├── api.ts         # API种子脚本
│           ├── auth.ts        # 认证种子脚本
│           ├── creation.ts    # 创作记录种子脚本
│           ├── order.ts       # 订单种子脚本
│           ├── plan.ts        # 套餐种子脚本
│           └── referral.ts    # 推荐种子脚本
```

### 运行方式

在项目根目录运行以下命令初始化数据库：

```bash
pnpm seed
```

此命令会清空现有数据并按照预定义的顺序依次初始化各模块数据。

### 初始化流程

数据初始化按以下顺序执行：

1. **清理数据库**：删除所有现有数据
2. **初始化用户认证**：创建管理员和代理商层级结构  
3. **初始化API功能**：创建API功能和密钥
4. **初始化套餐**：创建套餐和功能配置
5. **初始化订单**：创建测试订单数据
6. **初始化创作记录**：创建数字人和创作内容
7. **初始化推荐关系**：创建代理商推荐数据

### 数据配置

#### 用户和代理商体系

`auth.json` 定义了初始用户数据和代理商层级结构：

```json
{
  "admin": { /* 管理员数据 */ },
  "branch": { /* 分公司示例数据 */ },
  "director": { /* 联席董事示例数据 */ },
  "partner": { /* 合伙人示例数据 */ },
  "sales": { /* 销售示例数据 */ },
  "defaultUsers": [ /* 批量创建的默认用户 */ ]
}
```

代理商形成完整的层级树状结构：
- 管理员 (ADMIN)
  - 分公司 (BRANCH)
    - 联席董事 (DIRECTOR)
      - 合伙人 (PARTNER)
        - 销售 (SALES)

#### API功能配置

`api.json` 定义了系统支持的API功能：

```json
{
  "features": {
    "avatar": [ /* 数字人平台API功能 */ ],
    "viral": [ /* 图文平台API功能 */ ],
    "video": [ /* 视频平台API功能 */ ]
  },
  "apiKeyDefaults": {
    "ADMIN": { /* 管理员API密钥默认配置 */ },
    "BRANCH": { /* 分公司API密钥默认配置 */ },
    /* 其他角色配置... */
  }
}
```

每个API功能定义了类型、名称、端点和算力消耗。

#### 套餐配置

`plan.json` 定义了系统的套餐体系：

```json
{
  "plans": [
    {
      "id": "plan_9000ai_trial_001",
      "name": "9000AI体验套餐",
      "level": "TRIAL",
      /* 套餐详细配置... */
      "features": [ /* 套餐包含的功能 */ ]
    },
    /* 其他套餐... */
  ]
}
```

套餐定义了价格、有效期、算力值和可用功能的限制。

#### 订单配置

`order.json` 定义了测试订单数据：

```json
{
  "consumerOrders": [ /* 消费者套餐订单 */ ],
  "agentOrders": [ /* 代理商算力订单 */ ]
}
```

#### 推荐关系配置

`referral.json` 定义了代理商推荐关系和奖励：

```json
{
  "referrals": [
    {
      "referrerId": "agent_5",
      "userId": "1",
      /* 推荐详情... */
      "rewards": [ /* 推荐奖励 */ ]
    },
    /* 其他推荐记录... */
  ]
}
```

### 扩展种子数据

#### 添加新用户

在 `auth.json` 的 `defaultUsers` 数组中添加新用户：

```json
{
  "id": "new_user_id",
  "email": "<EMAIL>",
  "name": "新用户",
  /* 其他用户属性... */
  "agent": {
    "role": "SALES",
    /* 代理商属性... */
  },
  "consumer": {
    "role": "REGULAR",
    /* 消费者属性... */
  }
}
```

#### 添加新套餐

在 `plan.json` 的 `plans` 数组中添加新套餐：

```json
{
  "id": "plan_new_001",
  "name": "新套餐",
  "level": "BASIC",
  /* 套餐详细配置... */
  "features": [
    {
      "type": "AVATAR_CLONE",
      "enabled": true,
      /* 功能详细配置... */
    },
    /* 其他功能... */
  ]
}
```

#### 添加新API功能

在 `api.json` 的相应分类中添加新API功能：

```json
{
  "type": "AI_NEW_FEATURE",
  "name": "新功能",
  "code": "new-feature",
  "description": "描述",
  "endpoint": "/api/v1/new-feature",
  "method": "POST",
  "pointsCost": 10,
  "rateLimit": 50
}
```

### 注意事项

1. **ID关联**：确保引用ID在相关实体间保持一致
2. **执行顺序**：种子脚本按特定顺序执行，保证实体间依赖关系
3. **数据清理**：每次运行种子脚本会清空现有数据，请在非生产环境使用
4. **事务处理**：所有操作在事务中执行，确保数据一致性
5. **默认密码**：所有测试用户的默认密码为"123456"

### 自定义种子数据

如需定制更复杂的种子数据，可以：

1. 修改 `config/` 目录下的JSON配置文件
2. 更新 `seeders/` 目录下对应模块的种子脚本
3. 在 `index.ts` 中调整初始化顺序或添加新模块

完成修改后，运行 `pnpm seed` 应用更改。

