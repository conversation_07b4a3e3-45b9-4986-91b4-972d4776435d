---
description: 
globs: frontend/apps/web/app/(saas)/**
alwaysApply: false
---
---
description: 当要编写前端frontend项目的时候
globs: 
alwaysApply: false
---
# 9000AI前端开发指南

## 技术栈概览

- **核心框架**：Next.js 15.2.3 (App Router)
- **JS框架**：React 19.0.0
- **类型系统**：TypeScript 5.8.2
- **样式方案**：Tailwind CSS 4.0.15
- **UI组件**：Shadcn UI、Radix UI
- **状态管理**：React Query 5.66.0、Jotai 2.12.2、Zustand 5.0.2
- **表单处理**：React Hook Form 7.54.2、Zod 3.24.1
- **国际化**：next-intl 4.0.2
- **包管理器**：pnpm 9.3.0
- **数据库访问**：Prisma ORM 6.3.1
- **构建工具**：Turbo 2.4.4
- **代码规范**：Biome 1.9.4

## 核心原则

1. **函数式编程**：使用函数式和声明式编程模式，避免类组件
2. **类型安全**：所有代码使用TypeScript，合理定义接口和类型
3. **组件模块化**：组件精简、职责单一，避免过大组件
4. **性能优先**：优先使用React Server Components，合理拆分客户端组件
5. **代码复用**：通过抽象共享逻辑，避免代码重复
6. **命名规范**：使用描述性命名，采用辅助动词（如isLoading, hasError）

## 项目结构

```
frontend/
├── apps/                    # 应用目录
│   └── web/                # 主Web应用 (Next.js)
│       ├── app/            # App Router路由
│       │   ├── agent/      # 代理商平台
│       │   ├── ecosystem/  # 生态平台
│       │   └── app/        # 应用主页
│       ├── components/     # 组件库
│       └── lib/            # 工具函数
├── packages/               # 共享包
│   ├── ai/                # AI功能集成
│   ├── api/               # API客户端
│   ├── auth/              # 认证相关
│   ├── core/              # 核心包 (处理数字人核心算法逻辑) 
│   ├── database/          # 数据库模型
│   ├── i18n/              # 国际化
│   ├── logs/              # 日志系统
│   ├── mail/              # 邮件功能
│   ├── mq/                # 消息队列包
│   ├── payments/          # 支付系统
│   ├── redis/             # Redis包
│   ├── storage/           # 存储系统
│   └── utils/             # 通用工具
└── tooling/               # 构建工具
    ├── scripts/           # 脚本工具
    ├── tailwind/          # Tailwind配置
    └── typescript/        # TS配置
```

## 角色系统架构

### 代理商平台角色 (AgentRole)

- **ADMIN (0)**：管理员 - 最高权限，管理所有功能
- **BRANCH (1)**：分公司 - 城市级运营权，管理联席董事
- **DIRECTOR (2)**：联席董事 - 区域级运营权，管理合伙人
- **PARTNER (3)**：合伙人 - 团队管理权，管理销售
- **SALES (4)**：销售 - 个人分销权，无下级

### 消费者平台角色 (ConsumerRole)

- **INTERNAL**：内部测试用户 - 系统测试和内部使用
- **REGULAR**：普通用户 - 标准消费者角色

## 导航结构

- 登录后重定向至系统选择页 `/app/system-select`
- 基于用户角色的系统选择:
  - **代理商平台**:
    - `/agent/sales` - 销售系统
    - `/agent/merchant` - 招商系统
    - `/agent/admin` - 管理系统
  - **生态平台**:
    - `/ecosystem/avatar` - 数字人系统
    - `/ecosystem/content` - 图文系统
    - `/ecosystem/video` - 视频系统

## 身份认证流程

- 使用多种认证方式:
  - 手机号+验证码登录
  - 密码登录
  - 微信登录
  - Passkey (WebAuthn)
- 每个用户同时具有Agent和Consumer两种身份
- 测试环境中所有用户默认密码为"123456"

## 命名规范

### 目录和文件

- 目录使用小写中划线分隔 (kebab-case)
  ```
  frontend/apps/web/components/auth-wizard/
  ```
- 组件文件使用PascalCase
  ```
  AgentDashboard.tsx
  UserProfileCard.tsx
  ```
- 工具函数使用camelCase
  ```
  formatCurrency.ts
  useUserData.ts
  ```

### 组件命名

- 优先使用具名导出而非默认导出
- 角色相关组件使用角色前缀
  ```tsx
  export function AgentDashboard() { ... }
  export function ConsumerVideoList() { ... }
  ```
- 通用组件使用功能描述性命名
  ```tsx
  export function Pagination() { ... }
  export function FormTextField() { ... }
  ```

## TypeScript最佳实践

- 使用接口定义组件props
  ```tsx
  interface UserCardProps {
    userId: string;
    name: string;
    role: AgentRole;
    isActive?: boolean;
  }
  
  export function UserCard({ userId, name, role, isActive = true }: UserCardProps) {
    // ...
  }
  ```

- 使用枚举和联合类型增强类型安全
  ```tsx
  // 使用角色枚举
  function getRoleLabel(role: AgentRole): string {
    switch (role) {
      case "ADMIN": return "管理员";
      case "BRANCH": return "分公司";
      case "DIRECTOR": return "联席董事";
      case "PARTNER": return "合伙人";
      case "SALES": return "销售";
      default: return "未知角色";
    }
  }
  
  // 使用可辨识联合类型
  type AgentAction = 
    | { type: "PROMOTE"; targetId: string; newRole: AgentRole }
    | { type: "SUSPEND"; targetId: string; reason: string }
    | { type: "ACTIVATE"; targetId: string };
  ```

## UI和样式指南

- 使用Shadcn UI和Radix UI组件
- 所有样式通过Tailwind CSS实现
- 使用`cn`函数合并类名
  ```tsx
  import { cn } from "@/lib/utils";
  
  function Button({ className, variant = "default", ...props }) {
    return (
      <button
        className={cn(
          "px-4 py-2 rounded font-medium",
          variant === "primary" && "bg-blue-500 text-white",
          variant === "secondary" && "bg-gray-200 text-gray-800",
          className
        )}
        {...props}
      />
    );
  }
  ```

- 遵循角色主题规范:
  - 代理商平台: 蓝色主题
  - 消费者平台: 绿色主题

## 性能优化

- 优先使用React Server Components (RSC)
- 仅在必要时使用`'use client'`指令
  - 需要用户交互和客户端状态管理
  - 需要使用浏览器API
  - 需要使用React hooks
- 使用Suspense包装客户端组件
  ```tsx
  import { Suspense } from "react";
  import Loading from "./loading";
  import DynamicChart from "./DynamicChart";
  
  export default function Dashboard() {
    return (
      <div>
        <h1>Dashboard</h1>
        <Suspense fallback={<Loading />}>
          <DynamicChart />
        </Suspense>
      </div>
    );
  }
  ```
- 实现按需加载和代码分割
  ```tsx
  import dynamic from "next/dynamic";
  
  const AdminPanel = dynamic(() => import("./AdminPanel"), {
    loading: () => <p>Loading...</p>,
  });
  ```
- 图片优化
  - 使用Next.js的Image组件
  - 设置合适的width和height属性
  - 使用优化的图像格式(WebP)

## 数据获取和状态管理

- 使用React Query进行服务端状态管理
  ```tsx
  'use client'
  
  import { useQuery } from "@tanstack/react-query";
  import { fetchUserData } from "@/lib/api";
  
  export function UserProfile({ userId }) {
    const { data, isLoading, error } = useQuery({
      queryKey: ["user", userId],
      queryFn: () => fetchUserData(userId)
    });
    
    if (isLoading) return <p>Loading...</p>;
    if (error) return <p>Error: {error.message}</p>;
    
    return <div>{data.name}</div>;
  }
  ```
- 使用Jotai或Zustand进行客户端本地状态管理
- URL参数状态管理使用nuqs
  ```tsx
  'use client'
  
  import { useQueryState } from "nuqs";
  
  export function FilteredList() {
    const [filter, setFilter] = useQueryState("filter");
    const [page, setPage] = useQueryState("page", { defaultValue: "1" });
    
    // ...
  }
  ```

## 角色权限控制

- 在中间件中实现角色权限检查
  ```tsx
  // middleware.ts
  export function middleware(request: NextRequest) {
    const user = getUser(request);
    const path = request.nextUrl.pathname;
    
    // 代理商平台访问控制
    if (path.startsWith("/agent")) {
      if (!user || !user.agent) {
        return NextResponse.redirect(new URL("/auth/login", request.url));
      }
      
      // 管理员路由限制
      if (path.startsWith("/agent/admin") && user.agent.role !== "ADMIN") {
        return NextResponse.redirect(new URL("/agent/sales", request.url));
      }
    }
    
    // 生态平台访问控制
    if (path.startsWith("/ecosystem")) {
      if (!user || !user.consumer) {
        return NextResponse.redirect(new URL("/auth/login", request.url));
      }
    }
  }
  ```
- 组件级权限控制
  ```tsx
  function RoleGuard({ 
    children, 
    allowedRoles,
    fallback = <p>无权访问</p>
  }: {
    children: React.ReactNode;
    allowedRoles: AgentRole[];
    fallback?: React.ReactNode;
  }) {
    const { user } = useUser();
    
    if (!user?.agent || !allowedRoles.includes(user.agent.role)) {
      return <>{fallback}</>;
    }
    
    return <>{children}</>;
  }
  ```

## 国际化和多语言

- 使用next-intl进行国际化
- 支持中文(默认)、英文和德文
- 使用方式：
  ```tsx
  'use client'
  
  import { useTranslations } from "next-intl";
  
  export function WelcomeMessage() {
    const t = useTranslations("common");
    
    return <h1>{t("welcome")}</h1>;
  }
  ```

## 开发流程

- 开发命令: `pnpm dev`
- 构建命令: `pnpm build`
- 类型检查: `pnpm type-check`
- 代码格式化: `pnpm format`
- Shadcn UI组件添加: `pnpm shadcn-ui add [component]`

## 最佳实践

1. 文件结构遵循以下顺序:
   - 导出组件
   - 子组件
   - 辅助函数
   - 常量和静态内容
   - 类型定义

2. 组件实现:
   ```tsx
   // UserCard.tsx
   
   // 1. 导入
   import { useState } from "react";
   import { Avatar } from "@/components/ui/avatar";
   import { formatDate } from "@/lib/utils";
   
   // 2. 类型定义
   interface UserCardProps {
     id: string;
     name: string;
     role: AgentRole;
     createdAt: string;
   }
   
   // 3. 主组件
   export function UserCard({ id, name, role, createdAt }: UserCardProps) {
     // 实现
     return (
       <div className="p-4 border rounded">
         <CardHeader name={name} role={role} />
         <CardContent id={id} createdAt={createdAt} />
       </div>
     );
   }
   
   // 4. 子组件
   function CardHeader({ name, role }: { name: string; role: AgentRole }) {
     return (
       <div className="flex items-center gap-3">
         <Avatar name={name} />
         <div>
           <h3 className="font-medium">{name}</h3>
           <p className="text-sm text-gray-500">{getRoleLabel(role)}</p>
         </div>
       </div>
     );
   }
   
   function CardContent({ id, createdAt }: { id: string; createdAt: string }) {
     return (
       <div className="mt-3 text-sm">
         <p>ID: {id}</p>
         <p>注册时间: {formatDate(createdAt)}</p>
       </div>
     );
   }
   
   // 5. 辅助函数
   function getRoleLabel(role: AgentRole): string {
     // 实现
   }
   ```

3. 避免大量使用useEffect和useState，优先考虑React Server Components

4. 使用组合而非继承来共享组件逻辑

5. 遵循"从表单到API"的数据流，使用Zod进行数据验证
