---
description: 
globs: 
alwaysApply: false
---
# RabbitMQ (mq) 模块使用最佳实践

兄弟，用咱们的 `mq` 模块处理消息时，记住下面这些最佳实践，保证你的服务稳如老狗，高效如风！

## 1. 消费者 (`consumer.ts`)核心要点

我们已经为你封装了 `startConsumer`，它处理了不少棘手的问题，但你还是得留神：

### 消息处理 (`onMessage` 回调)
- **幂等性处理**: 你的 `onMessage` 回调必须保证幂等性！天晓得消息会不会因为网络抽风重发一次。确保多次处理同一消息的结果和一次处理完全一样。
- **错误处理**:
    - **解析错误**: `startConsumer` 会自动处理 JSON 解析错误。默认情况下，解析失败的消息会被 `nack` 并且不会重新入队。你可以通过 `ConsumerOptions.requeueFailedMessages` 来改变这个行为，但想清楚了再改，别搞出个死信循环！
    - **业务逻辑错误**: 在你的 `onMessage` 逻辑中，如果遇到可重试的错误，调用 `nack(true)` 来让消息重新入队。如果是不可逆的错误（比如数据格式彻底不对），调用 `nack(false)` 把它送走，别占着茅坑不拉屎。
    - **异步确认**: `onMessage` 是 `async` 的，并且接收 `ack` 和 `nack` 作为参数。处理完消息后，**务必、一定、必须** 调用 `ack()` 或 `nack()` 中的一个。不然消息会一直悬着，直到超时，然后 RabbitMQ 会认为你挂了，把消息重新发给别人。
- **日志记录**: `startConsumer` 已经打了不少日志，但你自己的业务逻辑也别忘了打关键日志，方便排查问题。特别是 `messageId` 和 `correlationId`，带上它们，查日志快人一步。

### 消费者配置 (`ConsumerOptions`)
- **`durable`**: 队列持久化默认是 `true`。如果你的消息很重要，丢了就要赔钱，那务必保持这个设置。如果只是些临时通知，丢了也无所谓，可以设为 `false` 提升点性能。
- **`prefetchCount`**: 控制消费者一次能从 RabbitMQ 预取多少条消息。设置太小，网络开销大；设置太大，如果一个消费者挂了，一堆消息得等超时才能被重新分配。根据你的消息处理速度和资源情况来调整。`startConsumer` 提供了这个选项，用起来！
- **`requeueFailedMessages`**:
    - 解析消息失败时，是否重新入队。默认为 `false`。
    - 业务处理失败时（`onMessage` 抛异常），是否重新入队。默认为 `false`。
    想清楚失败的原因和后果再决定是否重入队。
- **`queueOptions`**: 高级队列参数，比如 `messageTtl`, `maxLength` 等，按需配置。
- **`consumerTag`**: 给你的消费者起个响亮的名字，方便在 RabbitMQ管理界面识别。

### 优雅关闭
- `startConsumer` 返回一个 `close` 函数。当你的应用要关闭时，记得调用它。它会帮你取消消费者注册，并关闭 channel，做到善始善终。

## 2. 生产者 (当前未提供，但若要实现)
- **连接和 Channel 管理**: 别每次发消息都新建连接和 Channel，那效率低得感人。考虑使用连接池或者长效 Channel。
- **消息持久化**: 如果消息很重要，发送时设置 `persistent: true`。当然，队列也得是持久化的才行。
- **发布确认 (Publisher Confirms)**: 想知道消息是不是真的到 RabbitMQ 了？用发布确认机制。有性能开销，但可靠性更高。
- **错误处理**: 处理连接失败、Channel 关闭等异常。

## 3. 通用建议
- **队列命名**: 清晰、一致。比如 `service.event.object`。
- **消息内容**: 尽量简洁。JSON 是个不错的选择。复杂对象序列化前考虑下版本兼容问题。
- **监控**: RabbitMQ Management Plugin 是你的好朋友，多看看队列积压、消费者状态。
- **避免长任务**: `onMessage` 回调别搞太耗时的操作，会阻塞其他消息处理。实在不行，拆成多个步骤，或者扔到后台任务里。

记住，用好消息队列，系统解耦、异步处理、削峰填谷都不是梦！写出牛逼的 MQ 应用，就靠你了！💪

---
**相关文件参考:**
- 消费者实现: [frontend/packages/mq/src/consumer.ts](mdc:frontend/packages/mq/src/consumer.ts)
- MQ 类型定义: [frontend/packages/mq/src/types.ts](mdc:frontend/packages/mq/src/types.ts)
- MQ 客户端: [frontend/packages/mq/src/client.ts](mdc:frontend/packages/mq/src/client.ts)

