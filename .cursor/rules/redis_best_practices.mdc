---
description: 
globs: 
alwaysApply: false
---
# Redis 模块使用最佳实践

嘿，伙计！用咱们的 `redis` 模块跟 Redis 打交道？那可得讲究点章法。遵循这些最佳实践，让你的应用性能起飞，数据安全有保障！

## 1. 连接管理
- **连接池**: 别傻乎乎地每次操作都新建连接，用连接池！咱们的模块（希望）已经封装好了，你就放心用。如果没封装，赶紧提需求！
- **优雅关闭**: 应用退出时，记得关闭 Redis 连接，释放资源。

## 2. Key 设计
- **命名规范**: 清晰、简洁、有层级。比如 `user:{userId}:profile` 或者 `session:{sessionId}`。
- **Key 长度**: 别太长，浪费内存，影响性能。也别太短，看不懂啥意思。
- **避免 Big Keys**: 一个 Key 存太多数据（比如几百 MB 的 JSON），会导致 Redis 阻塞，操作变慢，甚至引发内存问题。尽量拆分。

## 3. 数据类型选择
- **按需选择**: Redis 提供多种数据类型 (String, Hash, List, Set, Sorted Set, Stream)。根据你的业务场景选最合适的，别万物皆 String。
    - **Hash**: 存对象结构数据时，用 Hash 比 JSON String 更高效，特别是只更新对象部分字段时。
    - **Set**: 存唯一值集合，比如用户标签、IP 黑名单。
    - **Sorted Set**: 存有序集合，比如排行榜、带时间戳的任务队列。
- **序列化**: 存复杂对象时，考虑用 Protocol Buffers 或 MessagePack 代替 JSON，体积更小，解析更快。如果用 JSON，也要注意选择高效的库。

## 4. 命令使用
- **批量操作**: 尽量用 `MSET`/`MGET`、`HMSET`/`HMGET` 等批量命令，减少网络往返。
- **避免慢查询**: `KEYS` 命令在生产环境是大忌！它会遍历所有 Key，数据量大时直接卡死 Redis。用 `SCAN` 代替。其他耗时长的命令（如 `SORT`, `LREM` 操作大列表）也要小心。
- **Lua 脚本**: 对于需要原子性执行的多个操作，考虑用 Lua 脚本。减少网络开销，保证操作原子性。但脚本别太复杂，不然也慢。

## 5. 内存管理与持久化
- **设置过期时间 (TTL)**: 给缓存数据设置合理的过期时间，避免内存被无用数据占满。用 `EXPIRE` 或 `SETEX`。
- **内存淘汰策略**: 当内存不足时，Redis 会根据配置的淘汰策略（如 `volatile-lru`, `allkeys-lru`）删除 Key。理解这些策略，选个适合你业务的。
- **持久化**:
    - **RDB**: 快照持久化，适合备份，恢复速度快。但有数据丢失风险（两次快照之间的数据）。
    - **AOF**: 命令追加持久化，数据更安全，但文件可能较大，恢复速度比 RDB 慢。
    - 根据业务对数据安全性的要求选择。可以同时开启。

## 6. 错误处理与监控
- **客户端错误处理**: 捕获并妥善处理 Redis 操作可能发生的错误（连接错误、超时等）。
- **监控**: 用 Redis 自带的 `INFO` 命令，或者集成 Prometheus、Grafana 等监控工具，关注内存使用、连接数、命中率、慢查询等指标。

## 7. 安全
- **密码保护**: 给 Redis 设置密码。
- **禁用危险命令**: 如果用不到，可以在配置中禁用一些危险命令，如 `FLUSHALL`, `CONFIG`。
- **网络安全**: 限制只有应用服务器能访问 Redis 实例。

## 8. 模块使用 (假设咱们的模块提供了以下功能)
- **封装**: 我们的 `redis` 模块应该封装了连接管理、常用命令等，让你用起来更方便。
- **Promise 支持**: 异步操作应该返回 Promise，方便你用 `async/await`。
- **类型提示**: 如果是用 TypeScript，模块应该提供良好的类型定义。

写出高性能、高可用的 Redis 应用，这些经验能帮你少走弯路。骚年，我看好你哦！😉

---
**相关文件参考:**
- Redis 模块入口: [frontend/packages/redis/index.ts](mdc:frontend/packages/redis/index.ts)
- Redis 模块核心实现: (请根据实际情况引用 `src` 目录下的核心文件，例如 `client.ts` 或 `commands.ts`)

