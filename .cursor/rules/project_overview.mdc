---
description: 当要了解整个项目的概况的时候调用此规则。
globs: 
alwaysApply: false
---
---
description: 当要了解整个项目的概况的时候调用此工具。
globs: 
alwaysApply: false
---
# 项目概述

9000AI数字人创作平台是一个企业级AI驱动的数字人创作平台，提供数字人合成、声音克隆和AI内容创作的一站式解决方案。平台分为代理商平台和生态平台两大核心业务模块，通过算力系统实现资源计费与控制。

# 项目结构

9000AI/
├── frontend/                # 前端应用 (Monorepo)
│   ├── apps/               # Next.js应用
│   │   └── web/           # 主Web应用
│   ├── packages/          # 后端逻辑处理及共享功能包
│   │   ├── api/           # API客户端
│   │   ├── auth/          # 认证相关功能
│   │   ├── database/      # 数据库模型和迁移 (Prisma ORM)
│   │   ├── i18n/          # 国际化 (next-intl)
│   │   ├── logs/          # 日志系统
│   │   ├── mail/          # 邮件功能
│   │   ├── mq/            # 消息队列包
│   │   ├── payments/      # 支付系统
│   │   ├── redis/         # Redis包
│   │   ├── scheduler/     # 调度器包
│   │   ├── storage/       # 存储系统 (MinIO)
│   │   ├── utils/         # 通用工具
│   │   └── websocket-service/ # WebSocket 服务
│   ├── config/            # 前端及Turborepo配置
│   └── tooling/           # 构建与开发工具 (TypeScript, Tailwind CSS, Biome)
├── docs/                   # 项目文档
│   ├── api/               # API文档
│   ├── implementation_notes/ # 实现说明
│   ├── api_reference.md  # API参考
│   ├── backend_development_guide.md # 后端开发指南
│   ├── database_design.md # 数据库设计
│   ├── deployment_guide.md # 部署指南
│   ├── frontend_development_guide.md # 前端开发指南
│   ├── project_handover.md # 项目交接文档
│   └── testing_guide.md  # 测试指南
├── docker/                # Docker配置
│   └── postgres/         # PostgreSQL配置
└── docker-compose.yml    # 服务编排配置

# 系统架构

## 前端架构
- **技术栈**: 
  - Next.js 15.2.3 (App Router)
  - React 19.0.0
  - TypeScript 5.8.2
  - Tailwind CSS 4.0.15
  - Prisma ORM 6.3.1
  - tRPC
  - Zod 3.24.1
  - React Query 5.66.0
  - next-intl 4.0.2
- **Node环境**:
  - Node.js >= 20
  - pnpm 9.3.0
- **项目结构**: 
  - Monorepo架构，前端代码位于`frontend`目录
  - 主Web应用位于`frontend/apps/web`
  - 后端逻辑共享包位于`frontend/packages`目录
- **核心模块**:
  - 认证系统(`/auth`)
  - 代理商平台(`/agent`)
  - 生态平台(`/ecosystem`)

## 数据库设计
- **核心用户体系**:
  - User: 用户基础信息、认证数据和算力账户
  - Agent: 代理商信息、层级关系和业绩统计
  - Consumer: 消费者信息、套餐订阅和创作记录
  - Session/Account/Passkey: 多方式认证支持
- **多级代理结构**:
  - 管理员(ADMIN, 0): 最高权限，可管理所有功能
  - 分公司(BRANCH, 1): 城市级运营权，加盟费200万元，每小时永续收益60元，可管理1020个超级个体
  - 联席董事(DIRECTOR, 2): 区域级运营权，加盟费29.8万元，每小时永续收益40元，可管理76个超级个体
  - 合伙人(PARTNER, 3): 团队管理权，加盟费6.98万元，每小时永续收益20元，可管理11个超级个体
  - 销售(SALES, 4): 个人分销权，加盟费9800元，无下级管理名额
- **消费者角色**:
  - 内部用户(INTERNAL): 用于系统测试和内部使用
  - 普通用户(REGULAR): 标准消费者角色

## 订单和支付系统
- **订单类型**:
  - ConsumerOrder: 消费者套餐购买订单
  - AgentOrder: 代理商套餐购买订单
  - QuotaOrder: 代理商团队名额分配记录
- **支付方式**:
  - 微信支付: 原生扫码、JSAPI、H5、APP
  - 支付宝: 网页支付、手机网站支付
- **订单状态流转**:
  - PENDING→PAID→COMPLETED
  - 支持FAILED、CANCELLED、REFUNDED状态

# 核心业务逻辑

## 代理商平台
1. **销售系统** (`/agent/sales`)
   - 客户管理: 跟踪和管理潜在客户
   - 订单管理: 处理客户订单和支付
   - 佣金管理: 计算和跟踪销售佣金
   - 销售业绩分析: 分析销售数据和趋势

2. **招商系统** (`/agent/merchant`)
   - 代理商管理: 招募和管理下级代理商
   - 团队业绩: 跟踪团队销售情况
   - 线索管理: 管理潜在代理商线索
   - 区域管理: 划分和管理销售区域

3. **管理系统** (`/agent/admin`)
   - 全局管理: 平台核心设置和配置
   - 数据分析: 综合业务数据分析
   - 系统配置: 管理系统参数和设置
   - 用户管理: 管理平台用户

## 生态平台
1. **数字人系统** (`/ecosystem/avatar`)
   - 数字人创建与克隆: 通过图片或视频创建数字人模型(创建本身不消耗算力)
   - 声音合成: 创建和克隆声音模型
   - 实时视频生成: 使用数字人模型生成视频内容(消耗算力)
   - 数字人管理: 管理和组织数字人资产

2. **图文系统** (`/ecosystem/content`)
   - AI内容生成: 生成营销文案和内容
   - AI图片生成: 创建AI生成图像
   - 内容分发: 分发内容到多个渠道
   - 模板管理: 创建和使用内容模板

3. **视频系统** (`/ecosystem/video`)
   - 视频剪辑与混剪: AI辅助视频编辑
   - 字幕生成: 自动生成视频字幕
   - 视频特效: 添加AI生成特效
   - 视频配乐: 自动匹配背景音乐

## 算力经济体系
- **套餐订阅管理**:
  - 试用套餐(TRIAL): 免费体验
  - 基础套餐(BASIC): 1580元/2小时/90天
  - 增强套餐(ENHANCED): 7900元/10小时/365天
  - 企业套餐(ENTERPRISE): 23700元/30小时/365天
  - 高级套餐(PREMIUM): 63200元/80小时/365天
- **消耗模式**:
  - 按功能类型不同消耗一定算力
  - 一小时约等于6000算力
  - 不同套餐包含不同的算力额度
  - 支持算力充值和余额管理

## 推荐与奖励系统
- **推荐状态流程**:
  - PROCESSING→APPROVED/REJECTED
  - 支持CANCELLED、ESCALATED状态
- **推荐奖励**:
  - 超级个体推荐超级个体: 奖励1960元
  - 超级个体推荐合伙人: 奖励4000元
  - 合伙人推荐合伙人: 奖励13960元
  - 联席董事推荐联席董事: 奖励59600元
  - 分公司推荐分公司: 奖励40万元
- **批量购买优惠**:
  - 合伙人: 2个起购，享受6折优惠
  - 联席董事: 3个起购，享受4折优惠
  - 分公司: 10个起购，享受2折优惠
- **升级机制**:
  - 超级个体推荐11个超级个体，可升级为合伙人
  - 合伙人推荐6个合伙人，可升级为联席董事
  - 联席董事推荐10个联席董事，可升级为分公司

## API系统
- **API密钥管理**:
  - 支持多API密钥创建和管理
  - 提供密钥状态控制(ACTIVE/DISABLED/EXPIRED/REVOKED)
  - 可设置速率限制和使用配额
- **功能类型**:
  - AI文案生成
  - AI文案翻译
  - AI热点文案生成
  - AI普通音频生成
  - AI高级音频生成
  - AI数字人视频合成
  - AI图片生成
  - AI图片变体生成
  - AI图片编辑
  - AI视频剪辑
  - AI视频特效
  - AI视频字幕
  - AI视频模板
  - AI视频配乐
- **使用记录与统计**:
  - 跟踪API调用的端点、方法和状态码
  - 记录每次请求消耗的算力
  - 提供详细的使用日志和错误信息

# 认证与安全
- **认证方式**:
  - 手机号+验证码登录
  - 密码登录
  - 微信登录(JSAPI/扫码)
  - Passkey(WebAuthn)
  - 支持单点登录(SSO)
- **安全机制**:
  - JWT认证与刷新机制
  - 基于角色的访问控制(RBAC)
  - API请求签名验证
  - 敏感数据加密存储

# 国际化与多语言
- **支持语言**:
  - 中文(默认)
  - 英文
  - 德文
- **多货币支持**:
  - CNY(人民币,默认)
  - USD(美元)
  - HKD(港币)

# 交互流程

1. **用户注册/登录流程**:
   - 用户通过手机号/验证码、密码或微信登录
   - 登录成功后跳转至平台选择页(/app/system-select)
   - 根据用户角色，可以选择进入代理商平台或生态平台
   - 系统自动根据用户权限展示可访问的功能模块

2. **数字人创作流程**:
   - 用户上传素材(图片/视频)至MinIO存储
   - 系统处理并生成数字人模型(创建模型不消耗算力)
   - 用户可调整模型参数
   - 用户使用数字人生成视频内容(此时消耗算力)
   - 系统记录算力消耗情况

3. **代理商销售流程**:
   - 代理商邀请客户注册
   - 客户购买套餐
   - 系统记录销售关系
   - 根据分润规则计算佣金
   - 定期结算佣金

4. **代理商升级流程**:
   - 代理商发起下级代理招募
   - 潜在代理商注册并支付加盟费
   - 系统记录推荐关系
   - 计算推荐奖励并更新团队结构
   - 满足条件时触发自动升级
