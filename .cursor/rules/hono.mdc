---
description: 
globs: frontend/packages/api/**
alwaysApply: false
---
---
description: 当要编写frontend中的api模块hono的时候，使用这个规则。
globs: 
alwaysApply: false
---
# 9000AI Hono后端开发指南

## 框架概述

9000AI后端采用Hono框架构建RESTful API服务，具有以下特点：

- **轻量高效**：Hono是一个超轻量级的Web框架，专为边缘计算环境设计
- **TypeScript友好**：完整的类型支持，提供优秀的开发体验
- **路由系统**：基于文件的分层路由架构，清晰直观
- **中间件机制**：灵活的中间件链，支持请求/响应处理
- **OpenAPI集成**：使用hono-openapi自动生成API文档
- **内置验证**：集成Zod架构验证，确保请求数据有效性

## 项目架构

前缀目录：frontend/packages/api/

```
src/
├── app.ts                 # 应用入口
├── middleware/            # 全局中间件
│   ├── auth.ts            # 认证中间件
│   ├── cors.ts            # CORS处理
│   └── logger.ts          # 日志中间件
└── routes/                # API路由
    ├── auth/              # 认证相关路由
    ├── health/            # 健康检查
    ├── newsletter/        # 通讯相关
    ├── uploads/           # 文件上传
    └── v1/                # v1版本API
        ├── router.ts      # v1路由聚合
        ├── ecosystem      # 生态系统
        └── agent/         # 代理商系统
            ├── router.ts  # 代理商路由聚合
            ├── middleware.ts # 代理商中间件
            ├── referral/  # 推荐管理模块
            │   ├── approval/ # 审批相关
            │   ├── rewards/  # 奖励相关
            │   └── router.ts # 推荐路由
            ├── quota/     # 名额管理模块
            └── ...        # 其他业务模块
```

## 路由设计模式

### 基本路由结构

```typescript
// 创建基础路由实例
const router = new Hono()
  .basePath("/referral")  // 设置基础路径
  .route("/", rewardsRouter)  // 挂载子路由
  .route("/", approvalRouter);  // 挂载另一子路由

// 创建API端点
router.get("/stats", statsRoute, statsHandler);
router.post(
  "/approve",
  validator("json", schema),  // 参数验证
  approveRoute,              // 路由描述(OpenAPI)
  approveHandler             // 处理函数
);
```

### 模块化路由

```typescript
// 每个功能模块独立路由文件
export const referralRouter = new Hono()
  .basePath("/referral")
  .route("/rewards", referralRewardsRouter)
  .route("/approval", approvalRouter);

// 主路由聚合各模块
export const agentRouter = new Hono()
  .basePath("/agent")
  .use(authMiddleware)       // 应用中间件
  .use(validateAgentMiddleware)
  .route("/referral", referralRouter)
  .route("/quota", quotaRouter);
```

## API端点实现

### 标准处理器模式

```typescript
export const approveReferral = async (c: Context) => {
  const requestId = Math.random().toString(36).substring(7);
  const user = c.get("user");
  const { agentId } = user;
  const { id, note } = await c.req.json();

  try {
    logger.info("[API] 开始处理请求", {
      requestId,
      agentId,
      timestamp: new Date().toLocaleString(),
    });
    
    // 业务逻辑处理...
    
    return c.json({
      code: 200,
      data: {
        // 返回数据...
      },
    });
  } catch (error) {
    logger.error("[API] 处理请求失败", {
      requestId,
      error: error instanceof Error ? error.message : "未知错误",
      stack: error instanceof Error ? error.stack : undefined,
    });
    
    if (error instanceof HTTPException) {
      throw error;
    }
    
    return c.json(
      {
        code: 500,
        error: ErrorCode.INTERNAL_ERROR,
        message: ErrorMessages.INTERNAL_ERROR,
      },
      500
    );
  }
};
```

### OpenAPI 文档集成

```typescript
export const approveReferralRoute = describeRoute({
  tags: ["Agent/Merchant/Referral"],
  summary: "审批推荐申请",
  description: "审批指定的推荐申请",
  requestBody: {
    content: {
      "application/json": {
        schema: {
          type: "object",
          required: ["id"],
          properties: {
            id: {
              type: "string",
              description: "推荐记录ID",
            },
            note: {
              type: "string",
              description: "审批备注",
            },
          },
        },
      },
    },
  },
  responses: {
    200: {
      description: "审批成功",
      content: {
        "application/json": {
          schema: {
            // 响应架构定义...
          },
        },
      },
    },
    // 其他响应定义...
  },
});
```

## 请求验证

### Zod 验证架构

```typescript
// 定义验证架构
export const ApprovalActionSchema = z.object({
  id: z.string().min(1, { message: "推荐ID不能为空" }),
  note: z.string().optional(),
});

// 应用验证中间件
router.post(
  "/approve",
  validator("json", ApprovalActionSchema),
  approveHandler
);

// 导出类型定义
export type ApprovalActionRequest = z.infer<typeof ApprovalActionSchema>;
```

### 枚举与常量

```typescript
// 角色等级定义
export const ROLE_LEVEL = {
  SALES: 1,    // 超级个体/销售
  PARTNER: 2,  // 合伙人
  DIRECTOR: 3, // 董事
  BRANCH: 4,   // 分公司
  ADMIN: 5,    // 系统管理员
} as const;

export type RoleLevelType = keyof typeof ROLE_LEVEL;

// 枚举验证
export const ReferralStatusEnum = z.enum([
  "PROCESSING",
  "APPROVED",
  "REJECTED",
  "CANCELLED",
  "ESCALATED",
]);

export type ReferralStatusType = z.infer<typeof ReferralStatusEnum>;
```

## 中间件使用

### 认证中间件

```typescript
// 身份验证中间件
export const authMiddleware = async (
  c: Context,
  next: Next
): Promise<Response | void> => {
  try {
    const token = extractTokenFromRequest(c);
    if (!token) {
      return c.json(
        {
          code: 401,
          error: "UNAUTHORIZED",
          message: "未授权访问",
        },
        401
      );
    }
    
    const session = await validateToken(token);
    if (!session) {
      return c.json(
        {
          code: 401,
          error: "INVALID_TOKEN",
          message: "无效的访问令牌",
        },
        401
      );
    }
    
    // 将用户信息添加到上下文
    c.set("user", session.user);
    c.set("session", session);
    
    return next();
  } catch (error) {
    logger.error("认证中间件错误", {
      error: error instanceof Error ? error.message : "未知错误",
    });
    
    return c.json(
      {
        code: 500,
        error: "AUTH_ERROR",
        message: "认证处理错误",
      },
      500
    );
  }
};
```

### 权限中间件

```typescript
// 代理商权限验证中间件
export const validateAgentMiddleware = async (
  c: Context,
  next: Next
): Promise<Response | void> => {
  const user = c.get("user");
  
  if (!user || !user.agentId) {
    return c.json(
      {
        code: 403,
        error: "FORBIDDEN",
        message: "需要代理商权限",
      },
      403
    );
  }
  
  // 获取代理商详情
  const agent = await db.agent.findUnique({
    where: { id: user.agentId },
    include: {
      user: {
        select: {
          name: true,
          email: true,
        },
      },
    },
  });
  
  if (!agent) {
    return c.json(
      {
        code: 404,
        error: "AGENT_NOT_FOUND",
        message: "代理商信息不存在",
      },
      404
    );
  }
  
  // 将代理商信息添加到上下文
  c.set("agent", agent);
  
  return next();
};
```

## 错误处理机制

### 标准错误响应

```typescript
// 错误码定义
export const ReferralErrorCode = {
  INVALID_PARAMS: "INVALID_PARAMS",
  QUOTA_REQUIRED: "QUOTA_REQUIRED",
  UNAUTHORIZED: "UNAUTHORIZED",
  NOT_FOUND: "NOT_FOUND",
  INTERNAL_ERROR: "INTERNAL_ERROR",
} as const;

// 错误消息定义
export const ReferralErrorMessages = {
  INVALID_PARAMS: "无效的请求参数",
  QUOTA_REQUIRED: "需要团队名额",
  UNAUTHORIZED: "您没有权限执行此操作",
  NOT_FOUND: "推荐记录不存在",
  INTERNAL_ERROR: "服务器内部错误",
} as const;

// 统一错误响应
return c.json(
  {
    code: 404,
    error: ReferralErrorCode.NOT_FOUND,
    message: ReferralErrorMessages.NOT_FOUND,
  },
  404
);
```

### 异常捕获

```typescript
try {
  // 业务逻辑...
} catch (error) {
  logger.error("[API] 操作失败", {
    requestId,
    operationName: "createReferral",
    error: error instanceof Error ? error.message : "未知错误",
    stack: error instanceof Error ? error.stack : undefined,
  });

  if (error instanceof HTTPException) {
    throw error;  // 传递Hono异常
  }

  // 转换为标准错误响应
  return c.json(
    {
      code: 500,
      error: ErrorCode.INTERNAL_ERROR,
      message: ErrorMessages.INTERNAL_ERROR,
    },
    500
  );
}
```

## 最佳实践

### 按职责分层

1. **路由层**：负责URL映射和请求验证
2. **控制器层**：处理请求，调用服务
3. **服务层**：实现业务逻辑
4. **数据访问层**：与数据库交互

### API实现规范

1. **请求验证**：所有请求必须通过Zod验证
2. **日志记录**：操作日志记录请求ID、操作人、操作内容
3. **错误处理**：使用统一错误码和消息
4. **权限检查**：在业务逻辑开始前验证权限
5. **事务处理**：涉及多表操作使用数据库事务

### 日志最佳实践

```typescript
// 开始处理请求
logger.info("[API名称] 开始处理请求", {
  requestId,
  userId: user.id,
  operation: "操作名称",
  params: { /* 请求参数 */ },
  timestamp: new Date().toLocaleString(),
});

// 成功完成请求
logger.success("[API名称] 操作成功", {
  requestId,
  result: { /* 结果摘要 */ },
  timestamp: new Date().toLocaleString(),
});

// 请求处理失败
logger.error("[API名称] 操作失败", {
  requestId,
  error: error instanceof Error ? error.message : "未知错误",
  stack: error instanceof Error ? error.stack : undefined,
  timestamp: new Date().toLocaleString(),
});
```

### 常用命名约定

- **路由文件**：`router.ts`
- **验证架构**：`schemas.ts`
- **类型定义**：`types.ts`
- **API处理函数**：`操作名称-资源.ts` (如 `approve-referral.ts`)
- **中间件**：`middleware.ts`
- **工具函数**：`utils.ts`

## 路由测试

在开发过程中，可以通过以下方式测试API端点：

```bash
# 使用curl测试
curl -X POST http://localhost:3000/api/v1/agent/referral/rewards/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"phone":"13800138000","targetRole":"PARTNER","note":"测试推荐","userId":"user_123"}'

# 使用Thunder Client或Postman等工具测试
# URL: http://localhost:3000/api/v1/agent/referral/rewards/create
# Method: POST
# Headers: 
#   Content-Type: application/json
#   Authorization: Bearer YOUR_TOKEN
# Body:
# {
#   "phone": "13800138000",
#   "targetRole": "PARTNER",
#   "note": "测试推荐",
#   "userId": "user_123"
# }
```

## 启动与部署

### 开发环境

```bash
# 启动开发服务器
pnpm dev

# 构建生产版本
pnpm build

# 运行测试
pnpm test
```

### 生产环境

```bash
# 使用Docker部署
docker-compose up -d api-server
```

## 性能优化

1. **缓存策略**：对频繁访问的数据使用Redis缓存
2. **并行查询**：使用Promise.all并行执行多个数据库查询
3. **批量操作**：使用批处理替代循环单个处理
4. **查询优化**：只选择需要的字段，减少数据传输
5. **分页处理**：所有列表接口支持分页
