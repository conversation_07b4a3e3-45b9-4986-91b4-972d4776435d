{"[python]": {"editor.codeActionsOnSave": {"source.fixAll": "explicit"}}, "terminal.integrated.defaultProfile.windows": "PowerShell", "terminal.integrated.profiles.windows": {"PowerShell": {"source": "PowerShell", "args": ["-NoProfile"]}, "Command Prompt": {"path": "C:\\Windows\\System32\\cmd.exe", "args": []}, "Git Bash": {"source": "<PERSON><PERSON>"}}, "terminal.integrated.shellIntegration.enabled": true, "terminal.integrated.persistentSessionReviveProcess": "never", "terminal.integrated.enablePersistentSessions": false}