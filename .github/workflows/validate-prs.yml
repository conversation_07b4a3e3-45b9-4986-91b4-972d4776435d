name: Validate PRs

on:
  pull_request:
    branches: [main]
    paths:
      - 'frontend/**'
      - '.github/workflows/**'

jobs:
  lint:
    name: Lint code
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: frontend
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.x"
          
      - name: Setup PNPM
        uses: pnpm/action-setup@v4
        with:
          version: '9.3.0'
      
      - name: Install dependencies
        run: pnpm install
      
      - name: Setup Biome
        uses: biomejs/setup-biome@v2
      
      - name: Run Biome
        run: biome ci .
        
      - name: Type check
        run: pnpm type-check