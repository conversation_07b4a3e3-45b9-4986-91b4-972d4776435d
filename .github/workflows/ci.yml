name: 持续集成

on:
  pull_request:
    types: [opened, synchronize, reopened]
    branches:
      - main
      - develop
      - release

jobs:
  lint:
    name: 代码检查
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: frontend
    steps:
      - uses: actions/checkout@v4
      
      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.x"
          
      - name: 设置 PNPM
        uses: pnpm/action-setup@v4
        with:
          version: '9.3.0'
      
      - name: 安装依赖
        run: pnpm install
        
      - name: 设置 Biome
        uses: biomejs/setup-biome@v2
        with:
          working-dir: 'frontend'
        
      - name: 运行 Biome 检查
        run: biome ci .
  
  build:
    name: 构建测试
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: frontend
    needs: [lint]
    steps:
      - uses: actions/checkout@v4
      
      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.x"
          
      - name: 设置 PNPM
        uses: pnpm/action-setup@v4
        with:
          version: '9.3.0'
      
      - name: 安装依赖
        run: pnpm install
        
      - name: 生成 Prisma 客户端
        run: pnpm db:generate
      
      - name: 构建
        run: pnpm build
        env:
          # 使用项目中的环境变量
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
          DIRECT_URL: ${{ secrets.DIRECT_URL }}
          
          # 站点 URL
          NEXT_PUBLIC_SITE_URL: ${{ secrets.NEXT_PUBLIC_SITE_URL}}
          NEXT_PUBLIC_BASE_URL: ${{ secrets.NEXT_PUBLIC_BASE_URL}}
          NEXT_PUBLIC_WEBSOCKET_URL: ${{ secrets.NEXT_PUBLIC_WEBSOCKET_URL }}
          NEXT_PUBLIC_ENABLE_LOGGING: ${{ secrets.NEXT_PUBLIC_ENABLE_LOGGING }}
          
          # 认证配置
          BETTER_AUTH_SECRET: ${{ secrets.BETTER_AUTH_SECRET || 'b3tt3r@uth2024!' }}
          
          # 存储配置
          S3_ACCESS_KEY_ID: ${{ secrets.S3_ACCESS_KEY_ID || '9000ai_storage_admin' }}
          S3_SECRET_ACCESS_KEY: ${{ secrets.S3_SECRET_ACCESS_KEY || 'm1n10Adm1n2024!' }}
          S3_ENDPOINT: ${{ secrets.S3_ENDPOINT}}
          NEXT_PUBLIC_BUCKET_FOLDER_CLONE_INPUT_AUDIO: ${{ secrets.NEXT_PUBLIC_BUCKET_FOLDER_CLONE_INPUT_AUDIO }}
          NEXT_PUBLIC_BUCKET_FOLDER_CLONE_INPUT_VIDEO: ${{ secrets.NEXT_PUBLIC_BUCKET_FOLDER_CLONE_INPUT_VIDEO }}
          
          # 短信配置
          SMS_API_URL: ${{ secrets.SMS_API_URL || 'https://api.smsbao.com/sms' }}
          SMS_USERNAME: ${{ secrets.SMS_USERNAME || '9000ai' }}
          SMS_API_KEY: ${{ secrets.SMS_API_KEY }}
          SMS_MOCK: ${{ secrets.SMS_MOCK || 'true' }}

          # API设置
          API_VERSION: ${{ secrets.API_VERSION || '1.0.0' }}
          API_PREFIX: ${{ secrets.API_PREFIX || '/api' }}
          DEBUG: ${{ secrets.DEBUG || 'True' }}
          NODE_ENV: 'production'
          
          # 安全
          API_SECRET_KEY: ${{ secrets.API_SECRET_KEY || 'b3tt3r@uth2024!' }}
          ACCESS_TOKEN_EXPIRE_MINUTES: ${{ secrets.ACCESS_TOKEN_EXPIRE_MINUTES || '30' }}
          
          # MinIO设置
          S3_REGION: ${{ secrets.S3_REGION || 'us-east-1' }}
          NEXT_PUBLIC_DEFAULT_BUCKET_NAME: ${{ secrets.NEXT_PUBLIC_DEFAULT_BUCKET_NAME || 'avatars' }}
          
          # 腾讯云配置
          TENCENT_SECRET_ID: ${{ secrets.TENCENT_SECRET_ID }}
          TENCENT_SECRET_KEY: ${{ secrets.TENCENT_SECRET_KEY }}
          TENCENT_CLOUD_NAMESPACE: ${{ secrets.TENCENT_CLOUD_NAMESPACE || '9000ai' }}

          # RabbitMQ配置
          RABBITMQ_HOST: ${{ secrets.RABBITMQ_HOST }}
          RABBITMQ_PORT: ${{ secrets.RABBITMQ_PORT }}
          RABBITMQ_USER: ${{ secrets.RABBITMQ_USER }}
          RABBITMQ_PASSWORD: ${{ secrets.RABBITMQ_PASSWORD }}
          RABBITMQ_VHOST_ENV: ${{ secrets.RABBITMQ_VHOST_ENV }}

          # 数字人供应商设置
          VENDOR1_APP_ID: ${{ secrets.VENDOR1_APP_ID }}
          VENDOR1_SECRET_KEY: ${{ secrets.VENDOR1_SECRET_KEY }}
          VENDOR1_BASE_URL: ${{ secrets.VENDOR1_BASE_URL }}
          VENDOR1_CALLBACK_URL: ${{ secrets.VENDOR1_CALLBACK_URL }}
          
          # 飞书机器人配置
          FEISHU_API_MONITOR_WEBHOOK: ${{ secrets.FEISHU_API_MONITOR_WEBHOOK }}
          FEISHU_KAFKA_PRODUCER_WEBHOOK: ${{ secrets.FEISHU_KAFKA_PRODUCER_WEBHOOK }}
          FEISHU_KAFKA_CONSUMER_WEBHOOK: ${{ secrets.FEISHU_KAFKA_CONSUMER_WEBHOOK }}
          
          # Ngrok配置
          ENABLE_NGROK: ${{ secrets.ENABLE_NGROK }}
          NGROK_AUTH_TOKEN: ${{ secrets.NGROK_AUTH_TOKEN }}
          NGROK_DOMAIN: ${{ secrets.NGROK_DOMAIN }}
          NGROK_REGION: ${{ secrets.NGROK_REGION }}

          # 其余系统URL
          NEXT_PUBLIC_TRAFFIC_SYSTEM_URL: ${{ secrets.NEXT_PUBLIC_TRAFFIC_SYSTEM_URL }}
          NEXT_PUBLIC_LIVE_AGENT_URL: ${{ secrets.NEXT_PUBLIC_LIVE_AGENT_URL }}
