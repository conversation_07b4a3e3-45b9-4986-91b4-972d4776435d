name: 持续部署

on:
  pull_request:
    types: [closed]
    branches: [release]

jobs:
  build:
    if: github.event.pull_request.merged == true
    name: 构建与部署
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: frontend
    
    steps:
      - uses: actions/checkout@v4
      
      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.x"
          
      - name: 设置 PNPM
        uses: pnpm/action-setup@v4
        with:
          version: '9.3.0'
          
      - name: 安装依赖
        run: pnpm install
        
      - name: 生成 Prisma 客户端
        run: pnpm db:generate
        
      - name: 数据库迁移
        run: pnpm db:push
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
          DIRECT_URL: ${{ secrets.DIRECT_URL }}
        
      - name: 构建
        run: |
          pnpm build
        env:
          # 数据库
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
          DIRECT_URL: ${{ secrets.DIRECT_URL }}
          
          # 站点URL
          NEXT_PUBLIC_SITE_URL: ${{ secrets.NEXT_PUBLIC_SITE_URL }}
          NEXT_PUBLIC_BASE_URL: ${{ secrets.NEXT_PUBLIC_BASE_URL }}
          NEXT_PUBLIC_WEBSOCKET_URL: ${{ secrets.NEXT_PUBLIC_WEBSOCKET_URL }}
          NEXT_PUBLIC_ENABLE_LOGGING: ${{ secrets.NEXT_PUBLIC_ENABLE_LOGGING }}
          NEXT_PUBLIC_BUCKET_FOLDER_CLONE_INPUT_AUDIO: ${{ secrets.NEXT_PUBLIC_BUCKET_FOLDER_CLONE_INPUT_AUDIO }}
          NEXT_PUBLIC_BUCKET_FOLDER_CLONE_INPUT_VIDEO: ${{ secrets.NEXT_PUBLIC_BUCKET_FOLDER_CLONE_INPUT_VIDEO }}
          
          # 认证
          BETTER_AUTH_SECRET: ${{ secrets.BETTER_AUTH_SECRET }}
          
          # 存储
          S3_ACCESS_KEY_ID: ${{ secrets.S3_ACCESS_KEY_ID }}
          S3_SECRET_ACCESS_KEY: ${{ secrets.S3_SECRET_ACCESS_KEY }}
          S3_ENDPOINT: ${{ secrets.S3_ENDPOINT }}
          S3_CDN_ENDPOINT: ${{ secrets.S3_CDN_ENDPOINT }}
          NEXT_PUBLIC_DEFAULT_BUCKET_NAME: ${{ secrets.NEXT_PUBLIC_DEFAULT_BUCKET_NAME }}
          NEXT_PUBLIC_BUCKET_FOLDER_UPLOAD_AUDIO: ${{secrets.NEXT_PUBLIC_BUCKET_FOLDER_UPLOAD_AUDIO}}

          NEXT_PUBLIC_TRAFFIC_SYSTEM_URL: ${{ secrets.NEXT_PUBLIC_TRAFFIC_SYSTEM_URL }}
          NEXT_PUBLIC_LIVE_AGENT_URL: ${{ secrets.NEXT_PUBLIC_LIVE_AGENT_URL }}
          
          
          # 短信服务
          SMS_API_URL: ${{ secrets.SMS_API_URL }}
          SMS_USERNAME: ${{ secrets.SMS_USERNAME }}
          SMS_API_KEY: ${{ secrets.SMS_API_KEY }}
          SMS_MOCK: ${{ secrets.SMS_MOCK }}
          
          # API设置
          API_VERSION: ${{ secrets.API_VERSION }}
          API_PREFIX: ${{ secrets.API_PREFIX }}
          DEBUG: ${{ secrets.DEBUG }}
          NODE_ENV: ${{ secrets.NODE_ENV }}
          
          # 安全
          API_SECRET_KEY: ${{ secrets.API_SECRET_KEY }}
          ACCESS_TOKEN_EXPIRE_MINUTES: ${{ secrets.ACCESS_TOKEN_EXPIRE_MINUTES }}
          
          # MinIO设置
          S3_REGION: ${{ secrets.S3_REGION }}
          AVATARS_BUCKET_NAME: ${{ secrets.AVATARS_BUCKET_NAME }}
          
          # 腾讯云配置
          TENCENT_SECRET_ID: ${{ secrets.TENCENT_SECRET_ID }}
          TENCENT_SECRET_KEY: ${{ secrets.TENCENT_SECRET_KEY }}
          TENCENT_CLOUD_NAMESPACE: ${{ secrets.TENCENT_CLOUD_NAMESPACE }}
        
          # RabbitMQ配置
          RABBITMQ_HOST: ${{ secrets.RABBITMQ_HOST }}
          RABBITMQ_PORT: ${{ secrets.RABBITMQ_PORT }}
          RABBITMQ_USER: ${{ secrets.RABBITMQ_USER }}
          RABBITMQ_PASSWORD: ${{ secrets.RABBITMQ_PASSWORD }}
          RABBITMQ_VHOST: ${{ secrets.RABBITMQ_VHOST }}
          
          # 邮件设置
          MAIL_HOST: ${{ secrets.MAIL_HOST }}
          MAIL_PORT: ${{ secrets.MAIL_PORT }}
          RESEND_API_KEY: ${{ secrets.RESEND_API_KEY }}
          
          # 数字人供应商设置
          VENDOR1_APP_ID: ${{ secrets.VENDOR1_APP_ID }}
          VENDOR1_SECRET_KEY: ${{ secrets.VENDOR1_SECRET_KEY }}
          VENDOR1_BASE_URL: ${{ secrets.VENDOR1_BASE_URL }}
          VENDOR1_CALLBACK_URL: ${{ secrets.VENDOR1_CALLBACK_URL }}
          
          # 飞书机器人配置
          FEISHU_API_MONITOR_WEBHOOK: ${{ secrets.FEISHU_API_MONITOR_WEBHOOK }}
          FEISHU_KAFKA_PRODUCER_WEBHOOK: ${{ secrets.FEISHU_KAFKA_PRODUCER_WEBHOOK }}
          FEISHU_KAFKA_CONSUMER_WEBHOOK: ${{ secrets.FEISHU_KAFKA_CONSUMER_WEBHOOK }}
          
          # Ngrok配置
          ENABLE_NGROK: ${{ secrets.ENABLE_NGROK }}
          NGROK_AUTH_TOKEN: ${{ secrets.NGROK_AUTH_TOKEN }}
          NGROK_DOMAIN: ${{ secrets.NGROK_DOMAIN }}
          NGROK_REGION: ${{ secrets.NGROK_REGION }}
          
          # MaxMind许可证密钥
          MAXMIND_LICENSE_KEY: ${{ secrets.MAXMIND_LICENSE_KEY }}
          
      - name: 登录到腾讯云容器仓库
        uses: docker/login-action@v3
        with:
          registry: ccr.ccs.tencentyun.com
          username: ${{ secrets.TENCENT_CLOUD_ACCOUNT_ID }}
          password: ${{ secrets.TENCENT_CLOUD_TCR_PASSWORD }}
          
      - name: 设置 Docker Buildx
        uses: docker/setup-buildx-action@v3
          
      - name: 构建和推送 Docker 镜像
        uses: docker/build-push-action@v6
        with:
          context: ./frontend
          file: ./frontend/apps/web/dockerfile
          push: true
          tags: |
            ccr.ccs.tencentyun.com/${{ secrets.TENCENT_CLOUD_NAMESPACE }}/frontend:${{ github.sha }}
            ccr.ccs.tencentyun.com/${{ secrets.TENCENT_CLOUD_NAMESPACE }}/frontend:latest
          build-args: |
            TZ=Asia/Shanghai
            DATABASE_URL=${{ secrets.DATABASE_URL }}
            DIRECT_URL=${{ secrets.DIRECT_URL }}
            NEXT_PUBLIC_SITE_URL=${{ secrets.NEXT_PUBLIC_SITE_URL }}
            NEXT_PUBLIC_BASE_URL=${{ secrets.NEXT_PUBLIC_BASE_URL }}
            BETTER_AUTH_SECRET=${{ secrets.BETTER_AUTH_SECRET }}
            S3_ACCESS_KEY_ID=${{ secrets.S3_ACCESS_KEY_ID }}
            S3_SECRET_ACCESS_KEY=${{ secrets.S3_SECRET_ACCESS_KEY }}
            S3_CDN_ENDPOINT: ${{ secrets.S3_CDN_ENDPOINT }}
            NEXT_PUBLIC_DEFAULT_BUCKET_NAME: ${{ secrets.NEXT_PUBLIC_DEFAULT_BUCKET_NAME }}
            NEXT_PUBLIC_BUCKET_FOLDER_UPLOAD_AUDIO: ${{secrets.NEXT_PUBLIC_BUCKET_FOLDER_UPLOAD_AUDIO}}
            S3_ENDPOINT=${{ secrets.S3_ENDPOINT }}
            SMS_API_URL=${{ secrets.SMS_API_URL }}
            SMS_USERNAME=${{ secrets.SMS_USERNAME }}
            SMS_API_KEY=${{ secrets.SMS_API_KEY }}
            SMS_MOCK=${{ secrets.SMS_MOCK }}
            API_VERSION=${{ secrets.API_VERSION }}
            API_PREFIX=${{ secrets.API_PREFIX }}
            DEBUG=${{ secrets.DEBUG }}
            NODE_ENV=${{ secrets.NODE_ENV }}
            RABBITMQ_HOST=${{ secrets.RABBITMQ_HOST }}
            RABBITMQ_PORT=${{ secrets.RABBITMQ_PORT }}
            RABBITMQ_USER=${{ secrets.RABBITMQ_USER }}
            RABBITMQ_PASSWORD=${{ secrets.RABBITMQ_PASSWORD }}
            RABBITMQ_VHOST_ENV=${{ secrets.RABBITMQ_VHOST_ENV }}
            API_SECRET_KEY=${{ secrets.API_SECRET_KEY }}
            ACCESS_TOKEN_EXPIRE_MINUTES=${{ secrets.ACCESS_TOKEN_EXPIRE_MINUTES }}
            S3_REGION=${{ secrets.S3_REGION }}
            AVATARS_BUCKET_NAME=${{ secrets.AVATARS_BUCKET_NAME }}
            TENCENT_SECRET_ID=${{ secrets.TENCENT_SECRET_ID }}
            TENCENT_SECRET_KEY=${{ secrets.TENCENT_SECRET_KEY }}
            TENCENT_CLOUD_NAMESPACE=${{ secrets.TENCENT_CLOUD_NAMESPACE }}
            VENDOR1_APP_ID=${{ secrets.VENDOR1_APP_ID }}
            VENDOR1_SECRET_KEY=${{ secrets.VENDOR1_SECRET_KEY }}
            VENDOR1_BASE_URL=${{ secrets.VENDOR1_BASE_URL }}
            VENDOR1_CALLBACK_URL=${{ secrets.VENDOR1_CALLBACK_URL }}
            FEISHU_API_MONITOR_WEBHOOK=${{ secrets.FEISHU_API_MONITOR_WEBHOOK }}
            FEISHU_KAFKA_PRODUCER_WEBHOOK=${{ secrets.FEISHU_KAFKA_PRODUCER_WEBHOOK }}
            FEISHU_KAFKA_CONSUMER_WEBHOOK=${{ secrets.FEISHU_KAFKA_CONSUMER_WEBHOOK }}
            ENABLE_NGROK=${{ secrets.ENABLE_NGROK }}
            NGROK_AUTH_TOKEN=${{ secrets.NGROK_AUTH_TOKEN }}
            NGROK_DOMAIN=${{ secrets.NGROK_DOMAIN }}
            NGROK_REGION=${{ secrets.NGROK_REGION }}
            NEXT_PUBLIC_BUCKET_FOLDER_CLONE_INPUT_AUDIO=${{ secrets.NEXT_PUBLIC_BUCKET_FOLDER_CLONE_INPUT_AUDIO }}
            NEXT_PUBLIC_BUCKET_FOLDER_CLONE_INPUT_VIDEO=${{ secrets.NEXT_PUBLIC_BUCKET_FOLDER_CLONE_INPUT_VIDEO }}
            NEXT_PUBLIC_WEBSOCKET_URL=${{ secrets.NEXT_PUBLIC_WEBSOCKET_URL }}
            NEXT_PUBLIC_ENABLE_LOGGING=${{ secrets.NEXT_PUBLIC_ENABLE_LOGGING }}
            MAXMIND_LICENSE_KEY=${{ secrets.MAXMIND_LICENSE_KEY }}

            # 其余系统URL
            NEXT_PUBLIC_TRAFFIC_SYSTEM_URL: ${{ secrets.NEXT_PUBLIC_TRAFFIC_SYSTEM_URL }}
            NEXT_PUBLIC_LIVE_AGENT_URL: ${{ secrets.NEXT_PUBLIC_LIVE_AGENT_URL }}
          
      - name: 构建和推送 WebSocket 服务 Docker 镜像
        uses: docker/build-push-action@v6
        with:
          context: ./frontend
          file: ./frontend/packages/websocket-service/dockerfile
          push: true
          tags: |
            ccr.ccs.tencentyun.com/${{ secrets.TENCENT_CLOUD_NAMESPACE }}/websocket-service:${{ github.sha }}
            ccr.ccs.tencentyun.com/${{ secrets.TENCENT_CLOUD_NAMESPACE }}/websocket-service:latest
          build-args: |
            TZ=Asia/Shanghai
            NODE_ENV=production
            RABBITMQ_HOST=${{ secrets.RABBITMQ_HOST }}
            RABBITMQ_PORT=${{ secrets.RABBITMQ_PORT }}
            RABBITMQ_USER=${{ secrets.RABBITMQ_USER }}
            RABBITMQ_PASSWORD=${{ secrets.RABBITMQ_PASSWORD }}
            RABBITMQ_VHOST=${{ secrets.RABBITMQ_VHOST_ENV }}
          
      - name: 部署到服务器
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SERVER_SSH_KEY }}
          script: |
            cd /home/<USER>
            export TAG=${{ github.sha }}
            bash deploy.sh 