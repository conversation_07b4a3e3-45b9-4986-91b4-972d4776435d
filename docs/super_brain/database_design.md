# 超级大脑模块数据库设计文档

## 概述
超级大脑模块是9000AI生态系统中的AI智能体对话助手子系统，主要通过Dify API提供AI对话功能，并将生成的内容传递给工作流的下一环节。

## 表结构设计

### 1. Dify应用配置表 (`dify_app_config`)

**用途**：存储Dify平台的各种应用配置信息（智能体、对话、工作流、文本生成等）

| 字段名 | 类型 | 说明 | 约束 |
|-------|------|------|------|
| id | String | 主键 | @id @default(cuid()) |
| name | String | 应用名称（我们定义） | 必填 |
| description | String? | 应用描述（我们定义） | 可选 |
| apiKey | String | Dify API密钥 | 必填 |
| appId | String? | Dify应用ID | 可选 |
| appType | DifyAppType | 应用类型 | 必填 |
| displayName | String | 显示名称 | 必填 |
| avatar | String? | 应用头像 | 可选 |
| tags | String[] | 标签数组 | 数组 |
| category | String? | 分类 | 可选 |
| introduction | String? | 介绍文本 | 可选 |
| isActive | Boolean | 是否启用 | @default(true) |
| isPublic | Boolean | 是否公开 | @default(false) |
| createdBy | String | 创建者用户ID | 必填 |
| usageCount | Int | 使用次数 | @default(0) |
| createdAt | DateTime | 创建时间 | @default(now()) |
| updatedAt | DateTime | 更新时间 | @updatedAt |
| deletedAt | DateTime? | 软删除时间 | 可选 |
| remark | String? | 备注 | 可选 |

**枚举类型**：
```prisma
enum DifyAppType {
    CHAT_BOT      @map("chatbot")      // 聊天机器人
    AGENT         @map("agent")        // 智能体
    WORKFLOW      @map("workflow")     // 工作流
    TEXT_GEN      @map("text_gen")     // 文本生成
}
```

### 2. 超级大脑任务表 (`superbrain_task`)

**用途**：记录AI任务的核心信息和执行状态

| 字段名 | 类型 | 说明 | 约束 |
|-------|------|------|------|
| id | String | 主键 | @id @default(cuid()) |
| name | String | 任务名称 | 必填 |
| consumerId | String | 消费者ID | 必填 |
| difyAppId | String | Dify应用配置ID | 必填 |
| inputContent | String | 输入内容 | 必填 |
| outputContent | String? | 输出内容 | 可选 |
| status | SuperBrainTaskStatus | 任务状态 | @default(PENDING) |
| progress | Float | 进度(0-100) | @default(0) |
| errorMessage | String? | 错误信息 | 可选 |
| difyConversationId | String? | Dify对话ID | 可选 |
| difyMessageId | String? | Dify消息ID | 可选 |
| rawRequest | Json? | 原始请求数据 | 可选 |
| rawResponse | Json? | 原始响应数据 | 可选 |
| computingPowerCost | Int | 算力消耗 | @default(50) |
| tokenCount | Int | 消耗token数 | @default(0) |
| usageId | String? | 算力记录ID | 可选 |
| startTime | DateTime? | 任务开始时间 | 可选 |
| completionTime | DateTime? | 任务完成时间 | 可选 |
| createdAt | DateTime | 创建时间 | @default(now()) |
| updatedAt | DateTime | 更新时间 | @updatedAt |
| deletedAt | DateTime? | 软删除时间 | 可选 |
| remark | String? | 备注 | 可选 |

**枚举类型**：
```prisma
enum SuperBrainTaskStatus {
    PENDING       @map("PENDING")        // 等待处理
    PROCESSING    @map("PROCESSING")     // 处理中
    COMPLETED     @map("COMPLETED")      // 已完成
    FAILED        @map("FAILED")         // 失败
    CANCELLED     @map("CANCELLED")      // 已取消
}
```

### 3. 角色IP定位表 (`character_ip_profile`)

**用途**：存储用户角色IP定位信息和核心价值主张

| 字段名 | 类型 | 说明 | 约束 |
|-------|------|------|------|
| id | String | 主键 | @id @default(cuid()) |
| consumerId | String | 消费者ID | 必填 |
| ipName | String | IP名称 | 必填 |
| ipType | String | IP类型（个人品牌/企业品牌/知识IP等） | 必填 |
| coreConcept | Json | 核心理念（结构化） | 必填 |
| superQuestion | Json | 超级问题（结构化的核心问题） | 必填 |
| questionVersion | Int | 问题版本号 | @default(1) |
| targetAudience | Json? | 目标受众画像 | 可选 |
| valueProposition | String? | 价值主张 | 可选 |
| brandStory | String? | 品牌故事 | 可选 |
| contentStyle | Json? | 内容风格定义 | 可选 |
| keywords | String[] | 关键词列表 | 数组 |
| tags | String[] | 标签列表 | 数组 |
| status | CharacterIPStatus | IP状态 | @default(ACTIVE) |
| createdAt | DateTime | 创建时间 | @default(now()) |
| updatedAt | DateTime | 更新时间 | @updatedAt |
| deletedAt | DateTime? | 软删除时间 | 可选 |
| remark | String? | 备注 | 可选 |

**枚举类型**：
```prisma
enum CharacterIPStatus {
    ACTIVE        @map("ACTIVE")        // 活跃
    DEVELOPING    @map("DEVELOPING")    // 开发中
    ARCHIVED      @map("ARCHIVED")      // 已归档
}
```

### 4. 脚本表 (`content_script`)

**用途**：存储从其他系统同步的结构化脚本信息

| 字段名 | 类型 | 说明 | 约束 |
|-------|------|------|------|
| id | String | 主键 | @id @default(cuid()) |
| title | String | 脚本标题 | 必填 |
| scriptContent | Json | 结构化脚本内容（包含分类、标签等所有信息） | 必填 |
| scriptVersion | Int | 脚本版本 | @default(1) |
| externalId | String? | 外部系统ID | 可选 |
| externalSource | String? | 外部系统来源 | 可选 |
| syncedAt | DateTime? | 同步时间 | 可选 |
| status | ContentStatus | 状态 | @default(ACTIVE) |
| isPublic | Boolean | 是否公开 | @default(false) |
| usageCount | Int | 使用次数 | @default(0) |
| createdAt | DateTime | 创建时间 | @default(now()) |
| updatedAt | DateTime | 更新时间 | @updatedAt |
| deletedAt | DateTime? | 软删除时间 | 可选 |
| remark | String? | 备注 | 可选 |

**注**：分类、标签等信息都包含在 `scriptContent` JSON字段中，避免数据冗余。

### 5. 选题表 (`topic_selection`)

**用途**：存储与智能体和工作流确认的最终选题结果

| 字段名 | 类型 | 说明 | 约束 |
|-------|------|------|------|
| id | String | 主键 | @id @default(cuid()) |
| title | String | 选题标题 | 必填 |
| consumerId | String | 消费者ID | 必填 |
| scriptId | String? | 关联脚本ID | 可选 |
| taskId | String? | 关联AI任务ID | 可选 |
| topicContent | Json | 结构化选题内容（包含分类、标签、关键词、优先级等所有信息） | 必填 |
| contentVersion | Int | 内容版本 | @default(1) |
| status | TopicStatus | 选题状态 | @default(DRAFT) |
| confirmedAt | DateTime? | 确认时间 | 可选 |
| createdAt | DateTime | 创建时间 | @default(now()) |
| updatedAt | DateTime | 更新时间 | @updatedAt |
| deletedAt | DateTime? | 软删除时间 | 可选 |
| remark | String? | 备注 | 可选 |

**注**：分类、标签、关键词、优先级、质量评分等信息都包含在 `topicContent` JSON字段中。

**枚举类型**：
```prisma
enum TopicStatus {
    DRAFT       @map("DRAFT")       // 草稿
    CONFIRMED   @map("CONFIRMED")   // 已确认
    REJECTED    @map("REJECTED")    // 已拒绝
    ARCHIVED    @map("ARCHIVED")    // 已归档
}
```

### 6. 成品文案表 (`final_content`)

**用途**：存储最终确认的原始素材库，可用于多处使用

| 字段名 | 类型 | 说明 | 约束 |
|-------|------|------|------|
| id | String | 主键 | @id @default(cuid()) |
| title | String | 文案标题 | 必填 |
| consumerId | String | 消费者ID | 必填 |
| scriptId | String? | 关联脚本ID | 可选 |
| topicId | String? | 关联选题ID | 可选 |
| content | String | 主要文案内容 | 必填 |
| contentType | String | 内容类型 | @default("text") |
| contentFormat | Json? | 结构化内容格式（包含分类、标签、关键词、素材等所有信息） | 可选 |
| status | ContentStatus | 内容状态 | @default(ACTIVE) |
| version | Int | 版本号 | @default(1) |
| usageCount | Int | 使用次数 | @default(0) |
| lastUsedAt | DateTime? | 最后使用时间 | 可选 |
| reviewStatus | ReviewStatus | 审核状态 | @default(PENDING) |
| reviewedBy | String? | 审核人 | 可选 |
| reviewedAt | DateTime? | 审核时间 | 可选 |
| reviewComment | String? | 审核意见 | 可选 |
| isPublished | Boolean | 是否发布 | @default(false) |
| publishedAt | DateTime? | 发布时间 | 可选 |
| createdAt | DateTime | 创建时间 | @default(now()) |
| updatedAt | DateTime | 更新时间 | @updatedAt |
| deletedAt | DateTime? | 软删除时间 | 可选 |
| remark | String? | 备注 | 可选 |

**注**：分类、标签、关键词、媒体文件URLs、附件信息、质量评分等都包含在 `contentFormat` JSON字段中。

**枚举类型**：
```prisma
enum ContentStatus {
    ACTIVE      @map("ACTIVE")      // 活跃
    INACTIVE    @map("INACTIVE")    // 非活跃
    ARCHIVED    @map("ARCHIVED")    // 已归档
}

enum ReviewStatus {
    PENDING     @map("PENDING")     // 待审核
    APPROVED    @map("APPROVED")    // 已通过
    REJECTED    @map("REJECTED")    // 已拒绝
    REVISE      @map("REVISE")      // 需修改
}
```

## 设计要点

### 1. 任务表优化说明
- 移除了消息队列、限流、重试等配置字段，这些逻辑应在代码层面实现
- 保留核心业务字段：任务状态、输入输出、Dify关联信息、算力消耗等
- 简化表结构，提高查询性能

### 2. 角色IP定位说明
- IP指的是"Intellectual Property"（知识产权）或个人/品牌IP
- 存储用户的角色定位、核心价值主张、超级问题等
- 用于指导AI生成符合用户IP特征的内容

### 3. 结构化数据设计
- 使用JSON字段存储结构化内容，便于前端灵活展示
- 支持版本控制，便于内容迭代
- 标签和分类系统支持内容组织和检索

### 4. 工作流集成
- 任务表关联Dify应用配置，支持多种AI能力
- 选题和成品文案可关联AI任务，形成完整的创作链路
- 支持内容在不同模块间的流转和复用

## 关联关系补充

需要在现有模型中添加以下关联：

```prisma
// 在 Consumer 模型中添加
model Consumer {
    // ... 现有字段
    
    // 超级大脑相关
    superbrainTasks     SuperBrainTask[]
    characterIPProfiles CharacterIPProfile[]
    topicSelections     TopicSelection[]
    finalContents       FinalContent[]
}

// 在 User 模型中添加
model User {
    // ... 现有字段
    
    // Dify应用配置
    difyAppConfigs      DifyAppConfig[]
}

// 在 ComputingPowerRecord 模型中添加
model ComputingPowerRecord {
    // ... 现有字段
    
    // 超级大脑任务关联
    superbrainTasks     SuperBrainTask[]
}
```