# 超级大脑 UI/布局与主题统一 - 增量记录（2025-08-17）

> 本文档汇总本次针对 SuperBrain 模块的主题色、布局背景与美观性优化改动，并给出关联实现文件，便于后续统一维护与回溯。

---

## 目标与范围
- 统一 SuperBrain 与 Avatar/Agent 的设计语言与交互细节
- 主题色从青色系切换为偏紫至白的高亮渐变，风格“深色科技 + 轻玻璃 + 克制霓虹”
- 修复 Logo 区域裁切与侧边栏若干视觉瑕疵，完善折叠/展开两种形态的观感与动效

---

## 主题与配色策略
- 主主题渐变（45°）：白色占比上调，整体更通透、带轻微 3D 与柔白光
  - 渐变序列（由左至右）：`#8394fb → #aab6fd → #c9d4ff → #eef3ff → #ffffff`
  - 统一用于 Logo 文本、高亮描边、选中/提示态渐变线条与图标描边（SVG linearGradient）
- 页面背景（还原深黑）：
  - 根容器背景：`bg-gradient-to-br from #01030c via #0e1018 to #01030c`
  - 组件背景：`#10152b`（侧栏、卡片等），与页面背景对比更清晰
- 光效与阴影：整体亮度降低、白光比例提升，保留“克制的科技感”

---

## Logo 改进（SuperBrainLogo）
- 展开态：显示“9000AI + 超级大脑”，`9000AI` 采用连续渐变；“AI” 右下增加定向白光（text-shadow）
- 收起态：仅显示“AI”，保持字体与渐变一致，并有轻微 hover 动效
- 字体与布局：`inline-block + fit-content`，`overflow: visible`，修复右侧裁切问题；父容器 `cursor-default`
- 渐变起点修正：所有 Logo 渐变统一以 `#8394fb` 为起点，序列 `#8394fb → #aab6fd → #c9d4ff → #eef3ff → #ffffff`，方向左上→右下
- 悬浮星光（展开态）：hover 时 2 颗小星“由 0 长出”（framer-motion rest/hover 两态，scale 0→1 + opacity 0→1，spring：stiffness 260 / damping 18；第二颗延迟 0.06s），发光采用小半径 drop-shadow，克制不过度

---

## 侧边栏（Sidebar）优化
- 可见性：始终显示；移除顶部导航条，侧栏贴合页面顶部
- 外观：圆角矩形、有外边距；容器风格统一为 `styles.glass`（半透明深灰 + blur + 细边 + 圆角 + Shine 自然淡出）
- 折叠/展开行为：
  - 外置折叠按钮（与侧栏宽度联动）：
    - 展开：`left: calc(15rem + 0.5rem)`；收起：`left: calc(4rem + 0.5rem)`
  - 导航项文字与图标在两态下均进行了对齐和间距微调，避免溢出
- 菜单项与动效：
  - 展开态：选中背景 `#1b2130`；字体更清晰（`subpixel-antialiased`、字号/行高/字距优化）
  - 折叠态：
    - Tooltip 美化（毛玻璃、圆角、细薄底部高亮线、图标+文字居中）
    - 图标 hover：`scale + rotate` 轻动效，光晕减弱；角度与缩放进一步收敛，更克制
  - 选中图标：
    - SVG `stroke` 应用 Logo 同源渐变（`id="sbIconGrad"`）
    - 底部“细薄高亮线”与“轻微柔光晕”
    - 关键帧放大动画（选中切换时 1.18 → 1.04 → 1.10）
- 图标规范：
  - “选题库”图标由 `Library` 更换为 `BookOpen`，避免收起选中态的渲染缺失
  - 收起/展开两态图标尺寸提升（收起 1.75rem、展开 1.5rem），并匹配容器尺寸避免裁切

---

## 其他 UI 调整
- 系统选择页卡片（公域获客）：大屏由 3 列改为 2 列，视觉更均衡
- 细节统一：选中态/悬浮态光晕整体下调；边距与滚动区域裁切（`overflow`）一致处理

---

## 关联实现文件
- 侧边栏与 Logo：
  - `frontend/apps/web/modules/saas/ecosystem/superbrain/layout/components/SuperBrainSidebar.tsx`
  - `frontend/apps/web/modules/saas/ecosystem/superbrain/layout/components/SuperBrainLogo.tsx`
  - 主题容器（Glassmorphism）：`frontend/apps/web/modules/saas/ecosystem/superbrain/sb-theme.module.css`
  - 参考对齐：
    - `frontend/apps/web/modules/saas/ecosystem/avatar/components/layout/AvatarSidebar.tsx`
    - `frontend/apps/web/modules/saas/agent/components/layout/AgentSidebar.tsx`
    - `frontend/apps/web/modules/saas/ecosystem/avatar/components/layout/AvatarLogo.tsx`
    - `frontend/apps/web/modules/saas/agent/components/layout/AgentLogo.tsx`
- 布局容器与背景：
  - `frontend/apps/web/modules/saas/ecosystem/superbrain/layout/SuperBrainWrapper.tsx`
- 系统选择页：
  - `frontend/apps/web/modules/saas/auth/components/SystemSelect.tsx`（`lg:grid-cols-3 → lg:grid-cols-2`）

---

## 新增（2025-08-18）

- 首页“我的超级大脑”区域首屏常显：
  - `SuperBrainDashboard.tsx` 取消 `whileInView`，改为初始 `animate`，防止不滚动看不到。
- 布局统一：
  - `SuperBrainWrapper.tsx` 主内容区去除内部上下 padding，内容紧贴容器上沿，顶部与侧边栏外边距对齐。
  - 工作流页移除自定义外层 padding，统一采用布局层间距。
- 路由统一：
  - “完善资料/去完善/完善画像”统一跳转 `'/app/ecosystem/superbrain/persona'`。
- 工作流卡片与主题：
  - 全量改用 `WorkflowCard.tsx`；移除 `AgentCard.tsx` 与 `WorkflowTabs.tsx`。
  - 主题配色切换为紫—白主梯度，hover 放大，去除流光，标签与按钮样式对齐主题。


## 新增（2025-08-19）

- 工作流卡片（WorkflowCard）更新：
  - 类型标签移动至卡片右上角；移除卡片内“打开”按钮，点击整卡即可进入。
  - 分流逻辑（根据 appType 跳转）：
    - 工作流/文本生成 → `/app/ecosystem/superbrain/workflow/view?app=<id>&mode=run`
    - 智能体/聊天类 → `/app/ecosystem/superbrain/workflow/agent?agent=<id>`
  - 关联文件：
    - `frontend/apps/web/modules/saas/ecosystem/superbrain/workflow/components/WorkflowCard.tsx`
    - `frontend/apps/web/modules/saas/ecosystem/superbrain/workflow/index.tsx`
    - `frontend/apps/web/app/(saas)/app/ecosystem/superbrain/workflow/view/page.tsx`

- 运行页（WorkflowRunView）基础展示：
  - 结构：居中偏上的输入表单 + 下方结果分区（选题/实体连爆/成文 三种示例渲染）。
  - 顶部标题栏 TopBar：左侧返回、右侧用户信息“内联”展示（非卡片按钮）。
  - 动画：
    - 页面容器淡入；表单卡片与结果区淡入/上移动效（framer-motion）。
  - 关联文件：
    - `frontend/apps/web/modules/saas/ecosystem/superbrain/workflow/viewer/WorkflowRunView.tsx`
    - `frontend/apps/web/modules/saas/ecosystem/superbrain/workflow/viewer/TopBar.tsx`

- 对话页（ChatAgentView）动效补充：
  - 左侧会话侧栏 `motion.aside`：x 轴轻滑入 + 淡入。
  - 右侧聊天区域 `motion.section`：x 轴轻滑入 + 淡入。
  - 说明：顶部是否使用 TopBar 根据产品意见可选；当前版本保留侧栏上方结构，不启用 TopBar。
  - 关联文件：
    - `frontend/apps/web/modules/saas/ecosystem/superbrain/workflow/viewer/ChatAgentView.tsx`

- 顶部标题栏（TopBar）风格统一：
  - 返回按钮靠左；用户信息直接内联展示在标题栏右侧（去掉边框/卡片感）。
  - 关联文件：
    - `frontend/apps/web/modules/saas/ecosystem/superbrain/workflow/viewer/TopBar.tsx`

- 路由与全屏布局：
  - 新增 view 路由页：`/app/ecosystem/superbrain/workflow/view/page.tsx` 作为运行页容器（缺少 appId 时重定向回列表页）。
  - 现有全屏规则已包含 `workflow/agent` 与 `workflow/view`（参考 `layout.tsx`）。
  - 关联文件：
    - `frontend/apps/web/app/(saas)/app/ecosystem/superbrain/workflow/view/page.tsx`
    - `frontend/apps/web/app/(saas)/app/ecosystem/superbrain/layout.tsx`

- 动效建议规范（补充）：
  - 页面/容器：duration 0.3~0.5，ease: "easeOut"；子区块可错位 delay 0.05~0.1。
  - 列表/卡片流：支持逐项 stagger，hover 动效克制（scale ≤ 1.05）。

## 数据库模型调整（SuperBrain）

- 选题与脚本关系调整：
  - `TopicSelection` 移除单一 `scriptId` 关联，改为 `scripts: ContentScript[]`（一个选题产出多个脚本）
  - `ContentScript` 新增 `topicId` 指向源选题
- 工作流来源追踪：
  - `ContentScript` 新增 `difyAppId` → `DifyAppConfig`（标明脚本由哪个 Dify 应用/工作流生成）
  - `FinalContent` 新增 `difyAppId` → `DifyAppConfig`（标明成品文案来源工作流）
- 新增表：黄金三秒开场 `OpeningContent`（表名 `opening_content`）
  - 字段：`id`、`scriptId`（脚本外键，级联删除）、`content`（黄金三秒文本）、`difyAppId`（来源工作流，可空）、时间戳/软删除/备注
  - 反查：`ContentScript.openings: OpeningContent[]`
  - 索引：`[scriptId]`、`[difyAppId]`、`[createdAt]`

说明：接口规范中的 `openings[].text/tags/notes`，当前版本仅持久化 `text → content`；如需保留 `tags/notes` 将在后续 schema 增量中补充。

---

## 实施要点与注意事项
- 防裁切：`aside` 与 Logo 父容器设置 `overflow: visible`；合理设置内边距与外置按钮位置，避免覆盖与遮挡
- SVG 渐变：通过 `<defs><linearGradient id="sbIconGrad" ... /></defs>` 定义后，以 `style={{ stroke: 'url(#sbIconGrad)' }}` 应用于 `Lucide` 图标
- 动效触发：选中态使用 `key` 绑定与 framer-motion 关键帧，确保每次切换均能重放动画
- 亮度控制：白色占比上调但整体 glow 下调，避免刺眼；光影以“细线 + 轻柔光晕”为主

---

## 与既有文档的关系
- 本文档为 UI/主题与布局统一的独立增量记录，配合以下文档一同参考：
  - `docs/super_brain/context/开发记录01.md`
  - `docs/super_brain/context/开发记录02.md`
  - `docs/super_brain/context/开发记录03.md`
  - `docs/super_brain/context/开发记录04.md`
  - `docs/super_brain/context/主题对标.md`

---

## 后续可选优化
- 主题渐变抽象为 Token，集中管理（色阶、角度、白色比例与 glow 强度）
- 折叠态选中高亮线的长度/透明度按图标宽度自适应
- Logo 悬浮星光位置与大小根据实际字体与字距计算动态微调

---

最后更新：2025-08-17
维护人员：9000AI SuperBrain 前端
