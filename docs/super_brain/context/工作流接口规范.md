## 9000AI 超级大脑 - 工作流接口规范（V1）

本文面向后端/前端/工作流编排开发，统一定义已落地与在研工作流之请求与响应参数格式，便于联调与测试。示例中敏感数据已省略。

### 统一约定

- Content-Type: application/json; charset=utf-8
- 时间单位：秒；金额单位：与 `currency` 一致
- 响应包装：若无特殊说明，工作流响应可包含 `usage`（总用量）与 `usage_1`、`usage_2`…（节点用量）字段，用于成本审计

### 与 Dify 的映射与调用约定

- 接口类型映射
  - 超级画像生成（对话式）：Dify Agent（需工具调用时优先）/ Chatbot（纯对话）
  - 营销选题工作流：Dify Workflow
  - 实体连爆工作流：Dify Workflow
  - 成文工作流（含黄金三秒）：Dify Workflow
  - 私域营销工作流：Dify Workflow
  - 销冠复刻工作流（对话式）：Dify Agent
  - 中央素材库搭建工作流：Dify Workflow

- 后端网关与调用
  - 统一经超脑模块网关：`/api/v1/ecosystem/superbrain/*`
  - Workflow：内部使用 `DifyWorkflowClient.runWorkflow` 调用 Dify `/workflows/run`，默认流式（`response_mode=streaming`），需要同步拿结果时设为 `blocking`
  - Agent/Chatbot：内部使用 `DifyAgentClient` / `DifyChatbotClient` 发起流式会话
  - user 透传：所有 Dify 调用必须携带稳定的 `user` 标识（映射到终端用户），用于限流、审计与会话隔离
  - usage 对齐：从 Dify 返回的 `total_tokens/elapsed_time` 等聚合为本文档的 `usage` 字段；节点级用量按需要从工作流节点产物汇总为 `usage_1/usage_2/...`

---

## 一、超级画像生成（对话式）

- 用途：通过对话采集事实，生成“行业画像”，可展示并供后续工作流消费
- 体验参考：`https://ew.9000aigc.com/chat/8xcMwhg7QPh44ErD`

### 请求（Request）

```json
{
  "input": "用户在对话中提供的事实型信息或汇总后的超级问题",
  "mode": "upsert", 
  "options": {
    "language": "zh-CN"
  }
}
```

- input: string，事实型背景材料（避免主观人群框定）
- mode: enum【upsert|preview】，默认 upsert（写入画像库）
- options.language: 可选，默认 zh-CN

### 响应（Response）

```json
{
  "profile": {
    "business_identity": {
      "name": "品牌/业务名称",
      "concept": "一句话核心理念/价值主张（可AI提炼）",
      "address": "大致地理位置",
      "keywords": ["关键词1", "关键词2", "关键词3"]
    },
    "market_position": {
      "differentiation_points": ["差异化卖点1", "差异化卖点2"]
    },
    "core_offering": {
      "offerings": [
        {
          "name": "产品/服务名",
          "description": "客观功能与内容",
          "price": "订阅模式与价格，如：每月xxx元"
        }
      ]
    },
    "operational_facts": {
      "scale": "业务规模，如：5人团队",
      "model": "核心运营模式",
      "key_processes": ["流程1", "流程2"]
    },
    "summary": "≤500字业务总结（基于上述事实）"
  },
  "usage": {
    "total_tokens": 0,
    "total_price": "0",
    "currency": "USD",
    "latency": 0
  }
}
```

---

## 二、营销选题工作流（一次性生成）

- 用途：基于画像与约束批量生成选题；产出含“产品标签矩阵”“用户标签矩阵”与选题结果
- 测试页：`https://ew.9000aigc.com/workflow/xIG2tbwfzpW82hBW`

### 请求（Request）

```json
{
  "input": "行业画像（超级问题文本或JSON）",
  "topic_type": "教知识",
  "number": 20,
  "titled": [
    "已生成过的topic_direction文本用于去重"
  ],
  "additional": "额外要求，例如：偏向活动策划灵感"
}
```

- input: string|object，画像文本或JSON
- topic_type: enum【教知识|讲故事|聊观点|讲产品|晒过程】
- number: int，生成条数
- titled: string[]，历史选题（传 `topic_direction` 以去重）可选
- product_info: string，单个产品信息（传 `name` 与 `description` 以辅助理解）
- additional: string，可选补充约束

### 中间产出（节点）

- 节点一：产品关键词标签矩阵（字段见示例 A1~A7）
- 节点二：用户标签矩阵（persona 列表，含需求/痛点分组）

### 响应（Response）

```json
{
  "node_outputs": {
    "product_tag_matrix": {
      "A1_core_tags": ["核心标签1"],
      "A2_features_selling_points": ["功能与卖点"],
      "A4_unique_advantages": ["独特优势"],
      "A5_sensory_experience": ["五感体验"],
      "A6_service_process_flow": ["服务流程节点…"],
      "A7_culture_symbol": ["文化/精神意涵"]
    },
    "user_persona_matrix": [
      {
        "persona_name": "画像名",
        "persona_description": "关键词式概述",
        "persona_quote": "代表语录",
        "core_needs": ["需求1"],
        "detailed_pain_points": {
          "decision_making": ["痛点…"],
          "in-use_experience": ["痛点…"],
          "social_dynamics": ["痛点…"],
          "post-use_concerns": ["痛点…"],
          "identity_conflict": ["痛点…"]
        }
      }
    ]
  },
  "result": [
    {
      "topic_direction": "选题走向",
      "angle_keywords": ["视角/镜头关键词"],
      "target_user": "目标用户",
      "formula_cn": "组合法",
      "type": "晒过程"
    }
  ],
  "usage": {
    "prompt_tokens": 0,
    "completion_tokens": 0,
    "total_tokens": 0,
    "total_price": "0",
    "currency": "USD",
    "latency": 0
  },
  "usage_1": { "prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0, "total_price": "0", "currency": "USD", "latency": 0 },
  "usage_2": { "prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0, "total_price": "0", "currency": "USD", "latency": 0 }
}
```

---

## 三、实体连爆工作流（一次性生成）

- 用途：结合地址/地域标签与画像，批量生成“开场/卖点/CTA”并输出成品文案
- 测试页：`https://ew.9000aigc.com/workflow/U5X6HTlR14MXXseH`

### 请求（Request）

```json
{
  "address": "深圳市福田区",
  "num": 5,
  "super_question": "行业画像（超级问题）"
}
```

### 响应（Response）

```json
{
  "result": {
    "ctas": ["结尾转化话术…"],
    "reasons": ["卖点/理由…"],
    "openings": ["黄金三秒开场…"]
  },
  "output": [
    {
      "generated_texts": [
        "成品文案1…",
        "成品文案2…"
      ]
    }
  ],
  "usage": {"total_tokens": 0, "total_price": "0", "currency": "USD", "latency": 0}
}
```

---

## 四、成文工作流（多节点一次性生成）

- 用途：将选题快速成稿，包含分镜段落与整段口播，后续可接“黄金三秒”等节点

### 请求（Node 1：成文）

```json
{
  "topic": {
    "topic_direction": "所选选题对象(可直接传二次加工后的文案字段)",
    "angle_keywords": ["员工教学", "第一人称…"],
    "target_user": "目标用户",
    "formula_cn": "组合提示",
    "type": "晒过程"
  },
  "model": "可选其他模型标识"
}
```

### 响应（Node 1：成文）

```json
{
  "result": {
    "segments": [
      {
        "segment_number": 1,
        "emotion": "情绪标签",
        "visuals": "镜头/画面建议",
        "dialogue": "口播文案"
      }
    ],
    "text": "整段口播文案"
  },
  "usage": {"total_tokens": 0, "total_price": "0", "currency": "USD", "latency": 0}
}
```

### 请求（Node 2：黄金三秒生成）

```json
{
  "topic": "与Node1一致",
  "style": "三秒开场风格（本地标签/冲突/反差等，可选）"
}
```

### 响应（Node 2：黄金三秒生成）

```json
{
  "result": {
    "openings": [
      {
        "text": "黄金三秒开场"
      }
    ]
  },
  "usage": {"total_tokens": 0, "total_price": "0", "currency": "USD", "latency": 0}
}
```

#### 持久化（数据库映射）

- 对应表：`opening_content`
- 映射规则：
  - `openings[].text` → `OpeningContent.content`
  - 关联脚本：`OpeningContent.scriptId` 指向生成该开场的 `ContentScript.id`
  - 来源工作流：可选写入 `OpeningContent.difyAppId`（与 `DifyAppConfig.id` 对应）
  - 反查：`ContentScript.openings` 获取该脚本的所有黄金三秒

---

## 五、私域营销工作流（一次性生成）

- 用途：基于画像与核心素材，按策略模型批量生成朋友圈内容（支持多变体）

### 请求（Request）

```json
{
  "super_question": "行业画像（超级问题）",
  "core_material": [
    "可粘贴的核心素材文本或链接",
    "亦可传字符串而非数组"
  ]
}
```

### 响应（Response）

```json
{
  "result": {
    "suggested_posts": [
      {
        "post_id": 1,
        "strategy_used": "策略标签，如：T2 过程揭秘",
        "post_content": "朋友圈正文",
        "image_suggestion": "配图/视频建议",
        "insight_analysis": {
          "title": "洞察标题",
          "content": "洞察内容"
        }
      }
    ]
  },
  "usage": {"total_tokens": 0, "total_price": "0", "currency": "USD", "latency": 0}
}
```

---

## 六、销冠复刻工作流（对话式）

- 用途：基于“私域成交流程”输出可直接复制的话术路径/警告/教练/追问

### 请求（Request）

```json
{
  "super_question": "行业画像（超级问题）",
  "reply_type": "pathway_suggestion",
  "customer_context": "可选，客户最新对话/状态补充"
}
```

- reply_type: enum【pathway_suggestion|warning|coach|question】

### 响应（Response）

#### pathway_suggestion

```json
{
  "reply_type": "pathway_suggestion",
  "diagnosed_segment": "分层诊断结果",
  "nurturing_advice": "培育方向",
  "strategy_analysis": {"title": "标题", "content": "解析"},
  "conversational_pathway": [
    {
      "step_number": 1,
      "step_title": "步骤名",
      "step_objective": "目的",
      "suggested_reply": ["话术1", "话术2"]
    }
  ],
  "usage": {"total_tokens": 0, "total_price": "0", "currency": "USD", "latency": 0}
}
```

#### warning

```json
{
  "reply_type": "warning",
  "suggested_reply": ["单条或多条警告话术"],
  "strategy_analysis": {"title": "操作提示", "content": "说明"}
}
```

#### question

```json
{
  "reply_type": "question",
  "suggested_reply": ["请补充哪些关键信息…"]
}
```

#### coach

```json
{
  "reply_type": "coach",
  "suggested_reply": ["教练式解释与引导"]
}
```

---

## 七、中央素材库搭建工作流（索引与结构化）

- 用途：将用户粘贴/上传素材进行索引与结构化抽取，作为乐高式创作底座

### 请求（Request）

```json
{
  "materials": [
    {
      "type": "text|image|video|url",
      "title": "可选",
      "content": "当 type=text/url 时传文本或链接",
      "tags": ["行业", "主题", "人群"],
      "source": "manual|import|crawler"
    }
  ],
  "operation": "index_and_enrich" 
}
```

- operation: enum【index_only|index_and_enrich】

### 响应（Response）

```json
{
  "indexed_ids": ["mat_123"],
  "extracted": {
    "products": [
      {
        "name": "产品名",
        "description": "客观功能/场景",
        "selling_points": ["卖点1"],
        "pricing": {"model": "一次性|订阅|套餐", "price": "￥/月", "currency": "CNY"},
        "assets": ["图片/视频URL"]
      }
    ],
    "tags": ["自动补全标签"]
  },
  "usage": {"total_tokens": 0, "total_price": "0", "currency": "USD", "latency": 0}
}
```

---

## 附：用量（usage）字段说明

```json
{
  "prompt_tokens": 0,
  "prompt_unit_price": "0",
  "prompt_price_unit": "0",
  "prompt_price": "0",
  "completion_tokens": 0,
  "completion_unit_price": "0",
  "completion_price_unit": "0",
  "completion_price": "0",
  "total_tokens": 0,
  "total_price": "0",
  "currency": "USD",
  "latency": 0
}
```

- 建议：总览保留 `usage`；节点级保留 `usage_1`、`usage_2`… 以匹配现有实现；后续可收敛为 `usage_nodes` 字段（Map）


