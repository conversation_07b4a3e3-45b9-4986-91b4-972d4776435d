# 超级大脑模块开发记录 - 工作流与对话流集成（V3.1）

## 项目概述

本次完成了 Super Brain 模块的“语义化后台网关”落地：后端对接 Dify 的 Workflow/Chatbot，前端仅关注业务语义参数与 `app_id`。后端负责参数编排、类型与字段名兼容、SSE 转发、用户注入与默认配置（流式模式）等，保证稳定可测、可扩展。

技术栈：Hono（API）+ TypeScript + Zod（校验）+ Prisma（DB）+ Dify API（Workflow/Chatbot）

---

## 交付清单

### 路由与模块

Base Path: `/api/v1/ecosystem/superbrain/workflows`

- 选题裂变：`POST /topic-generation/run`
- 实体连爆：`POST /entity-burst/run`
- 成文脚本：`POST /script-compose/run`
- 黄金三秒：`POST /opening/run`
- 私域营销：`POST /private-marketing/run`
- 销冠复刻（Chatbot 对话流）：`POST /sales-coach/run`

说明：删除通用 `/:id/run`；按“一个业务一个路由”解耦，后端统一编排 Dify `inputs`。

### 通用参数处理（后端自动注入）

- `response_mode` 固定为 `streaming`
- `user`: 从鉴权上下文注入用户标识（如 `consumerId` 或 `user.id`）
- 复杂对象统一转字符串传递给 Dify（`JSON.stringify`）
- 数组型长文本按行拼接为字符串

### Dify 客户端默认超时

- 位置：`frontend/packages/api/src/routes/v1/ecosystem/superbrain/dify/index.ts`
- 变更：`DIFY_DEFAULT_CONFIG.timeout` 从 30s → 110s（避免长流程/首包超时）

### 结构化日志

- 请求摘要、Dify 调用开始/节点/完成/错误均输出结构化日志，便于排障

---

## 参数编排与兼容策略

- 选题裂变（`topic-generation`）
  - 兼容字段：`input` 与 `object` 双写
  - `titled`、`additional` 强制为字符串（数组使用 `\n` 拼接；对象 stringify）

- 实体连爆（`entity-burst`）
  - 兼容大小写：同时传 `Address` 与 `address`
  - 业务字段：`num`、`super_question`

- 成文脚本（`script-compose`）
  - `topic` 映射至 Dify `inputs`

- 黄金三秒（`opening`）
  - 只传 `segment`：为上一阶段“成文脚本”的完整脚本 JSON 字符串

- 私域营销（`private-marketing`）
  - `super_question` 支持对象或字符串（对象将 stringify）
  - `core_material` 支持 string|string[]（数组按行拼接）

- 销冠复刻（`sales-coach` / Chatbot）
  - 自动从表 `CharacterIPProfile` 获取该用户最近的 `superQuestion` 写入 `inputs.super_question`（若存在）
  - 前端可通过 `inputs.reply_type` 控制输出风格：`pathway_suggestion` / `warning` / `coach` / `question`

---

## 数据库与应用配置（SQL）

以下 SQL 基于表 `dify_app_config`。请将 `createdBy` 替换为有效的用户 ID；`id` 作为前端请求 `app_id` 使用。

### 实体连爆（Workflow）
```sql
INSERT INTO "dify_app_config"
  ("id","name","description","apiKey","appId","appType","displayName","avatar","tags","category","introduction","isActive","isPublic","createdBy","usageCount","createdAt","updatedAt")
VALUES
  (
    'dify_app_entity_burst_workflow',
    '实体连爆工作流',
    '基于门店地址与行业画像，一次性生成本地化开场、卖点与CTA，并输出多条成文素材。',
    'app-7VD12jx9CkWsH5cfJGHaMyCB',
    NULL,
    'workflow',
    '实体连爆工作流',
    NULL,
    ARRAY['entity_burst','本地','连爆','工作流']::text[],
    '内容创作',
    '结合本地标签与行业画像，生成“黄金三秒/卖点/CTA”等实体连爆素材，用于快速起量。',
    true,
    true,
    'REPLACE_USER_ID',
    0,
    now(),
    now()
  );
```

### 成文脚本（Workflow）
```sql
INSERT INTO "dify_app_config"
  ("id","name","description","apiKey","appId","appType","displayName","avatar","tags","category","introduction","isActive","isPublic","createdBy","usageCount","createdAt","updatedAt")
VALUES
  (
    'dify_app_script_compose_workflow',
    '成文工作流',
    '将选题/要点快速成稿，输出分镜段落（segments）与整段口播（text）。',
    'app-7VD12jx9CkWsH5cfJGHaMyCB',
    NULL,
    'workflow',
    '成文脚本生成',
    NULL,
    ARRAY['script_compose','成文','脚本','工作流']::text[],
    '内容创作',
    '按固定脚本模型生成结构化分镜与整段口播，支持在下一步衔接黄金三秒优化。',
    true,
    true,
    'REPLACE_USER_ID',
    0,
    now(),
    now()
  );
```

### 黄金三秒（Workflow）
```sql
INSERT INTO "dify_app_config"
  ("id","name","description","apiKey","appId","appType","displayName","avatar","tags","category","introduction","isActive","isPublic","createdBy","usageCount","createdAt","updatedAt")
VALUES
  (
    'dify_app_opening_workflow',
    '黄金三秒工作流',
    '接收完整脚本并生成/优化“黄金三秒”开场标题与文案。',
    'app-wsaVVOcXWg4pFJ9Co71bJ5Uu',
    NULL,
    'workflow',
    '黄金三秒优化',
    NULL,
    ARRAY['opening','黄金三秒','标题优化','工作流']::text[],
    '内容创作',
    '结合脚本内容生成强吸引力的开场标题与开场段，提升点击与停留。',
    true,
    true,
    'REPLACE_USER_ID',
    0,
    now(),
    now()
  );
```

### 私域营销（Workflow）
```sql
INSERT INTO "dify_app_config"
  ("id","name","description","apiKey","appId","appType","displayName","avatar","tags","category","introduction","isActive","isPublic","createdBy","usageCount","createdAt","updatedAt")
VALUES
  (
    'dify_app_private_marketing_workflow',
    '私域营销工作流',
    '基于行业画像(super_question)与核心素材(core_material)批量生成朋友圈建议文案。',
    'app-Yr7ue6MIgdUcqmGmTsDd2SFX',
    NULL,
    'workflow',
    '私域营销工作流',
    NULL,
    ARRAY['private_marketing','朋友圈','私域','工作流']::text[],
    '内容创作',
    '按策略模型输出建议朋友圈清单，可直接用于批量落地。',
    true,
    true,
    'REPLACE_USER_ID',
    0,
    now(),
    now()
  );
```

### 销冠复刻（Chatbot）
```sql
INSERT INTO "dify_app_config"
  ("id","name","description","apiKey","appId","appType","displayName","avatar","tags","category","introduction","isActive","isPublic","createdBy","usageCount","createdAt","updatedAt")
VALUES
  (
    'dify_app_sales_coach_chatbot',
    '销冠复刻对话',
    '使用Chatbot提供成交路径建议/教练/警告/追问等对话式输出。',
    'app-CC7X4T5s4N5kQva2sJn9Nndz',
    NULL,
    'chatbot',
    '销冠复刻助手',
    NULL,
    ARRAY['sales_coach','私域','话术','对话']::text[],
    '销售',
    '通过对话式问答给出路径建议、追问、教练式指引与提醒，用于私域成交提升。',
    true,
    true,
    'REPLACE_USER_ID',
    0,
    now(),
    now()
  );
```

---

## 测试用例（curl）

> 注意：`Authorization` 请替换为后端鉴权所需凭据；`app_id` 传入上方 SQL 中的 `id`。

### 选题裂变（SSE）
```bash
curl -N -X POST "http://localhost:3000/api/v1/ecosystem/superbrain/workflows/topic-generation/run" \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "app_id": "dify_app_topic_generation_workflow",
    "input": "行业：火锅；门店：福田CBD；诉求：引流到店。",
    "topic_type": "教知识",
    "number": 10,
    "titled": ["历史topic_direction..."],
    "additional": "偏向活动策划灵感"
  }'
```

### 实体连爆（SSE）
```bash
curl -N -X POST "http://localhost:3000/api/v1/ecosystem/superbrain/workflows/entity-burst/run" \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "app_id": "dify_app_entity_burst_workflow",
    "address": "深圳市福田区",
    "num": 5,
    "super_question": "我是福田CBD的潮汕牛肉火锅店，主打温体牛肉+花胶鸡金汤，希望做本地引流。"
  }'
```

### 成文脚本（SSE）
```bash
curl -N -X POST "http://localhost:3000/api/v1/ecosystem/superbrain/workflows/script-compose/run" \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "app_id": "dify_app_script_compose_workflow",
    "topic": "下班聚餐人均一百多，还能在CBD吃到M9和牛？..."
  }'
```

### 黄金三秒（SSE）
```bash
curl -N -X POST "http://localhost:3000/api/v1/ecosystem/superbrain/workflows/opening/run" \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "app_id": "dify_app_opening_workflow",
    "script": "{\"result\":{\"segments\":[{\"segment_number\":1,\"emotion\":\"认知颠覆\",\"visuals\":\"...\",\"dialogue\":\"...\"}],\"text\":\"...整段口播...\"}}"
  }'
```

### 私域营销（SSE）
```bash
curl -N -X POST "http://localhost:3000/api/v1/ecosystem/superbrain/workflows/private-marketing/run" \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "app_id": "dify_app_private_marketing_workflow",
    "super_question": {
      "business_identity": { "name": "福田潮汕牛肉火锅", "address": "福田CBD" }
    },
    "core_material": ["点评4.9分，近30天回头客占比42%", "午市40分钟快享金汤套餐"]
  }'
```

### 销冠复刻（Chatbot/SSE）
```bash
curl -N -X POST "http://localhost:3000/api/v1/ecosystem/superbrain/workflows/sales-coach/run" \
  -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "app_id": "dify_app_sales_coach_chatbot",
    "query": "客户觉得我们价格高，犹豫不下单，怎么推进？",
    "inputs": { "reply_type": "pathway_suggestion" },
    "conversation_id": "conv_demo_001"
  }'
```

---

## 已知问题与修复

- Dify 表单校验差异：
  - `object/input` 必须为字符串 → 对象统一 stringify
  - `titled/additional` paragraph 类型必须为字符串 → 统一转字符串，数组按行拼接
  - `Address` 大小写差异 → 同时传 `Address` 与 `address`
  - 黄金三秒字段统一为 `segment`
- 超时：默认 30s 导致中断 → 默认提升至 110s

---

## 前端集成与UI统一（2025-08-18）

- 工作流前端页面（`workflow/index.tsx`）
  - 取消骨架屏，统一卡片网格渲染。
  - 移除 `WorkflowTabs`，与 Agent 一并通过 `WorkflowCard.tsx` 展示。
  - 卡片 hover 改为放大；去除流光特效；类型标签固定左下角，与“打开”同一行。
- 主题与组件：
  - `sb-theme.module.css` 增加粘土风头像、skeu 按钮、选择器主题等工具类。
  - `SearchBar.tsx` 下拉边框、内容、hover 配色统一为紫白主题。
- 布局：
  - `SuperBrainWrapper.tsx` 统一内容间距并与侧边栏外边距对齐；页面内容去除自定义顶部 padding，依赖布局层统一控制。
- 首页体验：
  - “我的超级大脑”区域改为页面加载即显示（去除 whileInView），避免用户不滚动看不到。

---

## 后续优化

- 在流式事件中聚合 usage，补齐 `usage/usage_1/usage_2` 统计
- 字段别名映射抽配置，低成本适配不同 Dify 表单
- 销冠复刻前端接入连续会话与 `reply_type` 快捷切换

---

## 参考

- `docs/super_brain/context/工作流接口规范.md`
- `frontend/packages/api/src/routes/v1/ecosystem/superbrain/workflows/*`
- `frontend/packages/api/src/routes/v1/ecosystem/superbrain/dify/*`

---

## 🧩 增量更新 · UI/布局统一（SuperBrain）(2025-08-17)

- 根路径始终展示侧边栏，并重定向到 dashboard。
- 新增 `SuperBrainLogo`（展开“9000AI + 超级大脑”，收起“AI”），与 Avatar/Agent 视觉一致。
- 移除顶部 Header，布局统一为“侧栏 + 内容区”。
- 侧栏展开宽度 `15rem`，外置折叠按钮位置与宽度联动；折叠态 Tooltip 与图标 hover 动效统一（轻放大/轻旋转/细薄高亮线），整体光效下调。
- 修复 Logo 右侧溢出被遮挡的问题：容器 `overflow: visible` 并调整 padding。
