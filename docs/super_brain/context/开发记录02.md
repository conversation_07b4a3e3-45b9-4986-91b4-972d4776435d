# 超级大脑模块开发记录 - Dify API 封装实现

## 📋 项目概述

本文档记录了9000AI超级大脑模块中 Dify API 封装的完整开发过程，包括架构设计、模块实现和技术决策。

**开发目标**: 为 SuperBrain 模块提供完整的 Dify 平台 API 集成
**核心功能**: Agent 智能体、Chatbot 对话机器人的完整 API 封装
**技术栈**: TypeScript + Fetch API + 流式处理

---

## 🏗️ 架构设计原则

### 1. 独立模块设计

**设计理念**: 每个模块完全独立，避免共享依赖
- **Agent 模块**: 独立的类型定义、客户端、API实现
- **Chatbot 模块**: 独立的类型定义、客户端、API实现
- **避免基础类**: 不使用共享的 Base 类，各模块自行实现

### 2. 关注点分离

**客户端职责**: 仅负责 HTTP 通信
- 请求发送和响应处理
- 流式数据解析
- 错误处理和重试机制

**API 层职责**: 封装具体业务逻辑
- 参数验证和构建
- 业务日志记录
- 响应数据处理

### 3. 文件组织结构

```
dify/
├── agent/                          # Agent 智能体模块
│   ├── types.ts                   # 类型定义
│   ├── client.ts                  # HTTP 客户端
│   └── api/                       # API 封装层
│       ├── chat/                  # 聊天相关 API
│       │   ├── chat.ts           # 发送消息
│       │   ├── stop.ts           # 停止响应
│       │   └── index.ts          # 统一导出
│       └── conversation/          # 会话管理 API
│           ├── types.ts          # 会话类型定义
│           ├── list.ts           # 获取会话列表
│           ├── messages.ts       # 获取历史消息
│           ├── rename.ts         # 重命名会话
│           ├── delete.ts         # 删除会话
│           └── index.ts          # 统一导出
└── chatbot/                       # Chatbot 对话机器人模块
    ├── types.ts                   # (结构同 agent)
    ├── client.ts
    └── api/
        ├── chat/
        └── conversation/
```

---

## 🔧 核心技术实现

### 1. HTTP 客户端设计

#### 统一的错误处理
```typescript
export class DifyAgentError extends Error {
  public code: string;
  public status: number;
  
  constructor(code: string, message: string, status: number) {
    super(message);
    this.name = "DifyAgentError";
    this.code = code;
    this.status = status;
  }
}
```

#### 超时和取消机制
```typescript
const response = await fetch(url, {
  ...options,
  headers: {
    "Content-Type": "application/json",
    "Authorization": `Bearer ${this.config.apiKey}`,
    ...options.headers,
  },
  signal: AbortSignal.timeout(this.config.timeout!), // 30秒默认超时
});
```

#### 流式响应处理
```typescript
async *parseStreamData(
  reader: ReadableStreamDefaultReader<Uint8Array>,
  requestId?: string
): AsyncGenerator<DifyAgentStreamEvent, void, unknown> {
  const decoder = new TextDecoder();
  let buffer = "";
  
  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      
      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split("\n");
      buffer = lines.pop() || "";
      
      for (const line of lines) {
        if (line.startsWith("data: ")) {
          const eventData = line.slice(6);
          if (eventData === "[DONE]") return;
          
          try {
            const event = JSON.parse(eventData) as DifyAgentStreamEvent;
            yield event;
          } catch (parseError) {
            // 日志记录解析错误
          }
        }
      }
    }
  } finally {
    reader.releaseLock();
  }
}
```

### 2. API 层封装设计

#### 函数重载支持
```typescript
// 统一接口，支持阻塞和流式模式
async sendChatMessage(
  request: DifyAgentChatRequest,
  requestId?: string
): Promise<DifyAgentChatResponse>;
async sendChatMessage(
  request: DifyAgentChatRequest & { response_mode: DifyResponseMode.BLOCKING },
  requestId?: string
): Promise<DifyAgentChatResponse>;
async sendChatMessage(
  request: DifyAgentChatRequest & { response_mode: DifyResponseMode.STREAMING },
  requestId?: string
): Promise<AsyncGenerator<DifyAgentStreamEvent, void, unknown>>;
```

#### 请求 ID 追踪
```typescript
const id = requestId || Math.random().toString(36).substring(7);

logger.info("[AgentChatAPI] 发送对话消息", {
  requestId: id,
  user: request.user,
  conversationId: request.conversation_id,
  queryLength: request.query.length,
});
```

### 3. 类型安全实现

#### 枚举定义
```typescript
export enum DifyResponseMode {
  STREAMING = "streaming",
  BLOCKING = "blocking"
}

export enum DifyAgentStreamEventType {
  MESSAGE = "message",
  MESSAGE_FILE = "message_file", 
  MESSAGE_END = "message_end",
  MESSAGE_REPLACE = "message_replace",
  ERROR = "error",
  PING = "ping"
}
```

#### 完整的请求/响应类型
```typescript
export interface DifyAgentChatRequest {
  inputs: Record<string, any>;
  query: string;
  response_mode?: DifyResponseMode;
  conversation_id?: string;
  user: string;
  files?: Array<{
    type: "image";
    transfer_method: "remote_url";
    url: string;
  }>;
}

export interface DifyAgentChatResponse {
  event: string;
  message_id: string;
  conversation_id: string;
  mode: string;
  answer: string;
  metadata: {
    usage: {
      prompt_tokens: number;
      completion_tokens: number;
      total_tokens: number;
    };
    retriever_resources: Array<{
      position: number;
      dataset_id: string;
      dataset_name: string;
      document_id: string;
      document_name: string;
      segment_id: string;
      score: number;
      content: string;
    }>;
  };
  created_at: number;
}
```

---

## 📡 API 功能实现

### 1. 聊天消息 API

#### 发送消息功能
- **阻塞模式**: 等待完整响应后返回
- **流式模式**: 实时返回消息片段
- **文件支持**: 图片上传功能
- **会话管理**: 自动会话 ID 管理

#### 停止响应功能
- **Task ID 追踪**: 从流式响应中获取任务 ID
- **优雅停止**: 仅支持流式模式的停止
- **状态反馈**: 返回操作结果状态

```typescript
// 使用示例
const taskId = "task-uuid-from-stream";
await chatAPI.stop.stopChatMessage(taskId, {
  user: "user-123"
});
```

### 2. 会话管理 API

#### 会话列表查询
- **分页支持**: 完整的分页参数
- **排序功能**: 按创建时间、更新时间排序
- **搜索过滤**: 支持多字段搜索

```typescript
const conversations = await conversationAPI.list.getConversations({
  user: "user-123",
  limit: 20,
  sort_by: "created_at",
  last_id: "last-conversation-id"
});
```

#### 历史消息查询
- **会话筛选**: 按会话 ID 查询消息
- **分页加载**: 支持分页和偏移查询
- **完整元数据**: 包含文件、反馈、检索资源

```typescript
const messages = await conversationAPI.messages.getMessages({
  conversation_id: "conv-123",
  user: "user-123",
  limit: 50,
  first_id: "first-message-id"
});
```

#### 会话重命名
- **手动命名**: 用户指定会话名称
- **自动生成**: AI 自动生成合适的标题

```typescript
// 手动指定名称
await conversationAPI.rename.renameConversation("conv-123", {
  name: "关于AI的讨论",
  user: "user-123"
});

// 自动生成名称
await conversationAPI.rename.renameConversation("conv-123", {
  auto_generate: true,
  user: "user-123"
});
```

#### 会话删除
- **软删除**: 标记删除，不物理删除数据
- **权限验证**: 确保用户有删除权限

```typescript
await conversationAPI.delete.deleteConversation("conv-123", {
  user: "user-123"
});
```

---

## 🎯 使用方式

### 1. 基础使用

#### Agent 模块
```typescript
import { DifyAgentClient } from './dify/agent/client';
import { AgentChatManager } from './dify/agent/api/chat';
import { AgentConversationAPI } from './dify/agent/api/conversation';

// 初始化客户端
const client = new DifyAgentClient({
  baseUrl: 'https://api.dify.ai/v1',
  apiKey: 'your-agent-api-key',
  timeout: 30000
});

// 创建 API 实例
const chatManager = new AgentChatManager(client);
const conversationAPI = new AgentConversationAPI(client);
```

#### Chatbot 模块
```typescript
import { DifyChatbotClient } from './dify/chatbot/client';
import { ChatbotChatManager } from './dify/chatbot/api/chat';
import { ChatbotConversationAPI } from './dify/chatbot/api/conversation';

// 初始化客户端  
const client = new DifyChatbotClient({
  baseUrl: 'https://api.dify.ai/v1',
  apiKey: 'your-chatbot-api-key',
  timeout: 30000
});

// 创建 API 实例
const chatManager = new ChatbotChatManager(client);
const conversationAPI = new ChatbotConversationAPI(client);
```

### 2. 流式对话处理

```typescript
// 发送流式消息
const stream = await chatManager.chat.sendMessageStream({
  query: "你好，请介绍一下你自己",
  user: "user-123",
  inputs: {}
});

let taskId: string | undefined;

// 处理流式响应
for await (const event of stream) {
  switch (event.event) {
    case 'message':
      console.log('消息内容:', event.answer);
      if (event.task_id) {
        taskId = event.task_id;
      }
      break;
      
    case 'message_end':
      console.log('消息结束');
      break;
      
    case 'error':
      console.error('错误:', event.message);
      break;
  }
}

// 如果需要停止响应
if (taskId) {
  await chatManager.stop.stopChatMessage(taskId, {
    user: "user-123"
  });
}
```

### 3. 会话管理操作

```typescript
// 获取用户的会话列表
const conversationList = await conversationAPI.list.getConversations({
  user: "user-123",
  limit: 10,
  sort_by: "-updated_at" // 按更新时间倒序
});

// 获取特定会话的消息历史
const messageHistory = await conversationAPI.messages.getMessages({
  conversation_id: conversationList.data[0].id,
  user: "user-123",
  limit: 20
});

// 重命名会话
await conversationAPI.rename.renameConversation(
  conversationList.data[0].id,
  {
    name: "AI助手对话",
    user: "user-123"
  }
);

// 删除不需要的会话
await conversationAPI.delete.deleteConversation(
  conversationList.data[0].id,
  {
    user: "user-123"
  }
);
```

---

## 🔍 技术决策记录

### 1. 为什么不使用基础类？

**用户反馈**: "我认为不需要这个基础类，各自实现各自的封装会不会好一些"

**决策原因**:
- **独立维护**: 每个模块可独立更新，避免相互影响
- **类型安全**: 各模块有专门的类型定义，避免泛型复杂性
- **清晰职责**: 模块边界清楚，便于理解和维护

### 2. 客户端与 API 层分离

**设计思路**: "client 就担当客户端的作用就行，具体调用还是需要先创建一个 API 文件夹"

**实现方案**:
- **Client**: 纯 HTTP 通信，不包含业务逻辑
- **API**: 业务封装，参数验证，日志记录
- **分层调用**: API 层调用 Client 进行实际请求

### 3. 文件拆分策略

**组织原则**: 按功能模块拆分，便于后续修改
- **会话管理**: 拆分为 list、messages、rename、delete 四个文件
- **聊天功能**: 拆分为 chat、stop 两个文件
- **统一入口**: 通过 index.ts 提供管理器模式的 API

### 4. 错误处理策略

**多层错误处理**:
- **网络层**: HTTP 状态码和连接错误
- **协议层**: Dify API 错误响应
- **业务层**: 参数验证和逻辑错误
- **用户层**: 友好的错误提示

```typescript
// 错误处理示例
try {
  const response = await chatAPI.sendMessage(request);
} catch (error) {
  if (error instanceof DifyAgentError) {
    console.error(`Dify错误 [${error.code}]: ${error.message}`);
  } else {
    console.error('未知错误:', error);
  }
}
```

---

## 📊 性能优化

### 1. 请求优化

#### 超时控制
- **默认超时**: 30秒，适合大多数AI响应时间
- **可配置**: 允许用户自定义超时时间
- **自动取消**: 使用 AbortSignal 自动取消超时请求

#### 流式处理优化
- **缓冲机制**: 行缓冲处理，避免数据丢失
- **解析错误**: 单条消息解析失败不影响整体流
- **资源释放**: 确保 Reader 正确释放

### 2. 内存管理

#### 流式数据处理
- **逐行解析**: 避免大量数据积累在内存
- **及时释放**: Reader 使用完毕立即释放锁
- **错误清理**: 异常情况下确保资源清理

#### 类型定义优化
- **按需导入**: 仅导入需要的类型
- **避免循环依赖**: 合理的模块依赖关系

### 3. 日志系统

#### 结构化日志
```typescript
logger.info("[AgentChatAPI] 发送对话消息", {
  requestId: id,
  user: request.user,
  conversationId: request.conversation_id,
  queryLength: request.query.length,
});
```

#### 性能监控
- **请求时间**: 记录API调用耗时
- **流式延迟**: 监控流式响应的首包时间
- **错误率**: 统计各类错误的发生频率

---

## 🚀 未来扩展计划

### 1. 功能增强

#### 更多 API 支持
- [ ] 文件上传 API
- [ ] 知识库管理 API  
- [ ] 工作流执行 API
- [ ] 应用配置 API

#### 高级特性
- [ ] 请求重试机制
- [ ] 智能限流控制
- [ ] 缓存策略支持
- [ ] 离线消息队列

### 2. 开发体验优化

#### 开发工具
- [ ] API Mock 服务
- [ ] 调试工具集成
- [ ] 性能分析工具
- [ ] 自动化测试

#### 文档完善
- [ ] API 使用示例
- [ ] 错误处理指南
- [ ] 最佳实践文档
- [ ] 迁移升级指南

### 3. 生产环境优化

#### 稳定性提升
- [ ] 连接池管理
- [ ] 熔断器模式
- [ ] 健康检查机制
- [ ] 优雅降级策略

#### 监控告警
- [ ] 性能指标监控
- [ ] 错误率告警
- [ ] 实时日志分析
- [ ] 用户行为追踪

---

## 🎯 后端服务层实现 (v3.0.0)

### 1. SuperBrain Agents 后端架构

基于前面的 Dify API 封装，实现了完整的后端服务层，为前端提供统一的智能体交互接口。

#### API 端点设计
```
/api/v1/ecosystem/superbrain/agents
├── GET /                                    # 获取智能体列表
├── POST /:id/chat                          # 发送聊天消息
├── POST /:id/chat/stop                     # 停止聊天响应
├── GET /:id/conversations                   # 获取会话列表
├── GET /:id/conversations/:conversation_id/messages  # 获取消息历史
├── PUT /:id/conversations/:conversation_id/name      # 重命名会话
└── DELETE /:id/conversations/:conversation_id        # 删除会话
```

### 2. 核心实现特性

#### 身份认证与授权
- 集成现有的认证中间件
- 自动获取用户身份标识
- 基于 consumer 的多租户支持

#### 参数组装逻辑
```typescript
// 1. 根据 agentId 查找 DifyAppConfig
const difyApp = await db.difyAppConfig.findFirst({
  where: {
    id: agentId,
    deletedAt: null,
    isActive: true
  }
});

// 2. 动态创建对应类型的 Dify 客户端
const difyClient = createDifyClientWithDefaults(
  difyApp.appType === "AGENT" ? "agent" : "chatbot",
  difyApp.apiKey  // 使用应用专属的 API Key
);

// 3. 组装请求参数
const requestData = {
  query: body.query,
  inputs: body.inputs || {},
  response_mode: body.response_mode,
  conversation_id: body.conversation_id,  // 支持历史会话
  user: user.id,  // 用户标识
  files: body.files
};
```

#### 会话连续性支持
- 支持传入 `conversation_id` 继续历史对话
- 不传则创建新会话
- 完整的会话生命周期管理

### 3. 任务记录与统计

#### SuperBrainTask 记录
```typescript
async function createTaskRecord(data: {
  consumerId: string;
  difyAppId: string;
  inputContent: string;
  outputContent: string;
  conversationId?: string;
  messageId?: string;
  tokenCount: number;
  status: "COMPLETED" | "FAILED";
}) {
  await db.superBrainTask.create({
    data: {
      name: `聊天任务-${new Date().toLocaleString()}`,
      consumerId: data.consumerId,
      difyAppId: data.difyAppId,
      inputContent: data.inputContent,
      outputContent: data.outputContent,
      status: data.status,
      progress: 100,
      difyConversationId: data.conversationId,
      difyMessageId: data.messageId,
      tokenCount: data.tokenCount,
      startTime: new Date(),
      completionTime: new Date()
    }
  });
}
```

#### 使用统计更新
- 每次调用自动增加 `usageCount`
- 便于热门应用排序和分析

### 4. 流式响应处理

#### SSE (Server-Sent Events) 实现
```typescript
if (body.response_mode === "streaming") {
  return streamSSE(c, async (stream) => {
    const messageStream = await difyClient.chat.sendChatMessage(requestData);
    
    for await (const event of messageStream) {
      await stream.writeSSE({
        data: JSON.stringify(event),
        event: event.event || "message"
      });
      
      // 消息结束时记录任务
      if (event.event === "message_end") {
        await createTaskRecord({...});
      }
    }
  });
}
```

### 5. 错误处理与日志

#### 结构化日志记录
```typescript
logger.info("[SuperBrain] 发送聊天消息", {
  requestId,
  agentId,
  consumerId: consumer.id,
  userId: user.id,
  queryLength: body.query.length,
  conversationId: body.conversation_id,
  responseMode: body.response_mode
});
```

#### 用户友好的错误响应
- 404: "智能体不存在或不可用"
- 400: "该应用不支持聊天功能"
- 500: 通用错误处理

### 6. 参数验证优化

遵循项目规范，使用手动参数验证：
```typescript
// 手动验证查询参数
const validatedQuery = {
  page: parseInt(query.page || "1"),
  limit: Math.min(parseInt(query.limit || "20"), 100),
  sort_by: query.sort_by || "-updated_at",
  last_id: query.last_id,
  first_id: query.first_id
};
```

### 7. 前端集成要点

#### 选择智能体后的流程
1. 前端获取智能体列表 (`GET /agents`)
2. 用户选择智能体后，获取历史会话 (`GET /agents/:id/conversations`)
3. 显示会话列表，同时提供"新建对话"选项
4. 用户可以：
   - 点击历史会话继续对话（带 `conversation_id`）
   - 创建新对话（不带 `conversation_id`）

#### 聊天交互流程
1. 发送消息 (`POST /agents/:id/chat`)
2. 处理流式响应或阻塞响应
3. 支持中断响应 (`POST /agents/:id/chat/stop`)
4. 实时更新UI显示

### 8. 技术亮点

- **零配置集成**: 自动从数据库获取 API Key 和配置
- **多应用类型支持**: 同时支持 Agent 和 Chatbot
- **完整的会话管理**: CRUD 操作全覆盖
- **任务追踪**: 每次对话都有完整的记录
- **流式优先**: 提供更好的用户体验

---

## 📚 相关文档

- [开发记录01.md](./开发记录01.md) - SuperBrain 前端页面实现
- [Dify官方API文档](https://docs.dify.ai/api-reference) - API规范参考
- [TypeScript类型指南] - 类型定义最佳实践
- [错误处理规范] - 统一错误处理机制

---

**最后更新**: 2025年1月
**维护人员**: Claude Code Assistant  
**版本**: v3.0.0
**新增内容**: 
- v2.0.0: Dify API 完整封装实现 - 独立模块架构、客户端分离、会话管理、聊天功能
- v3.0.0: 后端服务层实现 - 完整的 API 端点、参数组装、任务记录、流式响应处理

---

## ♻️ 增量更新（v3.1.0）

### 1. Agent/Chatbot 模块增强

- 新增 `files/` 文件模块（两端结构一致）：
  - `POST /files/upload` → `AgentFilesAPI.upload.uploadFile()`、`ChatbotFilesAPI.upload.uploadFile()`
  - 支持 multipart/form-data；客户端自动判断 FormData，不强制设置 `Content-Type`

- 新增 `feedback/` 消息反馈模块（两端结构一致）：
  - `POST /messages/{message_id}/feedbacks` → `AgentFeedbackAPI.message.sendFeedback()`、`ChatbotFeedbackAPI.message.sendFeedback()`
  - `rating`: `like | dislike | null`（null 为撤销点赞）；`content` 可选

- 聚合导出更新：
  - `agent/api/index.ts` 导出 `AgentFilesAPI`、`AgentFeedbackAPI`
  - `chatbot/api/index.ts` 导出 `ChatbotFilesAPI`、`ChatbotFeedbackAPI`

### 2. Workflow 模块重构与增强

- 子模块化目录：`workflow/api/` 下划分
  - `workflow/`（工作流本体）
    - 运行：`POST /workflows/run` → `WorkflowRunAPI.run | runStream | execute`
    - 结果：`GET /workflows/run/{workflow_run_id}` → `WorkflowResultAPI.get`
    - 停止：`POST /workflows/run/{workflow_run_id}/stop` → `WorkflowStopAPI.stop`
    - 日志：`GET /workflows/logs` → `WorkflowLogsAPI.list`
  - `files/`
    - 上传：`POST /files/upload` → `WorkflowFileUploadAPI.uploadFile`

- 顶层聚合：`WorkflowAPI` 暴露 `workflow` 与 `files` 两个域

### 3. Workflow 客户端增强

- 新增方法：
  - `listWorkflowLogs({ keyword?, status?, page?, limit? }, requestId?)`
  - `getConfig()`（与其他客户端对齐，用于扩展场景）

- 方法可见性调整：
  - `request / requestStream / parseStreamData` 公开，便于 API 层以强类型方式复用

- multipart 支持：
  - 根据请求体是否为 `FormData` 动态设置 `Content-Type`，避免破坏边界

### 4. 类型与返回

- Workflow 日志类型补充：
  - `DifyEndUserSummary`、`DifyWorkflowRunSummary`、`DifyWorkflowLogItem`、`DifyWorkflowLogsResponse`

### 5. 最小用法示例（片段）

```ts
// Agent 文件上传
const agentFiles = new AgentFilesAPI(agentClient);
const fileRes = await agentFiles.upload.uploadFile({ file, user: "user-123" });

// Chatbot 消息点赞
const chatbotFeedback = new ChatbotFeedbackAPI(chatbotClient);
await chatbotFeedback.message.sendFeedback({ message_id, user: "user-123", rating: "like" });

// Workflow 执行 + 轮询 + 停止 + 日志
const wf = new WorkflowAPI(workflowClient);
await wf.workflow.run.run({ inputs, user: "user-123", response_mode: "blocking" });
await wf.workflow.result.get({ workflow_run_id, user: "user-123" });
await wf.workflow.stop.stop({ workflow_run_id, user: "user-123" });
await wf.workflow.logs.list({ status: "failed", page: 1, limit: 20 });
```

### 6. 新增模块：角色 IP 定位（内部接口）

- Base Path: `/api/v1/ecosystem/superbrain/ip`
- 认证：无需 `authMiddleware`，通过 `userId` 精准定位消费者（仅限可信内部调用）

#### 接口：更新/创建“超级问题”
- 方法与路径：`PUT /super-question`
- 请求体：
  - `userId: string` 必填（用于定位 `Consumer`）
  - `superQuestion: object` 必填（任意 JSON 结构）
- 行为：
  - 根据 `userId` 查询 `consumer`
  - 若存在 `CharacterIPProfile`：更新 `superQuestion`，`questionVersion + 1`
  - 不存在：创建新记录，最小必填默认值：`coreConcept = {}`，`ipName = "IP-<consumer.id>"`，`ipType = "unknown"`
- 响应（核心字段）：`{ id, superQuestion, questionVersion, createdAt, updatedAt, ipName?, ipType? }`

后端实现位置：
- 路由：`frontend/packages/api/src/routes/v1/ecosystem/superbrain/ip/router.ts`
- 处理器：`.../ip/super_question/update.ts`
- 校验：`.../ip/schemas.ts`

---

## 🧩 增量更新 · UI/布局统一（SuperBrain）(2025-08-17)

- SuperBrain 前端容器层更新，确保与 Avatar/Agent 的侧边栏 UI/交互完全一致：
  - 新增 `SuperBrainLogo` 组件（`zywhFont/aqbFont/qxygFont`），展开“9000AI + 超级大脑”，收起仅“AI”。
  - 布局切换为“侧栏 + 内容区”，移除顶部 Header；根路径始终显示侧栏。
  - 侧栏展开宽度 `15rem`，外置折叠按钮位置与侧栏宽度联动：展开 `left: calc(15rem + 0.5rem)`，收起 `left: calc(4rem + 0.5rem)`。
  - 折叠态 Tooltip 样式统一（行内居中、细薄底部高亮线、柔光晕），图标 hover 有轻微放大与旋转；选中/悬浮光晕整体减弱。
  - 修复 Logo 右侧被边框/按钮遮挡：顶部容器与侧栏设置 `overflow: visible`，并统一 padding。

说明：该段为前端 UI 文档化记录，不影响 Dify API 封装与后端接口定义。

---

## 前端页面与交互统一（2025-08-18）

- 首页：
  - “我的超级大脑”区域页面加载即显示，取消滚动触发（避免遗漏）。
- 工作流页面：
  - 统一使用 `WorkflowCard.tsx`；去掉骨架屏与 `WorkflowTabs`；hover 改为放大，去除流光；类型标签左下角与“打开”同行。
- 布局：
  - `SuperBrainWrapper.tsx` 统一内容间距与顶部对齐，子页面移除自定义外层 padding。
- 主题组件：
  - `sb-theme.module.css` 新增粘土风头像、skeu 按钮、选择器主题等工具类，搜索与下拉统一为紫白主题。


