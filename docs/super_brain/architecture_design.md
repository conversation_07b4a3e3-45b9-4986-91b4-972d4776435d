# 超级大脑模块架构设计文档

## 概述
超级大脑模块是9000AI生态系统的AI智能体对话子系统，通过集成Dify API提供智能对话、内容生成、脚本创作等功能，并与其他生态系统模块无缝集成。

## 前端架构设计

### 1. 路由结构

```
/app/ecosystem/superbrain/                  # 超级大脑根路径
├── dashboard/                              # 仪表板页面
├── chat/                                   # AI对话页面
├── agents/                                 # 智能体管理
├── scripts/                                # 脚本库
├── topics/                                 # 选题管理
├── contents/                               # 成品文案库
├── character-ip/                          # 角色IP定位
└── settings/                               # 模块设置
```

### 2. 页面模块设计

#### 2.1 仪表板页面 (`/dashboard`)
- **功能**：展示超级大脑使用统计、快速入口、最新动态
- **组件结构**：
  ```tsx
  SuperbrainDashboard/
  ├── components/
  │   ├── StatsCards.tsx           # 统计卡片
  │   ├── QuickActions.tsx         # 快速操作
  │   ├── RecentActivities.tsx     # 最近活动
  │   └── UsageChart.tsx           # 使用图表
  └── DashboardPage.tsx
  ```

#### 2.2 AI对话页面 (`/chat`)
- **功能**：与AI智能体进行对话，生成内容
- **组件结构**：
  ```tsx
  SuperbrainChat/
  ├── components/
  │   ├── ChatInterface/
  │   │   ├── MessageList.tsx      # 消息列表
  │   │   ├── InputArea.tsx        # 输入区域
  │   │   └── AgentSelector.tsx    # 智能体选择器
  │   ├── WorkflowOutput/
  │   │   ├── OutputPreview.tsx    # 输出预览
  │   │   └── TransferDialog.tsx   # 传递对话框
  │   └── ChatSidebar.tsx          # 侧边栏
  └── ChatPage.tsx
  ```

#### 2.3 智能体管理 (`/agents`)
- **功能**：管理Dify应用配置，创建和配置智能体
- **组件结构**：
  ```tsx
  SuperbrainAgents/
  ├── components/
  │   ├── AgentList.tsx            # 智能体列表
  │   ├── AgentCard.tsx            # 智能体卡片
  │   ├── CreateAgentDialog.tsx    # 创建对话框
  │   └── AgentConfigForm.tsx      # 配置表单
  └── AgentsPage.tsx
  ```

#### 2.4 脚本库 (`/scripts`)
- **功能**：管理和浏览同步的脚本内容
- **组件结构**：
  ```tsx
  SuperbrainScripts/
  ├── components/
  │   ├── ScriptList.tsx           # 脚本列表
  │   ├── ScriptDetail.tsx         # 脚本详情
  │   ├── ScriptFilter.tsx         # 筛选器
  │   └── SyncStatus.tsx           # 同步状态
  └── ScriptsPage.tsx
  ```

#### 2.5 选题管理 (`/topics`)
- **功能**：管理AI生成的选题，确认和编辑
- **组件结构**：
  ```tsx
  SuperbrainTopics/
  ├── components/
  │   ├── TopicList.tsx            # 选题列表
  │   ├── TopicEditor.tsx          # 选题编辑器
  │   ├── TopicStatusFlow.tsx      # 状态流程
  │   └── GenerateTopicDialog.tsx  # 生成对话框
  └── TopicsPage.tsx
  ```

#### 2.6 成品文案库 (`/contents`)
- **功能**：管理最终的文案内容，支持复用和发布
- **组件结构**：
  ```tsx
  SuperbrainContents/
  ├── components/
  │   ├── ContentGrid.tsx          # 内容网格
  │   ├── ContentPreview.tsx       # 内容预览
  │   ├── ContentEditor.tsx        # 内容编辑器
  │   ├── PublishDialog.tsx        # 发布对话框
  │   └── ReviewPanel.tsx          # 审核面板
  └── ContentsPage.tsx
  ```

#### 2.7 角色IP定位 (`/character-ip`)
- **功能**：管理用户的角色IP定位和核心价值主张
- **组件结构**：
  ```tsx
  SuperbrainCharacterIP/
  ├── components/
  │   ├── IPProfileForm.tsx        # IP档案表单
  │   ├── SuperQuestionEditor.tsx  # 超级问题编辑器
  │   ├── IPConceptBuilder.tsx     # 理念构建器
  │   └── AudienceDefiner.tsx      # 受众定义器
  └── CharacterIPPage.tsx
  ```

### 3. 共享组件

```tsx
modules/saas/ecosystem/superbrain/
├── components/
│   ├── common/
│   │   ├── LoadingState.tsx     # 加载状态
│   │   ├── ErrorBoundary.tsx    # 错误边界
│   │   ├── EmptyState.tsx       # 空状态
│   │   └── ConfirmDialog.tsx    # 确认对话框
│   ├── layout/
│   │   ├── SuperbrainWrapper.tsx # 布局包装器
│   │   ├── SuperbrainNav.tsx    # 导航组件
│   │   └── SuperbrainSidebar.tsx # 侧边栏
│   └── shared/
│       ├── TaskStatusBadge.tsx   # 任务状态徽章
│       ├── TokenCounter.tsx      # Token计数器
│       └── ComputingPowerBar.tsx # 算力条
├── hooks/
│   ├── use-dify-agents.ts        # Dify智能体
│   ├── use-superbrain-tasks.ts  # 任务管理
│   ├── use-character-ip.ts      # 角色IP
│   ├── use-scripts.ts           # 脚本管理
│   ├── use-topics.ts            # 选题管理
│   └── use-contents.ts          # 内容管理
├── lib/
│   ├── dify-client.ts           # Dify客户端
│   ├── task-queue.ts            # 任务队列
│   └── content-formatter.ts     # 内容格式化
└── types/
    └── index.ts                 # 类型定义
```

## 后端架构设计

### 1. API路由结构

```typescript
/api/v1/ecosystem/superbrain/
├── agents/                      # 智能体管理
│   ├── GET    /                # 获取智能体列表
│   ├── POST   /                # 创建智能体
│   ├── GET    /:id             # 获取智能体详情
│   ├── PUT    /:id             # 更新智能体
│   └── DELETE /:id             # 删除智能体
├── chat/                        # 对话接口
│   ├── POST   /conversations   # 创建对话
│   ├── POST   /messages        # 发送消息（流式）
│   └── POST   /workflow-output # 创建工作流输出
├── tasks/                       # 任务管理
│   ├── GET    /                # 获取任务列表
│   ├── GET    /:id             # 获取任务详情
│   ├── POST   /                # 创建任务
│   └── GET    /:id/status      # 获取任务状态
├── scripts/                     # 脚本管理
│   ├── GET    /                # 获取脚本列表
│   ├── GET    /:id             # 获取脚本详情
│   └── POST   /sync            # 同步外部脚本
├── topics/                      # 选题管理
│   ├── GET    /                # 获取选题列表
│   ├── POST   /                # 创建选题
│   ├── PUT    /:id             # 更新选题
│   └── PUT    /:id/confirm     # 确认选题
├── contents/                    # 内容管理
│   ├── GET    /                # 获取内容列表
│   ├── POST   /                # 创建内容
│   ├── PUT    /:id             # 更新内容
│   ├── PUT    /:id/review      # 审核内容
│   └── PUT    /:id/publish     # 发布内容
├── character-ip/                # 角色IP管理
│   ├── GET    /                # 获取IP列表
│   ├── POST   /                # 创建IP定位
│   ├── GET    /:id             # 获取IP详情
│   └── PUT    /:id             # 更新IP定位
└── stats/                       # 统计接口
    └── GET    /dashboard        # 获取仪表板数据
```

### 2. 服务层架构

```typescript
src/routes/v1/ecosystem/superbrain/
├── router.ts                    # 主路由文件
├── middleware/
│   └── rate-limit.ts           # 限流中间件
├── agents/
│   ├── router.ts               # 智能体路由
│   ├── handlers/
│   │   ├── create.ts           # 创建处理器
│   │   ├── list.ts             # 列表处理器
│   │   ├── update.ts           # 更新处理器
│   │   └── delete.ts           # 删除处理器
│   └── schemas.ts              # 验证模式
├── chat/
│   ├── router.ts               # 对话路由
│   ├── handlers/
│   │   ├── conversation.ts     # 对话处理器
│   │   ├── message.ts          # 消息处理器（SSE）
│   │   └── workflow-output.ts  # 工作流输出
│   ├── lib/
│   │   ├── dify-service.ts     # Dify服务
│   │   ├── stream-handler.ts   # 流处理器
│   │   └── message-queue.ts    # 消息队列
│   └── schemas.ts              # 验证模式
├── tasks/
│   ├── router.ts               # 任务路由
│   ├── handlers/
│   │   ├── create-task.ts      # 创建任务
│   │   ├── get-task.ts         # 获取任务
│   │   └── task-consumer.ts    # 任务消费者
│   ├── lib/
│   │   ├── task-processor.ts   # 任务处理器
│   │   └── task-queue.ts       # 任务队列
│   └── schemas.ts              # 验证模式
├── scripts/
│   ├── router.ts               # 脚本路由
│   ├── handlers/
│   │   ├── list.ts             # 列表处理器
│   │   ├── detail.ts           # 详情处理器
│   │   └── sync.ts             # 同步处理器
│   └── schemas.ts              # 验证模式
├── topics/
│   ├── router.ts               # 选题路由
│   ├── handlers/
│   │   ├── create.ts           # 创建选题
│   │   ├── update.ts           # 更新选题
│   │   └── confirm.ts          # 确认选题
│   └── schemas.ts              # 验证模式
├── contents/
│   ├── router.ts               # 内容路由
│   ├── handlers/
│   │   ├── create.ts           # 创建内容
│   │   ├── update.ts           # 更新内容
│   │   ├── review.ts           # 审核内容
│   │   └── publish.ts          # 发布内容
│   └── schemas.ts              # 验证模式
├── character-ip/
│   ├── router.ts               # IP路由
│   ├── handlers/
│   │   ├── create.ts           # 创建IP
│   │   ├── list.ts             # 列表处理器
│   │   └── update.ts           # 更新IP
│   └── schemas.ts              # 验证模式
├── stats/
│   ├── router.ts               # 统计路由
│   ├── handlers/
│   │   └── dashboard.ts        # 仪表板数据
│   └── types.ts                # 类型定义
└── types.ts                    # 共享类型定义
```

### 3. 核心服务设计

#### 3.1 Dify服务集成
```typescript
// lib/dify-service.ts
export class DifyService {
  // 对话API
  async createConversation(appId: string, user: string)
  async sendMessage(appId: string, conversationId: string, message: string)
  
  // 工作流API
  async runWorkflow(appId: string, inputs: Record<string, any>)
  
  // 文本生成API
  async generateText(appId: string, prompt: string)
  
  // SSE流处理
  async streamChat(appId: string, message: string): AsyncGenerator
}
```

#### 3.2 任务队列服务
```typescript
// lib/task-queue.ts
export class TaskQueueService {
  // 任务创建
  async createTask(task: TaskInput): Promise<Task>
  
  // 任务处理
  async processTask(taskId: string): Promise<void>
  
  // 任务状态更新
  async updateTaskStatus(taskId: string, status: TaskStatus)
  
  // 任务重试
  async retryTask(taskId: string): Promise<void>
}
```

#### 3.3 内容处理服务
```typescript
// lib/content-processor.ts
export class ContentProcessor {
  // 格式化内容
  formatContent(raw: string, format: ContentFormat): Json
  
  // 提取关键信息
  extractMetadata(content: Json): ContentMetadata
  
  // 内容验证
  validateContent(content: Json): ValidationResult
  
  // 内容转换
  convertToTargetModule(content: Json, target: string): Json
}
```

## 关键技术实现

### 1. 实时对话流（SSE）
- 使用Server-Sent Events实现流式响应
- 支持打字机效果和实时token统计
- 错误处理和断线重连机制

### 2. 任务队列系统
- 基于RabbitMQ的异步任务处理
- 支持任务优先级和延迟执行
- 自动重试和错误恢复机制

### 3. 算力消耗统计
- 实时统计token使用量
- 自动扣除算力值
- 算力不足时的优雅降级

### 4. 工作流集成
- 支持将AI生成内容传递到其他模块
- 用户确认机制，非全自动化
- 数据格式自动转换和适配

## 安全与权限

### 1. API安全
- 所有接口需要用户认证
- 基于角色的访问控制（RBAC）
- API密钥加密存储

### 2. 数据安全
- 敏感数据加密
- 审计日志记录
- 数据隔离机制

### 3. 限流保护
- 用户级别限流
- IP级别限流
- 自适应限流策略

## 部署架构

### 1. 服务部署
- 前端：Next.js应用部署
- 后端：Node.js服务部署
- 队列：RabbitMQ服务
- 缓存：Redis服务

### 2. 扩展性设计
- 水平扩展支持
- 负载均衡配置
- 服务熔断机制

## 监控与日志

### 1. 性能监控
- API响应时间监控
- 任务处理效率监控
- 资源使用率监控

### 2. 业务监控
- 用户行为分析
- 内容生成质量监控
- 算力消耗趋势分析

### 3. 日志管理
- 结构化日志记录
- 日志聚合分析
- 异常告警机制