# 超级大脑 · 工作流渲染与联调 Todolist（2025-08-19）

> 用于跟踪当前「工作流渲染架构 + 运行页联调」相关的待办、进展与验收标准。

---

## 里程碑
- [ ] M1：运行页最小可用（1 条工作流成功流式、页面分区展示完成）
- [ ] M2：事件规范化与前端消费（后端 normalize + 前端 Renderer 分区）
- [ ] M3：渲染架构骨架（Runner/Adapters/Registry/Viewer/GenericRenderer）
- [ ] M4：2 条工作流完整打通（实体连爆、成文）+ 单测覆盖

---

## 近期（本周）
- [x] 工作流卡片：类型标签改右上角，移除“打开”按钮，整卡点击分流
- [x] 列表点击分流：workflow/text_gen → view；agent/chatbot → chat
- [x] 顶部 TopBar：返回靠左 + 用户信息内联展示
- [x] 动画：运行页/对话页分区淡入与滑入
- [x] 运行页接入「营销选题」工作流（SSE），打印完整事件
- [ ] 运行页：停止（Stop）按钮（前端中止 + 预留后端真正停止）
- [ ] 运行页：参数化控件（topic_type、number、titled 去重）
- [ ] 运行页：节点 node_finished 的 outputs/inputs 折叠展示
- [ ] 运行页：消费“规范化事件”而非 raw 事件（待后端输出）

---

## 前端（apps/web）
- 运行页（WorkflowRunView）
  - [x] 表单 + 结果分区结构与动效
  - [x] 接入 useTopicGenerationWorkflow（SSE）
  - [ ] Stop 按钮（hook.stop 终止本地流；后端停止功能打通后接入）
  - [ ] 参数化控件：
    - [ ] topic_type（枚举：教知识/讲故事/聊观点/讲产品/晒过程）
    - [ ] number（int）
    - [ ] titled（多行/逗号分隔，汇总为数组）
  - [ ] 节点 outputs/inputs 折叠展示（details）
  - [ ] 切换到“规范化事件”消费（保留 Raw 调试折叠）
- Hooks
  - [x] hooks/workflow/use-topic-generation.ts（apiClient + SSE 解析）
  - [ ] hooks/workflow/use-entity-burst.ts（预留）
  - [ ] hooks/workflow/use-script-compose.ts（预留）
- 渲染架构骨架（与 docs/…/工作流渲染架构方案.md 对齐）
  - [ ] api/runner.ts（统一 run/轮询/停止）
  - [ ] adapters/types.ts（Usage/NormalizedResult 判别联合）
  - [ ] renderers/registry.ts（kind/appId → 适配器 + 组件）
  - [ ] viewer/WorkflowViewer.tsx（URL→run→normalize→render）
  - [ ] GenericRenderer.tsx（兜底）

---

## 后端（packages/api）
- topic-generation/run
  - [x] SSE 透传（已可用）
  - [ ] normalizeWorkflowEvent：
    - [ ] 字段收敛与脱敏：只保留必要字段（event/runId/taskId/node 摘要/outputs 摘要/usage）
    - [ ] usage 聚合：workflow_finished 中提供 total_tokens/total_price/currency/latency
    - [ ] 错误事件归一化：统一 message/节点失败汇总
    - [ ] （可选）text_chunk 节流/合并
- 停止运行
  - [ ] 暴露 POST /workflows/run/:runId/stop（代理 Dify stopWorkflow）
  - [ ] 前端联调：Stop 按钮真实停止

---

## 测试
- [ ] 前端：SSE 解析与累积的单元测试（mock ReadableStream）
- [ ] 前端：参数化控件交互（最小 e2e/集成）
- [ ] 后端：normalizeWorkflowEvent 的快照测试
- [ ] 后端：stop 路由集成测试（模拟 run→stop→状态）

---

## 文档
- [x] docs/super_brain/context/开发记录05.md 更新（2025-08-19）
- [ ] docs/super_brain/context/工作流接口规范.md：补充“规范化事件”建议用法与 usage 对齐
- [ ] docs/super_brain/todolist/工作流渲染架构方案.md：标注已落地与下一步（Runner/Registry/Adapters）
- [x] 本 todolist 创建

---

## 验收标准（M1）
- [ ] 在运行页输入画像文本，点击“运行”，能持续收到 SSE 事件并在页面分区展示：
  - [ ] 状态概览（runId/taskId/运行中→完成）
  - [ ] 节点进度（node_started/node_finished 列表）
  - [ ] 文本输出流（text_chunk 拼接）
  - [ ] 最终输出（workflow_finished.outputs 渲染）
  - [ ] 无明显报错/阻塞；网络中断能给出提示

---

## 风险与预案
- Dify 字段演进/差异：
  - 预案：后端 normalize，前端仅消费收敛后的稳定事件；保留 Raw 调试开关
- 长流程超时/断流：
  - 预案：前端 Abort + 后端 stop 双通道；UI 给出恢复/重试入口

---

## 依赖
- 需要一个可用的「营销选题」Dify 工作流 appId（DB 中有效且 isActive）
- 登录态/鉴权：确保 /api/v1/ecosystem/superbrain/workflows/* 可访问

---

## 责任人与时间
- 前端：WorkflowRunView + Hooks + UI（负责人：TBD）
- 后端：normalize/stop 路由（负责人：TBD）
- 截止：M1 本周内完成；M2 下周内完成

