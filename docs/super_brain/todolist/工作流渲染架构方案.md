### 超级大脑 · 工作流渲染架构方案（V1）

更新时间：2025-08-18

---

### 背景与目标

- 工作流接口规范中的各类工作流（选题、实体连爆、成文/黄金三秒、私域营销、素材索引、画像/对话等）返回的是 JSON 结果。
- 需求：为不同工作流提供“各自的展示逻辑与风格”，但复用统一的数据获取、主题风格、交互与能力（运行、日志、用量、复制、下载、分享）。
- 目标：通过“Schema/Adapter + 渲染注册表”的方式，把“原始 JSON → 规范化 ViewModel → 匹配 Renderer 组件”串起来，新增工作流只需登记适配器与渲染器即可。

---

### 设计原则

- 解耦：数据获取、数据标准化、渲染展示层互相解耦。
- 可扩展：任意新增工作流 = 新增 Schema + Normalize + Renderer + Registry 注册。
- 一致体验：主题（玻璃拟态 + 轻拟物）与动效克制统一；用量/复制/导出等操作统一。
- 兜底：未知工作流走通用渲染器（GenericRenderer）。

---

### 总体架构

- 数据层（Runner）
  - 统一发起执行（blocking/streaming）、轮询结果、获取日志、停止运行。
- Schema/Adapter 层
  - 针对不同工作流对“原始 JSON”做类型校验并转换为规范化 ViewModel。
- 渲染层（Renderer Registry）
  - 用注册表把 `kind/appId` 映射到：适配器 + 渲染器组件 + 元信息。
- 容器层（Viewer/Runner）
  - 解析 URL（`app、mode、runId`），调用 Runner 得到 `raw → normalize → render` 流程。
- 通用组件层
  - 结果卡、矩阵表格、列表/宫格、Usage 徽章、节点 Tabs、复制/下载、空/错态。

---

### 路由与交互

- 查看/运行页面：`/app/ecosystem/superbrain/workflow/view?app=<appId>&mode=run|detail&runId=...`
- 聊天/对话类沿用现有 `chat.tsx`（`/workflow/chat?agent=<id>`）。

---

### 规范化类型与注册表（示意）

```ts
// adapters/types.ts
export type Usage = { total_tokens: number; total_price: string; currency: string; latency?: number };

export type NormalizedResult =
  | { type: 'topics'; items: Array<{ topic_direction: string; angle_keywords: string[]; target_user?: string; formula_cn?: string }>; matrices?: any; usage?: Usage }
  | { type: 'entity_burst'; openings: string[]; reasons: string[]; ctas: string[]; outputs: string[]; usage?: Usage }
  | { type: 'script_compose'; segments: Array<{ segment_number: number; emotion?: string; visuals?: string; dialogue: string }>; text?: string; usage?: Usage }
  | { type: 'opening'; openings: Array<{ text: string }>; usage?: Usage }
  | { type: 'private_marketing'; posts: Array<{ post_content: string; strategy_used?: string; image_suggestion?: string; insight?: { title: string; content: string } }>; usage?: Usage }
  | { type: 'material_index'; products: any[]; tags: string[]; assets?: string[]; usage?: Usage }
  | { type: 'persona'; profile: any; usage?: Usage };

export type WorkflowKind =
  | 'topic_generation'
  | 'entity_burst'
  | 'script_compose'
  | 'opening'
  | 'private_marketing'
  | 'material_index'
  | 'persona'
  | 'chat';

export interface RendererConfig<T extends NormalizedResult = NormalizedResult> {
  kind: WorkflowKind;
  appIds?: string[];            // 可绑定具体 appId
  normalize: (raw: any) => T;   // 适配器
  Component: React.ComponentType<{ data: T }>; // 渲染器
  meta: { title: string; icon: any; theme: 'purple'|'green'|'cyan' };
}
```

---

### 目录结构（新增/调整）

```
workflow/
├── api/
│   ├── runner.ts                # 运行/轮询/日志/停止统一封装
│   └── clients.ts               # 可选，底层 fetch 封装
├── schemas/                     # 各工作流的输入/输出 Schema
│   ├── common.ts
│   ├── topic-generation.ts
│   ├── entity-burst.ts
│   ├── script-compose.ts
│   ├── opening.ts
│   ├── private-marketing.ts
│   ├── material-index.ts
│   └── persona-chat.ts
├── adapters/                    # 原始 JSON → 规范化 ViewModel
│   ├── types.ts
│   ├── normalize-topic-generation.ts
│   ├── normalize-entity-burst.ts
│   ├── normalize-script-compose.ts
│   ├── normalize-opening.ts
│   ├── normalize-private-marketing.ts
│   ├── normalize-material-index.ts
│   └── normalize-persona-chat.ts
├── renderers/
│   ├── TopicRenderer.tsx
│   ├── EntityBurstRenderer.tsx
│   ├── ScriptComposeRenderer.tsx
│   ├── OpeningRenderer.tsx
│   ├── PrivateMarketingRenderer.tsx
│   ├── MaterialIndexRenderer.tsx
│   ├── PersonaRenderer.tsx
│   ├── GenericRenderer.tsx      # 兜底通用渲染器
│   └── registry.ts              # 渲染注册表
├── viewer/
│   └── WorkflowViewer.tsx       # 容器：解析URL→运行→normalize→render
├── components/                  # 复用 UI 组件
│   ├── ResultCard.tsx
│   ├── SectionHeader.tsx
│   ├── UsageBadge.tsx
│   ├── MatrixTable.tsx
│   ├── ListGrid.tsx
│   ├── NodeTabs.tsx
│   ├── CopyButton.tsx
│   ├── DownloadButton.tsx
│   ├── EmptyState.tsx
│   └── ErrorState.tsx
└── hooks/
    ├── use-workflow-run.ts
    ├── use-workflow-logs.ts
    └── use-workflow-registry.ts
```

与现有：
- `WorkflowCard.tsx`：点击跳转 `/workflow/view?app=<id>`（若为聊天类仍跳 `chat.tsx`）。
- `index.tsx`（列表页）：保持；后续可在卡片上增加“查看/运行”入口。
- `chat.tsx` 与 `components/chat/*`：保持。

---

### 各工作流的 UI 设计要点（对齐《工作流接口规范》）

- 选题（topic_generation）：
  - 左侧：产品/用户标签矩阵；右侧：选题列表（复制/导出）。
- 实体连爆（entity_burst）：
  - 三列卡：openings/reasons/ctas；下方“成品文案”。
- 成文（script_compose）：
  - 分镜时间线 + 整段口播文本区域，支持“复制到剪贴板/插入下游”。
- 黄金三秒（opening）：
  - 开场语宫格 + 一键复制/插入。
- 私域营销（private_marketing）：
  - 建议朋友圈卡片流：正文/策略/图片建议/洞察。
- 素材索引（material_index）：
  - 索引摘要 + 抽取产品/标签/资产列表。
- 画像（persona）：
  - 事实项矩阵 + 总结卡片（若是聊天形态则走 Chat）。

---

### 主题与动效

- 统一使用 `sb-theme.module.css`：`glass/skeuButton/tagPill/tagCount/clayAvatar` 等。
- 动效克制：进入淡入/细微缩放；悬浮轻微反馈；统一 Usage 徽章。

---

### 渐进落地计划（建议）

1) 落地骨架：`api/runner`、`adapters/types`、`renderers/registry`、`viewer/WorkflowViewer`。

---

### 基础页面 + 插拔式渲染器（与现状对齐补充，2025-08-19）

- 基础页面：
  - ChatAgentView、WorkflowRunView、TopBar 已实现主题与动效，承载输入/状态/调试；结果区作为“渲染槽位”。
- 渲染注册表：
  - 新增 workflow/renderers/registry.ts 与 GenericRenderer.tsx；提供 pickRendererForRaw(raw) 以原始 outputs 或事件选择渲染器。
- Runner：
  - 新增 workflow/api/runner.ts（最小流式 runner）。
- 下一步：
  - 在 WorkflowRunView 结果区实际接入 pickRendererForRaw，喂入 outputs 或规范化事件；逐步为各工作流提供 Adapter+Renderer。

2) 先接 2 个高频工作流（实体连爆、成文），打通链路。
3) 覆盖其余工作流；完善通用组件（矩阵表、卡片流）。
4) 单元测试：normalize 的快照测试 + registry 解析测试。
5) 统一路由入口；完善错误与空状态。

---

### 风险与兜底

- Dify 返回字段差异/演进：通过 Schema 校验与适配器版本化解决。
- 未注册/不可识别类型：`GenericRenderer` 展示结构化 JSON 与用量，避免阻断使用。
- 流式/阻塞模式差异：Runner 统一封装；页面通过 `mode` 切换体验。

---

### TODO（工程任务清单）

- [ ] 建立 `api/runner.ts` 与 `renderers/registry.ts` 骨架（不实现业务）。
- [ ] 定义 `adapters/types.ts` 与基础 `Usage`/`NormalizedResult` 判别联合。
- [ ] 规划 2 个示例适配器与渲染器的接口形状（不写实现）。
- [ ] `WorkflowCard` 的路由约定文档化（聊天/非聊天分流）。
- [ ] 错误/空状态与 Usage 徽章的通用规范。
- [ ] 单测规划：normalize/registry 快照与类型断言用例。


