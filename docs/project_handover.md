# 9000AI 数字人创作平台交接文档

## 文档概述

本文档旨在帮助新接手9000AI平台的开发人员快速了解项目架构、技术栈和关键业务流程，实现无缝工作交接。文档覆盖前端、后端、数据库、部署和业务逻辑等多个方面。

**目录**:
- [项目概述](#项目概述)
- [开发环境设置](#开发环境设置)
- [项目结构](#项目结构)
- [前端架构](#前端架构)
- [后端架构](#后端架构)
- [数据库设计](#数据库设计)
- [关键业务流程](#关键业务流程)
- [部署流程](#部署流程)
- [常见问题与解决方案](#常见问题与解决方案)
- [待完成任务](#待完成任务)
- [联系信息](#联系信息)

## 项目概述

9000AI是一个企业级AI数字人创作平台，提供数字人合成、声音克隆和AI内容创作的一站式解决方案。平台分为**代理商平台**和**生态平台**两大核心业务模块，通过算力系统实现资源计费与控制。

### 关键特性

- **多级代理商系统**: 支持ADMIN、BRANCH、DIRECTOR、PARTNER、SALES五级代理结构
- **算力经济系统**: 以算力为核心资源单位，不同功能消耗不同算力
- **套餐订阅模式**: 支持多种套餐级别和计费周期
- **AI创作功能**: 数字人克隆、声音合成、视频生成等核心AI功能
- **支付与结算**: 集成微信支付、支付宝等支付方式，支持佣金分润

### 技术栈概览

- **前端**: Next.js 14+, React 18+, TypeScript, Tailwind CSS
- **后端**: Python FastAPI, PostgreSQL, RabbitMQ, MinIO
- **部署**: Docker, Docker Compose
- **监控**: 日志系统, 错误追踪

## 开发环境设置

### 环境要求

```bash
# 前端
node >= 18.0.0
pnpm >= 8.0.0

# 后端
Python >= 3.8
PostgreSQL >= 14.0
Docker & Docker Compose
```

### 本地开发环境搭建

1. **克隆代码库**

```bash
git clone [repository-url]
cd 9000AI
```

2. **启动开发环境**

```bash
# 复制环境变量文件
cp .env.example .env

# 编辑.env文件，配置必要的环境变量
# 特别注意配置数据库连接、MinIO凭证和支付相关信息

# 启动Docker服务
docker compose up -d postgres rabbitmq minio maildev

# 安装前端依赖
cd frontend
pnpm install

# 启动前端开发服务
pnpm dev

# 安装后端依赖
cd ../backend
python -m venv .venv
source .venv/bin/activate  # Windows: .venv\Scripts\activate
pip install -e .

# 启动后端开发服务
python -m backend.main
```

### 重要环境变量说明

开发中最关键的环境变量包括：

- `DATABASE_URL`: PostgreSQL连接字符串
- `RABBITMQ_URL`: RabbitMQ连接字符串
- `S3_*`: MinIO/S3存储配置
- `WECHAT_PAY_*`/`ALIPAY_*`: 支付配置
- `OPENAI_API_KEY`: AI服务集成密钥

详细环境变量清单请参考`.env.example`文件。

## 项目结构

9000AI采用模块化架构，前后端分离设计：

### 顶层结构

```
9000AI/
├── frontend/        # 前端应用 (Next.js)
├── backend/         # 后端服务 (FastAPI)
├── docs/            # 项目文档
├── docker/          # Docker配置文件
└── docker-compose.yml  # 开发环境容器编排
```

### 前端结构

```
frontend/
├── apps/
│   └── web/        # 主Web应用
│       └── app/    # Next.js App Router结构
│           ├── (saas)/
│           │   ├── auth/       # 认证系统
│           │   ├── agent/      # 代理商平台
│           │   └── ecosystem/  # 生态平台
│           └── api/            # API路由
├── packages/       # 共享包
│   ├── database/   # 数据库模型和迁移
│   ├── api/        # API客户端
│   ├── auth/       # 认证功能
│   └── ...         # 其他功能包
```

### 后端结构

```
backend/
├── src/
│   └── backend/
│       ├── mq/      # 消息队列相关
│       ├── storage/ # 存储相关
│       └── main.py  # 入口文件
├── tests/          # 测试代码
└── Dockerfile      # 构建文件
```

## 前端架构

### 技术栈详细说明

- **框架**: Next.js 14+ (App Router)
- **UI**: React 18+, Tailwind CSS, shadcn/ui组件
- **状态管理**: React Context + React Query
- **类型安全**: TypeScript + tRPC + Zod
- **数据库交互**: Prisma ORM
- **构建工具**: Turbo, PNPM Workspace
- **国际化**: next-intl

### 代码规范

- 使用ESLint和Prettier进行代码格式化
- 遵循Airbnb React风格指南
- 组件使用函数式组件和React Hooks

### 关键目录说明

1. **应用结构**
   - `apps/web/app/(saas)/agent/`: 代理商平台相关页面
   - `apps/web/app/(saas)/ecosystem/`: 生态平台相关页面
   - `apps/web/app/(saas)/auth/`: 认证相关页面

2. **共享包**
   - `packages/database/`: Prisma模型定义及数据库操作
   - `packages/auth/`: 认证逻辑
   - `packages/api/`: tRPC API定义
   - `packages/ai/`: AI服务集成
   - `packages/payments/`: 支付功能集成

### 路由结构

平台使用Next.js App Router，主要路由包括：

- `/auth/*`: 认证相关（登录、注册、找回密码）
- `/agent/dashboard`: 代理商仪表盘
- `/agent/consumers`: 客户管理
- `/agent/orders`: 订单管理
- `/ecosystem/avatar`: 数字人创作
- `/ecosystem/content`: 内容创作
- `/ecosystem/video`: 视频创作

### 状态管理

- 使用React Context管理全局状态
- 使用React Query管理服务器状态和数据获取
- 使用tRPC保证前后端类型安全

### 前端开发指南

1. **添加新页面**:
   ```bash
   # 在相应模块下创建新页面目录和文件
   mkdir -p apps/web/app/(saas)/agent/new-feature
   touch apps/web/app/(saas)/agent/new-feature/page.tsx
   ```

2. **添加新API路由**:
   ```typescript
   // 在packages/api/src/router/下添加路由
   export const newFeatureRouter = router({
     getData: procedure.query(async () => {
       // 实现逻辑
     }),
   });
   
   // 在主路由中注册
   export const appRouter = router({
     // 其他路由...
     newFeature: newFeatureRouter,
   });
   ```

3. **添加新数据模型**:
   - 在`packages/database/prisma/schema/`目录下修改相应的.prisma文件
   - 运行`pnpm db:generate`生成类型和客户端

## 后端架构

### 技术栈详细说明

- **框架**: Python FastAPI
- **ORM**: SQLAlchemy (直接数据库操作)
- **消息队列**: RabbitMQ + aio_pika
- **存储**: MinIO (S3兼容对象存储)
- **认证**: JWT + OAuth2
- **API文档**: OpenAPI (Swagger)

### 代码规范

- 使用Black和isort格式化代码
- 使用Ruff进行代码质量检查
- 函数和类使用类型注解

### 关键模块说明

1. **API服务**: `backend/src/backend/main.py`
   - REST API入口点
   - 路由和中间件配置

2. **消息队列**: `backend/src/backend/mq/`
   - `producer.py`: 消息生产者
   - `consumer.py`: 消息消费者
   - `worker.py`: 工作线程
   - `handlers/`: 消息处理器

3. **存储服务**: `backend/src/backend/storage/`
   - 文件上传下载
   - 对象存储集成

### API架构

后端API遵循RESTful设计原则：

- 使用HTTP方法语义 (GET, POST, PUT, DELETE)
- 资源基于URL路径
- 使用HTTP状态码表示结果
- JSON格式响应

### 后端开发指南

1. **添加新API端点**:
   ```python
   from fastapi import APIRouter, Depends

   router = APIRouter(prefix="/new-feature", tags=["new-feature"])

   @router.get("/")
   async def get_data():
       return {"message": "New feature data"}
   
   # 在main.py中注册
   app.include_router(new_feature_router)
   ```

2. **添加新消息处理器**:
   ```python
   # 在handlers目录下添加
   async def handle_new_task(message: dict):
       # 实现处理逻辑
       pass
   
   # 在consumer.py中注册
   handlers = {
       # 其他处理器...
       "new_task": handle_new_task,
   }
   ```

3. **集成外部服务**:
   - 在相应目录下创建新的客户端类
   - 使用环境变量配置凭证
   - 实现异步接口

## 数据库设计

### 数据库技术

- 主数据库: PostgreSQL 16
- ORM: Prisma (前端), SQLAlchemy (后端)
- 缓存: Redis

### 核心数据模型

1. **用户与认证**:
   - `User`: 基础用户信息
   - `Agent`: 代理商信息
   - `Consumer`: 消费者信息
   - `Session`: 会话管理

2. **代理商体系**:
   - 多级代理结构
   - 团队关系
   - 佣金计算

3. **订阅与计费**:
   - `Plan`: 套餐定义
   - `ConsumerPlan`: 用户套餐订阅
   - `ComputingPower`: 算力记录

4. **创作功能**:
   - `AvatarCloneRecord`: 数字人克隆记录
   - `VoiceCloneRecord`: 声音克隆记录
   - `AvatarVideoRecord`: 视频生成记录

5. **订单与支付**:
   - `ConsumerOrder`: 消费者订单
   - `AgentOrder`: 代理商订单
   - 支付记录与日志

### 数据库迁移

- 使用Prisma Migrate管理数据库结构变更
- 迁移命令: `pnpm db:migrate`

### 数据库扩展建议

- 对频繁访问的数据添加适当索引
- 考虑长期数据归档策略
- 对大表考虑分区策略

## 关键业务流程

### 用户注册与认证

1. 用户注册流程
   - 手机号/邮箱注册
   - 验证码验证
   - 创建User记录
   - 根据角色创建Agent/Consumer记录

2. 多角色登录流程
   - 统一登录入口
   - 角色选择页面
   - 根据角色跳转不同平台

### 代理商体系

1. 代理商创建流程
   - 上级代理发起推荐
   - 推荐信息审核
   - 配额扣减
   - 代理商账号激活

2. 分润结算流程
   - 销售产生订单
   - 计算多级分润
   - 佣金记录生成
   - 定期结算

### 订购与支付

1. 套餐购买流程
   - 选择套餐
   - 创建订单
   - 调用支付接口
   - 支付结果回调
   - 订单完成与算力发放

2. 算力消费流程
   - 功能调用请求
   - 算力检查
   - 预扣算力
   - 任务执行
   - 算力使用确认

### AI创作流程

1. 数字人克隆
   - 上传源素材
   - 创建克隆任务
   - 消息队列处理
   - 结果通知与保存

2. 内容生成
   - 提交生成请求
   - AI服务调用
   - 结果处理与优化
   - 内容保存与展示

## 部署流程

### 开发环境

使用Docker Compose启动所有服务:
```bash
docker compose up -d
```

### 测试环境

1. 构建镜像:
```bash
docker compose build
```

2. 测试部署:
```bash
docker compose -f docker-compose.yml -f docker-compose.test.yml up -d
```

### 生产环境

1. 准备环境变量:
```bash
# 复制环境变量模板
cp .env.example .env.production

# 编辑生产环境配置
# 特别注意安全相关配置
```

2. 部署:
```bash
# 使用生产配置部署
docker compose -f docker-compose.yml -f docker-compose.prod.yml --env-file .env.production up -d
```

### 数据备份

1. 数据库备份:
```bash
# 定期备份数据库
docker exec ai9000-db pg_dump -U ${POSTGRES_USER} ${POSTGRES_DB} > backup_$(date +%Y%m%d).sql
```

2. 对象存储备份:
- 使用MinIO客户端配置定期备份
- 考虑异地备份策略

## 常见问题与解决方案

### 前端问题

1. **构建失败**
   - 检查Node.js版本
   - 清理.next缓存目录: `rm -rf .next`
   - 重新安装依赖: `pnpm install`

2. **API连接错误**
   - 确认后端服务运行状态
   - 检查CORS配置
   - 验证API路由路径正确

### 后端问题

1. **数据库连接问题**
   - 验证PostgreSQL运行状态
   - 检查连接字符串格式
   - 确认网络连通性和防火墙设置

2. **消息队列阻塞**
   - 检查RabbitMQ服务状态
   - 查看Worker服务日志
   - 可能需要增加Worker实例数量

3. **存储服务问题**
   - 确认MinIO运行状态
   - 验证存储桶权限配置
   - 检查文件上传大小限制

### 支付问题

1. **支付回调失败**
   - 确认回调URL可公网访问
   - 检查支付平台配置
   - 验证签名逻辑

2. **订单状态更新异常**
   - 检查事务处理逻辑
   - 查看数据库锁状态
   - 可能需要手动补偿

## 待完成任务

1. **功能优化**
   - 优化视频生成性能
   - 改进移动端响应式设计
   - 增强数据分析报表

2. **架构改进**
   - 考虑分离更多微服务
   - 引入Kubernetes编排
   - 完善监控和警报系统

3. **安全加固**
   - 实施定期安全审计
   - 加强API权限控制
   - 增加敏感数据加密

## 联系信息

**技术支持联系人**:
- 前端负责人: [姓名] [电话] [邮箱]
- 后端负责人: [姓名] [电话] [邮箱]
- 数据库管理员: [姓名] [电话] [邮箱]
- 运维负责人: [姓名] [电话] [邮箱]

**第三方服务联系人**:
- 支付通道: [联系人] [电话] [邮箱]
- 云服务: [联系人] [电话] [邮箱]
- AI服务: [联系人] [电话] [邮箱]

---

本文档最后更新时间: [日期] 