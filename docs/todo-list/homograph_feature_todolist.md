# 多音字标记功能开发 - To-Do List

此清单用于跟踪多音字标记功能的开发进度，确保从后端到前端的完整实现。

## 阶段一：后端支持

-   [ ] **设计并实现多音字查询API**
    -   [ ] 确定API路由: `GET /api/v1/dict/homographs`
    -   [ ] 请求参数: `?word=关键词`
    -   [ ] 响应格式: `[{ pinyin: 'pin1 yin1', description: '含义描述', word: '关键词' }, ...]`
    -   [ ] 数据源：准备或集成一个多音字词典数据库。
    -   [ ] 编写单元测试，覆盖常见词、生僻词和非多音字词的情况。

-   [ ] **改造TTS生成逻辑以支持SSML**
    -   [ ] 后端接收带有 `{单词|发音}` 标记的文本。
    -   [ ] 实现一个解析器，用于从标记文本中提取多音字和其指定的发音。
    -   [ ] 将解析出的发音转换为SSML `<phoneme>` 标签格式。例如，将 `{音乐|yue4}` 转换为 `<phoneme alphabet="sapi" ph="yue4">音乐</phoneme>`。
    -   [ ] 在调用TTS大模型服务前，将处理好的SSML文本传入。
    -   [ ] 编写集成测试，确保后端能正确处理标记文本并生成正确的SSML。

## 阶段二：前端实现

-   [ ] **重构 `TextEditorTabs.tsx` 组件**
    -   [ ] 将原有的 `<Textarea>` 替换为基于 `contentEditable` 的 `div` 或引入一个轻量级的富文本编辑器库（如 `react-text-annotate`）。
    -   [ ] 新的编辑器必须支持渲染高亮/带下划线的文本，以区分已标记的多音字。

-   [ ] **开发多音字标注交互功能**
    -   [ ] 实现文本选中事件监听。
    -   [ ] 当用户选中文字时，调用后端的多音字查询API。
    -   [ ] 如果API返回结果，显示一个浮动气泡（Popover/Tooltip），列出所有可选的拼音和解释。
    -   [ ] 允许用户点击气泡中的选项来选择正确的发音。
    -   [ ] 用户选择后，在编辑器中对该词进行高亮处理。

-   [ ] **状态管理与数据流**
    -   [ ] 组件内部状态需要能存储和更新我们约定的 `{单词|发音}` 格式的文本。
    -   [ ] 实现将 `标记文本 -> HTML高亮` 的渲染逻辑。
    -   [ ] 实现将 `用户操作 -> 标记文本` 的更新逻辑。
    -   [ ] 确保标记好的文本数据流正确传递：`TextEditorTabs` -> `LeftPanel` -> `PublicVoiceForm` -> `AudioPreviewControls`。

-   [ ] **修改与删除标记**
    -   [ ] 用户再次点击已高亮的词语时，应能重新弹出选择框进行修改。
    -   [ ] 在弹出的选择框中提供一个"清除标记"的按钮。

## 阶段三：测试与部署

-   [ ] **端到端测试**
    -   [ ] 测试场景1: 用户输入普通文本，不进行任何标记，TTS正常发音。
    -   [ ] 测试场景2: 用户标记一个多音字，TTS能按照指定的拼音发音。
    -   [ ] 测试场景3: 用户标记多个多音字，TTS均能正确处理。
    -   [ ] 测试场景4: 用户修改和删除标记，功能正常。
    -   [ ] 浏览器兼容性测试 (Chrome, Firefox, Safari)。

-   [ ] **部署**
    -   [ ] 部署后端服务。
    -   [ ] 部署前端应用。
    -   [ ] 编写功能使用说明文档。 