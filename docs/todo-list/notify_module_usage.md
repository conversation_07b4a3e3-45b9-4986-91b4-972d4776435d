# 9000AI 通知模块使用文档

## 简介

9000AI通知模块提供了一个统一的接口，用于向不同的通知渠道发送消息。目前主要支持飞书机器人通知，后续可扩展到其他通知渠道。

## 配置

在使用通知模块之前，需要在环境变量中配置相关的Webhook URL：

```bash
# Feishu Bot Configuration
FEISHU_API_MONITOR_WEBHOOK="https://open.feishu.cn/open-apis/bot/v2/hook/xxx"
FEISHU_KAFKA_PRODUCER_WEBHOOK="https://open.feishu.cn/open-apis/bot/v2/hook/xxx"
FEISHU_KAFKA_CONSUMER_WEBHOOK="https://open.feishu.cn/open-apis/bot/v2/hook/xxx"
```

## 初始化

通知模块已在主应用启动时自动初始化，无需手动初始化。如果需要在其他地方初始化，可以使用以下代码：

```python
from backend.notify import initialize_notification

async def init():
    success = await initialize_notification()
    if success:
        print("通知服务初始化成功")
    else:
        print("通知服务初始化失败")
```

## 发送通知

### 基本使用

```python
from backend.notify import send_notification, NotifyType

async def send_alert():
    await send_notification(
        notify_type=NotifyType.SYSTEM_ALERT,
        title="系统警告",
        content="数据库连接失败，请检查网络配置"
    )
```

### 添加任务ID和自定义字段

```python
await send_notification(
    notify_type=NotifyType.TASK_STATUS,
    title="任务状态更新",
    content="数字人克隆任务已完成",
    task_id=123456,
    fields=[
        {"key": "任务类型", "value": "数字人克隆"},
        {"key": "处理时间", "value": "120秒"},
        {"key": "任务结果", "value": "成功"}
    ]
)
```

### 通知类型

通知模块定义了多种通知类型，每种类型对应不同的图标、颜色和机器人：

- `NotifyType.TASK_STATUS`: 任务状态通知
- `NotifyType.TASK_PROGRESS`: 任务进度通知
- `NotifyType.TASK_ERROR`: 任务错误通知
- `NotifyType.SYSTEM_ALERT`: 系统告警通知
- `NotifyType.API_ERROR`: API错误通知
- `NotifyType.MQ_MESSAGE`: 消息队列通知

### 高级使用

#### 直接使用客户端

如果需要更灵活的控制，可以直接使用通知客户端：

```python
from backend.notify import get_notification_client, MessageType, BotType

async def send_custom_message():
    client = get_notification_client()
    await client.send_message(
        bot_type=BotType.MONITOR,
        title="自定义消息",
        content="这是一条自定义消息",
        msg_type=MessageType.SUCCESS,
        fields=[{"key": "自定义字段", "value": "自定义值"}]
    )
```

#### 使用不同的提供商

如果配置了多个通知提供商，可以指定使用特定的提供商：

```python
await send_notification(
    notify_type=NotifyType.SYSTEM_ALERT,
    title="系统警告",
    content="数据库连接失败",
    provider="feishu"  # 指定使用飞书提供商
)
```

## 消息格式

飞书机器人消息使用交互式卡片格式，包含以下元素：

1. 标题：消息的主要标题
2. 内容：详细的消息内容，支持Markdown格式
3. 额外字段：以键值对形式展示的额外信息
4. 时间戳：消息发送的时间

消息颜色根据消息类型自动设置：
- 信息（INFO）：蓝色
- 成功（SUCCESS）：绿色
- 警告（WARNING）：橙色
- 错误（ERROR）：红色

## 测试

可以使用以下命令测试通知功能：

```bash
# 设置测试环境变量
export TEST_SEND_REAL_MESSAGE=true

# 运行测试
pytest backend/src/backend/notify/tests/test_notification.py -v
```

## 扩展

### 添加新的通知类型

在 `enums.py` 文件中的 `NotifyType` 枚举类中添加新的通知类型，并在 `settings.py` 中的 `NOTIFY_CONFIGS` 添加对应的配置。

### 添加新的通知提供商

1. 创建新的通知提供商客户端类
2. 在初始化时注册新的提供商

示例：

```python
from backend.notify import NotificationProviderRegistry, NotificationClient

# 创建新的客户端
new_provider = NewProviderClient(settings)

# 注册到注册表
NotificationProviderRegistry.register("new_provider", new_provider)
``` 