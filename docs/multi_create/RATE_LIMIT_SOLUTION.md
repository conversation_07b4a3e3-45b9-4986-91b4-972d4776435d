# 数字人视频合成限流方案技术文档

## 🎯 方案概述

本方案为数字人视频合成功能实现了一套完整的限流保护系统，确保对第三方API的调用严格遵守10次/分钟的QPS限制，同时保证消息不丢失和系统的高可用性。

## 🏗️ 架构设计

### 整体架构图
```
HTTP请求 → generate-video-audio.ts → RabbitMQ → 限流协调器 → 第三方API
                                        ↓
                              两个消费者并行处理：
                              - tts-driven-consumer
                              - audio-driven-consumer
                                        ↓
                              全局限流协调 (Redis令牌桶)
                                        ↓
                              video-synthesis.ts (限流包装器)
                                        ↓
                              第三方视频合成API
```

### 核心组件

1. **全局限流协调器** (`rate-limiter.ts`)
   - 基于Redis令牌桶算法
   - 全局共享限流状态
   - 支持动态重试和降级

2. **消费者限流工具** (`consumer-rate-limit-utils.ts`)
   - 消费者级别的限流检查
   - 智能延迟和重试机制
   - 与全局限流协调器集成

3. **消息保护机制** (`message-protection.ts`)
   - 消息重试记录管理
   - 指数退避算法
   - 防止消息丢失

4. **配置管理系统** (`rate-limit-config.ts`)
   - 环境变量解析和验证
   - 动态配置加载
   - 参数范围检查

5. **监控告警系统** (`rate-limit-monitoring.ts`)
   - 实时指标收集
   - 性能监控
   - 自动告警机制

## 🔧 技术实现

### 1. 限流策略选择

**选择令牌桶算法的原因：**
- ✅ **突发处理能力**：允许短时间内处理多个请求
- ✅ **平滑限流**：长期平均速率控制精确
- ✅ **实现简单**：Redis原子操作保证一致性
- ✅ **容错性好**：支持降级策略

**配置参数：**
- 桶容量：10个令牌（允许短时突发）
- 填充速率：0.167令牌/秒（10次/分钟）
- 全局Key：`video_synthesis_api_global`

### 2. 消费者改造方案

**原有流程：**
```typescript
// 旧的处理方式
for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
  try {
    vendorTaskId = await createVendor1Video(simplifiedParams);
    break;
  } catch (err) {
    // 简单重试
    await delay(RETRY_DELAY * attempt);
  }
}
```

**新的限流保护流程：**
```typescript
// 新的限流保护方式
const rateLimitedResult = await executeWithConsumerRateLimit(
  async () => {
    return await createVendor1Video(simplifiedParams);
  },
  {
    consumerId: "audio-driven-consumer",
    messageId: `msg_${taskId}_${Date.now()}`,
    taskId,
    enableRateLimitCheck: true,
    maxRetries: MAX_RETRIES,
  },
);
```

### 3. 消息重试和延迟处理

**指数退避算法：**
```typescript
const calculateRetryDelay = (retryCount: number, isRateLimited: boolean): number => {
  const baseDelay = isRateLimited 
    ? MESSAGE_RETRY_BASE_DELAY_MS * 2 
    : MESSAGE_RETRY_BASE_DELAY_MS;
  
  const exponentialDelay = baseDelay * Math.pow(2, retryCount);
  const jitter = exponentialDelay * (0.75 + Math.random() * 0.5);
  
  return Math.min(jitter, MESSAGE_RETRY_MAX_DELAY_MS);
};
```

**消息保护机制：**
- 重试记录存储在Redis中
- 支持最大重试次数限制
- 自动清理成功处理的记录
- TTL机制防止内存泄漏

### 4. 全局限流协调

**Lua脚本保证原子性：**
```lua
-- 令牌桶限流Lua脚本
local tokens_key = KEYS[1]
local timestamp_key = KEYS[2]
local current_time_ms = tonumber(ARGV[1])
local capacity = tonumber(ARGV[2])
local refill_rate_s = tonumber(ARGV[3])
local cost = tonumber(ARGV[4] or 1)

-- 计算令牌填充和消耗
-- 返回 {允许状态, 剩余令牌数}
```

## 📊 监控和告警

### 关键指标

1. **令牌桶状态**
   - 当前令牌数
   - 利用率百分比
   - 下次令牌可用时间

2. **API调用统计**
   - 总请求数
   - 允许/拒绝请求数
   - 成功率
   - 平均响应时间

3. **消费者性能**
   - 消息处理数量
   - 成功/失败率
   - 限流重试次数
   - 平均处理时间

4. **系统性能**
   - Redis延迟
   - 内存使用
   - CPU使用率

### 告警规则

- 令牌桶利用率 > 90%：警告
- Redis延迟 > 100ms：警告
- 消费者失败率 > 10%：错误
- 内存使用 > 500MB：警告

## 🔧 部署和配置

### 环境变量配置

**生产环境推荐配置：**
```bash
# 限流配置
VIDEO_SYNTHESIS_RATE_LIMIT_ENABLED=true
VIDEO_SYNTHESIS_RATE_LIMIT_CAPACITY=10
VIDEO_SYNTHESIS_RATE_LIMIT_REFILL_RATE=0.167
VIDEO_SYNTHESIS_RATE_LIMIT_MAX_RETRIES=3
VIDEO_SYNTHESIS_RATE_LIMIT_RETRY_DELAY=6000

# 消费者配置
CONSUMER_RATE_LIMIT_ENABLED=true
MQ_CONSUMER_PREFETCH_COUNT=2

# 消息保护
MESSAGE_PROTECTION_ENABLED=true
MESSAGE_MAX_RETRIES=5

# 监控
RATE_LIMIT_ENABLE_METRICS=true
RATE_LIMIT_DETAILED_LOGGING=false
```

### 部署步骤

1. **更新环境变量**
2. **重启消费者服务**
3. **验证限流功能**
4. **监控系统状态**

## 🧪 测试验证

### 测试用例覆盖

1. **基础功能测试**
   - 限流允许/拒绝逻辑
   - 令牌桶状态管理
   - 重试机制验证

2. **边界情况测试**
   - 并发请求处理
   - 极小/极大配置值
   - Redis连接异常

3. **性能测试**
   - 高并发场景
   - 长时间运行稳定性
   - 内存泄漏检查

4. **故障恢复测试**
   - Redis故障恢复
   - 消费者重启
   - 配置热更新

## 📈 性能优化

### 优化策略

1. **Redis优化**
   - 使用Lua脚本减少网络往返
   - 合理设置TTL避免内存泄漏
   - Pipeline批量操作

2. **消费者优化**
   - 合理设置prefetch数量
   - 智能延迟算法
   - 错误分类处理

3. **监控优化**
   - 异步指标收集
   - 批量数据存储
   - 智能告警去重

## 🔒 安全考虑

1. **降级策略**
   - Redis故障时允许请求通过
   - 配置验证失败使用默认值
   - 监控异常时记录详细日志

2. **数据保护**
   - 敏感信息不记录在日志中
   - Redis数据设置合理TTL
   - 错误信息脱敏处理

## 🚀 未来扩展

1. **多租户支持**
   - 按用户/租户独立限流
   - 动态配额分配
   - 优先级队列

2. **智能限流**
   - 基于历史数据的动态调整
   - 机器学习预测流量
   - 自适应限流算法

3. **分布式限流**
   - 多实例协调
   - 一致性哈希分片
   - 跨区域限流同步

## 📝 总结

本限流方案通过以下技术手段确保了系统的稳定性和可靠性：

✅ **全局协调**：Redis令牌桶算法确保全局限流一致性
✅ **消息保护**：完善的重试机制防止消息丢失
✅ **智能延迟**：指数退避算法优化重试策略
✅ **实时监控**：全面的指标收集和告警机制
✅ **配置灵活**：环境变量驱动的动态配置
✅ **测试完备**：全面的测试用例覆盖
✅ **文档详细**：完整的部署和运维文档

该方案已经过充分的设计和测试，可以直接应用于生产环境，有效解决数字人视频合成的限流需求。
