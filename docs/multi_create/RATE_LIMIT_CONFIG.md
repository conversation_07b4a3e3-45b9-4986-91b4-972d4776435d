# 数字人视频合成限流系统配置文档

## 概述

本文档描述了数字人视频合成限流系统的所有环境变量配置选项。限流系统采用Redis令牌桶算法，确保对第三方API的调用不超过QPS限制。

## 环境变量配置

### 1. 视频合成API限流配置

#### `VIDEO_SYNTHESIS_RATE_LIMIT_ENABLED`
- **描述**: 是否启用视频合成API限流
- **类型**: Boolean
- **默认值**: `true`
- **可选值**: `true`, `false`, `1`, `0`, `yes`, `no`
- **示例**: `VIDEO_SYNTHESIS_RATE_LIMIT_ENABLED=true`

#### `VIDEO_SYNTHESIS_RATE_LIMIT_CAPACITY`
- **描述**: 令牌桶容量（最大令牌数）
- **类型**: Number
- **默认值**: `10`
- **范围**: 1-100
- **示例**: `VIDEO_SYNTHESIS_RATE_LIMIT_CAPACITY=10`
- **说明**: 允许短时间内的突发请求数量

#### `VIDEO_SYNTHESIS_RATE_LIMIT_REFILL_RATE`
- **描述**: 令牌填充速率（令牌/秒）
- **类型**: Number (Float)
- **默认值**: `0.167` (10次/分钟)
- **范围**: 0.01-10
- **示例**: `VIDEO_SYNTHESIS_RATE_LIMIT_REFILL_RATE=0.167`
- **说明**: 第三方API限制10次/分钟，即0.167次/秒

#### `VIDEO_SYNTHESIS_RATE_LIMIT_MAX_RETRIES`
- **描述**: 限流重试最大次数
- **类型**: Number
- **默认值**: `3`
- **范围**: 0-10
- **示例**: `VIDEO_SYNTHESIS_RATE_LIMIT_MAX_RETRIES=3`

#### `VIDEO_SYNTHESIS_RATE_LIMIT_RETRY_DELAY`
- **描述**: 限流重试基础延迟（毫秒）
- **类型**: Number
- **默认值**: `6000` (6秒)
- **范围**: 1000-60000
- **示例**: `VIDEO_SYNTHESIS_RATE_LIMIT_RETRY_DELAY=6000`

#### `VIDEO_SYNTHESIS_RATE_LIMIT_MAX_DELAY`
- **描述**: 限流重试最大延迟（毫秒）
- **类型**: Number
- **默认值**: `60000` (1分钟)
- **范围**: 10000-600000
- **示例**: `VIDEO_SYNTHESIS_RATE_LIMIT_MAX_DELAY=60000`

### 2. 消费者限流配置

#### `CONSUMER_RATE_LIMIT_ENABLED`
- **描述**: 是否启用消费者级别的限流检查
- **类型**: Boolean
- **默认值**: `true`
- **示例**: `CONSUMER_RATE_LIMIT_ENABLED=true`

#### `CONSUMER_RATE_LIMIT_MAX_RETRIES`
- **描述**: 消费者限流重试最大次数
- **类型**: Number
- **默认值**: `5`
- **范围**: 0-20
- **示例**: `CONSUMER_RATE_LIMIT_MAX_RETRIES=5`

#### `CONSUMER_RATE_LIMIT_BASE_DELAY`
- **描述**: 消费者限流基础延迟（毫秒）
- **类型**: Number
- **默认值**: `5000` (5秒)
- **范围**: 1000-60000
- **示例**: `CONSUMER_RATE_LIMIT_BASE_DELAY=5000`

#### `CONSUMER_RATE_LIMIT_MAX_DELAY`
- **描述**: 消费者限流最大延迟（毫秒）
- **类型**: Number
- **默认值**: `30000` (30秒)
- **范围**: 5000-600000
- **示例**: `CONSUMER_RATE_LIMIT_MAX_DELAY=30000`

#### `MQ_CONSUMER_PREFETCH_COUNT`
- **描述**: 消费者预取消息数量
- **类型**: Number
- **默认值**: `2`
- **范围**: 1-50
- **示例**: `MQ_CONSUMER_PREFETCH_COUNT=2`
- **说明**: 控制每个消费者同时处理的消息数量

### 3. 消息保护配置

#### `MESSAGE_PROTECTION_ENABLED`
- **描述**: 是否启用消息保护机制
- **类型**: Boolean
- **默认值**: `true`
- **示例**: `MESSAGE_PROTECTION_ENABLED=true`

#### `MESSAGE_MAX_RETRIES`
- **描述**: 消息重试最大次数
- **类型**: Number
- **默认值**: `5`
- **范围**: 0-20
- **示例**: `MESSAGE_MAX_RETRIES=5`

#### `MESSAGE_RETRY_BASE_DELAY`
- **描述**: 消息重试基础延迟（毫秒）
- **类型**: Number
- **默认值**: `5000` (5秒)
- **范围**: 1000-60000
- **示例**: `MESSAGE_RETRY_BASE_DELAY=5000`

#### `MESSAGE_RETRY_MAX_DELAY`
- **描述**: 消息重试最大延迟（毫秒）
- **类型**: Number
- **默认值**: `300000` (5分钟)
- **范围**: 10000-1800000
- **示例**: `MESSAGE_RETRY_MAX_DELAY=300000`

#### `MESSAGE_RETRY_RECORD_TTL`
- **描述**: 消息重试记录TTL（秒）
- **类型**: Number
- **默认值**: `3600` (1小时)
- **范围**: 300-86400
- **示例**: `MESSAGE_RETRY_RECORD_TTL=3600`

### 4. 监控配置

#### `RATE_LIMIT_DETAILED_LOGGING`
- **描述**: 是否启用详细日志记录
- **类型**: Boolean
- **默认值**: `false`
- **示例**: `RATE_LIMIT_DETAILED_LOGGING=false`
- **说明**: 启用后会记录更多调试信息，建议仅在开发环境使用

#### `RATE_LIMIT_ENABLE_METRICS`
- **描述**: 是否启用性能指标收集
- **类型**: Boolean
- **默认值**: `true`
- **示例**: `RATE_LIMIT_ENABLE_METRICS=true`

#### `RATE_LIMIT_STATS_REFRESH_INTERVAL`
- **描述**: 统计信息刷新间隔（秒）
- **类型**: Number
- **默认值**: `60` (1分钟)
- **范围**: 10-3600
- **示例**: `RATE_LIMIT_STATS_REFRESH_INTERVAL=60`

### 5. 消费者特定配置

#### `MQ_AUDIO_DRIVEN_PREFETCH`
- **描述**: Audio-driven消费者预取数量
- **类型**: Number
- **默认值**: `2`
- **示例**: `MQ_AUDIO_DRIVEN_PREFETCH=2`

#### `MQ_TTS_DRIVEN_PREFETCH`
- **描述**: TTS-driven消费者预取数量
- **类型**: Number
- **默认值**: `2`
- **示例**: `MQ_TTS_DRIVEN_PREFETCH=2`

## 推荐配置

### 生产环境
```bash
# 限流配置
VIDEO_SYNTHESIS_RATE_LIMIT_ENABLED=true
VIDEO_SYNTHESIS_RATE_LIMIT_CAPACITY=10
VIDEO_SYNTHESIS_RATE_LIMIT_REFILL_RATE=0.167
VIDEO_SYNTHESIS_RATE_LIMIT_MAX_RETRIES=3
VIDEO_SYNTHESIS_RATE_LIMIT_RETRY_DELAY=6000
VIDEO_SYNTHESIS_RATE_LIMIT_MAX_DELAY=60000

# 消费者配置
CONSUMER_RATE_LIMIT_ENABLED=true
CONSUMER_RATE_LIMIT_MAX_RETRIES=5
CONSUMER_RATE_LIMIT_BASE_DELAY=5000
CONSUMER_RATE_LIMIT_MAX_DELAY=30000
MQ_CONSUMER_PREFETCH_COUNT=2

# 消息保护
MESSAGE_PROTECTION_ENABLED=true
MESSAGE_MAX_RETRIES=5
MESSAGE_RETRY_BASE_DELAY=5000
MESSAGE_RETRY_MAX_DELAY=300000
MESSAGE_RETRY_RECORD_TTL=3600

# 监控
RATE_LIMIT_DETAILED_LOGGING=false
RATE_LIMIT_ENABLE_METRICS=true
RATE_LIMIT_STATS_REFRESH_INTERVAL=60
```

### 开发环境
```bash
# 限流配置（更宽松）
VIDEO_SYNTHESIS_RATE_LIMIT_ENABLED=true
VIDEO_SYNTHESIS_RATE_LIMIT_CAPACITY=20
VIDEO_SYNTHESIS_RATE_LIMIT_REFILL_RATE=0.5
VIDEO_SYNTHESIS_RATE_LIMIT_MAX_RETRIES=2
VIDEO_SYNTHESIS_RATE_LIMIT_RETRY_DELAY=3000
VIDEO_SYNTHESIS_RATE_LIMIT_MAX_DELAY=30000

# 消费者配置
CONSUMER_RATE_LIMIT_ENABLED=true
CONSUMER_RATE_LIMIT_MAX_RETRIES=3
CONSUMER_RATE_LIMIT_BASE_DELAY=2000
CONSUMER_RATE_LIMIT_MAX_DELAY=15000
MQ_CONSUMER_PREFETCH_COUNT=1

# 消息保护
MESSAGE_PROTECTION_ENABLED=true
MESSAGE_MAX_RETRIES=3
MESSAGE_RETRY_BASE_DELAY=2000
MESSAGE_RETRY_MAX_DELAY=60000
MESSAGE_RETRY_RECORD_TTL=1800

# 监控（详细日志）
RATE_LIMIT_DETAILED_LOGGING=true
RATE_LIMIT_ENABLE_METRICS=true
RATE_LIMIT_STATS_REFRESH_INTERVAL=30
```

### 测试环境
```bash
# 禁用限流进行快速测试
VIDEO_SYNTHESIS_RATE_LIMIT_ENABLED=false
CONSUMER_RATE_LIMIT_ENABLED=false
MESSAGE_PROTECTION_ENABLED=false

# 监控
RATE_LIMIT_DETAILED_LOGGING=true
RATE_LIMIT_ENABLE_METRICS=false
```

## 配置验证

系统启动时会自动验证配置的合理性：
- 检查数值范围
- 验证逻辑关系（如基础延迟不能大于最大延迟）
- 记录配置加载日志

如果配置验证失败，系统会使用默认配置并记录警告日志。

## 动态配置

目前配置在系统启动时加载，如需修改配置：
1. 更新环境变量
2. 重启相关服务

未来可以考虑实现热更新功能。
