# 环境变量配置说明

9000AI 项目使用不同的环境变量文件来支持不同的环境配置和CI/CD流程。

## 环境变量文件

项目包含以下环境变量文件：

1. `.env` - 包含所有环境变量的主配置文件，主要用于本地开发和容器运行
2. `.env.local` - 本地开发环境特定配置，不应提交到Git仓库
3. `.env.prd` - 生产环境配置，提交到Git仓库用于CI/CD部署

## 文件使用场景

| 文件 | 使用场景 | 是否提交Git | 权重 |
|------|----------|-------------|------|
| `.env` | 默认配置，容器环境 | 是 | 低 |
| `.env.local` | 本地开发覆盖 | 否 | 高 |
| `.env.prd` | CI/CD部署 | 是 | 中 |

## GitHub Secrets 同步

可以使用项目根目录下的 `setup_secrets.ps1` 脚本，将 `.env.prd` 中的环境变量同步到GitHub Secrets：

```powershell
# 在PowerShell中运行
.\setup_secrets.ps1
```

该脚本会读取 `.env.prd` 中的每一行，并将其添加到当前Git仓库的GitHub Secrets中。

## 生产环境部署

在持续部署流程中，环境变量的来源优先级如下：

1. GitHub Secrets（最高优先级）
2. `.env.prd` 文件中的值
3. 默认值（最低优先级）

## 敏感信息处理

敏感信息（如密钥、密码等）应该：

1. 存储在 `.env.local` 中但不提交到Git
2. 不要在 `.env` 或 `.env.prd` 中存储真实的生产环境敏感信息
3. 生产环境的真实密钥应直接在GitHub Secrets中设置，或使用安全的密钥管理服务

## 环境变量注意事项

- `NEXT_PUBLIC_` 前缀的变量会在构建时嵌入到前端代码中，对用户可见
- 敏感信息永远不要使用 `NEXT_PUBLIC_` 前缀
- 生产环境中应关闭 `DEBUG=True`
- 生产环境应使用真实的第三方服务配置而非本地模拟服务
- 短信服务在生产环境应设置 `SMS_MOCK="false"` 