# GitHub Actions CI/CD 配置说明

9000AI 项目使用 GitHub Actions 进行持续集成和持续部署。以下是配置和使用说明文档。

## 工作流文件

项目包含三个主要工作流文件：

1. `.github/workflows/validate-prs.yml`：验证 PR 代码质量
2. `.github/workflows/ci.yml`：针对主分支和开发分支的持续集成
3. `.github/workflows/cd.yml`：针对 release 分支的持续部署

## 前端 CI/CD 流程

### PR 验证流程

当开发者创建 PR 到 main 分支时，会触发 `validate-prs.yml` 工作流：

1. 在 frontend 目录下执行代码检查
2. 使用 Biome 进行代码风格检查
3. 运行类型检查
4. PR 检查通过后才允许合并

### 持续集成流程

当代码推送到 main 或 develop 分支时，会触发 `ci.yml` 工作流：

1. 代码检查（包括自动格式化）
2. 构建测试
3. 端到端测试

所有测试通过后，代码被认为是可发布的。

### 持续部署流程

当代码推送到 release 分支时，会触发 `cd.yml` 工作流：

1. 代码检查
2. 构建前端应用
3. 构建并推送 Docker 镜像到腾讯云容器仓库
4. 通过 SSH 连接到服务器执行部署脚本

## 环境变量配置

CI/CD 工作流使用以下环境变量来源：

1. GitHub Secrets：敏感信息（API 密钥、密码等）
2. GitHub Variables：非敏感配置
3. 默认值：部分变量提供开发环境的默认值

### 必需的 Secrets

在 GitHub 仓库设置中需要配置以下 Secrets：

```
# 数据库
DATABASE_URL
DIRECT_URL

# 认证
BETTER_AUTH_SECRET
GITHUB_CLIENT_ID
GITHUB_CLIENT_SECRET
GOOGLE_CLIENT_ID
GOOGLE_CLIENT_SECRET

# 邮件
MAIL_HOST
MAIL_PORT
MAIL_USER
MAIL_PASS
RESEND_API_KEY

# 存储
S3_ACCESS_KEY_ID
S3_SECRET_ACCESS_KEY
S3_ENDPOINT
NEXT_PUBLIC_AVATARS_BUCKET_NAME

# AI
OPENAI_API_KEY

# 短信
SMS_API_URL
SMS_USERNAME
SMS_API_KEY
SMS_MOCK

# 部署
SERVER_HOST
SERVER_USER
SERVER_SSH_KEY
TENCENT_CLOUD_ACCOUNT_ID
TENCENT_CLOUD_TCR_PASSWORD
TENCENT_CLOUD_NAMESPACE

# 构建加速
TURBO_TEAM
TURBO_TOKEN
```

## 本地环境变量

本地开发使用 `.env.local` 和 `.env` 文件中的环境变量，这些文件不应该提交到代码仓库。

## 部署脚本

服务器上需要一个 `deploy.sh` 脚本来执行最终部署操作。该脚本应该：

1. 拉取最新的 Docker 镜像
2. 停止并移除旧的容器
3. 启动新的容器
4. 执行健康检查

示例脚本：

```bash
#!/bin/bash
# 部署脚本示例

# 获取最新镜像
docker pull ccr.ccs.tencentyun.com/9000ai/web:${TAG:-latest}

# 停止并移除旧容器
docker stop 9000ai-web || true
docker rm 9000ai-web || true

# 启动新容器
docker run -d \
  --name 9000ai-web \
  --restart unless-stopped \
  --network ai9000-network \
  -p 3000:3000 \
  ccr.ccs.tencentyun.com/9000ai/web:${TAG:-latest}

# 等待启动并检查健康状态
echo "等待服务启动..."
sleep 10
HEALTH_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/api/health)

if [ "$HEALTH_STATUS" == "200" ]; then
  echo "部署成功！服务运行正常。"
else
  echo "警告：服务可能未正确启动。状态码：$HEALTH_STATUS"
fi
```

## 注意事项

1. 确保服务器上的 Docker 已经正确配置
2. 确保服务器用户有执行 Docker 命令的权限
3. 定期检查和更新 GitHub Actions 的版本
4. 密钥和敏感信息只能存储在 GitHub Secrets 中，永远不要硬编码在代码中
5. 定期轮换密钥和凭证 