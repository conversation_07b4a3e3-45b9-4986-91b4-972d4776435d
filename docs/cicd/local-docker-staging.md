# 本地Docker预发布环境搭建指南

## 概述

本文档详细说明如何在本地服务器上使用Docker搭建预发布(Staging)环境，并通过内网穿透技术将其暴露到公网，实现与生产环境流程一致的测试和验证环境。通过本方案，可以：

1. 节省云服务器成本
2. 完美模拟生产环境
3. 保持与生产环境相同的部署流程
4. 提供团队成员可访问的测试环境

## 环境架构

```
[开发机器] -> [CI/CD系统] -> [本地Docker预发布环境] -> [内网穿透] -> [公网域名]
                                     ↑
                                 同步配置和流程
                                     ↓
                           [云服务器生产环境]
```

## 实施步骤

### 1. 本地服务器准备

#### 硬件要求

选择一台性能较好的本地服务器作为预发布环境宿主机，建议配置：

- CPU: 4核心以上
- 内存: 8GB以上
- 存储: 100GB以上SSD
- 网络: 固定内网IP，稳定的上行带宽

#### 软件安装

```bash
# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# 安装Docker Compose
sudo apt-get update
sudo apt-get install docker-compose-plugin

# 创建专用网络
docker network create staging-network
```

### 2. 容器编排配置

创建`docker-compose.staging.yml`文件：

```yaml
version: "3.8"

services:
  frontend:
    image: ${REGISTRY}/9000ai/frontend:${TAG}
    environment:
      - NODE_ENV=staging
      - API_URL=https://staging-api.9000ai.com
    restart: always
    networks:
      - staging-network
    
  backend:
    image: ${REGISTRY}/9000ai/backend:${TAG}
    environment:
      - ENV=staging
      - DB_HOST=db
    depends_on:
      - db
    networks:
      - staging-network
    volumes:
      - ./logs:/app/logs
      
  db:
    image: postgres:13
    volumes:
      - staging_db_data:/var/lib/postgresql/data
    environment:
      - POSTGRES_PASSWORD_FILE=/run/secrets/db_password
    networks:
      - staging-network
    ports:
      - "127.0.0.1:5432:5432"  # 仅本地访问
      
  nginx:
    image: nginx:latest
    volumes:
      - ./nginx/staging.conf:/etc/nginx/conf.d/default.conf
    ports:
      - "80:80"  # 给内网穿透工具使用
      - "443:443"  # 如果使用SSL
    depends_on:
      - frontend
      - backend
    networks:
      - staging-network

networks:
  staging-network:
    external: true

volumes:
  staging_db_data:
```

### 3. Nginx配置

创建`nginx/staging.conf`配置文件：

```nginx
server {
    listen 80;
    server_name staging.9000ai.com;
    
    # 如果启用HTTPS，取消下面注释
    # listen 443 ssl;
    # ssl_certificate /etc/nginx/certs/staging.9000ai.com.crt;
    # ssl_certificate_key /etc/nginx/certs/staging.9000ai.com.key;
    
    # 可选：添加访问保护
    # auth_basic "Staging Environment";
    # auth_basic_user_file /etc/nginx/.htpasswd;
    
    location / {
        proxy_pass http://frontend:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /api {
        proxy_pass http://backend:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 4. 内网穿透配置

我们使用frp作为内网穿透工具：

#### 4.1 在有公网IP的服务器上安装frps(服务端)

创建`frps.ini`文件：

```ini
[common]
bind_port = 7000
token = your_secure_token
dashboard_port = 7500
dashboard_user = admin
dashboard_pwd = secure_password
```

启动frps：

```bash
./frps -c frps.ini
```

#### 4.2 在本地服务器上安装frpc(客户端)

创建`frpc.ini`文件：

```ini
[common]
server_addr = your_server_ip
server_port = 7000
token = your_secure_token

[staging-web]
type = http
local_ip = 127.0.0.1
local_port = 80
custom_domains = staging.9000ai.com

# 如果使用HTTPS，添加以下配置
[staging-web-https]
type = https
local_ip = 127.0.0.1
local_port = 443
custom_domains = staging.9000ai.com
```

启动frpc并设置为系统服务：

```bash
# 启动frpc
./frpc -c frpc.ini

# 创建systemd服务
cat > /etc/systemd/system/frpc.service << EOF
[Unit]
Description=Frp Client Service
After=network.target

[Service]
Type=simple
ExecStart=/usr/local/bin/frpc -c /etc/frp/frpc.ini
Restart=on-failure

[Install]
WantedBy=multi-user.target
EOF

# 启用并启动服务
sudo systemctl enable frpc
sudo systemctl start frpc
```

### 5. 域名配置

1. 在你的域名提供商控制面板中添加以下DNS记录：
   ```
   staging.9000ai.com -> 你的frps服务器公网IP
   ```

2. 如果需要HTTPS，配置SSL证书：
   ```bash
   # 使用Let's Encrypt获取免费证书
   sudo certbot --nginx -d staging.9000ai.com
   
   # 或手动配置证书
   mkdir -p /etc/nginx/certs
   # 将证书文件放置到适当位置后修改nginx配置
   ```

### 6. CI/CD配置

修改GitHub Actions工作流，创建`staging-deploy.yml`：

```yaml
name: Deploy to Staging

on:
  push:
    branches: [release/*]
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Login to Registry
        uses: docker/login-action@v2
        with:
          registry: ${{ secrets.REGISTRY }}
          username: ${{ secrets.REGISTRY_USERNAME }}
          password: ${{ secrets.REGISTRY_PASSWORD }}
      - name: Build and push
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          tags: ${{ secrets.REGISTRY }}/9000ai/app:staging-${{ github.sha }}
          
  deploy:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to staging server
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.STAGING_HOST }}
          username: ${{ secrets.STAGING_USERNAME }}
          key: ${{ secrets.STAGING_SSH_KEY }}
          script: |
            cd /opt/staging
            export REGISTRY=${{ secrets.REGISTRY }}
            export TAG=staging-${{ github.sha }}
            docker-compose -f docker-compose.staging.yml pull
            docker-compose -f docker-compose.staging.yml up -d
            docker system prune -f
```

在GitHub项目设置中添加以下Secrets：

- `STAGING_HOST`: 本地服务器的IP地址
- `STAGING_USERNAME`: SSH用户名
- `STAGING_SSH_KEY`: SSH私钥
- `REGISTRY`: 容器仓库地址
- `REGISTRY_USERNAME`: 容器仓库用户名
- `REGISTRY_PASSWORD`: 容器仓库密码

### 7. 数据管理

#### 7.1 从生产环境同步数据（并脱敏）

创建`sync-data.sh`脚本：

```bash
#!/bin/bash
# 从生产环境同步并脱敏数据

# 停止预发布环境
cd /opt/staging
docker-compose -f docker-compose.staging.yml down

# 从生产环境获取数据库备份
ssh $PROD_SERVER "pg_dump -U postgres -d 9000ai > /tmp/prod_dump.sql"
scp $PROD_SERVER:/tmp/prod_dump.sql /tmp/
ssh $PROD_SERVER "rm /tmp/prod_dump.sql"

# 脱敏处理
cat /tmp/prod_dump.sql | python3 /opt/staging/scripts/anonymize_data.py > /tmp/anonymized_dump.sql

# 导入脱敏后的数据
docker run --rm -v /tmp/anonymized_dump.sql:/dump.sql --network staging-network postgres:13 \
  psql -h db -U postgres -d 9000ai -f /dump.sql

# 重启预发布环境
docker-compose -f docker-compose.staging.yml up -d

# 清理临时文件
rm /tmp/prod_dump.sql /tmp/anonymized_dump.sql
```

#### 7.2 创建脱敏脚本

创建`anonymize_data.py`脚本：

```python
#!/usr/bin/env python3
# 数据脱敏脚本
import sys
import re
import random
import hashlib

def anonymize_email(email):
    """匿名化邮箱地址"""
    username, domain = email.split('@')
    hashed = hashlib.md5(username.encode()).hexdigest()[:8]
    return f"user_{hashed}@example.com"

def anonymize_phone(phone):
    """匿名化手机号码"""
    # 保留前两位，其余随机化
    if len(phone) == 11 and phone.startswith('1'):
        prefix = phone[:2]
        return prefix + ''.join([str(random.randint(0, 9)) for _ in range(9)])
    return phone

# 主处理逻辑
for line in sys.stdin:
    # 替换邮箱
    line = re.sub(r'([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})', 
                 lambda m: anonymize_email(m.group(1)), line)
    
    # 替换手机号
    line = re.sub(r'1[3-9]\d{9}', 
                 lambda m: anonymize_phone(m.group(0)), line)
    
    # 替换姓名(假设有name字段)
    line = re.sub(r'("name"\s*:\s*)"([^"]+)"', 
                 r'\1"用户_' + str(random.randint(1000, 9999)) + '"', line)
    
    sys.stdout.write(line)
```

### 8. 监控与日志

添加与生产环境相同的监控工具：

```yaml
# 添加到docker-compose.staging.yml
services:
  # ...现有服务...
  
  prometheus:
    image: prom/prometheus:latest
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    networks:
      - staging-network
      
  grafana:
    image: grafana/grafana:latest
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - staging-network
    ports:
      - "127.0.0.1:3000:3000"  # 本地访问

volumes:
  # ...现有卷...
  grafana_data:
```

### 9. 自动化维护

创建维护脚本`maintenance.sh`：

```bash
#!/bin/bash
# 维护脚本，定期清理资源并检查系统状态

# 清理未使用的Docker资源
docker system prune -af --volumes

# 备份数据库
BACKUP_FILE="/opt/backups/staging_db_$(date +%Y%m%d).sql"
docker exec -t staging_db_1 pg_dump -U postgres 9000ai > $BACKUP_FILE
gzip $BACKUP_FILE

# 仅保留最近7天的备份
find /opt/backups -name "staging_db_*.sql.gz" -type f -mtime +7 -delete

# 检查服务健康状态
echo "检查服务健康状态..."
curl -s -o /dev/null -w "%{http_code}" http://localhost/api/health
```

设置定期执行：

```bash
# 编辑crontab
crontab -e

# 添加以下内容
# 每天凌晨2:30执行清理
30 2 * * * /opt/staging/scripts/maintenance.sh >> /var/log/staging-maintenance.log 2>&1

# 每周日凌晨3:00同步数据
0 3 * * 0 /opt/staging/scripts/sync-data.sh >> /var/log/staging-sync.log 2>&1
```

## 故障排除

### 常见问题与解决方案

1. **内网穿透连接不稳定**
   - 检查frpc客户端日志
   - 确认服务器防火墙设置
   - 考虑使用备用穿透工具如ngrok或Cloudflare Tunnel

2. **容器服务无法启动**
   - 检查docker-compose日志
   - 确认磁盘空间充足
   - 验证环境变量配置正确

3. **数据同步失败**
   - 检查产生环境连接权限
   - 确认脱敏脚本工作正常
   - 检查目标数据库可用空间

4. **性能问题**
   - 调整容器资源限制
   - 监控主机资源使用情况
   - 考虑为数据库使用专用卷

## 最佳实践

1. **安全性**
   - 为预发布环境添加访问控制
   - 不使用真实的生产环境密钥
   - 定期更新所有组件和依赖

2. **资源管理**
   - 定期清理未使用的Docker镜像和容器
   - 监控磁盘使用情况
   - 为数据库和日志设置单独的卷

3. **团队协作**
   - 清晰记录预发布环境的访问信息
   - 建立明确的准入控制
   - 记录所有环境变更

4. **部署策略**
   - 使用Git标签和语义化版本号
   - 实现自动和手动部署双模式
   - 建立应急回滚机制

## 总结

通过在本地Docker环境上搭建预发布环境并使用内网穿透技术，我们能够创建一个与生产环境流程完全一致的测试平台，同时节省云服务器成本。这种方案特别适合中小型项目团队，既能保证代码质量和部署流程的一致性，又能灵活控制资源使用。

随着项目规模扩大，这套配置也可以无缝迁移到云服务器上，不需要改变CI/CD流程，确保长期可维护性。 