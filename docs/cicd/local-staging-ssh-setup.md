# 设置本地服务器接收GitHub Actions远程部署

本文档详细说明如何配置本地服务器，使其能够接收来自GitHub Actions的远程部署指令，实现预发布环境的自动化部署。

## 概述

GitHub Actions运行在GitHub的云服务器上，无法直接访问你的本地网络。要让GitHub Actions能够部署到本地服务器，我们需要：

1. 使本地服务器的SSH服务可从公网访问
2. 设置适当的SSH密钥认证
3. 在GitHub仓库中配置必要的Secrets

## Windows环境配置

### 1. 安装OpenSSH服务器

Windows 10/11内置了OpenSSH客户端，但需要手动安装OpenSSH服务器：

1. 以管理员身份打开PowerShell
2. 安装OpenSSH服务器：
   ```powershell
   # 检查OpenSSH是否可用
   Get-WindowsCapability -Online | Where-Object Name -like 'OpenSSH*'
   
   # 安装OpenSSH服务器
   Add-WindowsCapability -Online -Name OpenSSH.Server~~~~0.0.1.0
   ```

3. 启动并设置OpenSSH服务自动启动：
   ```powershell
   # 启动SSH服务
   Start-Service sshd
   
   # 设置服务自动启动
   Set-Service -Name sshd -StartupType 'Automatic'
   
   # 确认防火墙规则已添加（应该在安装时自动添加）
   Get-NetFirewallRule -Name *ssh*
   ```

### 2. 创建专用部署用户

在Windows上创建专用于部署的用户：

1. 打开"设置" -> "账户" -> "家庭和其他用户"
2. 点击"添加其他用户"，创建一个名为"deploy"的本地账户
3. 将用户添加到管理员组（或者Docker用户组，如果已创建）：
   ```powershell
   Add-LocalGroupMember -Group "Administrators" -Member "deploy"
   ```

4. 创建项目目录：
   ```powershell
   # 创建一个更符合Windows习惯的目录
   mkdir C:\9000AI\Staging
   # 设置权限
   icacls "C:\9000AI\Staging" /grant "deploy:(OI)(CI)F"
   ```

### 3. 设置SSH密钥认证

1. 在任意计算机上生成SSH密钥对：
   ```powershell
   # 在PowerShell中生成
   ssh-keygen -t ed25519 -C "github-actions-deploy"
   # 保存到文件如 C:\Users\<USER>\.ssh\github_actions_deploy
   ```

2. 配置SSH密钥认证：
   ```powershell
   # 以管理员身份运行PowerShell
   
   # 确保deploy用户的.ssh目录存在
   $deployUserHome = "C:\Users\<USER>\.ssh"
   if(!(Test-Path $sshDir)) {
       New-Item -Path $sshDir -ItemType Directory
   }
   
   # 创建或附加到authorized_keys文件
   $pubKeyContent = "YOUR_PUBLIC_KEY_CONTENT"
   Add-Content -Path "$sshDir\authorized_keys" -Value $pubKeyContent
   
   # 设置适当的权限
   icacls "$sshDir\authorized_keys" /inheritance:r
   icacls "$sshDir\authorized_keys" /grant "deploy:(R)"
   ```

3. 配置SSH服务器设置：
   ```powershell
   # 编辑SSH配置文件
   notepad C:\ProgramData\ssh\sshd_config
   ```

   在文件中修改或添加以下设置：
   ```
   PubkeyAuthentication yes
   PasswordAuthentication no
   PermitRootLogin no
   ```

   重启SSH服务：
   ```powershell
   Restart-Service sshd
   ```

### 4. 公网访问设置（Windows）

#### 方法1: 路由器端口转发（适用于Windows）

1. 登录到路由器管理界面
2. 找到"端口转发"或"虚拟服务器"设置
3. 添加新规则：
   - 外部端口: 选择一个非标准端口（如8022，避免使用默认22端口增加安全性）
   - 内部IP: Windows服务器的内网IP
   - 内部端口: SSH服务端口（通常为22）
4. 保存设置

#### 方法2: 使用frp进行内网穿透（Windows版）

在有公网IP的服务器上安装frps（与Linux步骤相同）。

在Windows本地服务器上安装frpc：

1. 下载Windows版frp：
   - 访问 https://github.com/fatedier/frp/releases
   - 下载适用于Windows的版本（如frp_x.xx.x_windows_amd64.zip）

2. 解压到合适的目录，如`C:\frp`

3. 创建`C:\frp\frpc.ini`文件：
   ```ini
   [common]
   server_addr = YOUR_FRPS_SERVER_IP
   server_port = 7000
   token = your_secure_token

   [ssh]
   type = tcp
   local_ip = 127.0.0.1
   local_port = 22
   remote_port = 6000
   ```

4. 将frpc设置为系统服务（选择以下任一方法）:

   #### 方法A: 使用Windows原生服务命令(sc)
   ```powershell
   # 使用Windows内置sc命令创建服务
   sc.exe create "FRPClient" binPath= "C:\frp\frpc.exe -c C:\frp\frpc.ini" start= auto displayname= "FRP Client Service"
   sc.exe description "FRPClient" "Frp client for intranet penetration"
   sc.exe start "FRPClient"
   ```

   #### 方法B: 使用WinSW (Windows Service Wrapper)
   ```powershell
   # 下载最新版WinSW
   Invoke-WebRequest -Uri "https://github.com/winsw/winsw/releases/download/v2.11.0/WinSW-x64.exe" -OutFile "C:\frp\winsw.exe"
   
   # 创建服务配置文件
   @"
<service>
  <id>frpc</id>
  <name>FRP Client Service</name>
  <description>Frp client for intranet penetration</description>
  <executable>C:\frp\frpc.exe</executable>
  <arguments>-c C:\frp\frpc.ini</arguments>
  <logmode>rotate</logmode>
</service>
"@ | Out-File -FilePath C:\frp\frpc.xml -Encoding utf8
   
   # 安装并启动服务
   cd C:\frp
   .\winsw.exe install frpc.xml
   .\winsw.exe start frpc
   ```
   重启服务：Restart-Service frpc
   停止服务：Stop-Service frpc
   卸载服务：cd C:\frp && .\winsw.exe uninstall frpc.xml

### 5. 配置Docker Desktop for Windows

1. 下载并安装Docker Desktop: https://www.docker.com/products/docker-desktop
2. 在系统托盘图标右键点击Docker Desktop -> 设置
3. 确保启用了"Expose daemon on tcp://localhost:2375 without TLS"选项（用于脚本操作）
4. 配置部署用户有权访问Docker：
   - 确保deploy用户是管理员组成员
   - 或者使用Docker Context进行配置

### 6. 配置GitHub仓库Secrets

在GitHub仓库设置中添加以下Secrets（步骤与Linux环境相同）:

1. 导航到你的GitHub仓库
2. 点击 "Settings" > "Secrets and variables" > "Actions"
3. 添加以下Secrets:
   - `STAGING_HOST`: 公网IP或域名（如果使用端口转发或frp，需要包含端口，例如: `example.com:8022`）
   - `STAGING_USERNAME`: 部署用户名（例如: `deploy`）
   - `STAGING_SSH_KEY`: 私钥内容（整个私钥文件内容，包括开头和结尾行）

### 7. 修改GitHub Actions工作流

为Windows环境调整部署脚本：

```yaml
deploy:
  needs: build
  runs-on: ubuntu-latest
  steps:
    - name: Deploy to Windows staging server
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.STAGING_HOST }}
        username: ${{ secrets.STAGING_USERNAME }}
        key: ${{ secrets.STAGING_SSH_KEY }}
        script: |
          cd C:/opt/staging
          set REGISTRY=${{ secrets.REGISTRY }}
          set TAG=staging-${{ github.sha }}
          docker-compose -f docker-compose.staging.yml pull
          docker-compose -f docker-compose.staging.yml up -d
          docker system prune -f
```

## Linux环境配置

### 1. 配置本地服务器SSH服务

确保本地服务器已安装并启用SSH服务：

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install openssh-server
sudo systemctl enable ssh
sudo systemctl start ssh

# CentOS/RHEL
sudo yum install openssh-server
sudo systemctl enable sshd
sudo systemctl start sshd
```

### 2. 创建专用部署用户

创建一个专用于部署的用户，提高安全性：

```bash
# 创建deploy用户
sudo useradd -m -s /bin/bash deploy

# 设置权限
sudo usermod -aG docker deploy  # 允许执行docker命令
sudo mkdir -p /opt/staging
sudo chown deploy:deploy /opt/staging
```

### 3. 设置SSH密钥认证

在本地计算机上生成SSH密钥对：

```bash
# 生成新的SSH密钥对
ssh-keygen -t ed25519 -C "github-actions-deploy"
# 保存到文件 ~/.ssh/github_actions_deploy 时不设置密码
```

将公钥添加到本地服务器的authorized_keys文件：

```bash
# 在服务器上创建.ssh目录
sudo mkdir -p /home/<USER>/.ssh
sudo chmod 700 /home/<USER>/.ssh

# 将公钥内容添加到authorized_keys
echo "YOUR_PUBLIC_KEY_CONTENT" | sudo tee -a /home/<USER>/.ssh/authorized_keys
sudo chmod 600 /home/<USER>/.ssh/authorized_keys
sudo chown -R deploy:deploy /home/<USER>/.ssh
```

### 4. 公网访问设置

选择以下三种方法之一使本地服务器SSH可从公网访问：

#### 方法1: 路由器端口转发

1. 登录到路由器管理界面
2. 找到"端口转发"或"虚拟服务器"设置
3. 添加新规则：
   - 外部端口: 选择一个非标准端口（如8022，避免使用默认22端口增加安全性）
   - 内部IP: 本地服务器的内网IP
   - 内部端口: SSH服务端口（通常为22）
4. 保存设置

#### 方法2: 使用frp进行内网穿透

在有公网IP的服务器上安装frps：

```bash
# 下载frp
wget https://github.com/fatedier/frp/releases/download/v0.51.3/frp_0.51.3_linux_amd64.tar.gz
tar -zxvf frp_0.51.3_linux_amd64.tar.gz
cd frp_0.51.3_linux_amd64

# 配置frps.ini
cat > frps.ini << EOF
[common]
bind_port = 7000
token = your_secure_token
EOF

# 启动frps
./frps -c frps.ini
```

在本地服务器上安装frpc：

```bash
# 下载frp
wget https://github.com/fatedier/frp/releases/download/v0.51.3/frp_0.51.3_linux_amd64.tar.gz
tar -zxvf frp_0.51.3_linux_amd64.tar.gz
cd frp_0.51.3_linux_amd64

# 配置frpc.ini
cat > frpc.ini << EOF
[common]
server_addr = YOUR_FRPS_SERVER_IP
server_port = 7000
token = your_secure_token

[ssh]
type = tcp
local_ip = 127.0.0.1
local_port = 22
remote_port = 6000
EOF

# 启动frpc并设为系统服务
./frpc -c frpc.ini

# 创建systemd服务
sudo tee /etc/systemd/system/frpc.service > /dev/null << EOF
[Unit]
Description=Frp Client Service
After=network.target

[Service]
Type=simple
User=nobody
Restart=on-failure
RestartSec=5s
ExecStart=/path/to/frpc -c /path/to/frpc.ini
LimitNOFILE=1048576

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl enable frpc
sudo systemctl start frpc
```

#### 方法3: 建立反向SSH隧道

在有公网IP的服务器上:

1. 确保允许SSH隧道连接:
   ```bash
   sudo nano /etc/ssh/sshd_config
   # 添加或修改
   GatewayPorts yes
   ```

2. 重启SSH服务:
   ```bash
   sudo systemctl restart sshd
   ```

在本地服务器上创建反向隧道脚本:

```bash
#!/bin/bash
# 创建反向SSH隧道

# 配置变量
PUBLIC_SERVER=<EMAIL>
REMOTE_PORT=8022  # 公网服务器上的端口
LOCAL_PORT=22     # 本地SSH端口

# 建立隧道
ssh -N -R $REMOTE_PORT:localhost:$LOCAL_PORT $PUBLIC_SERVER

# 自动重连
while true; do
    ssh -N -R $REMOTE_PORT:localhost:$LOCAL_PORT $PUBLIC_SERVER
    sleep 10
done
```

设置为系统服务:

```bash
sudo tee /etc/systemd/system/reverse-ssh.service > /dev/null << EOF
[Unit]
Description=Reverse SSH Tunnel
After=network.target

[Service]
User=deploy
ExecStart=/bin/bash /opt/scripts/reverse-ssh.sh
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl enable reverse-ssh
sudo systemctl start reverse-ssh
```

### 5. 配置GitHub仓库Secrets

在GitHub仓库设置中添加以下Secrets:

1. 导航到你的GitHub仓库
2. 点击 "Settings" > "Secrets and variables" > "Actions"
3. 添加以下Secrets:
   - `STAGING_HOST`: 公网IP或域名（如果使用端口转发或frp，需要包含端口，例如: `example.com:8022`）
   - `STAGING_USERNAME`: 部署用户名（例如: `deploy`）
   - `STAGING_SSH_KEY`: 私钥内容（整个私钥文件内容，包括开头和结尾行）

### 6. 测试连接

在完成上述设置后，可以手动测试连接是否正常:

```bash
# 从外部网络测试SSH连接
ssh deploy@your-public-ip-or-domain -p port

# 如果连接成功，应该可以登录到本地服务器
```

### 7. 安全性注意事项

1. **限制SSH访问**:
   ```bash
   sudo nano /etc/ssh/sshd_config
   
   # 添加或修改以下设置
   PermitRootLogin no
   PasswordAuthentication no
   AllowUsers deploy
   ```

2. **配置防火墙**:
   ```bash
   # Ubuntu/Debian
   sudo ufw allow from your_public_server_ip to any port 22
   
   # CentOS/RHEL
   sudo firewall-cmd --permanent --add-rich-rule='rule family="ipv4" source address="your_public_server_ip" port port=22 protocol=tcp accept'
   sudo firewall-cmd --reload
   ```

3. **监控登录尝试**:
   ```bash
   # 安装fail2ban以防止暴力破解
   sudo apt install fail2ban
   sudo systemctl enable fail2ban
   sudo systemctl start fail2ban
   ```

### 8. 故障排除

1. **SSH连接被拒绝**:
   - 检查SSH服务是否运行: `systemctl status sshd`
   - 检查防火墙设置: `sudo ufw status` 或 `sudo firewall-cmd --list-all`
   - 查看SSH日志: `sudo tail -f /var/log/auth.log` 或 `sudo tail -f /var/log/secure`

2. **GitHub Actions无法连接**:
   - 验证公网IP或域名是否正确
   - 确认SSH密钥格式正确（包括首尾行）
   - 检查本地服务器是否可从公网访问（使用在线SSH客户端测试）

3. **权限问题**:
   - 确认deploy用户有权访问/opt/staging目录
   - 检查deploy用户是否在docker组中: `groups deploy`
   - 确保脚本有执行权限: `chmod +x /path/to/script.sh`

## Windows环境故障排除

1. **SSH连接被拒绝**:
   - 检查SSH服务是否运行: `Get-Service sshd`
   - 检查Windows防火墙设置
   - 查看SSH日志: `Get-Content C:\ProgramData\ssh\logs\*`

2. **GitHub Actions无法连接**:
   - 检查公网IP和端口是否正确
   - 确认SSH密钥格式正确
   - 使用另一台电脑从外部测试SSH连接

3. **Docker权限问题**:
   - 确认deploy用户是管理员或Docker用户组成员
   - 检查Docker Desktop设置
   - 尝试使用完整路径执行Docker命令

4. **挂载卷和路径问题**:
   - Windows路径使用反斜杠，Docker使用正斜杠，注意转换
   - 使用绝对路径而不是相对路径
   - 注意Docker Desktop的路径映射设置

## 结论

完成上述配置后，GitHub Actions将能够通过SSH连接到你的本地服务器（无论是Windows还是Linux），执行部署脚本。这样就实现了全自动化的CI/CD流程，将代码从GitHub仓库部署到本地预发布环境。

该设置既保持了与生产环境相同的部署流程，又节省了云服务器成本，同时提供了更灵活的控制能力。 