# 9000AI数字人平台 - 交接文档索引

## 文档概述

这里是9000AI数字人创作平台的完整交接文档集合。为了方便接手团队快速理解系统架构和开发流程，我们将文档分为多个模块，每个模块专注于一个特定领域。

**所有文档按照从宏观到微观、从整体到细节的顺序组织。新人请先阅读项目总交接文档，再根据自己的职责阅读相应的专项文档。**

## 主要文档

1. [**项目总交接文档**](./project_handover.md)
   - 项目概述、业务流程和架构总览
   - 适合所有团队成员阅读，提供系统的全貌
   - 包含技术栈、项目结构、关键业务流程介绍

2. [**前端开发指南**](./frontend_development_guide.md)
   - 前端架构、组件设计和状态管理
   - 详细介绍Next.js、React开发流程
   - 代码规范、路由结构、API调用方式等

3. [**后端开发指南**](./backend_development_guide.md)
   - 后端架构、API设计原则和实现方法
   - FastAPI应用结构和设计模式
   - 消息队列集成、存储服务和测试策略

4. [**数据库设计文档**](./database_design.md)
   - 数据库架构和核心数据模型
   - 表结构、关系和索引设计
   - 迁移策略和性能优化建议

5. [**部署指南**](./deployment_guide.md)
   - 各环境部署流程和配置方法
   - Docker和Docker Compose的使用
   - 监控、备份和故障排查指南
   - 安全配置和性能优化策略

6. [**API参考文档**](./api_reference.md)
   - 详细的API接口说明
   - 请求/响应格式和参数解释
   - 认证方式和错误处理机制
   - 按功能模块组织的接口列表

7. [**测试指南**](./testing_guide.md)
   - 测试策略和方法论
   - 前端和后端测试框架与工具
   - 单元测试、集成测试和端到端测试示例
   - 性能测试和安全测试最佳实践

8. [**Ngrok内网穿透集成指南**](./ngrok_integration_guide.md)
   - Ngrok配置和使用方法
   - 固定域名申请和配置步骤
   - 内网穿透故障排查
   - 开发和测试环境的公网访问设置

## 如何使用这些文档

### 新团队成员

1. 首先阅读[项目总交接文档](./project_handover.md)获取项目总览
2. 根据自己的角色选择相应的专项文档深入了解:
   - 前端开发人员: [前端开发指南](./frontend_development_guide.md)
   - 后端开发人员: [后端开发指南](./backend_development_guide.md) + [API参考文档](./api_reference.md)
   - 数据库工程师: [数据库设计文档](./database_design.md)
   - 运维工程师: [部署指南](./deployment_guide.md)
   - 接口联调人员: [API参考文档](./api_reference.md)
   - 测试工程师: [测试指南](./testing_guide.md)

### 特定场景指引

- **需要添加新功能?** 
  1. 阅读项目总交接文档了解业务流程
  2. 查看相应的前端/后端开发指南
  3. 参考已有类似功能的实现方式

- **系统部署或升级?**
  1. 直接查阅[部署指南](./deployment_guide.md)
  2. 按照环境选择相应的部署步骤
  3. 遇到问题参考故障排查章节

- **数据库变更?**
  1. 查看[数据库设计文档](./database_design.md)
  2. 了解现有数据模型和关系
  3. 按照迁移策略执行变更

- **接口集成或联调?**
  1. 直接查阅[API参考文档](./api_reference.md)
  2. 了解认证方式和请求/响应格式
  3. 按照模块查找需要的具体接口

- **需要编写测试?**
  1. 查阅[测试指南](./testing_guide.md)
  2. 了解对应模块的测试策略和工具
  3. 参考示例编写相应的测试用例

## 文档维护

这些文档应该保持最新，每当系统架构或关键业务流程发生变化时，相关团队成员应当更新对应的文档。请在每次更新后在文档底部更新"最后更新时间"。

---

*最后文档更新: 2025年3月18日* 