# 9000AI 数据库设计文档

## 简介

本文档详细说明9000AI平台的数据库设计，包括架构、关键模型、表结构和关系。平台使用PostgreSQL 16作为主数据库，Prisma作为前端ORM，SQLAlchemy作为后端数据库交互层。

## 数据库架构

### 技术栈概览

- **数据库引擎**: PostgreSQL 16
- **前端ORM**: Prisma 6.x
- **后端ORM/查询构建器**: SQLAlchemy 2.0
- **缓存**: Redis (热点数据)
- **数据库迁移**: Prisma Migrate

### 数据库设计原则

1. **关系完整性**: 使用外键约束确保数据完整性
2. **规范化**: 遵循第三范式(3NF)设计，减少数据冗余
3. **性能优化**: 添加适当索引，优化查询
4. **安全性**: 敏感数据加密存储，权限控制
5. **可扩展性**: 表设计考虑未来扩展性，避免硬编码限制

## 核心数据模型

### 用户认证体系

数据库中的用户认证体系分为几个核心表:

1. **User**: 基础用户表，存储所有用户共有的信息
2. **Agent**: 代理商信息表，与User一对一关联
3. **Consumer**: 消费者信息表，与User一对一关联
4. **Session**: 用户会话表
5. **Account**: 外部账号关联表
6. **Passkey**: 密钥表

#### 用户表(User)

用户表是整个认证系统的核心，存储所有类型用户的共有信息。

```prisma
// 基础用户表 - 只保留核心认证字段
model User {
    id                  String        @id
    name                String        // 用户名称
    phoneCountryCode    String        @default("+86") // 手机号国际区号
    phoneNumber         String        // 手机号(必填)
    phoneNumberFull     String        @unique // 完整手机号(区号+号码)
    phoneNumberVerified Boolean       @default(false)
    email               String        @unique
    emailVerified       Boolean       @default(false)
    backupContact       String?       // 备用联系方式(微信/QQ等)
    
    // 实名认证相关字段
    realName           String?       // 真实姓名
    idCardNumber       String?       // 身份证号码
    idCardVerified     Boolean       @default(false)  // 实名认证状态
    verifiedAt         DateTime?     // 认证通过时间
    
    // 算力账户
    computingPower      Int          @default(0) // 算力值(1小时=6000算力)
    totalComputingPower Int          @default(0) // 历史总算力值
    computingPowerLogs  ComputingPowerLog[] // 算力值变更记录
    computingPowerUsages ComputingPowerUsage[] // 算力使用记录
    
    // 状态控制
    status             AccountStatus  @default(ACTIVE)
    banned             Boolean?       // 是否封禁
    banReason          String?        // 封禁原因
    banExpires         DateTime?      // 封禁过期时间
    
    // 登录相关
    lastLoginAt        DateTime?      
    lastLoginIp        String?        
    loginAttempts      Int           @default(0)
    lockedUntil        DateTime?      
    
    // 系统信息
    locale             String?        
    createdAt          DateTime      @default(now())
    updatedAt          DateTime      @updatedAt
    deletedAt          DateTime?
    remark             String?
    
    // 认证相关关联
    sessions           Session[]
    accounts           Account[]
    passkeys           Passkey[]
    
    // 身份关联
    agent              Agent?
    consumer           Consumer?
    
    // API相关
    apiKeys            ApiKey[]
    
    // 推荐记录关联
    referralRecords    ReferralRecord[] @relation("referralUser")

    @@map("user")
}
```

#### 代理商表(Agent)

代理商表存储与代理商相关的特定信息，实现多级代理结构。

```prisma
// 代理商表 - 专门处理代理商特有的业务字段
model Agent {
    id              String      @id @default(cuid())
    userId          String      @unique  // 关联到用户账号
    user            User        @relation(fields: [userId], references: [id])
    role            AgentRole   @default(SALES)
    
    // 层级关系
    parentId        String?     // 上级ID
    parent          Agent?      @relation("AgentHierarchy", fields: [parentId], references: [id])
    children        Agent[]     @relation("AgentHierarchy") // 下级代理
    
    // 团队配置
    teamCode        String?     @unique // 团队编码(唯一)
    teamName        String?     // 团队名称
    teamLogo        String?     // 团队logo
    inviteCode      String      @unique // 邀请码
    
    // 业务信息
    area            String?     // 负责区域
    address         String?     // 详细地址
    businessScope   String?     // 业务范围
    
    // 配额管理
    quota           Int         @default(0) // 超级个体配额(PARTNER以上角色使用)
    quotaUsed       Int         @default(0) // 已使用配额
    salesTarget     Int         @default(0) // 销售目标
    
    // 状态信息
    approvedAt      DateTime?   // 审核通过时间
    approvedBy      String?     // 审核人
    
    // 业绩统计
    salesAmount     Int         @default(0) // 销售总额(分)
    commission      Int         @default(0) // 佣金总额(分)
    orderCount      Int         @default(0) // 订单总数
    
    // 关联
    orders          AgentOrder[] // 订单关联
    processedReferrals ReferralRecord[] @relation("processor") // 处理的推荐
    referrals       ReferralRecord[] @relation("referrer") // 发起的推荐
    rewards         ReferralReward[] // 获得的奖励
    
    // 系统信息
    createdAt       DateTime    @default(now())
    updatedAt       DateTime    @updatedAt
    remark          String?
    
    @@index([userId])
    @@index([parentId])
    @@index([role])
    @@index([createdAt])
    @@map("agent")
}
```

#### 消费者表(Consumer)

消费者表存储平台普通用户的信息。

```prisma
// 消费者表 - 消费者特有的业务字段
model Consumer {
    id              String      @id @default(cuid())
    userId          String      @unique // 关联到用户账号
    user            User        @relation(fields: [userId], references: [id])
    role            ConsumerRole @default(REGULAR)
    
    // 来源信息
    source          String?     // 来源渠道
    agentId         String?     // 关联代理商ID
    agent           Agent?      @relation(fields: [agentId], references: [id])
    
    // 数字人资产
    avatars         AvatarCloneRecord[] // 数字人模型
    voices          VoiceCloneRecord[] // 声音模型
    videos          AvatarVideoRecord[] // 视频记录
    
    // 统计数据
    totalSpent      Int         @default(0) // 累计消费(分)
    orderCount      Int         @default(0) // 订单数
    
    // 订阅信息
    plans           ConsumerPlan[] // 套餐订阅
    orders          ConsumerOrder[] // 订单
    
    // 系统信息
    createdAt       DateTime    @default(now())
    updatedAt       DateTime    @updatedAt
    remark          String?
    
    @@index([userId])
    @@index([agentId])
    @@index([createdAt])
    @@map("consumer")
}
```

### 套餐与订阅系统

套餐与订阅系统包括以下核心表:

1. **Plan**: 套餐定义表
2. **PlanFeature**: 套餐功能表
3. **ConsumerPlan**: 用户套餐订阅表

#### 套餐表(Plan)

```prisma
// 套餐模型
model Plan {
    id    String    @id @default(cuid())
    name  String    // 套餐名称
    level PlanLevel // 套餐等级
    code  String    @unique // 套餐代码

    // 价格信息
    price        Int          // 售价（分）
    currency     Currency     @default(CNY) // 货币类型
    billingCycle BillingCycle @default(MONTHLY) // 计费周期

    // 基础配置
    computingPower Int     @map("computing_power") // 套餐包含的算力值
    validityDays   Int     // 有效期（天）
    features       PlanFeature[] // 套餐包含的功能

    // 分润配置
    commissionAmount Int // 分润金额（分）

    // 营销相关
    isShow          Boolean   @default(true) // 是否在商城展示
    priority        Int       @default(0) // 展示优先级(数字越小优先级越高)
    discountRate    Float     @default(0) @map("discount_rate") // 折扣率
    discountStartAt DateTime? @map("discount_start_at") // 折扣开始时间
    discountEndAt   DateTime? @map("discount_end_at") // 折扣结束时间
    
    // 系统信息
    createdAt       DateTime  @default(now())
    updatedAt       DateTime  @updatedAt
    deletedAt       DateTime?
    remark          String?

    // 关联
    consumerPlans   ConsumerPlan[] // 用户购买的套餐
    consumerOrders  ConsumerOrder[] // 套餐订单

    @@index([level])
    @@index([code])
    @@index([isShow])
    @@index([createdAt])
    @@map("plan")
}
```

#### 用户套餐订阅表(ConsumerPlan)

```prisma
// 用户套餐订阅
model ConsumerPlan {
    id          String      @id @default(cuid())
    consumerId  String      @map("consumer_id")
    consumer    Consumer    @relation(fields: [consumerId], references: [id], onDelete: Cascade)
    planId      String      @map("plan_id")
    plan        Plan        @relation(fields: [planId], references: [id])

    // 订阅信息
    status         SubscriptionStatus @default(PENDING)
    computingPower Int // 套餐算力值
    computingPowerUsed Int @default(0) @map("computing_power_used") // 已使用算力值
    
    // 时间信息
    startDate    DateTime @map("start_date") // 开始日期
    endDate      DateTime @map("end_date") // 结束日期
    activatedAt  DateTime? @map("activated_at") // 激活时间
    suspendedAt  DateTime? @map("suspended_at") // 暂停时间
    cancelledAt  DateTime? @map("cancelled_at") // 取消时间
    
    // 关联
    orders        ConsumerOrder[] // 相关订单
    usages        ComputingPowerUsage[] // 算力使用记录
    
    // 系统信息
    autoRenew     Boolean   @default(false) @map("auto_renew") // 是否自动续费
    createdAt     DateTime  @default(now())
    updatedAt     DateTime  @updatedAt
    remark        String?
    
    @@index([consumerId])
    @@index([planId])
    @@index([status])
    @@index([startDate])
    @@index([endDate])
    @@map("consumer_plan")
}
```

### 订单与支付系统

订单与支付系统包括以下核心表:

1. **ConsumerOrder**: 消费者订单表
2. **AgentOrder**: 代理商订单表
3. **PaymentRecord**: 支付记录表(未展示在设计中)

#### 消费者订单表(ConsumerOrder)

```prisma
// 消费者订单（套餐购买）
model ConsumerOrder {
    id             String        @id @default(cuid())
    orderNo        String        @unique // 订单编号(CO开头)
    consumerId     String        @map("consumer_id")
    consumer       Consumer      @relation(fields: [consumerId], references: [id], onDelete: Cascade)
    
    // 套餐信息
    planId         String        @map("plan_id")
    plan           Plan          @relation(fields: [planId], references: [id])
    consumerPlanId String?       @map("consumer_plan_id")
    consumerPlan   ConsumerPlan? @relation(fields: [consumerPlanId], references: [id])

    // 订单信息
    amount       Int // 订单金额（分）
    quantity     Int // 购买数量
    unitPrice    Int @map("unit_price") // 单价（分）

    // 支付信息
    paymentMethod  PaymentMethod? // 支付方式
    paymentOrderId String?        @unique @map("payment_order_id") // 支付平台订单号
    status         OrderStatus    @default(PENDING)
    paymentStatus  PaymentStatus  @default(PENDING)

    // 时间信息
    paidAt      DateTime? @map("paid_at")
    completedAt DateTime? @map("completed_at")
    refundedAt  DateTime? @map("refunded_at")

    // 系统字段
    remark    String?
    createdAt DateTime  @default(now()) @map("created_at")
    updatedAt DateTime  @updatedAt @map("updated_at")
    deletedAt DateTime? @map("deleted_at")

    @@index([consumerId])
    @@index([planId])
    @@index([consumerPlanId])
    @@index([status])
    @@index([paymentStatus])
    @@index([createdAt])
    @@map("consumer_order")
}
```

#### 代理商订单表(AgentOrder)

```prisma
// 代理商算力订单
model AgentOrder {
    id             String        @id @default(cuid())
    orderNo        String        @unique // 订单编号(AO开头)
    agentId        String        @map("agent_id")
    agent          Agent         @relation(fields: [agentId], references: [id], onDelete: Cascade)

    // 订单信息
    amount         Int // 订单金额（分）
    quantity       Int // 购买数量（算力值）
    unitPrice      Int @map("unit_price") // 单价（分/算力）

    // 支付信息
    paymentMethod  PaymentMethod? // 支付方式
    paymentOrderId String?        @unique @map("payment_order_id") // 支付平台订单号
    status         OrderStatus    @default(PENDING)
    paymentStatus  PaymentStatus  @default(PENDING)

    // 时间信息
    paidAt      DateTime? @map("paid_at")
    completedAt DateTime? @map("completed_at")
    refundedAt  DateTime? @map("refunded_at")

    // 系统字段
    remark    String?
    createdAt DateTime  @default(now()) @map("created_at")
    updatedAt DateTime  @updatedAt @map("updated_at")
    deletedAt DateTime? @map("deleted_at")

    @@index([agentId])
    @@index([status])
    @@index([paymentStatus])
    @@index([createdAt])
    @@map("agent_order")
}
```

### 算力系统

算力系统包括以下核心表:

1. **ComputingPowerUsage**: 算力使用记录
2. **ComputingPowerLog**: 算力变更日志

#### 算力使用记录(ComputingPowerUsage)

```prisma
// 算力使用记录
model ComputingPowerUsage {
    id         String @id @default(cuid())
    userId     String @map("user_id")
    user       User   @relation(fields: [userId], references: [id], onDelete: Cascade)

    // 使用信息
    amount      Int                      // 使用的算力值
    usageType   ComputingPowerUsageType @map("usage_type") // 使用类型
    status      ComputingPowerUsageStatus @default(PENDING)
    description String?                  // 使用说明
    metaInfo    Json?                   @map("meta_info") // 元数据

    // 时间信息
    requestedAt DateTime  @default(now()) @map("requested_at") // 请求时间
    confirmedAt DateTime? @map("confirmed_at") // 确认时间
    refundedAt  DateTime? @map("refunded_at") // 退还时间

    // 系统字段
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    // 关联
    consumerPlan        ConsumerPlan? @relation(fields: [consumerPlanId], references: [id])
    consumerPlanId      String?       @map("consumer_plan_id")
    avatarCloneRecords  AvatarCloneRecord[]
    voiceCloneRecords   VoiceCloneRecord[]
    avatarVideoRecords  AvatarVideoRecord[]
    aiTextRecords       AITextRecord[]

    @@index([userId])
    @@index([status])
    @@index([usageType])
    @@index([requestedAt])
    @@map("computing_power_usage")
}
```

### 数字人创作系统

数字人创作系统包括以下核心表:

1. **AvatarCloneRecord**: 数字人克隆记录
2. **VoiceCloneRecord**: 声音克隆记录
3. **AvatarVideoRecord**: 视频生成记录
4. **AITextRecord**: AI文案记录

#### 数字人克隆记录(AvatarCloneRecord)

```prisma
// 数字人形象克隆记录
model AvatarCloneRecord {
    id         String   @id @default(cuid())
    consumerId String   @map("consumer_id")
    consumer   Consumer @relation(fields: [consumerId], references: [id], onDelete: Cascade)

    // 克隆信息
    name        String // 形象名称
    description String? // 形象描述
    sourceType  String // 来源类型(图片/视频)
    sourceUrl   String // 源文件地址
    outputUrl   String? // 输出文件地址
    coverUrl    String? @map("cover_url") // 封面图片地址
    previewUrl  String? @map("preview_url") // 预览视频地址

    // 元数据
    metadata Json? // 元数据(如尺寸、时长等)

    // 状态信息
    status CreationStatus @default(PENDING)
    error  String? // 错误信息

    // 算力消耗
    computingPowerCost Int @default(3000) @map("computing_power_cost") // 消耗的算力值(3000/次)

    // 关联
    usageId String? @map("usage_id") // 关联的算力使用记录ID
    usage   ComputingPowerUsage? @relation(fields: [usageId], references: [id], onDelete: SetNull)

    // 系统字段
    createdAt DateTime  @default(now())
    updatedAt DateTime  @updatedAt
    deletedAt DateTime?
    remark    String?

    @@index([consumerId])
    @@index([status])
    @@index([createdAt])
    @@index([usageId])
    @@map("avatar_clone_record")
}
```

#### 声音克隆记录(VoiceCloneRecord)

```prisma
// 数字人声音克隆记录
model VoiceCloneRecord {
    id         String   @id @default(cuid())
    consumerId String   @map("consumer_id")
    consumer   Consumer @relation(fields: [consumerId], references: [id], onDelete: Cascade)

    // 克隆信息
    name        String // 声音名称
    description String? // 声音描述
    sourceUrl   String // 源文件地址
    outputUrl   String? // 输出文件地址
    previewUrl  String? @map("preview_url") // 试听音频地址
    duration    Int // 音频时长(秒)

    // 元数据
    metadata Json? // 元数据(如音频格式、比特率等)

    // 状态信息
    status CreationStatus @default(PENDING)
    error  String? // 错误信息

    // 算力消耗
    computingPowerCost Int @default(3000) @map("computing_power_cost") // 消耗的算力值(3000/次)

    // 关联
    usageId String? @map("usage_id")
    usage   ComputingPowerUsage? @relation(fields: [usageId], references: [id], onDelete: SetNull)

    // 系统字段
    createdAt DateTime  @default(now())
    updatedAt DateTime  @updatedAt
    deletedAt DateTime?
    remark    String?

    @@index([consumerId])
    @@index([status])
    @@index([createdAt])
    @@index([usageId])
    @@map("voice_clone_record")
}
```

### 代理商推荐系统

代理商推荐系统包括以下核心表:

1. **ReferralRecord**: 推荐记录表
2. **ReferralReward**: 推荐奖励表

#### 推荐记录表(ReferralRecord)

```prisma
// 推荐记录表
model ReferralRecord {
    id            String         @id @default(cuid())
    
    // 推荐人信息
    referrerId    String         // 推荐人ID
    referrer      Agent          @relation("referrer", fields: [referrerId], references: [id], onDelete: Cascade)
    
    // 被推荐人信息
    userId        String         // 被推荐用户ID (已注册的User)
    user          User           @relation("referralUser", fields: [userId], references: [id])
    phone         String         // 被推荐人手机号
    note          String?        // 推荐备注
    
    // 目标等级
    targetRole    AgentRole      // 目标角色
    
    // 处理信息
    processorId   String?        // 处理人ID
    processor     Agent?         @relation("processor", fields: [processorId], references: [id])
    processNote   String?        // 处理备注
    
    // 状态信息
    status        ReferralStatus @default(PROCESSING)
    
    // 配额信息
    quotaRequired Int            // 所需超级个体名额数量
    quotaUsed     Boolean        @default(false) // 是否已划扣名额
    
    // 时间信息
    expiresAt     DateTime      // 处理截止时间(创建时间+3天)
    processedAt   DateTime?     // 处理时间
    escalatedAt   DateTime?     // 升级至管理员时间
    
    // 奖励记录
    rewards       ReferralReward[]
    
    // 系统字段
    createdAt     DateTime       @default(now())
    updatedAt     DateTime       @updatedAt
    remark        String?        // 备注说明

    @@index([referrerId])
    @@index([processorId])
    @@index([status])
    @@index([expiresAt])
    @@map("referral_record")
}
```

## 数据库关系

### 核心实体关系图

下面是9000AI平台核心数据库表之间的关系:

```
User (1) ---> (0..1) Agent
    |
    +---> (0..1) Consumer
    |
    +---> (0..n) Session
    |
    +---> (0..n) Account
    |
    +---> (0..n) ComputingPowerUsage

Agent (1) ---> (0..n) Agent (下级代理)
    |
    +---> (0..n) Consumer (代理的客户)
    |
    +---> (0..n) AgentOrder (代理订单)
    |
    +---> (0..n) ReferralRecord (推荐记录)

Consumer (1) ---> (0..n) ConsumerPlan (用户套餐)
    |
    +---> (0..n) ConsumerOrder (用户订单)
    |
    +---> (0..n) AvatarCloneRecord (数字人记录)
    |
    +---> (0..n) VoiceCloneRecord (声音记录)

Plan (1) ---> (0..n) PlanFeature (套餐功能)
    |
    +---> (0..n) ConsumerPlan (用户订阅)
    |
    +---> (0..n) ConsumerOrder (用户订单)

ComputingPowerUsage (1) ---> (0..n) AvatarCloneRecord (数字人记录)
    |
    +---> (0..n) VoiceCloneRecord (声音记录)
    |
    +---> (0..n) AvatarVideoRecord (视频记录)
```

### 关键索引设计

为了优化查询性能，数据库中为各表添加了以下关键索引:

1. **用户认证相关**:
   - `user.email` (唯一索引)
   - `user.phoneNumberFull` (唯一索引)
   - `agent.userId` (唯一索引)
   - `consumer.userId` (唯一索引)

2. **套餐与订阅相关**:
   - `plan.code` (唯一索引)
   - `plan.level` (普通索引)
   - `consumer_plan.consumerId` (普通索引)
   - `consumer_plan.status` (普通索引)

3. **订单相关**:
   - `consumer_order.orderNo` (唯一索引)
   - `consumer_order.status` (普通索引)
   - `consumer_order.createdAt` (普通索引)
   - `agent_order.orderNo` (唯一索引)

4. **创作记录相关**:
   - `avatar_clone_record.consumerId` (普通索引)
   - `avatar_clone_record.status` (普通索引)
   - `voice_clone_record.consumerId` (普通索引)

5. **算力相关**:
   - `computing_power_usage.userId` (普通索引)
   - `computing_power_usage.status` (普通索引)
   - `computing_power_usage.usageType` (普通索引)

## 数据库迁移与版本控制

### Prisma迁移

前端使用Prisma Migrate管理数据库结构变更:

```bash
# 创建迁移文件
pnpm db:migrate -- --name "add_agent_team_fields"

# 应用迁移
pnpm db:push

# 生成Prisma客户端
pnpm db:generate
```

### 迁移最佳实践

1. **小批量迁移**: 每次变更尽量小而集中
2. **向后兼容**: 设计迁移时考虑向后兼容性
3. **测试迁移**: 在应用到生产前先在测试环境验证
4. **准备回滚**: 准备必要的回滚脚本
5. **记录变更**: 记录每次迁移的目的和影响

## 数据库性能优化

### 优化策略

1. **索引优化**:
   - 为频繁查询的字段添加索引
   - 避免过度索引，平衡写入性能
   - 定期分析慢查询，调整索引策略

2. **查询优化**:
   - 使用适当的WHERE子句限制结果集
   - 减少JOIN操作数量
   - 使用分页限制返回行数

3. **数据库配置优化**:
   - 调整连接池大小
   - 配置适当的缓存大小
   - 优化PostgreSQL配置参数

4. **数据分区**:
   - 考虑对大表进行水平分区
   - 基于时间或ID范围进行分区
   - 实现分区裁剪提高查询效率

### 监控与维护

1. **定期统计信息更新**:
   ```sql
   ANALYZE [table_name];
   ```

2. **索引维护**:
   ```sql
   REINDEX TABLE [table_name];
   ```

3. **数据库清理**:
   ```sql
   VACUUM FULL [table_name];
   ```

## 数据安全与合规

### 数据加密

敏感数据字段使用加密存储:

1. **静态加密**: 敏感信息在存储前加密
   - 身份证号码
   - 实名认证信息
   - 支付凭证

2. **传输加密**: 通过HTTPS和TLS保护数据传输
3. **备份加密**: 数据库备份文件加密存储

### 数据访问控制

1. **最小权限原则**:
   - 数据库用户仅拥有必要权限
   - 应用程序使用只读角色进行大部分操作

2. **行级安全**:
   - 为多租户实现行级安全策略
   - 数据隔离防止跨越权限访问

### 数据备份

1. **备份策略**:
   - 每日全量备份
   - 增量备份（每6小时）
   - 跨区域备份存储

2. **恢复测试**:
   - 定期测试备份恢复流程
   - 记录恢复时间目标(RTO)

## 数据库运维

### 常见运维任务

1. **性能监控**:
   - 慢查询日志分析
   - 连接数和锁监控
   - 磁盘空间监控

2. **扩容策略**:
   - 垂直扩展（增加资源）
   - 水平扩展（读写分离）
   - 分片策略评估

### 故障排查

1. **常见问题诊断**:
   - 连接超时问题
   - 锁竞争问题
   - 查询性能下降

2. **故障恢复**:
   - 从备份恢复
   - 故障切换流程
   - 高可用性配置

## 数据库迁移路径

随着业务增长，可能需要考虑以下数据库演进路径:

1. **PostgreSQL集群**:
   - 主从复制
   - 读写分离
   - 连接池管理

2. **分布式数据库**:
   - 垂直分片（按功能）
   - 水平分片（按用户）
   - 跨区域复制

3. **混合存储**:
   - 冷热数据分离
   - 时序数据特殊处理
   - 非结构化数据迁移至专用存储

---

本文档最后更新: [日期] 