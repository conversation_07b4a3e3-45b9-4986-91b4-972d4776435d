# 9000AI 后端开发指南

## 简介

本文档为9000AI平台后端开发指南，详细说明后端架构、API设计、消息队列和存储服务等关键模块。后端采用Python FastAPI框架，结合RabbitMQ消息队列和MinIO对象存储，提供高性能、可扩展的服务支持。

## 开发环境配置

### 环境要求

- Python >= 3.8
- PostgreSQL >= 14.0
- RabbitMQ >= 3.9
- Docker & Docker Compose
- 推荐IDE: PyCharm或VSCode (安装Python、Pylance插件)

### 本地开发设置

```bash
# 进入后端目录
cd backend

# 创建虚拟环境
python -m venv .venv

# 激活虚拟环境
# Windows:
.venv\Scripts\activate
# Linux/Mac:
source .venv/bin/activate

# 安装开发依赖
pip install -e ".[dev]"

# 运行开发服务器
python -m backend.main

# 运行测试
pytest
```

### 开发环境变量配置

关键环境变量（在`.env`文件中配置）:

```
# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/database_name

# RabbitMQ配置
RABBITMQ_URL=amqp://guest:guest@localhost:5672/

# MinIO/S3配置
S3_ACCESS_KEY_ID=minioadmin
S3_SECRET_ACCESS_KEY=minioadmin
S3_ENDPOINT=http://localhost:9000
```

## 架构概述

### 后端架构设计

后端采用分层架构设计：

1. **API层**: FastAPI路由和控制器
2. **服务层**: 业务逻辑和服务
3. **数据层**: 数据库访问和模型
4. **集成层**: 第三方服务集成

### 关键技术栈

- **Web框架**: FastAPI (异步支持)
- **数据库交互**: SQLAlchemy (Core + ORM)
- **消息队列**: RabbitMQ + aio_pika
- **对象存储**: MinIO (S3兼容)
- **API文档**: OpenAPI/Swagger (自动生成)
- **认证**: JWT + OAuth2
- **测试**: pytest + pytest-asyncio

## 代码风格与规范

### Python代码规范

- 使用Black格式化代码
- 使用isort整理导入
- 使用Ruff进行代码质量检查
- 使用mypy进行类型检查
- 遵循PEP 8风格指南

### 命名规范

- **类名**: PascalCase (如 `UserService`)
- **函数/变量**: snake_case (如 `get_user_by_id`)
- **常量**: UPPER_SNAKE_CASE (如 `MAX_RETRY_COUNT`)
- **私有成员**: 前缀下划线 (如 `_private_method`)

### 注释和文档

- 使用docstring记录函数和类的用途
- 复杂逻辑添加行内注释
- API端点添加详细的OpenAPI文档

```python
async def create_user(user_data: UserCreate) -> User:
    """
    创建新用户。
    
    Args:
        user_data: 用户创建数据模型
        
    Returns:
        新创建的用户对象
        
    Raises:
        HTTPException: 邮箱已存在或验证失败
    """
    # 实现逻辑
```

## API设计

### RESTful API设计原则

- 使用HTTP方法表示操作 (GET, POST, PUT, DELETE)
- 使用复数名词命名资源 (如 `/users`, `/orders`)
- 使用HTTP状态码表示结果
- 使用嵌套资源表示关系 (如 `/users/{id}/orders`)
- 支持过滤、排序和分页
- 版本化API (如 `/api/v1/users`)

### 标准端点结构

```python
from fastapi import APIRouter, Depends, HTTPException, status
from typing import List

from backend.models import UserCreate, UserResponse
from backend.services import UserService

router = APIRouter(prefix="/users", tags=["users"])

@router.get("/", response_model=List[UserResponse])
async def list_users(
    skip: int = 0,
    limit: int = 100,
    service: UserService = Depends()
):
    """获取用户列表。"""
    return await service.get_users(skip=skip, limit=limit)

@router.post("/", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def create_user(
    user_data: UserCreate,
    service: UserService = Depends()
):
    """创建新用户。"""
    try:
        return await service.create_user(user_data)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
```

### 错误处理

统一的错误响应格式:

```python
# 错误响应模型
class ErrorResponse(BaseModel):
    status: str = "error"
    message: str
    code: int
    details: Optional[Dict[str, Any]] = None

# 错误处理中间件
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            message=exc.detail,
            code=exc.status_code,
            details=exc.headers
        ).dict()
    )
```

## 数据库访问

### SQLAlchemy模型

```python
from sqlalchemy import Column, Integer, String, ForeignKey, DateTime, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from backend.database import Base

class User(Base):
    __tablename__ = "users"
    
    id = Column(String, primary_key=True, index=True)
    name = Column(String, nullable=False)
    email = Column(String, unique=True, index=True, nullable=False)
    phone_number = Column(String, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联
    orders = relationship("Order", back_populates="user")
```

### 数据库操作

使用异步SQLAlchemy进行数据库操作:

```python
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

async def get_user_by_id(db: AsyncSession, user_id: str) -> Optional[User]:
    """通过ID获取用户。"""
    result = await db.execute(select(User).filter(User.id == user_id))
    return result.scalars().first()

async def create_user(db: AsyncSession, user_data: UserCreate) -> User:
    """创建新用户。"""
    user = User(
        id=generate_id(),
        name=user_data.name,
        email=user_data.email,
        phone_number=user_data.phone_number
    )
    db.add(user)
    await db.commit()
    await db.refresh(user)
    return user
```

## 认证与授权

### JWT认证

使用FastAPI的OAuth2PasswordBearer实现JWT认证:

```python
from datetime import datetime, timedelta
from jose import jwt

# JWT配置
SECRET_KEY = os.getenv("SECRET_KEY")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# 创建访问令牌
def create_access_token(data: dict, expires_delta: timedelta = None):
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    
    return encoded_jwt

# 验证令牌
async def get_current_user(token: str = Depends(oauth2_scheme)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无效凭证",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        user_id: str = payload.get("sub")
        
        if user_id is None:
            raise credentials_exception
        
        user = await get_user_by_id(user_id)
        
        if user is None:
            raise credentials_exception
            
        return user
    except JWTError:
        raise credentials_exception
```

### 基于角色的访问控制

使用依赖项实现基于角色的访问控制:

```python
from enum import Enum
from fastapi import Depends, HTTPException, status

class UserRole(str, Enum):
    ADMIN = "ADMIN"
    BRANCH = "BRANCH"
    DIRECTOR = "DIRECTOR"
    PARTNER = "PARTNER"
    SALES = "SALES"

def require_role(required_role: UserRole):
    async def role_checker(
        current_user: User = Depends(get_current_user)
    ):
        if not current_user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="认证失败"
            )
            
        # 检查用户是否具有所需角色
        # ADMIN角色具有最高权限
        if current_user.role == UserRole.ADMIN:
            return current_user
            
        # 检查角色等级
        role_levels = {
            UserRole.ADMIN: 0,
            UserRole.BRANCH: 1,
            UserRole.DIRECTOR: 2,
            UserRole.PARTNER: 3,
            UserRole.SALES: 4
        }
        
        # 角色等级数字越小权限越高
        if role_levels.get(current_user.role, 99) <= role_levels.get(required_role, 99):
            return current_user
            
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
        
    return role_checker

# 使用示例
@router.get("/admin-only")
async def admin_only_route(user: User = Depends(require_role(UserRole.ADMIN))):
    return {"message": "仅管理员可访问"}
```

## 消息队列

### RabbitMQ集成

使用aio_pika库与RabbitMQ集成:

```python
import aio_pika
from aio_pika import connect_robust, Message, ExchangeType

class RabbitMQClient:
    def __init__(self, rabbitmq_url: str):
        self.rabbitmq_url = rabbitmq_url
        self.connection = None
        self.channel = None
        self.exchange = None
        
    async def connect(self):
        """建立到RabbitMQ的连接。"""
        self.connection = await connect_robust(self.rabbitmq_url)
        self.channel = await self.connection.channel()
        
        # 声明交换机
        self.exchange = await self.channel.declare_exchange(
            "ai9000_exchange", ExchangeType.TOPIC, durable=True
        )
        
        # 声明队列
        queue = await self.channel.declare_queue(
            "ai9000_tasks", durable=True
        )
        
        # 绑定队列
        await queue.bind(self.exchange, routing_key="task.#")
        
        return self
        
    async def close(self):
        """关闭连接。"""
        if self.connection:
            await self.connection.close()
            
    async def publish_message(self, routing_key: str, message_data: dict):
        """发布消息到队列。"""
        if not self.exchange:
            raise RuntimeError("连接未初始化")
            
        message = Message(
            body=json.dumps(message_data).encode(),
            content_type="application/json",
            delivery_mode=aio_pika.DeliveryMode.PERSISTENT
        )
        
        await self.exchange.publish(
            message=message,
            routing_key=routing_key
        )
```

### 消息消费者

实现消息队列消费者:

```python
import asyncio
import json
from typing import Dict, Callable, Awaitable

from aio_pika import connect_robust, IncomingMessage

# 消息处理器类型
MessageHandler = Callable[[dict], Awaitable[None]]

class RabbitMQConsumer:
    def __init__(self, rabbitmq_url: str, queue_name: str):
        self.rabbitmq_url = rabbitmq_url
        self.queue_name = queue_name
        self.connection = None
        self.channel = None
        self.handlers: Dict[str, MessageHandler] = {}
        
    def register_handler(self, task_type: str, handler: MessageHandler):
        """注册消息处理器。"""
        self.handlers[task_type] = handler
        
    async def connect(self):
        """建立到RabbitMQ的连接。"""
        self.connection = await connect_robust(self.rabbitmq_url)
        self.channel = await self.connection.channel()
        
        # 设置QoS
        await self.channel.set_qos(prefetch_count=10)
        
    async def close(self):
        """关闭连接。"""
        if self.connection:
            await self.connection.close()
            
    async def consume(self):
        """开始消费消息。"""
        if not self.channel:
            raise RuntimeError("连接未初始化")
            
        queue = await self.channel.declare_queue(
            self.queue_name, durable=True
        )
        
        async with queue.iterator() as queue_iter:
            async for message in queue_iter:
                async with message.process():
                    await self.process_message(message)
                    
    async def process_message(self, message: IncomingMessage):
        """处理接收到的消息。"""
        try:
            # 解析消息
            body = message.body.decode()
            data = json.loads(body)
            
            # 获取任务类型
            task_type = data.get("task_type")
            
            if not task_type:
                print(f"无法识别的任务类型: {data}")
                return
                
            # 查找并执行对应的处理器
            handler = self.handlers.get(task_type)
            
            if not handler:
                print(f"未找到处理器: {task_type}")
                return
                
            # 执行处理器
            await handler(data)
            
        except Exception as e:
            print(f"处理消息时出错: {str(e)}")
            # 在生产环境中应记录详细日志并考虑重试机制
```

### 工作者进程

实现Worker进程处理任务:

```python
import asyncio
import os
import signal
from typing import Dict

from backend.mq.consumer import RabbitMQConsumer, MessageHandler
from backend.mq.handlers import (
    handle_avatar_clone,
    handle_voice_clone,
    handle_VIDEO_CREATION
)

class Worker:
    def __init__(self):
        rabbitmq_url = os.getenv("RABBITMQ_URL")
        queue_name = os.getenv("QUEUE_NAME", "ai9000_tasks")
        
        self.consumer = RabbitMQConsumer(rabbitmq_url, queue_name)
        self.running = False
        
        # 注册消息处理器
        self.register_handlers()
        
    def register_handlers(self):
        """注册所有消息处理器。"""
        handlers: Dict[str, MessageHandler] = {
            "avatar_clone": handle_avatar_clone,
            "voice_clone": handle_voice_clone,
            "VIDEO_CREATION": handle_VIDEO_CREATION,
        }
        
        for task_type, handler in handlers.items():
            self.consumer.register_handler(task_type, handler)
            
    async def start(self):
        """启动工作者进程。"""
        self.running = True
        
        # 注册信号处理器
        loop = asyncio.get_event_loop()
        for sig in (signal.SIGINT, signal.SIGTERM):
            loop.add_signal_handler(sig, lambda: asyncio.create_task(self.shutdown()))
            
        try:
            await self.consumer.connect()
            await self.consumer.consume()
        finally:
            await self.shutdown()
            
    async def shutdown(self):
        """优雅关闭工作者进程。"""
        if not self.running:
            return
            
        self.running = False
        
        print("正在关闭工作者进程...")
        await self.consumer.close()
        
# 入口点
async def main():
    worker = Worker()
    await worker.start()
    
if __name__ == "__main__":
    asyncio.run(main())
```

## 存储服务

### MinIO/S3集成

使用boto3操作MinIO/S3存储:

```python
import boto3
from botocore.client import Config

class StorageClient:
    def __init__(
        self,
        endpoint_url: str,
        access_key_id: str,
        secret_access_key: str,
        region_name: str = None
    ):
        self.s3_client = boto3.client(
            's3',
            endpoint_url=endpoint_url,
            aws_access_key_id=access_key_id,
            aws_secret_access_key=secret_access_key,
            region_name=region_name or 'us-east-1',
            config=Config(signature_version='s3v4')
        )
        
    def create_bucket(self, bucket_name: str):
        """创建存储桶。"""
        try:
            self.s3_client.create_bucket(Bucket=bucket_name)
            return True
        except Exception as e:
            print(f"创建存储桶失败: {str(e)}")
            return False
            
    def upload_file(self, bucket_name: str, object_name: str, file_path: str):
        """上传文件到存储桶。"""
        try:
            self.s3_client.upload_file(file_path, bucket_name, object_name)
            return True
        except Exception as e:
            print(f"上传文件失败: {str(e)}")
            return False
            
    def upload_fileobj(self, bucket_name: str, object_name: str, file_obj):
        """上传文件对象到存储桶。"""
        try:
            self.s3_client.upload_fileobj(file_obj, bucket_name, object_name)
            return True
        except Exception as e:
            print(f"上传文件对象失败: {str(e)}")
            return False
            
    def download_file(self, bucket_name: str, object_name: str, file_path: str):
        """从存储桶下载文件。"""
        try:
            self.s3_client.download_file(bucket_name, object_name, file_path)
            return True
        except Exception as e:
            print(f"下载文件失败: {str(e)}")
            return False
            
    def get_presigned_url(
        self,
        bucket_name: str,
        object_name: str,
        expiration: int = 3600,
        http_method: str = 'GET'
    ):
        """生成预签名URL。"""
        try:
            url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={
                    'Bucket': bucket_name,
                    'Key': object_name
                },
                ExpiresIn=expiration,
                HttpMethod=http_method
            )
            return url
        except Exception as e:
            print(f"生成预签名URL失败: {str(e)}")
            return None
```

### 存储服务接口

实现存储服务接口:

```python
from fastapi import APIRouter, Depends, File, UploadFile, HTTPException, status
from backend.storage.client import StorageClient
from backend.auth import get_current_user

router = APIRouter(prefix="/storage", tags=["storage"])

@router.post("/upload")
async def upload_file(
    bucket: str,
    prefix: str = "",
    file: UploadFile = File(...),
    current_user = Depends(get_current_user),
    storage_client: StorageClient = Depends()
):
    """上传文件到存储桶。"""
    try:
        # 生成对象名称
        object_name = f"{prefix}/{file.filename}" if prefix else file.filename
        
        # 上传文件
        result = await storage_client.upload_fileobj(
            bucket_name=bucket,
            object_name=object_name,
            file_obj=file.file
        )
        
        if not result:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="文件上传失败"
            )
            
        # 生成预签名URL
        url = await storage_client.get_presigned_url(bucket, object_name)
        
        return {
            "success": True,
            "file_name": file.filename,
            "object_name": object_name,
            "url": url
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
```

## 测试

### 单元测试

使用pytest编写单元测试:

```python
import pytest
from fastapi.testclient import TestClient
from backend.main import app

@pytest.fixture
def client():
    return TestClient(app)

def test_read_health(client):
    """测试健康检查端点。"""
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json() == {
        "status": "healthy",
        "version": "1.0.0"
    }

def test_create_user(client):
    """测试创建用户API。"""
    user_data = {
        "name": "Test User",
        "email": "<EMAIL>",
        "phone_number": "+8613800138000"
    }
    
    response = client.post("/users/", json=user_data)
    assert response.status_code == 201
    
    # 验证响应
    user = response.json()
    assert user["name"] == user_data["name"]
    assert user["email"] == user_data["email"]
    assert "id" in user
```

### 异步测试

使用pytest-asyncio测试异步代码:

```python
import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from backend.database import Base
from backend.models import User
from backend.services.user_service import UserService

# 测试数据库URL
TEST_DATABASE_URL = "postgresql+asyncpg://test:test@localhost/test_db"

@pytest_asyncio.fixture
async def async_engine():
    engine = create_async_engine(TEST_DATABASE_URL)
    
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
        await conn.run_sync(Base.metadata.create_all)
        
    yield engine
    
    await engine.dispose()

@pytest_asyncio.fixture
async def async_session(async_engine):
    session_factory = sessionmaker(
        async_engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with session_factory() as session:
        yield session

@pytest.mark.asyncio
async def test_create_user_service(async_session):
    """测试用户服务创建功能。"""
    # 准备测试数据
    user_data = {
        "name": "Test User",
        "email": "<EMAIL>",
        "phone_number": "+8613800138000"
    }
    
    # 创建服务实例
    user_service = UserService(async_session)
    
    # 执行测试
    user = await user_service.create_user(user_data)
    
    # 验证结果
    assert user.name == user_data["name"]
    assert user.email == user_data["email"]
    assert user.id is not None
```

## 部署

### Docker部署

使用Docker容器部署FastAPI应用:

```dockerfile
FROM python:3.9-slim

WORKDIR /app

# 复制依赖文件
COPY pyproject.toml .
COPY README.md .

# 安装依赖
RUN pip install --no-cache-dir .

# 复制应用代码
COPY src/ src/

# 设置环境变量
ENV PYTHONPATH=/app

# 暴露端口
EXPOSE 8000

# 启动应用
CMD ["uvicorn", "backend.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 健康检查和监控

实现健康检查端点:

```python
@app.get("/health", tags=["health"])
async def health_check():
    """健康检查端点。"""
    # 检查数据库连接
    try:
        # 简单数据库查询
        pass
    except Exception as e:
        return {
            "status": "unhealthy",
            "database": str(e),
            "version": "1.0.0"
        }
        
    # 检查RabbitMQ连接
    try:
        # 简单RabbitMQ连接检查
        pass
    except Exception as e:
        return {
            "status": "unhealthy",
            "rabbitmq": str(e),
            "version": "1.0.0"
        }
        
    # 检查MinIO连接
    try:
        # 简单MinIO连接检查
        pass
    except Exception as e:
        return {
            "status": "unhealthy",
            "minio": str(e),
            "version": "1.0.0"
        }
    
    return {
        "status": "healthy",
        "version": "1.0.0"
    }
```

## 性能优化

### 后端性能优化策略

1. **数据库优化**:
   - 使用适当的索引
   - 优化查询语句
   - 批量操作减少数据库调用

2. **异步处理**:
   - 使用异步I/O操作
   - 将耗时任务放到消息队列

3. **缓存策略**:
   - 使用Redis缓存热点数据
   - 实现缓存失效机制

4. **响应优化**:
   - 适当使用分页
   - 限制响应字段
   - 启用GZIP压缩

## 问题排查指南

### 常见问题与解决方案

1. **数据库连接问题**:
   - 检查连接字符串格式
   - 验证数据库服务状态
   - 检查网络连接和防火墙

2. **API错误**:
   - 检查请求格式和参数
   - 验证认证令牌
   - 查看详细错误日志

3. **消息队列问题**:
   - 验证RabbitMQ服务状态
   - 检查队列和交换机配置
   - 查看消费者日志

4. **文件存储问题**:
   - 检查MinIO服务状态
   - 验证存储桶权限
   - 检查文件大小限制

## 安全最佳实践

1. **API安全**:
   - 使用HTTPS
   - 实现Rate Limiting
   - 验证所有输入
   - 防范SQL注入和XSS

2. **认证与授权**:
   - 使用安全的JWT实现
   - 实施适当的密码策略
   - 正确实现RBAC

3. **敏感数据保护**:
   - 加密敏感数据
   - 日志中移除敏感信息
   - 遵循数据保护法规

---

本文档最后更新: [日期] 