# 9000AI平台 API接口文档

## 简介

本文档详细说明9000AI平台提供的所有API接口，包括认证方式、请求格式、响应格式和错误处理。这些API接口按模块分组，每个模块对应平台的一个主要功能区域。

## API基础信息

### 基本URL

- 开发环境: `http://localhost:9527/api/v1`
- 测试环境: `https://test-api.9000ai.com/api/v1`
- 生产环境: `https://api.9000ai.com/api/v1`

### 认证方式

平台API使用JWT（JSON Web Token）认证：

1. 调用登录接口获取访问令牌
2. 在后续请求中添加Authorization头:
   ```
   Authorization: Bearer {access_token}
   ```

### 请求格式

- 所有请求内容类型为`application/json`
- GET请求使用URL查询参数
- POST/PUT/PATCH请求使用JSON请求体

### 响应格式

标准响应格式:

```json
{
  "code": 200,              // 状态码
  "message": "Success",     // 状态消息
  "data": { ... },          // 响应数据
  "meta": {                 // 元数据(分页等)
    "page": 1,
    "limit": 10,
    "total": 100
  }
}
```

### 错误处理

错误响应格式:

```json
{
  "code": 400,                  // HTTP状态码
  "message": "Bad Request",     // 错误消息
  "errors": [                   // 详细错误信息
    {
      "field": "email",
      "message": "Invalid email format"
    }
  ]
}
```

常见错误码:
- 400: 请求参数错误
- 401: 未认证/认证失败
- 403: 权限不足
- 404: 资源不存在
- 429: 请求频率超限
- 500: 服务器内部错误

## 认证API

### 登录

**POST** `/auth/login`

登录并获取访问令牌

**请求参数:**

```json
{
  "username": "<EMAIL>",
  "password": "password123",
  "role": "AGENT"  // AGENT或CONSUMER
}
```

**响应:**

```json
{
  "code": 200,
  "message": "Login successful",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1...",
    "refresh_token": "eyJhbGciOiJIUzI1...",
    "expires_in": 3600,
    "user": {
      "id": "usr_123456",
      "name": "张三",
      "role": "AGENT",
      "agent_level": "BRANCH"
    }
  }
}
```

### 刷新令牌

**POST** `/auth/refresh`

使用刷新令牌获取新的访问令牌

**请求参数:**

```json
{
  "refresh_token": "eyJhbGciOiJIUzI1..."
}
```

**响应:**

```json
{
  "code": 200,
  "message": "Token refreshed",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1...",
    "expires_in": 3600
  }
}
```

### 登出

**POST** `/auth/logout`

使当前令牌失效

**请求参数:** 无

**响应:**

```json
{
  "code": 200,
  "message": "Logout successful",
  "data": null
}
```

## 用户API

### 获取用户信息

**GET** `/users/me`

获取当前登录用户信息

**请求参数:** 无

**响应:**

```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "id": "usr_123456",
    "name": "张三",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "role": "AGENT",
    "agent_info": {
      "level": "BRANCH",
      "parent_id": "usr_654321",
      "team_size": 5
    }
  }
}
```

### 更新用户信息

**PATCH** `/users/me`

更新当前用户资料

**请求参数:**

```json
{
  "name": "李四",
  "phone": "13900139000"
}
```

**响应:**

```json
{
  "code": 200,
  "message": "User updated successfully",
  "data": {
    "id": "usr_123456",
    "name": "李四",
    "phone": "13900139000"
  }
}
```

## 代理商API

### 获取代理商列表

**GET** `/agents`

获取代理商列表(仅限管理员和上级代理商)

**请求参数:**
- `page`: 页码(默认1)
- `limit`: 每页条数(默认20)
- `parent_id`: 上级代理ID(可选)
- `level`: 代理级别(可选)

**响应:**

```json
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "id": "usr_123456",
      "name": "张三",
      "level": "BRANCH",
      "created_at": "2023-01-01T00:00:00Z",
      "team_size": 5,
      "total_sales": 10000
    }
  ],
  "meta": {
    "page": 1,
    "limit": 20,
    "total": 100
  }
}
```

### 创建代理商

**POST** `/agents`

创建新代理商(仅限管理员和有权限的上级代理商)

**请求参数:**

```json
{
  "name": "王五",
  "email": "<EMAIL>",
  "phone": "13700137000",
  "level": "PARTNER",
  "parent_id": "usr_123456"
}
```

**响应:**

```json
{
  "code": 200,
  "message": "Agent created successfully",
  "data": {
    "id": "usr_789012",
    "name": "王五",
    "email": "<EMAIL>",
    "level": "PARTNER",
    "invitation_code": "INVITE123",
    "activation_link": "https://9000ai.com/activate?code=xyz123"
  }
}
```

### 获取代理商详情

**GET** `/agents/{id}`

获取特定代理商详情

**响应:**

```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "id": "usr_123456",
    "name": "张三",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "level": "BRANCH",
    "parent": {
      "id": "usr_654321",
      "name": "总代理"
    },
    "team": [
      {
        "id": "usr_789012",
        "name": "王五",
        "level": "PARTNER"
      }
    ],
    "stats": {
      "total_consumers": 50,
      "monthly_sales": 5000,
      "total_sales": 20000
    },
    "created_at": "2023-01-01T00:00:00Z"
  }
}
```

## 消费者API

### 获取消费者列表

**GET** `/consumers`

获取消费者列表(限代理商查看自己的消费者)

**请求参数:**
- `page`: 页码(默认1)
- `limit`: 每页条数(默认20)
- `search`: 搜索关键词(可选)

**响应:**

```json
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "id": "usr_234567",
      "name": "消费者A",
      "email": "<EMAIL>",
      "created_at": "2023-02-01T00:00:00Z",
      "plan": "高级套餐",
      "computing_power": 500
    }
  ],
  "meta": {
    "page": 1,
    "limit": 20,
    "total": 50
  }
}
```

### 获取消费者详情

**GET** `/consumers/{id}`

获取特定消费者详情

**响应:**

```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "id": "usr_234567",
    "name": "消费者A",
    "email": "<EMAIL>",
    "phone": "13600136000",
    "created_at": "2023-02-01T00:00:00Z",
    "agent": {
      "id": "usr_123456",
      "name": "张三"
    },
    "subscription": {
      "plan_id": "plan_123",
      "plan_name": "高级套餐",
      "started_at": "2023-02-01T00:00:00Z",
      "expires_at": "2024-02-01T00:00:00Z",
      "status": "ACTIVE"
    },
    "computing_power": {
      "available": 500,
      "used": 1500,
      "total": 2000
    },
    "usage_stats": {
      "avatar_creations": 5,
      "voice_clones": 3,
      "videos_generated": 10
    }
  }
}
```

## 套餐API

### 获取套餐列表

**GET** `/plans`

获取可用套餐列表

**请求参数:**
- `type`: 套餐类型(可选, "CONSUMER"或"AGENT")

**响应:**

```json
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "id": "plan_123",
      "name": "基础套餐",
      "type": "CONSUMER",
      "price": 99.00,
      "computing_power": 500,
      "duration_days": 30,
      "features": [
        "数字人克隆(2个)",
        "声音克隆(5个)",
        "视频生成(10分钟)"
      ]
    },
    {
      "id": "plan_456",
      "name": "高级套餐",
      "type": "CONSUMER",
      "price": 299.00,
      "computing_power": 2000,
      "duration_days": 30,
      "features": [
        "数字人克隆(5个)",
        "声音克隆(10个)",
        "视频生成(30分钟)"
      ]
    }
  ]
}
```

### 获取套餐详情

**GET** `/plans/{id}`

获取特定套餐详情

**响应:**

```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "id": "plan_123",
    "name": "基础套餐",
    "description": "适合个人用户的入门级套餐",
    "type": "CONSUMER",
    "price": 99.00,
    "original_price": 199.00,
    "computing_power": 500,
    "duration_days": 30,
    "features": [
      "数字人克隆(2个)",
      "声音克隆(5个)",
      "视频生成(10分钟)"
    ],
    "limitations": {
      "max_avatar_clones": 2,
      "max_voice_clones": 5,
      "max_video_minutes": 10
    }
  }
}
```

## 订单API

### 创建订单

**POST** `/orders`

创建新订单

**请求参数:**

```json
{
  "plan_id": "plan_123",
  "consumer_id": "usr_234567",  // 如果代理商为消费者购买，需指定
  "payment_method": "WECHAT",   // WECHAT, ALIPAY
  "coupon_code": "SUMMER20"     // 可选
}
```

**响应:**

```json
{
  "code": 200,
  "message": "Order created successfully",
  "data": {
    "order_id": "ord_123456",
    "amount": 99.00,
    "discount_amount": 0,
    "final_amount": 99.00,
    "status": "PENDING",
    "payment": {
      "payment_method": "WECHAT",
      "payment_url": "https://pay.example.com/xyz",
      "qr_code": "data:image/png;base64,..."
    }
  }
}
```

### 获取订单列表

**GET** `/orders`

获取订单列表

**请求参数:**
- `page`: 页码(默认1)
- `limit`: 每页条数(默认20)
- `status`: 订单状态(可选, "PENDING", "PAID", "CANCELLED")
- `start_date`: 开始日期(可选)
- `end_date`: 结束日期(可选)

**响应:**

```json
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "id": "ord_123456",
      "created_at": "2023-03-01T00:00:00Z",
      "plan_name": "基础套餐",
      "amount": 99.00,
      "status": "PAID",
      "payment_method": "WECHAT",
      "consumer": {
        "id": "usr_234567",
        "name": "消费者A"
      }
    }
  ],
  "meta": {
    "page": 1,
    "limit": 20,
    "total": 45
  }
}
```

### 获取订单详情

**GET** `/orders/{id}`

获取订单详情

**响应:**

```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "id": "ord_123456",
    "created_at": "2023-03-01T00:00:00Z",
    "updated_at": "2023-03-01T00:10:00Z",
    "plan": {
      "id": "plan_123",
      "name": "基础套餐",
      "computing_power": 500
    },
    "consumer": {
      "id": "usr_234567",
      "name": "消费者A"
    },
    "agent": {
      "id": "usr_123456",
      "name": "张三"
    },
    "amount": 99.00,
    "discount_amount": 0,
    "final_amount": 99.00,
    "status": "PAID",
    "payment": {
      "method": "WECHAT",
      "transaction_id": "4200001234202303011234567890",
      "paid_at": "2023-03-01T00:10:00Z"
    },
    "commission": {
      "total": 29.70,
      "details": [
        {
          "agent_id": "usr_123456",
          "agent_name": "张三",
          "amount": 19.80
        },
        {
          "agent_id": "usr_654321",
          "agent_name": "总代理",
          "amount": 9.90
        }
      ]
    }
  }
}
```

## 算力API

### 获取算力记录

**GET** `/computing-power/records`

获取算力变更记录

**请求参数:**
- `page`: 页码(默认1)
- `limit`: 每页条数(默认20)
- `type`: 记录类型(可选, "INCREASE", "DECREASE")

**响应:**

```json
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "id": "cp_123456",
      "created_at": "2023-03-01T00:10:00Z",
      "type": "INCREASE",
      "amount": 500,
      "balance_after": 500,
      "source": "PLAN_PURCHASE",
      "description": "购买基础套餐",
      "related_id": "ord_123456"
    },
    {
      "id": "cp_123457",
      "created_at": "2023-03-02T10:00:00Z",
      "type": "DECREASE",
      "amount": 50,
      "balance_after": 450,
      "source": "AVATAR_CREATION",
      "description": "创建数字人形象",
      "related_id": "avatar_123"
    }
  ],
  "meta": {
    "page": 1,
    "limit": 20,
    "total": 45
  }
}
```

### 获取算力统计

**GET** `/computing-power/stats`

获取算力使用统计

**响应:**

```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "current_balance": 450,
    "total_acquired": 500,
    "total_used": 50,
    "usage_by_feature": {
      "AVATAR_CREATION": 50,
      "VOICE_CLONING": 0,
      "VIDEO_CREATION": 0
    },
    "usage_trend": [
      {
        "date": "2023-03-01",
        "used": 0
      },
      {
        "date": "2023-03-02",
        "used": 50
      }
    ]
  }
}
```

## 数字人API

### 创建数字人克隆任务

**POST** `/avatars/clone`

创建数字人克隆任务

**请求参数 (multipart/form-data):**
- `name`: 数字人名称
- `description`: 描述(可选)
- `image_files`: 形象照片文件(多个)
- `style`: 渲染风格(可选, "REALISTIC", "CARTOON")

**响应:**

```json
{
  "code": 200,
  "message": "Avatar clone task created successfully",
  "data": {
    "task_id": "task_123456",
    "name": "我的数字人",
    "status": "PENDING",
    "estimated_completion_time": "2023-03-02T12:00:00Z",
    "computing_power_cost": 50
  }
}
```

### 获取数字人列表

**GET** `/avatars`

获取数字人列表

**请求参数:**
- `page`: 页码(默认1)
- `limit`: 每页条数(默认20)
- `status`: 状态(可选, "TRAINING", "READY", "FAILED")

**响应:**

```json
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "id": "avatar_123",
      "name": "我的数字人",
      "created_at": "2023-03-02T10:00:00Z",
      "status": "READY",
      "thumbnail_url": "https://storage.9000ai.com/thumbnails/avatar_123.jpg"
    }
  ],
  "meta": {
    "page": 1,
    "limit": 20,
    "total": 2
  }
}
```

### 获取数字人详情

**GET** `/avatars/{id}`

获取数字人详情

**响应:**

```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "id": "avatar_123",
    "name": "我的数字人",
    "description": "销售助理形象",
    "created_at": "2023-03-02T10:00:00Z",
    "status": "READY",
    "style": "REALISTIC",
    "preview_url": "https://storage.9000ai.com/previews/avatar_123.mp4",
    "thumbnail_url": "https://storage.9000ai.com/thumbnails/avatar_123.jpg",
    "model_info": {
      "version": "1.0",
      "quality_score": 0.92,
      "training_images": 10
    },
    "usage_stats": {
      "total_videos": 5,
      "total_duration": 180 // 秒
    }
  }
}
```

## 声音API

### 创建声音克隆任务

**POST** `/voices/clone`

创建声音克隆任务

**请求参数 (multipart/form-data):**
- `name`: 声音名称
- `description`: 描述(可选)
- `audio_file`: 音频样本文件
- `gender`: 性别("MALE"或"FEMALE")

**响应:**

```json
{
  "code": 200,
  "message": "Voice clone task created successfully",
  "data": {
    "task_id": "task_234567",
    "name": "我的声音",
    "status": "PENDING",
    "estimated_completion_time": "2023-03-02T11:00:00Z",
    "computing_power_cost": 30
  }
}
```

### 获取声音列表

**GET** `/voices`

获取声音列表

**请求参数:**
- `page`: 页码(默认1)
- `limit`: 每页条数(默认20)
- `status`: 状态(可选, "TRAINING", "READY", "FAILED")

**响应:**

```json
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "id": "voice_123",
      "name": "我的声音",
      "created_at": "2023-03-02T09:00:00Z",
      "status": "READY",
      "gender": "MALE",
      "preview_url": "https://storage.9000ai.com/previews/voice_123.mp3"
    }
  ],
  "meta": {
    "page": 1,
    "limit": 20,
    "total": 3
  }
}
```

## 视频API

### 创建视频生成任务

**POST** `/videos/generate`

创建视频生成任务

**请求参数:**

```json
{
  "avatar_id": "avatar_123",
  "voice_id": "voice_123",
  "script": "你好，我是你的AI助手，很高兴为你服务。",
  "background_type": "GRADIENT",
  "background_color": "#f0f0f0",
  "output_format": "MP4",
  "resolution": "720p"
}
```

**响应:**

```json
{
  "code": 200,
  "message": "Video generation task created successfully",
  "data": {
    "task_id": "task_345678",
    "status": "PENDING",
    "estimated_completion_time": "2023-03-02T11:30:00Z",
    "computing_power_cost": 20,
    "estimated_duration": 10 // 秒
  }
}
```

### 获取视频任务列表

**GET** `/videos`

获取视频任务列表

**请求参数:**
- `page`: 页码(默认1)
- `limit`: 每页条数(默认20)
- `status`: 状态(可选, "PENDING", "PROCESSING", "COMPLETED", "FAILED")

**响应:**

```json
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "id": "video_123",
      "created_at": "2023-03-02T11:00:00Z",
      "status": "COMPLETED",
      "duration": 10,
      "thumbnail_url": "https://storage.9000ai.com/thumbnails/video_123.jpg",
      "avatar_name": "我的数字人",
      "script_preview": "你好，我是你的AI助手..."
    }
  ],
  "meta": {
    "page": 1,
    "limit": 20,
    "total": 5
  }
}
```

### 获取视频任务详情

**GET** `/videos/{id}`

获取视频任务详情

**响应:**

```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "id": "video_123",
    "created_at": "2023-03-02T11:00:00Z",
    "completed_at": "2023-03-02T11:05:00Z",
    "status": "COMPLETED",
    "avatar": {
      "id": "avatar_123",
      "name": "我的数字人",
      "thumbnail_url": "https://storage.9000ai.com/thumbnails/avatar_123.jpg"
    },
    "voice": {
      "id": "voice_123",
      "name": "我的声音"
    },
    "script": "你好，我是你的AI助手，很高兴为你服务。",
    "duration": 10,
    "resolution": "720p",
    "file_size": 5242880, // 字节
    "download_url": "https://storage.9000ai.com/videos/video_123.mp4",
    "stream_url": "https://stream.9000ai.com/videos/video_123.m3u8",
    "thumbnail_url": "https://storage.9000ai.com/thumbnails/video_123.jpg"
  }
}
```

## 任务API

### 获取任务状态

**GET** `/tasks/{id}`

获取异步任务状态

**响应:**

```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "id": "task_123456",
    "type": "AVATAR_CLONE",
    "status": "PROCESSING",
    "progress": 60,
    "message": "正在训练模型...",
    "created_at": "2023-03-02T10:00:00Z",
    "estimated_completion_time": "2023-03-02T12:00:00Z",
    "result": null
  }
}
```

### 获取任务列表

**GET** `/tasks`

获取异步任务列表

**请求参数:**
- `page`: 页码(默认1)
- `limit`: 每页条数(默认20)
- `type`: 任务类型(可选, "AVATAR_CLONE", "VOICE_CLONE", "VIDEO_CREATION")
- `status`: 状态(可选, "PENDING", "PROCESSING", "COMPLETED", "FAILED")

**响应:**

```json
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "id": "task_123456",
      "type": "AVATAR_CLONE",
      "status": "PROCESSING",
      "progress": 60,
      "created_at": "2023-03-02T10:00:00Z",
      "name": "我的数字人"
    }
  ],
  "meta": {
    "page": 1,
    "limit": 20,
    "total": 8
  }
}
```

## 健康检查

### 服务健康状态

**GET** `/health`

检查API服务健康状态

**响应:**

```json
{
  "status": "healthy",
  "version": "1.5.0",
  "services": {
    "database": "connected",
    "queue": "connected",
    "storage": "connected"
  },
  "timestamp": "2023-03-02T12:00:00Z"
}
```

---

本文档最后更新: [日期] 