# 9000AI 数据统计操作指南

## 📊 概述

本文档详细说明了9000AI平台的代理商业绩数据统计系统的设计理念、使用方法和维护策略。该系统通过预聚合的方式提供高性能的业绩查询和趋势分析功能。

## 🏗️ 系统架构

### 数据模型概览

```
原始业务数据 ──────┐
                   │
├─ QuotaAllocationRecord    ──┐
├─ Order                     ── ┬─→ 数据聚合处理 ──┐
├─ ActivationRecord          ──┘                  │
├─ Agent                                         │
└─ Consumer                                      │
                                                │
                            ┌───────────────────┘
                            │
                            ▼
                    ┌──────────────────────┐
                    │   统计数据存储层      │
                    └──────────────────────┘
                            │
            ┌───────────────┼───────────────┐
            │               │               │
            ▼               ▼               ▼
  ┌─────────────────┐ ┌─────────────┐ ┌──────────────┐
  │AgentPerformance │ │RevenueTrend │ │RevenueBreak  │
  │Stats            │ │Data         │ │downData      │
  │(核心统计)        │ │(趋势图)      │ │(饼图)         │
  └─────────────────┘ └─────────────┘ └──────────────┘
```

### 三层数据表设计

| 数据表 | 用途 | 特点 | 查询场景 |
|--------|------|------|----------|
| **AgentPerformanceStats** | 核心业绩统计 | 完整指标、多时间维度 | 仪表盘卡片、对比分析 |
| **RevenueTrendData** | 收入趋势分析 | 简化字段、时间序列 | 趋势图、折线图 |
| **RevenueBreakdownData** | 收入构成分析 | 占比计算、分类统计 | 饼图、占比分析 |

## ⏰ 时间维度系统

### StatsPeriod 枚举定义

```sql
enum StatsPeriod {
    DAILY     -- 日统计：精确到每天
    WEEKLY    -- 周统计：每周汇总  
    MONTHLY   -- 月统计：每月汇总
    QUARTERLY -- 季度统计：每季度汇总
    YEARLY    -- 年度统计：每年汇总
}
```

### 时间维度使用示例

```typescript
// 日期格式规范
const timeFormats = {
    DAILY: '2025-06-09',           // 具体日期
    WEEKLY: '2025-W23',            // 2025年第23周
    MONTHLY: '2025-06',            // 2025年6月
    QUARTERLY: '2025-Q2',          // 2025年第2季度
    YEARLY: '2025'                 // 2025年
};
```

## 🔄 数据更新策略

### 1. 定时批量更新 (推荐)

```typescript
// 每日凌晨更新前一天数据
import { db } from '@repo/database';
import { StatsPeriod } from '@prisma/client';

/**
 * 每日统计数据更新任务
 * 建议在每天凌晨1:00执行
 */
export async function updateDailyStats() {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    yesterday.setHours(0, 0, 0, 0);

    const agents = await db.agent.findMany({
        where: { status: 'ACTIVE' }
    });

    for (const agent of agents) {
        await calculateAndUpsertDailyStats(agent.id, yesterday);
    }
}

/**
 * 计算并更新单个代理商的日统计数据
 */
async function calculateAndUpsertDailyStats(agentId: string, date: Date) {
    const startOfDay = new Date(date);
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);

    // 1. 计算名额分配数据
    const quotaStats = await db.quotaAllocationRecord.aggregate({
        where: {
            fromAgentId: agentId,
            completedAt: {
                gte: startOfDay,
                lte: endOfDay
            }
        },
        _sum: {
            quantity: true,
            amount: true
        },
        _count: {
            id: true
        }
    });

    // 2. 计算收入数据
    const revenueStats = await calculateDailyRevenue(agentId, startOfDay, endOfDay);

    // 3. 计算团队数据
    const teamStats = await calculateTeamStats(agentId, date);

    // 4. 获取历史数据计算变化率
    const previousDayStats = await getPreviousDayStats(agentId, date);
    const changeRates = calculateChangeRates(revenueStats, previousDayStats);

    // 5. 插入或更新统计数据
    await db.agentPerformanceStats.upsert({
        where: {
            agentId_date_period: {
                agentId,
                date: formatDateForPeriod(date, StatsPeriod.DAILY),
                period: StatsPeriod.DAILY
            }
        },
        create: {
            agentId,
            date: formatDateForPeriod(date, StatsPeriod.DAILY),
            period: StatsPeriod.DAILY,
            // 收益数据
            totalRevenue: revenueStats.total,
            monthlyRevenue: revenueStats.monthly,
            growthFundRevenue: revenueStats.growthFund,
            salesRevenue: revenueStats.sales,
            // 变化率
            totalRevenueChange: changeRates.totalChange,
            monthlyRevenueChange: changeRates.monthlyChange,
            growthFundRevenueChange: changeRates.growthFundChange,
            salesRevenueChange: changeRates.salesChange,
            // 名额数据
            periodQuotaAllocated: quotaStats._sum.quantity || 0,
            quotaAllocationCount: quotaStats._count.id || 0,
            // 团队数据
            teamSize: teamStats.size,
            newTeamMembers: teamStats.newMembers,
            activeTeamMembers: teamStats.activeMembers,
            // 销售数据
            periodSales: revenueStats.sales,
            salesCount: revenueStats.salesCount
        },
        update: {
            // 同样的字段更新逻辑
            totalRevenue: revenueStats.total,
            monthlyRevenue: revenueStats.monthly,
            // ... 其他字段
            updatedAt: new Date()
        }
    });
}
```

### 2. 实时增量更新

```typescript
/**
 * 名额分配成功后的实时统计更新
 */
export async function onQuotaAllocated(allocation: QuotaAllocationRecord) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // 更新今日统计
    await incrementPerformanceStats({
        agentId: allocation.fromAgentId,
        date: today,
        period: StatsPeriod.DAILY,
        increment: {
            periodQuotaAllocated: allocation.quantity,
            quotaAllocationCount: 1,
            totalRevenue: allocation.amount || 0
        }
    });

    // 同步更新趋势数据
    await updateRevenueTrendData(allocation.fromAgentId, today, {
        totalRevenue: allocation.amount || 0
    });
}

/**
 * 增量更新统计数据
 */
async function incrementPerformanceStats(params: {
    agentId: string;
    date: Date;
    period: StatsPeriod;
    increment: {
        periodQuotaAllocated?: number;
        quotaAllocationCount?: number;
        totalRevenue?: number;
        // ... 其他可增量的字段
    };
}) {
    const { agentId, date, period, increment } = params;
    
    await db.agentPerformanceStats.upsert({
        where: {
            agentId_date_period: {
                agentId,
                date: formatDateForPeriod(date, period),
                period
            }
        },
        create: {
            agentId,
            date: formatDateForPeriod(date, period),
            period,
            ...increment
        },
        update: {
            periodQuotaAllocated: {
                increment: increment.periodQuotaAllocated || 0
            },
            quotaAllocationCount: {
                increment: increment.quotaAllocationCount || 0
            },
            totalRevenue: {
                increment: increment.totalRevenue || 0
            },
            updatedAt: new Date()
        }
    });
}
```

## 📈 数据查询示例

### 1. 业绩面板数据查询

```typescript
/**
 * 获取代理商业绩面板数据
 */
export async function getAgentPerformanceOverview(agentId: string, timeRange: string = 'month') {
    const currentDate = new Date();
    const period = timeRangeToPeriod(timeRange); // 'month' -> StatsPeriod.MONTHLY
    
    // 当期数据
    const currentStats = await db.agentPerformanceStats.findUnique({
        where: {
            agentId_date_period: {
                agentId,
                date: formatDateForPeriod(currentDate, period),
                period
            }
        }
    });

    // 上期数据（用于计算变化率）
    const previousDate = getPreviousPeriodDate(currentDate, period);
    const previousStats = await db.agentPerformanceStats.findUnique({
        where: {
            agentId_date_period: {
                agentId,
                date: formatDateForPeriod(previousDate, period),
                period
            }
        }
    });

    return {
        totalRevenue: currentStats?.totalRevenue || 0,
        monthlyRevenue: currentStats?.monthlyRevenue || 0,
        growthFundRevenue: currentStats?.growthFundRevenue || 0,
        salesRevenue: currentStats?.salesRevenue || 0,
        // 计算变化率
        yearlyChange: calculateChangeRate(
            currentStats?.totalRevenue,
            previousStats?.totalRevenue
        ),
        monthlyChange: calculateChangeRate(
            currentStats?.monthlyRevenue,
            previousStats?.monthlyRevenue
        ),
        growthFundChange: calculateChangeRate(
            currentStats?.growthFundRevenue,
            previousStats?.growthFundRevenue
        ),
        salesChange: calculateChangeRate(
            currentStats?.salesRevenue,
            previousStats?.salesRevenue
        )
    };
}
```

### 2. 收入趋势数据查询

```typescript
/**
 * 获取收入趋势图数据
 */
export async function getRevenueTrendData(agentId: string, timeRange: string = 'month') {
    const period = timeRangeToPeriod(timeRange);
    const limit = getDataPointsLimit(timeRange); // month: 30, quarter: 90, year: 365

    const trendData = await db.revenueTrendData.findMany({
        where: {
            agentId,
            period
        },
        orderBy: {
            date: 'desc'
        },
        take: limit
    });

    return trendData.reverse().map(item => ({
        date: formatDateForDisplay(item.date, period),
        totalRevenue: item.totalRevenue,
        salesRevenue: item.salesRevenue,
        growthFund: item.growthFundRevenue
    }));
}
```

### 3. 收入构成数据查询

```typescript
/**
 * 获取收入构成饼图数据
 */
export async function getRevenueBreakdownData(agentId: string, timeRange: string = 'month') {
    const period = timeRangeToPeriod(timeRange);
    const currentDate = new Date();

    const breakdownData = await db.revenueBreakdownData.findUnique({
        where: {
            agentId_date_period: {
                agentId,
                date: formatDateForPeriod(currentDate, period),
                period
            }
        }
    });

    if (!breakdownData) {
        return [];
    }

    return [
        {
            name: '销售分润',
            value: breakdownData.salesRevenue,
            percentage: breakdownData.salesRevenueRatio
        },
        {
            name: '交付收益',
            value: breakdownData.growthFundRevenue,
            percentage: breakdownData.growthFundRevenueRatio
        }
    ];
}
```

## 🔧 工具函数

### 时间处理工具

```typescript
/**
 * 时间范围转换为统计周期
 */
function timeRangeToPeriod(timeRange: string): StatsPeriod {
    const mapping = {
        'day': StatsPeriod.DAILY,
        'week': StatsPeriod.WEEKLY,
        'month': StatsPeriod.MONTHLY,
        'quarter': StatsPeriod.QUARTERLY,
        'year': StatsPeriod.YEARLY
    };
    
    return mapping[timeRange] || StatsPeriod.MONTHLY;
}

/**
 * 格式化日期为对应周期格式
 */
function formatDateForPeriod(date: Date, period: StatsPeriod): Date {
    const d = new Date(date);
    
    switch (period) {
        case StatsPeriod.DAILY:
            return new Date(d.getFullYear(), d.getMonth(), d.getDate());
        case StatsPeriod.WEEKLY:
            const weekStart = getWeekStart(d);
            return new Date(weekStart.getFullYear(), weekStart.getMonth(), weekStart.getDate());
        case StatsPeriod.MONTHLY:
            return new Date(d.getFullYear(), d.getMonth(), 1);
        case StatsPeriod.QUARTERLY:
            const quarter = Math.floor(d.getMonth() / 3);
            return new Date(d.getFullYear(), quarter * 3, 1);
        case StatsPeriod.YEARLY:
            return new Date(d.getFullYear(), 0, 1);
        default:
            return d;
    }
}

/**
 * 计算变化率
 */
function calculateChangeRate(current?: number, previous?: number): number {
    if (!previous || previous === 0) return 0;
    if (!current) return -100;
    
    return ((current - previous) / previous) * 100;
}
```

## 📊 定时任务配置

### 使用 node-cron 实现定时更新

```typescript
import cron from 'node-cron';

/**
 * 数据统计定时任务配置
 */
export function setupStatsScheduler() {
    // 每日凌晨1点更新昨日数据
    cron.schedule('0 1 * * *', async () => {
        console.log('开始执行每日统计数据更新...');
        try {
            await updateDailyStats();
            console.log('每日统计数据更新完成');
        } catch (error) {
            console.error('每日统计数据更新失败:', error);
        }
    });

    // 每周一凌晨2点更新上周数据
    cron.schedule('0 2 * * 1', async () => {
        console.log('开始执行每周统计数据更新...');
        try {
            await updateWeeklyStats();
            console.log('每周统计数据更新完成');
        } catch (error) {
            console.error('每周统计数据更新失败:', error);
        }
    });

    // 每月1号凌晨3点更新上月数据
    cron.schedule('0 3 1 * *', async () => {
        console.log('开始执行每月统计数据更新...');
        try {
            await updateMonthlyStats();
            console.log('每月统计数据更新完成');
        } catch (error) {
            console.error('每月统计数据更新失败:', error);
        }
    });
}
```

## 🎯 最佳实践

### 1. 数据一致性保障

```typescript
/**
 * 数据校验和修复
 */
export async function validateAndRepairStats(agentId: string, date: Date) {
    // 1. 从原始数据重新计算
    const calculatedStats = await calculateStatsFromRawData(agentId, date);
    
    // 2. 获取当前统计数据
    const currentStats = await db.agentPerformanceStats.findUnique({
        where: {
            agentId_date_period: {
                agentId,
                date: formatDateForPeriod(date, StatsPeriod.DAILY),
                period: StatsPeriod.DAILY
            }
        }
    });

    // 3. 对比并修复差异
    if (currentStats) {
        const differences = compareStats(calculatedStats, currentStats);
        if (differences.length > 0) {
            console.warn(`发现数据差异，准备修复:`, differences);
            await updateStatsData(agentId, date, calculatedStats);
        }
    } else {
        // 4. 数据缺失，直接插入
        console.info(`统计数据缺失，准备补充:`, { agentId, date });
        await insertStatsData(agentId, date, calculatedStats);
    }
}
```

### 2. 性能优化建议

```typescript
/**
 * 批量更新优化
 */
export async function batchUpdateStats(updates: StatsUpdate[]) {
    // 使用事务确保数据一致性
    await db.$transaction(async (tx) => {
        // 批量upsert，减少数据库连接开销
        const upsertPromises = updates.map(update => 
            tx.agentPerformanceStats.upsert({
                where: {
                    agentId_date_period: {
                        agentId: update.agentId,
                        date: update.date,
                        period: update.period
                    }
                },
                create: update.data,
                update: update.data
            })
        );

        await Promise.all(upsertPromises);
    });
}
```

### 3. 错误处理和监控

```typescript
/**
 * 带重试机制的统计更新
 */
export async function updateStatsWithRetry(
    agentId: string, 
    date: Date, 
    maxRetries: number = 3
) {
    let attempt = 0;
    
    while (attempt < maxRetries) {
        try {
            await calculateAndUpsertDailyStats(agentId, date);
            return; // 成功则返回
        } catch (error) {
            attempt++;
            console.error(`统计更新失败 (尝试 ${attempt}/${maxRetries}):`, error);
            
            if (attempt >= maxRetries) {
                // 记录到错误日志或发送告警
                await logStatsError(agentId, date, error);
                throw error;
            }
            
            // 指数退避策略
            await new Promise(resolve => 
                setTimeout(resolve, Math.pow(2, attempt) * 1000)
            );
        }
    }
}
```

## 🚨 注意事项

### 1. 数据安全

- **事务保护**：所有统计数据更新必须在事务中执行
- **备份策略**：定期备份统计数据，防止数据丢失
- **权限控制**：严格控制统计数据的读写权限

### 2. 性能考虑

- **索引优化**：确保查询字段都有适当的索引
- **分批处理**：大量数据更新时采用分批处理
- **缓存策略**：热点数据可以考虑Redis缓存

### 3. 扩展性

- **分表策略**：数据量增长时考虑按时间分表
- **归档机制**：历史数据定期归档到冷存储
- **监控告警**：建立完善的监控和告警机制

## 🔍 故障排查

### 常见问题及解决方案

1. **数据不一致**
   ```bash
   # 运行数据校验脚本
   pnpm run validate-stats --agent-id=xxx --date=2025-06-09
   ```

2. **性能问题**
   ```sql
   -- 检查慢查询
   EXPLAIN ANALYZE SELECT * FROM agent_performance_stats 
   WHERE agentId = 'xxx' AND period = 'MONTHLY';
   ```

3. **定时任务失败**
   ```typescript
   // 检查任务日志
   await db.systemLog.findMany({
       where: {
           type: 'STATS_UPDATE',
           status: 'FAILED'
       },
       orderBy: { createdAt: 'desc' }
   });
   ```

---

## 📝 总结

这套数据统计系统通过预聚合的方式，在保证查询性能的同时，提供了灵活的多维度数据分析能力。合理使用定时更新和实时增量更新相结合的策略，可以在数据实时性和系统性能之间取得最佳平衡。

记住：**数据一致性 > 性能 > 实时性**，在设计和维护时要始终把数据的准确性放在首位！ 🎯 