# CI/CD 流程与多环境部署规范 (预生产与生产)

## 1. 概述

本文档旨在规范项目的自动化持续集成 (CI) 与持续部署 (CD) 流程，确保代码从开发到生产环境的平稳、高效、可靠交付。我们将重点关注预生产 (Staging) 环境和生产 (Production) 环境的部署。

**核心环境划分:**

*   **本地开发环境 (Local Development)**: 开发者本地机器，用于日常编码和单元测试。
*   **预生产环境 (Staging Environment)**: 生产环境的镜像，用于部署前的最终测试、验收 (UAT) 和回归测试。
*   **生产环境 (Production Environment)**: 面向最终用户的正式运行环境。

**核心工具与技术:**

*   **版本控制**: Git (GitHub)
*   **CI/CD**: GitHub Actions
*   **容器化**: Docker
*   **容器编排 (服务器端)**: Docker Compose
*   **镜像仓库**: 腾讯云容器镜像服务 (TCR)
*   **部署服务器**: 腾讯云云服务器 (CVM)

## 2. 分支策略与环境映射

规范的分支策略是多环境部署的基础。

*   **特性/修复分支 (Feature/Bugfix/Hotfix Branches)**:
    *   命名规范: `feature/xxx`, `bugfix/xxx`, `hotfix/xxx`。
    *   创建来源: 从 `staging` 分支拉取创建。
    *   用途: 开发新功能、修复Bug。开发完成后，发起 Pull Request (PR) 到 `staging` 分支进行代码审查。
*   **`staging` 分支**:
    *   用途: 集成所有已完成并通过代码审查的特性和修复。
    *   触发: 当代码 **推送 (push)** 到此分支，或有 PR **合并 (merged)** 到此分支时，自动触发 **预生产环境 (Staging)** 的 CI/CD 流程。
*   **`main` 分支 (或 `release` 分支)**:
    *   用途: 代表稳定且可发布到生产的代码版本。
    *   来源: 仅从 `staging` 分支合并经过预生产环境充分验证的代码。
    *   触发: 当代码 **推送 (push)** 到此分支，或有 PR **合并 (merged)** 到此分支时，自动触发 **生产环境 (Production)** 的 CI/CD 流程。

## 3. 本地开发流程

1.  **拉取代码**: 从远程 `staging` 分支拉取最新代码。
    ```bash
    git checkout staging
    git pull origin staging
    ```
2.  **创建特性分支**:
    ```bash
    git checkout -b feature/your-feature-name
    ```
3.  **本地开发与测试**:
    *   进行代码编写、单元测试、集成测试。
    *   使用 Docker Compose 启动本地开发环境 (可基于 `docker-compose.prod.yml` 配合本地 `.env.dev` 文件，或创建一个专门的 `docker-compose.dev.yml`)。
4.  **提交与推送**:
    ```bash
    git add .
    git commit -m "feat: add your feature description"
    git push origin feature/your-feature-name
    ```
5.  **创建 Pull Request**: 在 GitHub上，从 `feature/your-feature-name` 分支向 `staging` 分支发起 Pull Request。
6.  **代码审查与合并**: 团队成员进行代码审查，通过后合并到 `staging` 分支，触发预生产环境部署。

## 4. GitHub Actions CI/CD 流程详解

CI/CD 流程定义在 `.github/workflows/cd.yml` (或类似名称) 文件中。

### 4.1. 触发条件 (Triggers)

工作流将由以下事件触发：

```yaml
on:
  push:
    branches:
      - staging # 预生产环境
      - main    # 生产环境 (或你的 release 分支)
  pull_request:
    types: [closed] # 当PR被合并时
    branches:
      - staging
      - main
```
确保 `if: github.event.pull_request.merged == true` 条件用于 `pull_request` 触发的部署。

### 4.2. CI (持续集成) 阶段 - (通用步骤，根据环境参数化)

此阶段主要负责代码检查、构建和Docker镜像打包。

1.  **设置 Job 和环境识别**:
    *   定义一个 job (例如 `build_and_deploy`)。
    *   在该 job 的早期步骤中，根据 `github.ref_name` (触发分支名) 判断当前是为 `staging` 还是 `production` 构建。
        ```yaml
        steps:
          - name: Determine Environment
            id: determine_env
            run: |
              if [[ "${{ github.ref_name }}" == "staging" ]]; then
                echo "env_suffix=staging" >> $GITHUB_OUTPUT
                echo "env_name=Staging" >> $GITHUB_OUTPUT
                echo "server_host=${{ secrets.SERVER_HOST_STAGING }}" >> $GITHUB_OUTPUT
                echo "server_user=${{ secrets.SERVER_USER_STAGING }}" >> $GITHUB_OUTPUT
                echo "server_ssh_key=${{ secrets.SERVER_SSH_KEY_STAGING }}" >> $GITHUB_OUTPUT
                # ... 其他 staging secrets
              elif [[ "${{ github.ref_name }}" == "main" ]]; then # 或你的生产分支
                echo "env_suffix=prod" >> $GITHUB_OUTPUT # prod 而非 production，以简化服务器端脚本
                echo "env_name=Production" >> $GITHUB_OUTPUT
                echo "server_host=${{ secrets.SERVER_HOST_PRODUCTION }}" >> $GITHUB_OUTPUT
                echo "server_user=${{ secrets.SERVER_USER_PRODUCTION }}" >> $GITHUB_OUTPUT
                echo "server_ssh_key=${{ secrets.SERVER_SSH_KEY_PRODUCTION }}" >> $GITHUB_OUTPUT
                # ... 其他 production secrets
              else
                echo "Unsupported branch: ${{ github.ref_name }}"
                exit 1
              fi
        ```

2.  **代码检出 (Checkout Code)**:
    ```yaml
    - uses: actions/checkout@v4
    ```

3.  **设置 Node.js 和 PNPM**: (与现有配置类似)
    ```yaml
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: "20.x"
    - name: 设置 PNPM
      uses: pnpm/action-setup@v4
      with:
        version: '9.3.0' # 使用你项目匹配的版本
    ```

4.  **(可选) 代码检查 (Lint)**: (如果之前 `lint` job 是独立的，可以考虑合并或按需触发)
    ```yaml
    - name: 代码检查 (Lint)
      if: steps.determine_env.outputs.env_name != '' # 确保环境已识别
      # ... (复用你现有的 lint 步骤)
    ```

5.  **安装依赖 (Install Dependencies)**:
    ```yaml
    - name: 安装依赖
      working-directory: frontend # 根据你的项目结构
      run: pnpm install
    ```

6.  **生成 Prisma 客户端 (Generate Prisma Client)**:
    ```yaml
    - name: 生成 Prisma 客户端
      working-directory: frontend
      run: pnpm db:generate
    ```

7.  **数据库迁移 (Database Push/Migrate)**:
    *   **注意**: 自动在生产环境执行 `db:push` 具有风险。对于生产环境，更稳妥的做法可能是手动执行迁移，或使用更专业的迁移工具配合蓝绿部署等策略。如果选择在CI中执行，请务必确保幂等性和充分测试。
    *   环境变量如 `DATABASE_URL` 需要根据 `steps.determine_env.outputs.env_suffix` 从对应的 GitHub Secrets (e.g., `secrets.DATABASE_URL_STAGING`, `secrets.DATABASE_URL_PRODUCTION`) 中获取。
    ```yaml
    - name: 数据库迁移 (${{ steps.determine_env.outputs.env_name }})
      working-directory: frontend
      run: pnpm db:push
      env:
        DATABASE_URL: ${{ secrets[format('DATABASE_URL_{0}', upper(steps.determine_env.outputs.env_suffix))] }}
        DIRECT_URL: ${{ secrets[format('DIRECT_URL_{0}', upper(steps.determine_env.outputs.env_suffix))] }}
    ```
    (你需要为 Staging 和 Production 在 GitHub Secrets 中分别创建 `DATABASE_URL_STAGING`, `DATABASE_URL_PROD` 等)

8.  **构建前端应用 (Build Frontend Application)**:
    *   环境变量需要根据目标环境从 GitHub Secrets (e.g., `secrets.NEXT_PUBLIC_SITE_URL_STAGING`) 中获取。
    ```yaml
    - name: 构建前端应用 (${{ steps.determine_env.outputs.env_name }})
      working-directory: frontend
      run: pnpm build
      env:
        # 示例：
        DATABASE_URL: ${{ secrets[format('DATABASE_URL_{0}', upper(steps.determine_env.outputs.env_suffix))] }}
        NEXT_PUBLIC_SITE_URL: ${{ secrets[format('NEXT_PUBLIC_SITE_URL_{0}', upper(steps.determine_env.outputs.env_suffix))] }}
        # ... 其他所有构建时需要的环境变量，均使用此模式从 secrets 加载
        NODE_ENV: ${{ steps.determine_env.outputs.env_suffix == 'prod' && 'production' || 'staging' }} # 或直接用 specific secret
    ```

9.  **登录腾讯云容器镜像服务 (TCR)**:
    ```yaml
    - name: 登录到腾讯云容器仓库
      uses: docker/login-action@v3
      with:
        registry: ccr.ccs.tencentyun.com
        username: ${{ secrets.TENCENT_CLOUD_ACCOUNT_ID }} # 腾讯云主账号ID或有TCR推送权限的子账号ID
        password: ${{ secrets.TENCENT_CLOUD_TCR_PASSWORD }} # TCR 实例密码或生成的长期访问凭证
    ```

10. **设置 Docker Buildx**:
    ```yaml
    - name: 设置 Docker Buildx
      uses: docker/setup-buildx-action@v3
    ```

11. **构建并推送 Docker 镜像 (Build & Push Docker Image)**:
    *   镜像标签包含环境信息和 Git SHA。
    *   `build-args` 也应根据环境从 Secrets 动态传入。
    ```yaml
    - name: 构建和推送 Docker 镜像 (${{ steps.determine_env.outputs.env_name }})
      uses: docker/build-push-action@v6
      with:
        context: ./frontend # Dockerfile 的上下文路径
        file: ./frontend/apps/web/dockerfile # Dockerfile 路径
        push: true
        tags: |
          ccr.ccs.tencentyun.com/${{ secrets.TENCENT_CLOUD_NAMESPACE }}/frontend-${{ steps.determine_env.outputs.env_suffix }}:${{ github.sha }}
          ccr.ccs.tencentyun.com/${{ secrets.TENCENT_CLOUD_NAMESPACE }}/frontend-${{ steps.determine_env.outputs.env_suffix }}:latest
        build-args: |
          TZ=Asia/Shanghai
          # 示例：
          DATABASE_URL=${{ secrets[format('DATABASE_URL_{0}', upper(steps.determine_env.outputs.env_suffix))] }}
          NEXT_PUBLIC_SITE_URL=${{ secrets[format('NEXT_PUBLIC_SITE_URL_{0}', upper(steps.determine_env.outputs.env_suffix))] }}
          # ... 其他所有 Dockerfile 中定义的 ARG，均使用此模式从 secrets 加载
          NODE_ENV=${{ steps.determine_env.outputs.env_suffix == 'prod' && 'production' || 'staging' }}
    ```

### 4.3. CD (持续部署) 阶段 - (根据环境执行)

此阶段负责将构建好的 Docker 镜像部署到对应的腾讯云 CVM 服务器。

1.  **部署到目标服务器 (Deploy to Server via SSH)**:
    ```yaml
    - name: 部署到服务器 (${{ steps.determine_env.outputs.env_name }})
      uses: appleboy/ssh-action@master
      with:
        host: ${{ steps.determine_env.outputs.server_host }}
        username: ${{ steps.determine_env.outputs.server_user }}
        key: ${{ steps.determine_env.outputs.server_ssh_key }}
        script: |
          cd /home/<USER>
          export GIT_SHA=${{ github.sha }}
          # 调用部署脚本，传入环境名和 GIT SHA
          bash deploy.sh ${{ steps.determine_env.outputs.env_suffix }} $GIT_SHA
    ```

## 5. 服务器端部署脚本 (`deploy.sh`) 逻辑

在你的腾讯云 CVM 上的 `/home/<USER>/deploy.sh` (或你选择的路径) 脚本内容大致如下：

```bash
#!/binbash

set -e # 如果任何命令失败，则立即退出

ENVIRONMENT=$1 # 第一个参数: "staging" 或 "prod"
IMAGE_SHA=$2   # 第二个参数: Git Commit SHA

# TCR 配置 (根据实际情况修改)
TCR_REGISTRY_URL="ccr.ccs.tencentyun.com"
TCR_NAMESPACE="${TENCENT_CLOUD_NAMESPACE_SECRET}" # 从服务器环境变量或安全途径获取
APP_NAME="frontend"

# 根据环境确定相关文件名和参数
DOCKER_COMPOSE_FILE="docker-compose.${ENVIRONMENT}.yml"
ENV_FILE=".env.${ENVIRONMENT}" # 服务器上存储对应环境的环境变量文件
IMAGE_NAME_WITH_ENV_TAG="${TCR_REGISTRY_URL}/${TCR_NAMESPACE}/${APP_NAME}-${ENVIRONMENT}:${IMAGE_SHA}"
IMAGE_NAME_WITH_ENV_LATEST="${TCR_REGISTRY_URL}/${TCR_NAMESPACE}/${APP_NAME}-${ENVIRONMENT}:latest"
WEB_SERVICE_NAME="web" # docker-compose.yml 中定义的前端服务名

echo "Starting deployment for ${ENVIRONMENT} environment with image SHA: ${IMAGE_SHA}"

# 0. (可选) 从安全存储中加载 TCR_NAMESPACE 等敏感配置
# export $(grep -v '^#' /path/to/secure/server.conf | xargs)

# 1. 拉取最新的特定 SHA 标签的镜像
echo "Pulling image: ${IMAGE_NAME_WITH_ENV_TAG}"
docker pull "${IMAGE_NAME_WITH_ENV_TAG}"

# 2. (可选但推荐) 更新 latest 标签指向新的 SHA 镜像
echo "Tagging ${IMAGE_NAME_WITH_ENV_TAG} as ${IMAGE_NAME_WITH_ENV_LATEST}"
docker tag "${IMAGE_NAME_WITH_ENV_TAG}" "${IMAGE_NAME_WITH_ENV_LATEST}"

# 3. 使用 Docker Compose 部署
#    确保 docker-compose.{ENVIRONMENT}.yml 文件中的 web 服务 image 指向的是带 :latest 的镜像名
#    例如：image: ccr.ccs.tencentyun.com/your-namespace/frontend-staging:latest
echo "Bringing down old services (if any) defined in ${DOCKER_COMPOSE_FILE}..."
# docker-compose -f "${DOCKER_COMPOSE_FILE}" --env-file "${ENV_FILE}" down # 如果需要先停掉旧的

echo "Pulling latest images defined in ${DOCKER_COMPOSE_FILE} (especially for 'web' service)..."
docker-compose -f "${DOCKER_COMPOSE_FILE}" --env-file "${ENV_FILE}" pull "${WEB_SERVICE_NAME}" # 确保拉取到刚打好latest tag的镜像

echo "Starting new services with ${DOCKER_COMPOSE_FILE} and ${ENV_FILE}..."
docker-compose -f "${DOCKER_COMPOSE_FILE}" --env-file "${ENV_FILE}" up -d --remove-orphans "${WEB_SERVICE_NAME}" # 只更新 web 服务，--no-deps 可根据情况添加

echo "Deployment for ${ENVIRONMENT} completed successfully."

# 4. (可选) 清理旧的、未被使用的 Docker 镜像以释放空间
echo "Cleaning up old Docker images..."
docker image prune -a -f --filter "until=24h" # 删除24小时前创建的无用镜像
# 或者更精确地删除不带 'latest' 或当前 SHA 标签的同名环境镜像

exit 0
```
**重要**:
*   确保服务器上存在 `docker-compose.staging.yml`, `docker-compose.prod.yml` 文件。
*   确保服务器上存在对应的 `.env.staging`, `.env.prod` 文件，且这些文件**不应**提交到 Git 仓库。
*   `TENCENT_CLOUD_NAMESPACE_SECRET` 应从一个安全的地方（比如服务器的环境变量或一个受保护的配置文件）读取，而不是硬编码在脚本中。

## 6. 环境配置管理

### 6.1. GitHub Secrets

*   在 GitHub 仓库 `Settings -> Secrets and variables -> Actions` 中：
    *   **Repository secrets (用于全局或无特定环境的 secrets)**:
        *   `TENCENT_CLOUD_ACCOUNT_ID`: 腾讯云账号ID。
        *   `TENCENT_CLOUD_TCR_PASSWORD`: TCR 实例密码。
        *   `TENCENT_CLOUD_NAMESPACE`: 你的TCR命名空间。
    *   **Environments**: 创建 `staging` 和 `production` 两个环境。
        *   **Staging Environment Secrets**:
            *   `SERVER_HOST_STAGING`: 预生产服务器 IP/域名。
            *   `SERVER_USER_STAGING`: SSH 用户名。
            *   `SERVER_SSH_KEY_STAGING`: SSH 私钥。
            *   `DATABASE_URL_STAGING`: 预生产数据库连接串。
            *   `NEXT_PUBLIC_SITE_URL_STAGING`: 预生产站点URL。
            *   ... (所有其他预生产环境特定的构建时变量)
        *   **Production Environment Secrets**:
            *   `SERVER_HOST_PRODUCTION`: 生产服务器 IP/域名。
            *   `SERVER_USER_PRODUCTION`: SSH 用户名。
            *   `SERVER_SSH_KEY_PRODUCTION`: SSH 私钥。
            *   `DATABASE_URL_PRODUCTION`: 生产数据库连接串。
            *   `NEXT_PUBLIC_SITE_URL_PRODUCTION`: 生产站点URL。
            *   ... (所有其他生产环境特定的构建时变量)

### 6.2. 服务器端 `.env` 文件

*   在服务器的部署目录下 (e.g., `/home/<USER>
    *   `.env.staging`
    *   `.env.prod`
*   这些文件包含运行时所需的环境变量，特别是敏感信息，如数据库密码、API 密钥等。
*   **严禁将这些 `.env` 文件提交到 Git 仓库！** 使用 `.gitignore` 忽略它们。
*   `docker-compose.{env}.yml` 文件通过 `env_file: .env.{env}` 来加载这些变量。

### 6.3. Docker Compose 文件 (`docker-compose.staging.yml`, `docker-compose.prod.yml`)

*   为每个环境创建一个 Docker Compose 文件。
*   主要区别可能包括：
    *   **`image`**: `web` 服务的镜像应指向对应环境的 TCR 镜像 (e.g., `ccr.ccs.tencentyun.com/your-namespace/frontend-staging:latest`)。
    *   **`container_name`**: 为容器名添加环境后缀 (e.g., `ai9000-web-staging`)。
    *   **`ports`**: 为不同环境的服务映射到服务器的不同端口。
        *   Staging Web: `7239:3000` (示例)
        *   Production Web: `7238:3000` (沿用现有)
    *   **`env_file`**: 指向对应环境的 `.env` 文件 (e.g., `.env.staging`)。
    *   **`volumes`**: 数据库等有状态服务的挂载卷名可以加上环境后缀 (e.g., `ai9000-postgres-data-staging`)。
    *   **`networks`**: 可以为各环境定义不同的外部网络，实现网络隔离 (e.g., `ai9000-staging-network`)。

**示例 `docker-compose.staging.yml` (部分)**:
```yaml
services:
  postgres:
    image: postgres:16-alpine
    container_name: ai9000-db-staging
    restart: unless-stopped
    env_file:
      - .env.staging # 加载预生产环境的.env文件
    ports:
      - "5439:5432" # 预生产数据库使用不同端口
    volumes:
      - postgres_data_staging:/var/lib/postgresql/data
    # ... 其他配置

  web:
    image: ccr.ccs.tencentyun.com/${TENCENT_CLOUD_NAMESPACE_VARIABLE}/frontend-staging:latest # 使用变量或直接写死
    container_name: ai9000-web-staging
    restart: always
    env_file:
      - .env.staging
    ports:
      - "7239:3000" # 预生产前端使用不同端口
    depends_on:
      postgres:
        condition: service_healthy
    # ... 其他配置

volumes:
  postgres_data_staging:
    name: ai9000-postgres-data-staging

# networks:
#   default:
#     name: ai9000-network-staging
#     external: true # 假设你已手动创建了此网络
```

## 7. 注意事项与最佳实践

*   **安全第一**: 严格管理所有 Secrets (GitHub Actions Secrets, 服务器上的 `.env` 文件, SSH 密钥)。SSH 密钥应具有最小必要权限。
*   **回滚策略**: 如果部署出现问题，应能快速回滚。最简单的方式是重新触发 GitHub Actions 部署上一个成功的 Git Commit SHA。或在 `deploy.sh` 中增加回滚逻辑，重新指向带有旧SHA的镜像标签。
*   **数据库迁移**: 再次强调，自动化生产数据库迁移的风险。评估当前 `db:push` 方案是否满足生产稳定性要求，或考虑引入更成熟的迁移方案。定期备份数据库。
*   **充分测试**: 预生产环境的核心价值在于测试。确保在合并到 `main` 分支前，在预生产环境进行了全面的功能测试、集成测试和用户验收测试。
*   **监控与告警**: 为预生产和生产环境配置完善的应用性能监控 (APM)、日志收集和告警机制。
*   **资源隔离**: 考虑为不同环境使用不同的云资源实例（如独立的数据库实例），以实现更彻底的隔离，但这会增加成本。
*   **`Dockerfile` 优化**: 确保 `frontend/apps/web/dockerfile` 已优化，以减少镜像大小和构建时间。
*   **`deploy.sh` 增强**: 可以根据需要增强 `deploy.sh`，例如添加更详细的日志、部署前备份、健康检查、部署后通知等。

---

本文档提供了基于双环境（预生产、生产）的 CI/CD 流程规范。请根据项目实际情况进行调整和细化。