# 支付模块 WebSocket 集成方案

## 1. 引言

为了提升用户在支付过程中的体验，使其能够实时感知订单支付状态的变化，本项目计划集成 WebSocket 功能。通过 WebSocket，服务器可以在订单状态发生变化（如支付成功、失败）时，主动向客户端推送消息，客户端无需频繁轮询即可更新UI，从而提供更流畅、更实时的交互。

本文档旨在详细阐述在现有 Hono 后端及 React 前端基础上集成 WebSocket 实现支付状态实时通知的方案。

## 2. 架构概览

WebSocket 的集成将作为现有支付流程的增强补充。整体流程如下：

1.  **客户端发起订单**: 用户在前端选择套餐并点击支付。
2.  **创建订单 API**: 前端调用后端 `/api/v1/ecosystem/avatar/payment/create` (`create-order.ts`) 接口。
3.  **后端处理创建订单**:
    *   后端生成订单记录，状态为 `PENDING`。
    *   后端调用第三方支付网关（如微信支付）创建预付单，获取二维码URL或支付跳转链接。
    *   后端返回订单信息及支付凭证给前端。
4.  **客户端展示支付界面**: 前端展示二维码或引导用户跳转支付。
5.  **客户端建立 WebSocket 连接**: 前端在展示支付界面后，向后端发起 WebSocket 连接请求，并携带当前 `orderId` 或 `orderNo` 进行订阅。
6.  **用户支付**: 用户通过微信支付等方式完成支付。
7.  **支付网关 Webhook 通知**: 第三方支付网关在支付成功/失败后，通过预设的 Webhook URL 通知后端服务器。
8.  **后端 Webhook 处理**:
    *   后端接收 Webhook 通知，校验签名，解析通知内容。
    *   更新数据库中对应订单 (`Order`) 和支付记录 (`PaymentRecord`) 的状态（如 `PAID`, `FAILED`）。
9.  **WebSocket 实时通知**:
    *   后端根据 Webhook 中的 `orderNo` 或其他关联信息，查找与该订单关联的 WebSocket 连接。
    *   如果找到活动的 WebSocket 连接，则通过该连接向对应的客户端推送支付结果消息。
10. **客户端更新 UI**: 前端 WebSocket 客户端接收到消息，解析后更新支付对话框 (`PaymentDialog.tsx`) 的 UI，例如显示支付成功或失败状态。

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant BackendAPI as 后端API (Hono)
    participant PaymentGateway as 支付网关 (如微信)
    participant WebSocketServer as WebSocket服务器 (集成在后端)

    Client->>BackendAPI: POST /create (创建订单)
    BackendAPI->>PaymentGateway: 创建预付单
    PaymentGateway-->>BackendAPI: 返回支付凭证(二维码URL等)
    BackendAPI-->>Client: 返回订单信息和支付凭证
    Client->>WebSocketServer: 发起WebSocket连接 (携带orderId)
    WebSocketServer-->>Client: 连接已建立
    Client->>PaymentGateway: 用户扫码支付
    PaymentGateway-->>PaymentGateway: 处理支付
    PaymentGateway->>BackendAPI: Webhook通知 (支付结果)
    BackendAPI->>BackendAPI: 校验签名，更新订单状态
    BackendAPI->>WebSocketServer: 查找与orderId关联的连接
    WebSocketServer->>Client: 推送支付结果消息 (通过WebSocket)
    Client->>Client: 更新UI (支付成功/失败)
```

## 3. 后端实现 (Hono + ws)

Hono 本身不直接支持 WebSocket，但由于其可以在 Node.js 环境中运行 (例如通过 `@hono/node-server`)，我们可以结合流行的 `ws` 库来实现 WebSocket 服务器。

### 3.1. WebSocket 服务器搭建与集成

我们将使用 `ws` 库在 Hono 应用的 Node.js 服务器实例上附加一个 WebSocket 服务器。

**依赖安装**:
```bash
pnpm add ws
pnpm add -D @types/ws
```

**集成示例 (`src/app.ts` 或新的 `src/websocketServer.ts`):**

```typescript
// src/app.ts (或独立的 websocketServer.ts)
import { Hono } from 'hono';
import { serve } from '@hono/node-server';
import { WebSocketServer, WebSocket } from 'ws';
import http from 'http';
import url from 'url';

// ... (现有的 Hono app 初始化)
const app = new Hono();
// ... (注册你的路由和中间件)

// 创建 HTTP 服务器
const httpServer = http.createServer((req, res) => {
  serve({ fetch: app.fetch.bind(app), server: { // 注意这里的适配器用法可能需要根据具体版本调整
    on: (event, listener) => { /* no-op for direct http.createServer */ },
    emit: (event, ...args) => { /* no-op */ },
    // @ts-ignore some typings might not align perfectly
    handle: app.fetch.bind(app)
  } })(req, res);
});


// 创建 WebSocket 服务器并附加到 HTTP 服务器
const wss = new WebSocketServer({ noServer: true }); // 使用 noServer 模式

// 存储活动的连接 (可以根据 orderId 或 userId 进行映射)
// 注意：在生产环境中，对于多实例部署，需要使用外部存储如 Redis 来管理连接状态
const activeConnections = new Map<string, WebSocket>(); // key: orderId, value: WebSocket instance

httpServer.on('upgrade', (request, socket, head) => {
  const pathname = request.url ? url.parse(request.url).pathname : undefined;

  // 假设 WebSocket 服务的路径是 /ws/payment-status
  if (pathname === '/ws/payment-status') {
    wss.handleUpgrade(request, socket, head, (ws) => {
      wss.emit('connection', ws, request);
    });
  } else {
    // 对于非 WebSocket 请求，销毁 socket
    socket.destroy();
  }
});

wss.on('connection', (ws, request) => {
  // 从请求中获取 orderId, 例如通过查询参数 ?orderId=xxx
  const queryParams = new URLSearchParams(url.parse(request.url || '').search || '');
  const orderId = queryParams.get('orderId');

  if (!orderId) {
    logger.warn('[WebSocket] 连接请求缺少 orderId, 关闭连接');
    ws.close(1008, 'Order ID is required');
    return;
  }

  logger.info(`[WebSocket] 客户端连接成功 for orderId: ${orderId}`);
  activeConnections.set(orderId, ws);

  ws.on('message', (message) => {
    // 通常客户端连接后不需要主动发送消息，主要是接收服务器推送
    // 但可以实现心跳机制
    logger.info(`[WebSocket] 收到消息 from orderId ${orderId}: ${message}`);
    if (message.toString() === 'ping') {
      ws.send('pong');
    }
  });

  ws.on('close', () => {
    logger.info(`[WebSocket] 客户端断开连接 for orderId: ${orderId}`);
    activeConnections.delete(orderId);
  });

  ws.on('error', (error) => {
    logger.error(`[WebSocket] 连接错误 for orderId ${orderId}:`, error);
    activeConnections.delete(orderId); // 发生错误时也移除连接
  });
});

// 暴露一个函数用于从其他模块发送消息
export const sendPaymentStatusUpdate = (orderId: string, statusDetails: PaymentStatusMessage) => {
  const ws = activeConnections.get(orderId);
  if (ws && ws.readyState === WebSocket.OPEN) {
    ws.send(JSON.stringify(statusDetails));
    logger.info(`[WebSocket] 已发送支付状态更新给 orderId: ${orderId}`, statusDetails);
    return true;
  }
  logger.warn(`[WebSocket] 未找到或连接未打开 for orderId: ${orderId}, 无法发送更新`);
  return false;
};

// 启动 HTTP 服务器 (Hono 应用会通过它处理 HTTP 请求)
const port = process.env.PORT ? parseInt(process.env.PORT, 10) : 3000; // 或从配置读取
httpServer.listen(port, () => {
  logger.info(`Server is running on port ${port}`);
  logger.info(`WebSocket server is running and attached to HTTP server`);
});

// Hono 路由中不需要直接处理 /ws/payment-status，由 http.Server 的 'upgrade' 事件处理
// 如果需要 Hono 中间件（如认证）来保护 WebSocket 连接，会更复杂，
// 可能需要在 'upgrade' 事件处理中手动调用 Hono 的中间件链或进行 token 验证。
```

### 3.2. 连接管理

*   **关联**: 如上例所示，客户端连接时通过 URL 查询参数 (`?orderId=xxx`) 传递 `orderId`。服务器将 `WebSocket` 实例与 `orderId` 关联存储在 `activeConnections` Map 中。
*   **存储**:
    *   **单实例**: 使用内存中的 `Map` (如 `activeConnections`) 即可。
    *   **多实例 (生产环境)**: 当后端服务水平扩展部署多个实例时，内存存储将失效。需要将 WebSocket 连接信息（哪个用户/订单对应哪个后端实例的哪个连接）存储在共享服务中，例如：
        *   **Redis Pub/Sub**: Webhook 处理实例可以将消息发布到 Redis 特定频道 (e.g., `payment_status:${orderId}`), 其他持有该订单对应 WebSocket 连接的实例订阅此频道并推送。
        *   **Redis + 实例标记**: 存储 `orderId -> { instanceId, connectionId }` 映射，Webhook 处理实例查询此映射，然后通过内部机制（如另一个消息队列或直接 HTTP 请求）通知目标实例。
*   **心跳机制**: 为了保持连接活跃并检测断开，可以实现心跳。客户端定时发送 `ping`，服务器回复 `pong`。如果服务器在一定时间内未收到客户端的 `ping`，则认为连接已断开。

### 3.3. 消息处理 (Webhook 触发)

在 Webhook 处理逻辑中（通常是一个独立的 Hono 路由，例如 `/webhook/payment-gateway`），当确认支付成功或失败并更新数据库后，调用 `sendPaymentStatusUpdate` 函数。

**Webhook 处理伪代码 (`src/routes/webhooks/payment-notify.ts`):**

```typescript
// import { sendPaymentStatusUpdate } from '../../websocketServer'; // 假设导出
// ...
// 在处理完支付网关的通知，并更新数据库订单状态后
// ...

const order = await db.order.findUnique({ where: { orderNo: webhookData.order_no } });
if (order) {
  const messageToClient: PaymentStatusMessage = {
    event: "paymentStatusUpdate",
    data: {
      orderId: order.id,
      orderNo: order.orderNo,
      status: order.status, // 'PAID', 'FAILED', etc.
      amount: order.amount,
      timestamp: new Date().toLocaleString(),
      // 可以包含更多有用的信息
    }
  };
  sendPaymentStatusUpdate(order.id, messageToClient);
}
// ...
```

### 3.4. 安全性

*   **WSS (TLS)**: 在生产环境中，必须使用 `wss://` (WebSocket Secure) 协议，这意味着 WebSocket 服务器需要运行在 HTTPS 服务器之上。
*   **认证**: WebSocket 连接的建立也应该受到保护。
    *   **Token in Query Param**: 客户端连接时可以在查询参数中携带一个短效的认证令牌 (JWT 或其他 session token)。服务器在 `upgrade` 事件处理中验证此令牌。**注意**: Token 通过URL传递存在安全风险 (服务器日志、浏览器历史等)。
    *   **Cookie-based Authentication**: 如果HTTP服务使用cookie进行认证，WebSocket升级请求通常也会携带这些cookie，可以在`upgrade`时验证。
    *   **Origin Check**: 验证 `request.headers.origin` 是否来自允许的域。

### 3.5. Hono 路由集成

如上所述，WebSocket 的升级请求 (`Upgrade: websocket`) 通常由底层的 HTTP 服务器在 Hono 处理之前或并行处理。如果需要 Hono 的中间件（如CORS、日志、认证）应用于 WebSocket 的初始 HTTP 握手请求，可以在 `httpServer.on('upgrade', ...)` 中尝试构造一个 `Context` 对象并手动执行 Hono 的中间件栈，但这比较复杂。一个更简单的方法是在握手时进行独立的 token 验证。

## 4. 前端实现 (React)

前端需要实现 WebSocket 客户端逻辑，在支付对话框展示时连接，并在接收到消息后更新UI。

**示例 (`PaymentDialog.tsx` 或 `usePayment` Hook):**

```typescript
// usePayment.ts 或 PaymentDialog.tsx
import { useEffect, useRef, useState } from 'react';
// ...

// 假设 usePayment hook 管理支付状态
const usePayment = () => {
  // ... existing state and logic
  const [paymentWebSocket, setPaymentWebSocket] = useState<WebSocket | null>(null);
  const currentOrderIdRef = useRef<string | null>(null); // 保存当前处理的订单ID

  // 更新此函数以处理来自WebSocket的支付状态
  const handleWebSocketMessage = (event: MessageEvent) => {
    try {
      const message = JSON.parse(event.data as string) as PaymentStatusMessage;
      logger.info('[Payment WS] Received message:', message);

      if (message.event === 'paymentStatusUpdate' && message.data.orderId === currentOrderIdRef.current) {
        const newStatus = message.data.status; // e.g., 'PAID', 'FAILED'
        // 更新内部的支付状态，这将触发UI重新渲染
        // setPaymentStatus(newStatus as UIPaymentStatus); // 假设你有这样的状态
        // 如果是 PAID，可能需要调用 handlePaymentSuccess 或类似逻辑
        if (newStatus === 'PAID') {
          // ... (触发支付成功逻辑) ...
          // 确保与 PaymentDialog 中的 isOrderPaid 状态同步
          logger.info(`[Payment WS] Order ${message.data.orderId} is PAID.`);
        } else if (newStatus === 'FAILED' || newStatus === 'CANCELLED' || newStatus === 'TIMEOUT') {
          // ... (触发支付失败/取消/超时逻辑) ...
          logger.warn(`[Payment WS] Order ${message.data.orderId} status: ${newStatus}`);
        }
        // 关闭 WebSocket 连接，因为订单已终态
        paymentWebSocket?.close();
        setPaymentWebSocket(null);
      }
    } catch (error) {
      logger.error('[Payment WS] Error processing message:', error);
    }
  };

  // 建立 WebSocket 连接的函数
  const connectWebSocket = (orderId: string) => {
    if (paymentWebSocket && paymentWebSocket.readyState === WebSocket.OPEN) {
      // 如果已有连接，可能需要先关闭或判断是否是同一个orderId
      if (currentOrderIdRef.current === orderId) return;
      paymentWebSocket.close();
    }
    currentOrderIdRef.current = orderId;

    // 从配置获取 WebSocket URL (应包含协议 wss:// 或 ws://)
    const wsUrl = `${process.env.NEXT_PUBLIC_WEBSOCKET_URL}/ws/payment-status?orderId=${orderId}`;
    // 可以添加认证token: &token=YOUR_TOKEN

    logger.info(`[Payment WS] Connecting to ${wsUrl}`);
    const ws = new WebSocket(wsUrl);

    ws.onopen = () => {
      logger.info('[Payment WS] Connected for orderId:', orderId);
      setPaymentWebSocket(ws);
      // 可以实现心跳
      // ws.send('ping');
    };

    ws.onmessage = handleWebSocketMessage;

    ws.onerror = (error) => {
      logger.error('[Payment WS] Error:', error);
      // 可以在这里实现重连逻辑，但对于支付状态，通常订单终态后就不再需要连接
    };

    ws.onclose = (event) => {
      logger.info('[Payment WS] Connection closed:', event.code, event.reason);
      setPaymentWebSocket(null);
      currentOrderIdRef.current = null;
    };
  };

  // 在创建订单并获取到 orderId 后调用 connectWebSocket
  // 例如，在 PaymentDialog 的 useEffect 中，当展示二维码且有 orderId 时：
  /*
  useEffect(() => {
    if (showPaymentDialog && qrCodeDataUrl && orderId && !paymentWebSocket) {
      connectWebSocket(orderId);
    }
    // 组件卸载或订单完成时关闭连接
    return () => {
      if (paymentWebSocket) {
        paymentWebSocket.close();
      }
    };
  }, [showPaymentDialog, qrCodeDataUrl, orderId]); // 调整依赖项
  */

  // ... (返回 connectWebSocket 等)
};
```

**注意**: 前端应处理重连逻辑（如果适用，但对于一次性支付状态通知可能不是必须的）、错误处理和连接关闭。

## 5. 消息协议定义

定义清晰的消息结构非常重要。

**服务器 -> 客户端: 支付状态更新** (`PaymentStatusMessage`)

```typescript
// 可以放在 types.ts 或共享的 types 包中
interface PaymentStatusMessageData {
  orderId: string;        // 内部订单ID
  orderNo: string;        // 支付订单号 (通常与支付网关关联)
  status: string;         // 订单状态，例如 'PAID', 'PENDING', 'FAILED', 'CANCELLED', 'TIMEOUT'
                          // 最好与数据库中的 Order.status 枚举值一致
  amount?: number;        // 订单金额 (分)
  paymentMethod?: string; // 支付方式
  message?: string;       // 附加信息，如失败原因
  timestamp: string;      // ISO 8601 格式时间戳
}

interface PaymentStatusMessage {
  event: "paymentStatusUpdate"; // 消息事件类型
  data: PaymentStatusMessageData;
}
```

**客户端 -> 服务器: (可选，例如心跳或初始订阅)**

```typescript
// 心跳
// C: "ping"
// S: "pong"

// 初始订阅 (如果不在 URL 参数中传递 orderId)
/*
interface SubscribeMessage {
  event: "subscribe";
  data: {
    orderId: string;
    // authToken?: string; // 如果需要通过消息体认证
  };
}
*/
```

## 6. 与现有模块的集成

### 6.1. `create-order.ts`

*   此文件主要逻辑不变，它负责创建订单和预付单。
*   关键在于，当 Webhook 模块接收到支付网关的通知并成功处理（更新数据库）后，该 Webhook 处理逻辑需要调用 `sendPaymentStatusUpdate` 函数将状态变更推送给前端。

### 6.2. `router.ts` (`frontend/packages/api/src/routes/v1/ecosystem/avatar/payment/router.ts`)

*   **HTTP 路由**: 现有的 `/create`, `/query`, `/close` 保持不变。
*   **WebSocket 路由**: WebSocket 连接的建立（HTTP Upgrade 请求）不由 Hono 的标准路由处理，而是由附加到 Node.js `http.Server` 实例上的 `ws.WebSocketServer` 处理。如 `src/app.ts` 示例中，通过检查 `request.url` 的 `pathname` (例如 `/ws/payment-status`) 来区分 WebSocket 握手请求。

### 6.3. `schemas.ts` & `types.ts` (`frontend/packages/api/src/routes/v1/ecosystem/avatar/payment/`)

*   **`types.ts`**:
    *   可以复用现有的 `OrderType` 枚举。
    *   `QueryOrderResponse` 中的 `status` 字段可以作为 WebSocket 推送消息中状态字段的参考。
    *   需要新增上面定义的 `PaymentStatusMessage` 和 `PaymentStatusMessageData` 接口。
*   **`schemas.ts`**:
    *   主要是用于 HTTP API 的请求/响应体验证，对于 WebSocket 消息，如果需要验证，可以在服务器接收或发送前手动使用 Zod schema 进行验证。一般服务器推送给客户端的消息是内部生成的，可信度较高。

## 7. 部署与运维注意事项

*   **WSS (TLS/SSL)**: 生产环境必须使用 `wss://`。确保你的 Node.js 服务器配置了 TLS/SSL 证书，或者部署在支持 WebSocket 并能进行 SSL 终止的反向代理（如 Nginx, Caddy, AWS ELB/ALB）之后。
*   **负载均衡与多实例**:
    *   如果后端服务有多个实例，普通的 WebSocket 连接是持久的，会固定到某个实例。如果该实例重启或下线，连接会断开。
    *   **Sticky Sessions**: 负载均衡器需要配置粘性会话 (基于 IP 或 Cookie)，确保同一用户的后续 HTTP 请求和 WebSocket 连接请求路由到同一个后端实例。但这并不能完全解决 Webhook 通知到持有连接的实例的问题。
    *   **外部连接管理 (如 Redis)**: 如 3.2 节所述，这是更健壮的方案。Webhook 可以通知任何实例，该实例通过 Redis Pub/Sub 将消息广播给所有订阅了该订单的实例，或者直接查找持有连接的实例进行通知。
*   **资源消耗**: 每个 WebSocket 连接都会消耗服务器资源（主要是内存）。需要监控连接数和服务器性能。
*   **日志记录**: 详细记录 WebSocket 的连接、断开、错误、消息收发情况，对于调试和监控至关重要。
*   **安全性**: 除了 WSS 和认证，还要注意防范DDoS攻击等。
*   **客户端兼容性**: 大部分现代浏览器都支持 WebSocket，但仍需注意。
*   **防火墙**: 确保服务器和中间网络设备（防火墙、代理）允许 WebSocket 协议 (通常是 HTTP/1.1 Upgrade 请求，端口与 HTTP/HTTPS 相同)。

## 8. 总结

通过集成 `ws` 库到 Hono 的 Node.js 服务中，并结合 Webhook 机制，我们可以为支付模块实现高效的实时状态通知功能。关键在于正确管理 WebSocket 连接与订单的关联，并在多实例部署时考虑连接状态的共享与消息的路由。前端则需要相应地建立和管理 WebSocket 连接，并根据接收到的消息更新用户界面。

这套方案将显著改善用户支付体验，减少客户端不必要的轮询，并使系统状态更加实时同步。 