# 9000AI 部署指南

## 简介

本文档详细说明9000AI平台的部署流程，包括环境准备、服务配置、部署步骤和维护操作。平台采用Docker和Docker Compose进行容器化部署，支持开发、测试和生产环境。

## 部署架构

### 服务组成

9000AI平台由以下核心服务组成:

1. **前端服务 (`web`)**:
   - Next.js应用
   - 提供Web界面

2. **后端API服务 (`backend`)**:
   - FastAPI应用
   - 提供REST API

3. **工作者服务 (`worker`)**:
   - 处理异步任务
   - 多实例并行

4. **数据库服务 (`postgres`)**:
   - PostgreSQL 16
   - 持久化存储

5. **消息队列 (`rabbitmq`)**:
   - RabbitMQ 3.12
   - 任务调度

6. **对象存储 (`minio`)**:
   - MinIO
   - 存储媒体文件

7. **邮件服务 (`maildev`, 开发环境)**:
   - 邮件测试

8. **管理工具**:
   - PgAdmin (数据库管理)
   - RabbitMQ Management (队列管理)
   - MinIO Console (存储管理)

9. **内网穿透服务 (开发环境)**:
   - Ngrok
   - 提供本地服务的公网访问

### 部署架构图

```
                  ┌─────────────┐
                  │   客户端    │
                  └──────┬──────┘
                         │
                         ▼
┌───────────────────────────────────────────┐
│                负载均衡器                  │
└─┬─────────────────┬──────────────────────┬┘
  │                 │                      │
  ▼                 ▼                      ▼
┌────────┐     ┌────────┐             ┌────────┐
│  Web 1  │     │  Web 2  │    ...     │  Web n  │
└────┬───┘     └────┬───┘             └────┬───┘
     │              │                      │
     └──────────────┼──────────────────────┘
                    │
                    ▼
┌───────────────────────────────────────────┐
│                API网关                    │
└─┬─────────────────┬──────────────────────┬┘
  │                 │                      │
  ▼                 ▼                      ▼
┌────────┐     ┌────────┐             ┌────────┐
│ API 1  │     │ API 2  │    ...     │ API n  │
└────┬───┘     └────┬───┘             └────┬───┘
     │              │                      │
     └──────────────┼──────────────────────┘
                    │
     ┌──────────────┼──────────────────────┐
     │              │                      │
     ▼              ▼                      ▼
┌────────┐     ┌────────┐             ┌────────┐
│Worker 1│     │Worker 2│    ...     │Worker n│
└────────┘     └────────┘             └────────┘

┌─────────────┐ ┌─────────────┐ ┌─────────────┐
│  PostgreSQL │ │   RabbitMQ  │ │    MinIO    │
└─────────────┘ └─────────────┘ └─────────────┘
```

## 环境要求

### 硬件要求

#### 最小配置(开发/测试环境)
- CPU: 4核
- 内存: 8GB
- 存储: 50GB SSD

#### 推荐配置(生产环境)
- CPU: 8+核
- 内存: 16+GB
- 存储: 200+GB SSD

### 软件要求

- Docker 20.10+
- Docker Compose 2.0+
- 操作系统: Ubuntu 20.04+/CentOS 8+
- 网络: 稳定的互联网连接

## 部署准备

### 安装Docker和Docker Compose

**Ubuntu**:
```bash
# 更新包索引
sudo apt update

# 安装依赖
sudo apt install -y apt-transport-https ca-certificates curl software-properties-common

# 添加Docker官方GPG密钥
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo apt-key add -

# 添加Docker仓库
sudo add-apt-repository "deb [arch=amd64] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable"

# 安装Docker
sudo apt update
sudo apt install -y docker-ce docker-ce-cli containerd.io

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.15.1/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 将当前用户添加到docker组
sudo usermod -aG docker $USER
```

**CentOS**:
```bash
# 安装依赖
sudo yum install -y yum-utils device-mapper-persistent-data lvm2

# 添加Docker仓库
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo

# 安装Docker
sudo yum install -y docker-ce docker-ce-cli containerd.io

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.15.1/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 将当前用户添加到docker组
sudo usermod -aG docker $USER
```

### 环境变量配置

1. **复制环境变量模板**:
```bash
cp .env.example .env
```

2. **编辑环境变量文件**:
```bash
# 使用编辑器打开.env文件
nano .env
```

3. **关键环境变量说明**:

| 变量名 | 描述 | 示例值 |
|-------|------|-------|
| `POSTGRES_USER` | PostgreSQL用户名 | `postgres` |
| `POSTGRES_PASSWORD` | PostgreSQL密码 | `your_secure_password` |
| `POSTGRES_DB` | PostgreSQL数据库名 | `ai9000_production` |
| `RABBITMQ_USER` | RabbitMQ用户名 | `rabbitmq` |
| `RABBITMQ_PASS` | RabbitMQ密码 | `your_secure_password` |
| `MINIO_ROOT_USER` | MinIO管理员用户名 | `minioadmin` |
| `MINIO_ROOT_PASSWORD` | MinIO管理员密码 | `your_secure_password` |
| `API_SECRET_KEY` | API服务密钥 | `your_secure_api_secret` |
| `BETTER_AUTH_SECRET` | 认证系统密钥 | `your_secure_auth_secret` |
| `NEXT_PUBLIC_SITE_URL` | 站点URL | `https://example.com` |
| `OPENAI_API_KEY` | OpenAI API密钥 | `sk-...` |
| `WECHAT_PAY_*` | 微信支付相关配置 | 见详细说明 |
| `ALIPAY_*` | 支付宝相关配置 | 见详细说明 |

4. **支付相关配置**:

微信支付配置:
```
WECHAT_PAY_APP_ID=your_app_id
WECHAT_PAY_MCH_ID=your_merchant_id
WECHAT_PAY_SERIAL_NO=your_serial_number
WECHAT_PAY_API_V3_KEY=your_api_v3_key
WECHAT_PAY_AGENT_PLAN_URL=https://example.com/api/payment/wechat/notify
WECHAT_PAY_REFUND_NOTIFY_URL=https://example.com/api/payment/wechat/refund-notify
WECHAT_PAY_PRIVATE_KEY=your_private_key_content
```

支付宝配置:
```
ALIPAY_APP_ID=your_app_id
ALIPAY_PRIVATE_KEY=your_private_key_content
ALIPAY_PUBLIC_KEY=your_public_key_content
ALIPAY_NOTIFY_URL=https://example.com/api/payment/alipay/notify
```

## 部署步骤

### 开发环境部署

开发环境通常在本地运行，用于开发和测试:

```bash
# 启动所有服务
docker compose up -d

# 检查服务状态
docker compose ps

# 查看日志
docker compose logs -f

# 停止所有服务
docker compose down
```

### 开发环境内网穿透设置

为了便于外部访问本地开发的API服务（如移动端测试、第三方回调等），我们集成了Ngrok内网穿透功能：

1. **安装Ngrok客户端**:
   ```bash
   # Windows (使用Chocolatey)
   choco install ngrok
   
   # MacOS (使用Homebrew)
   brew install ngrok
   
   # Linux
   snap install ngrok
   ```

2. **配置Ngrok认证**:
   ```bash
   ngrok config add-authtoken YOUR_AUTH_TOKEN
   ```

3. **配置环境变量**:
   在项目根目录的`.env`文件中添加：
   ```
   # Ngrok内网穿透配置
   ENABLE_NGROK=1
   NGROK_AUTH_TOKEN=your_auth_token
   NGROK_DOMAIN=your-domain.ngrok-free.app  # 如果有固定域名
   NGROK_REGION=us  # 选择最近的区域
   ```

4. **启动带内网穿透的API服务**:
   ```bash
   # Windows
   .\backend\start_with_ngrok.ps1
   
   # Linux/Mac
   ./backend/start_with_ngrok.sh
   ```

5. **查看公网访问地址**:
   启动成功后，控制台会显示类似以下信息：
   ```
   🌐 公网访问地址: https://your-domain.ngrok-free.app
   ```

> 注意：
> - 免费版Ngrok有连接数和带宽限制
> - 每个Ngrok账户可以申请一个免费的固定域名
> - 详细使用方法请参考[Ngrok内网穿透集成指南](./ngrok_integration_guide.md)

### 测试环境部署

测试环境通常用于功能验证和集成测试:

```bash
# 复制测试环境配置
cp .env.example .env.test
nano .env.test  # 编辑配置

# 构建镜像
docker compose -f docker-compose.yml -f docker-compose.test.yml build

# 启动服务
docker compose -f docker-compose.yml -f docker-compose.test.yml --env-file .env.test up -d

# 检查服务状态
docker compose -f docker-compose.yml -f docker-compose.test.yml ps

# 停止服务
docker compose -f docker-compose.yml -f docker-compose.test.yml down
```

### 生产环境部署

生产环境需要更谨慎的配置和部署:

1. **准备生产环境变量**:
```bash
cp .env.example .env.production
nano .env.production  # 配置生产环境变量
```

2. **构建生产镜像**:
```bash
docker compose -f docker-compose.yml -f docker-compose.prod.yml build
```

3. **启动生产服务**:
```bash
docker compose -f docker-compose.yml -f docker-compose.prod.yml --env-file .env.production up -d
```

4. **验证服务状态**:
```bash
docker compose -f docker-compose.yml -f docker-compose.prod.yml ps
```

5. **初始化数据库**:
```bash
# 将自动运行初始化脚本
docker compose -f docker-compose.yml -f docker-compose.prod.yml exec postgres psql -U ${POSTGRES_USER} -d ${POSTGRES_DB} -c "SELECT 1"
```

### 部署后验证

部署完成后，需要进行以下验证:

1. **网站可访问性**:
   - 前端网站能正常访问
   - 登录/注册功能正常

2. **API服务验证**:
   - 健康检查端点返回正常 (`/health`)
   - API认证功能正常

3. **异步任务验证**:
   - Worker服务正常运行
   - 消息队列连接正常

4. **数据持久化验证**:
   - 数据库连接正常
   - 数据正确存储

## 维护操作

### 备份与恢复

#### 数据库备份

**手动备份**:
```bash
# 备份到本地文件
docker compose exec postgres pg_dump -U ${POSTGRES_USER} ${POSTGRES_DB} > backup_$(date +%Y%m%d).sql

# 压缩备份文件
gzip backup_$(date +%Y%m%d).sql
```

**自动定时备份**:

创建备份脚本 `backup.sh`:
```bash
#!/bin/bash
BACKUP_DIR="/path/to/backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/backup_$TIMESTAMP.sql.gz"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
docker compose exec -T postgres pg_dump -U $POSTGRES_USER $POSTGRES_DB | gzip > $BACKUP_FILE

# 保留最近30天的备份
find $BACKUP_DIR -name "backup_*.sql.gz" -type f -mtime +30 -delete
```

设置定时任务:
```bash
chmod +x backup.sh
(crontab -l 2>/dev/null; echo "0 2 * * * /path/to/backup.sh") | crontab -
```

#### 数据库恢复

```bash
# 从未压缩的备份文件恢复
cat backup_file.sql | docker compose exec -T postgres psql -U ${POSTGRES_USER} ${POSTGRES_DB}

# 从压缩的备份文件恢复
zcat backup_file.sql.gz | docker compose exec -T postgres psql -U ${POSTGRES_USER} ${POSTGRES_DB}
```

#### MinIO对象存储备份

MinIO可以使用`mc`客户端进行备份:

```bash
# 安装MinIO客户端
wget https://dl.min.io/client/mc/release/linux-amd64/mc
chmod +x mc
sudo mv mc /usr/local/bin/

# 配置MinIO客户端
mc alias set myminio http://localhost:${MINIO_API_PORT} ${MINIO_ROOT_USER} ${MINIO_ROOT_PASSWORD}

# 备份特定存储桶
mc mirror myminio/avatars /path/to/backup/avatars

# 恢复存储桶
mc mirror /path/to/backup/avatars myminio/avatars
```

### 日志管理

#### 查看服务日志

```bash
# 查看所有服务日志
docker compose logs -f

# 查看特定服务日志
docker compose logs -f web
docker compose logs -f backend
docker compose logs -f worker
```

#### 日志轮转配置

在生产环境中，应配置日志轮转以避免日志文件过大:

编辑Docker配置 `/etc/docker/daemon.json`:
```json
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m",
    "max-file": "5"
  }
}
```

重启Docker服务:
```bash
sudo systemctl restart docker
```

### 服务扩展

根据负载需求，可以扩展特定服务的实例数量:

```bash
# 扩展Worker服务实例
docker compose up -d --scale worker=3

# 扩展API服务实例(需要配合负载均衡)
docker compose up -d --scale backend=2
```

### 服务升级

#### 升级流程

1. **备份数据**:
```bash
# 备份数据库
docker compose exec postgres pg_dump -U ${POSTGRES_USER} ${POSTGRES_DB} > backup_before_upgrade.sql
```

2. **拉取最新代码**:
```bash
git pull origin main
```

3. **构建新镜像**:
```bash
docker compose build
```

4. **优雅重启服务**:
```bash
docker compose up -d
```

#### 回滚策略

如果升级后出现问题，可以回滚到之前的版本:

```bash
# 回退代码
git checkout <previous_commit>

# 重建镜像
docker compose build

# 重启服务
docker compose up -d

# 如需恢复数据库
cat backup_before_upgrade.sql | docker compose exec -T postgres psql -U ${POSTGRES_USER} ${POSTGRES_DB}
```

## 监控与告警

### 基础监控设置

使用Docker原生工具监控容器状态:

```bash
# 检查容器资源使用情况
docker stats

# 检查容器健康状态
docker compose ps
```

### 高级监控配置

生产环境建议使用专业监控工具:

1. **Prometheus + Grafana**:
   - 监控系统资源使用
   - 监控应用性能指标
   - 设置阈值告警

2. **ELK堆栈**:
   - 集中收集日志
   - 日志分析和可视化
   - 异常日志告警

### 健康检查配置

所有服务都应该实现健康检查端点，可以使用以下脚本定期检查:

```bash
#!/bin/bash

# 检查API服务
API_HEALTH=$(curl -s http://localhost:9527/health | jq -r '.status')
if [ "$API_HEALTH" != "healthy" ]; then
  echo "API服务异常"
  # 发送告警
fi

# 检查Web服务
WEB_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000)
if [ "$WEB_STATUS" != "200" ]; then
  echo "Web服务异常"
  # 发送告警
fi

# 检查数据库连接
DB_CHECK=$(docker compose exec -T postgres pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB})
if [ $? -ne 0 ]; then
  echo "数据库连接异常"
  # 发送告警
fi
```

将脚本设置为定时任务:
```bash
(crontab -l 2>/dev/null; echo "*/5 * * * * /path/to/healthcheck.sh") | crontab -
```

## 安全配置

### 网络安全

1. **使用HTTPS**:
   - 配置SSL证书
   - 重定向HTTP到HTTPS
   - 启用HSTS

2. **防火墙配置**:
```bash
# 仅开放必要端口
sudo ufw allow ssh
sudo ufw allow http
sudo ufw allow https
sudo ufw enable
```

3. **使用反向代理**:

Nginx配置示例:
```nginx
server {
    listen 80;
    server_name example.com;
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name example.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://web:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /api {
        proxy_pass http://backend:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 容器安全

1. **最小化容器权限**:
   - 使用非root用户运行容器
   - 限制容器资源

2. **定期更新容器镜像**:
```bash
docker compose pull
docker compose up -d
```

3. **定期扫描镜像漏洞**:
```bash
# 使用工具如Trivy扫描镜像
trivy image 9000ai-web:latest
trivy image 9000ai-backend:latest
```

### 数据安全

1. **敏感数据加密**:
   - 确保环境变量安全存储
   - 敏感数据在数据库中加密

2. **定期备份**:
   - 数据库备份
   - 对象存储备份
   - 备份文件加密

3. **限制数据访问**:
   - 最小化数据库权限
   - API访问控制
   - 审计日志记录

## 故障排查

### 常见问题解决方案

1. **容器无法启动**:
```bash
# 检查容器日志
docker compose logs <service_name>

# 检查环境变量
docker compose config
```

2. **数据库连接问题**:
```bash
# 检查数据库容器状态
docker compose ps postgres

# 检查数据库日志
docker compose logs postgres

# 手动连接测试
docker compose exec postgres psql -U ${POSTGRES_USER} -d ${POSTGRES_DB}
```

3. **API服务异常**:
```bash
# 检查API服务日志
docker compose logs backend

# 验证API健康检查
curl http://localhost:9527/health
```

4. **消息队列问题**:
```bash
# 检查RabbitMQ状态
docker compose ps rabbitmq

# 检查RabbitMQ日志
docker compose logs rabbitmq

# 访问管理界面
# 浏览器访问 http://localhost:15672
```

### 紧急响应流程

1. **服务中断**:
   - 检查日志确定问题
   - 重启受影响服务
   - 如故障持续，回滚到稳定版本

2. **数据库故障**:
   - 检查数据库连接和日志
   - 如有数据损坏，使用最近备份恢复
   - 数据库服务完整性检查

3. **存储问题**:
   - 检查MinIO服务状态
   - 验证存储空间
   - 恢复必要数据

### 联系支持

如遇到无法解决的问题，请联系技术支持团队:

- 技术支持邮箱: <EMAIL>
- 紧急联系电话: +86-XXX-XXXX-XXXX

## 扩展部署

### 负载均衡配置

使用Nginx作为负载均衡器:

```nginx
upstream web_servers {
    server web1:3000;
    server web2:3000;
    server web3:3000;
}

upstream api_servers {
    server backend1:8000;
    server backend2:8000;
    server backend3:8000;
}

server {
    listen 80;
    server_name example.com;
    
    location / {
        proxy_pass http://web_servers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /api {
        proxy_pass http://api_servers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 多环境配置

创建不同环境的配置文件:

1. **开发环境**: `docker-compose.yml`
2. **测试环境**: `docker-compose.yml` + `docker-compose.test.yml`
3. **预生产环境**: `docker-compose.yml` + `docker-compose.staging.yml`
4. **生产环境**: `docker-compose.yml` + `docker-compose.prod.yml`

部署示例:
```bash
# 开发环境
docker compose -f docker-compose.yml up -d

# 测试环境
docker compose -f docker-compose.yml -f docker-compose.test.yml --env-file .env.test up -d

# 预生产环境
docker compose -f docker-compose.yml -f docker-compose.staging.yml --env-file .env.staging up -d

# 生产环境
docker compose -f docker-compose.yml -f docker-compose.prod.yml --env-file .env.production up -d
```

### Kubernetes部署

随着项目规模增长，可以考虑迁移到Kubernetes:

1. **准备Kubernetes清单文件**
2. **使用Helm Chart打包应用**
3. **配置CI/CD管道自动部署**

## 参考资料

- [Docker官方文档](https://docs.docker.com/)
- [Docker Compose官方文档](https://docs.docker.com/compose/)
- [PostgreSQL官方文档](https://www.postgresql.org/docs/)
- [RabbitMQ官方文档](https://www.rabbitmq.com/documentation.html)
- [MinIO官方文档](https://docs.min.io/)
- [Nginx官方文档](https://nginx.org/en/docs/)

---

本文档最后更新: [日期] 