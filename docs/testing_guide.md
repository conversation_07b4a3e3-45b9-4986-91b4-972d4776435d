# 9000AI平台测试指南

## 简介

本文档详细说明9000AI平台的测试策略、测试类型、工具和流程。测试在确保平台稳定性、性能和用户体验方面起着关键作用，是开发流程的核心环节。

## 测试策略概述

9000AI平台采用多层次、全方位的测试策略：

1. **单元测试**: 验证独立代码单元的功能
2. **集成测试**: 验证组件间的交互和数据流
3. **API测试**: 验证API接口的功能和性能
4. **UI测试**: 验证用户界面和用户体验
5. **性能测试**: 验证系统在不同负载下的表现
6. **安全测试**: 验证系统的安全性和防护能力

## 前端测试

### 测试工具和框架

- **单元测试**: Vitest + React Testing Library
- **组件测试**: Storybook + Chromatic
- **端到端测试**: Cypress
- **快照测试**: Jest

### 测试覆盖目标

- 关键业务逻辑: 100%
- UI组件: 95%
- 页面交互: 80%
- 整体代码: 75%

### 单元测试

单元测试主要针对工具函数、钩子函数和独立逻辑：

```typescript
// src/utils/format.test.ts
import { describe, it, expect } from 'vitest';
import { formatCurrency, formatDate } from './format';

describe('formatCurrency', () => {
  it('should format numbers to CNY correctly', () => {
    expect(formatCurrency(1000)).toBe('¥1,000.00');
    expect(formatCurrency(1000.5)).toBe('¥1,000.50');
  });
  
  it('should handle zero and negative values', () => {
    expect(formatCurrency(0)).toBe('¥0.00');
    expect(formatCurrency(-100)).toBe('-¥100.00');
  });
});
```

### 组件测试

使用React Testing Library测试组件：

```typescript
// components/Button/Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from './Button';

describe('Button component', () => {
  it('renders correctly with text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button')).toHaveTextContent('Click me');
  });
  
  it('calls onClick handler when clicked', () => {
    const handleClick = vi.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
  
  it('applies variant styles correctly', () => {
    render(<Button variant="primary">Primary</Button>);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('button-primary');
  });
});
```

### Storybook组件文档

使用Storybook记录和测试UI组件：

```typescript
// components/Button/Button.stories.tsx
import type { Meta, StoryObj } from '@storybook/react';
import { Button } from './Button';

const meta: Meta<typeof Button> = {
  component: Button,
  title: 'UI/Button',
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'primary', 'danger'],
      description: '按钮风格变体'
    }
  }
};

export default meta;
type Story = StoryObj<typeof Button>;

export const Default: Story = {
  args: {
    children: '默认按钮',
    variant: 'default'
  }
};

export const Primary: Story = {
  args: {
    children: '主要按钮',
    variant: 'primary'
  }
};
```

### 端到端测试

使用Cypress测试用户流程：

```javascript
// cypress/e2e/login.cy.js
describe('登录功能', () => {
  beforeEach(() => {
    cy.visit('/auth/login');
  });
  
  it('应该在有效凭据时登录成功', () => {
    cy.intercept('POST', '/api/v1/auth/login', {
      statusCode: 200,
      body: {
        code: 200,
        message: 'Login successful',
        data: {
          access_token: 'fake-token',
          user: { name: '测试用户' }
        }
      }
    }).as('loginRequest');
    
    cy.get('input[name="username"]').type('<EMAIL>');
    cy.get('input[name="password"]').type('password123');
    cy.get('button[type="submit"]').click();
    
    cy.wait('@loginRequest');
    cy.url().should('include', '/agent/dashboard');
    cy.contains('欢迎回来，测试用户');
  });
  
  it('应该在无效凭据时显示错误消息', () => {
    cy.intercept('POST', '/api/v1/auth/login', {
      statusCode: 401,
      body: {
        code: 401,
        message: '用户名或密码错误'
      }
    }).as('loginFailure');
    
    cy.get('input[name="username"]').type('<EMAIL>');
    cy.get('input[name="password"]').type('wrongpass');
    cy.get('button[type="submit"]').click();
    
    cy.wait('@loginFailure');
    cy.contains('用户名或密码错误');
    cy.url().should('include', '/auth/login');
  });
});
```

### 运行前端测试

```bash
# 运行所有单元测试
pnpm test

# 运行特定测试文件
pnpm test -- components/Button/Button.test.tsx

# 观察模式
pnpm test:watch

# 生成覆盖率报告
pnpm test:coverage

# 运行端到端测试
pnpm cypress:run

# 启动Storybook
pnpm storybook
```

## 后端测试

### 测试工具和框架

- **单元测试**: pytest
- **API测试**: pytest + requests
- **性能测试**: Locust
- **代码覆盖率**: pytest-cov

### 测试覆盖目标

- 核心业务逻辑: 95%
- API端点: 90%
- 数据访问层: 85%
- 整体代码: 80%

### 单元测试

使用pytest测试独立函数和类：

```python
# tests/utils/test_validators.py
import pytest
from backend.utils.validators import validate_email, validate_phone

def test_validate_email():
    # 有效邮箱测试
    assert validate_email('<EMAIL>') == True
    assert validate_email('<EMAIL>') == True
    
    # 无效邮箱测试
    assert validate_email('not-an-email') == False
    assert validate_email('missing@domain') == False
    assert validate_email('@example.com') == False

def test_validate_phone():
    # 有效中国手机号
    assert validate_phone('13800138000') == True
    assert validate_phone('+8613800138000') == True
    
    # 无效手机号
    assert validate_phone('1380013800') == False  # 位数不对
    assert validate_phone('23800138000') == False  # 非法前缀
```

### API测试

测试API端点：

```python
# tests/api/test_auth_api.py
import pytest
from fastapi.testclient import TestClient
from backend.main import app

client = TestClient(app)

def test_login_success():
    response = client.post(
        "/api/v1/auth/login",
        json={"username": "<EMAIL>", "password": "password123", "role": "AGENT"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 200
    assert "access_token" in data["data"]
    assert "refresh_token" in data["data"]
    assert data["data"]["user"]["role"] == "AGENT"

def test_login_invalid_credentials():
    response = client.post(
        "/api/v1/auth/login",
        json={"username": "<EMAIL>", "password": "wrongpassword", "role": "AGENT"}
    )
    assert response.status_code == 401
    data = response.json()
    assert data["code"] == 401
    assert "用户名或密码错误" in data["message"]
```

### 数据库测试

测试数据库操作：

```python
# tests/db/test_user_repository.py
import pytest
from backend.db.repositories.user_repository import UserRepository
from backend.models.user import User

@pytest.fixture
def user_repo():
    return UserRepository()

@pytest.mark.asyncio
async def test_create_user(user_repo, db_session):
    user = User(
        name="测试用户",
        email="<EMAIL>",
        phone="13800138000",
        role="AGENT"
    )
    
    created_user = await user_repo.create(db_session, user)
    assert created_user.id is not None
    assert created_user.name == "测试用户"
    assert created_user.email == "<EMAIL>"
    
    # 验证用户已保存到数据库
    found_user = await user_repo.get_by_email(db_session, "<EMAIL>")
    assert found_user is not None
    assert found_user.id == created_user.id
```

### 使用测试数据库

```python
# tests/conftest.py
import pytest
import asyncio
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from backend.db.base import Base

@pytest.fixture(scope="session")
def event_loop():
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="session")
async def db_engine():
    # 使用测试用SQLite内存数据库
    engine = create_async_engine("sqlite+aiosqlite:///:memory:")
    
    # 创建所有表
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    yield engine
    
    # 清理
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
    
    await engine.dispose()

@pytest.fixture
async def db_session(db_engine):
    async_session = sessionmaker(
        db_engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with async_session() as session:
        yield session
        await session.rollback()
```

### 运行后端测试

```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/api/test_auth_api.py

# 运行指定测试
pytest tests/api/test_auth_api.py::test_login_success

# 生成覆盖率报告
pytest --cov=backend tests/

# 并行运行测试
pytest -xvs -n 4 tests/
```

## 集成测试

### API到数据库的集成测试

测试API调用到数据库操作的完整流程：

```python
# tests/integration/test_agent_flow.py
import pytest
from fastapi.testclient import TestClient
from backend.main import app
from backend.db.repositories.agent_repository import AgentRepository

client = TestClient(app)

@pytest.mark.integration
def test_create_agent_integration(db_session, auth_headers):
    # 1. 通过API创建代理商
    response = client.post(
        "/api/v1/agents",
        headers=auth_headers,
        json={
            "name": "新代理商",
            "email": "<EMAIL>",
            "phone": "13900139000",
            "level": "PARTNER",
            "parent_id": "usr_123456"
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 200
    new_agent_id = data["data"]["id"]
    
    # 2. 验证数据库记录
    agent_repo = AgentRepository()
    agent = agent_repo.get_by_id(db_session, new_agent_id)
    assert agent is not None
    assert agent.name == "新代理商"
    assert agent.email == "<EMAIL>"
    assert agent.level == "PARTNER"
    
    # 3. 通过API获取并验证
    response = client.get(
        f"/api/v1/agents/{new_agent_id}",
        headers=auth_headers
    )
    assert response.status_code == 200
    data = response.json()
    assert data["data"]["name"] == "新代理商"
    assert data["data"]["level"] == "PARTNER"
```

### 消息队列集成测试

测试消息生产和消费：

```python
# tests/integration/test_message_queue.py
import pytest
import asyncio
from backend.mq.producer import MessageProducer
from backend.mq.consumer import MessageConsumer

@pytest.mark.integration
@pytest.mark.asyncio
async def test_message_queue_integration():
    # 设置测试消息
    test_message = {
        "task_type": "TEST_TASK",
        "payload": {"test_key": "test_value"},
        "priority": 1
    }
    
    # 创建消费者处理函数
    received_messages = []
    
    async def test_handler(message):
        received_messages.append(message)
        
    # 初始化生产者和消费者
    producer = MessageProducer()
    consumer = MessageConsumer({"TEST_TASK": test_handler})
    
    # 启动消费者
    await consumer.start()
    
    # 发送消息
    await producer.send_message("test_queue", test_message)
    
    # 等待消息处理
    for _ in range(5):
        if received_messages:
            break
        await asyncio.sleep(0.5)
    
    # 关闭连接
    await consumer.stop()
    await producer.close()
    
    # 验证消息接收和处理
    assert len(received_messages) == 1
    assert received_messages[0]["task_type"] == "TEST_TASK"
    assert received_messages[0]["payload"]["test_key"] == "test_value"
```

## 性能测试

### 负载测试

使用Locust进行负载测试：

```python
# locustfile.py
from locust import HttpUser, task, between

class WebsiteUser(HttpUser):
    wait_time = between(1, 3)
    
    def on_start(self):
        # 登录获取token
        response = self.client.post(
            "/api/v1/auth/login",
            json={
                "username": "<EMAIL>",
                "password": "password123",
                "role": "AGENT"
            }
        )
        data = response.json()
        self.token = data["data"]["access_token"]
        self.headers = {"Authorization": f"Bearer {self.token}"}
    
    @task(1)
    def view_agents(self):
        self.client.get("/api/v1/agents", headers=self.headers)
    
    @task(3)
    def view_consumers(self):
        self.client.get("/api/v1/consumers", headers=self.headers)
    
    @task(2)
    def view_orders(self):
        self.client.get("/api/v1/orders", headers=self.headers)
```

运行负载测试：

```bash
# 启动Locust
locust -f locustfile.py --host=http://localhost:9527

# 通过Web界面配置用户数和孵化率
# 浏览器访问: http://localhost:8089/
```

### 性能测试目标

API响应时间目标：

| API类型 | P95响应时间 | P99响应时间 |
|--------|------------|------------|
| 查询操作 | < 300ms | < 500ms |
| 创建操作 | < 500ms | < 800ms |
| 文件上传 | < 2s | < 3s |
| 复杂计算 | < 1s | < 1.5s |

并发用户支持：
- 测试环境: 50并发用户
- 生产环境: 500并发用户

### 性能测试报告

每次发布前进行性能测试，并生成报告包含以下内容：
- 响应时间统计 (平均、中位数、P95、P99)
- 每秒请求数 (RPS)
- 错误率
- 资源使用情况 (CPU、内存、网络)
- 与基准测试的比较
- 性能瓶颈分析

## 安全测试

### 常见安全漏洞检测

- **OWASP Top 10**漏洞检测
- SQL注入测试
- XSS (跨站脚本)测试
- CSRF (跨站请求伪造)测试
- 敏感数据泄露检测
- 认证和权限测试

### 安全扫描工具

- OWASP ZAP: Web应用安全扫描
- SonarQube: 代码质量和安全分析
- npm audit / pip-audit: 依赖项安全检查

## 测试自动化与CI/CD

### GitHub Actions工作流

```yaml
# .github/workflows/tests.yml
name: Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  frontend-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: 18
        cache: 'pnpm'
    
    - name: Install dependencies
      run: pnpm install
    
    - name: Lint
      run: pnpm lint
    
    - name: Type check
      run: pnpm type-check
    
    - name: Test
      run: pnpm test
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3

  backend-tests:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
        cache: 'pip'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[test]"
    
    - name: Run tests
      run: |
        pytest --cov=backend tests/
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
```

### 测试报告和追踪

- 使用GitHub Pages托管测试报告
- 将覆盖率报告上传到Codecov
- 测试失败通知到Slack/钉钉

## 测试流程指南

### 新功能测试流程

1. **开发前**:
   - 编写测试计划
   - 创建测试用例

2. **开发中**:
   - 编写单元测试
   - 运行现有测试确保无回归

3. **开发后**:
   - 完成所有测试用例
   - 执行端到端测试
   - 进行代码审查和测试审查

### 缺陷修复测试流程

1. **复现步骤**:
   - 编写测试用例复现问题
   - 验证测试失败

2. **修复过程**:
   - 实现修复
   - 确保测试通过

3. **回归测试**:
   - 运行完整测试套件
   - 确保未引入新问题

## 测试环境

### 环境配置

|     环境    |    用途    |        数据库        |    访问方式    |
|------------|-----------|---------------------|--------------|
| 本地开发环境 | 开发和单元测试 | SQLite / 本地PostgreSQL | localhost |
| 测试环境  | 集成测试和用户验收 | 测试PostgreSQL | test.9000ai.com |
| 预生产环境 | 性能测试和最终验证 | 预生产PostgreSQL | staging.9000ai.com |
| 生产环境  | 生产部署和监控  | 生产PostgreSQL | 9000ai.com |

### 测试数据管理

- 使用工厂模式生成测试数据
- 使用数据库快照进行快速测试重置
- 提供标准测试数据集用于一致性测试

```python
# tests/factories.py
import factory
from backend.models.user import User
from backend.models.agent import Agent

class UserFactory(factory.Factory):
    class Meta:
        model = User
    
    name = factory.Sequence(lambda n: f"用户{n}")
    email = factory.Sequence(lambda n: f"user{n}@example.com")
    phone = factory.Sequence(lambda n: f"1380013{n:04d}")
    role = "CONSUMER"

class AgentFactory(factory.Factory):
    class Meta:
        model = Agent
    
    user = factory.SubFactory(UserFactory, role="AGENT")
    level = "PARTNER"
```

## 测试最佳实践

### 单元测试最佳实践

1. **FIRST原则**:
   - 快速(Fast): 测试应该运行得快
   - 独立(Independent): 测试不应依赖其他测试
   - 可重复(Repeatable): 每次运行结果一致
   - 自验证(Self-validating): 自动判断通过或失败
   - 及时(Timely): 在代码实现前或同时编写

2. **测试策略**:
   - 优先测试核心业务逻辑
   - 边界条件测试
   - 错误路径测试
   - 覆盖所有代码分支

### 集成测试最佳实践

1. **测试关键路径**:
   - 模拟真实用户行为
   - 验证端到端流程

2. **环境管理**:
   - 使用隔离的测试环境
   - 测试前后重置环境
   - 最小化外部依赖

## 常见问题排查

### 测试失败排查步骤

1. **了解失败原因**:
   - 检查测试日志
   - 确定是测试问题还是代码问题

2. **隔离问题**:
   - 单独运行失败测试
   - 添加调试日志

3. **修复问题**:
   - 修复代码或更新测试
   - 确保测试通过

### 常见测试问题及解决方案

1. **测试很慢**:
   - 使用测试隔离和分组
   - 减少外部依赖
   - 优化测试数据准备

2. **不稳定测试**:
   - 移除时间和随机性依赖
   - 使用固定种子值
   - 增加测试等待和重试机制

## 参考资料

- [React Testing Library文档](https://testing-library.com/docs/react-testing-library/intro/)
- [pytest文档](https://docs.pytest.org/)
- [Cypress文档](https://docs.cypress.io/)
- [Storybook文档](https://storybook.js.org/docs/)
- [Locust文档](https://docs.locust.io/en/stable/)

---

本文档最后更新: [日期] 