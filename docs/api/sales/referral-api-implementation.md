# 销售代理推荐系统API实现文档

## 概述

本文档记录了销售代理推荐系统API的实现过程。系统包含了创建推荐、查询推荐列表和查看推荐详情等功能。

## 实现方案

采用了轻量级服务架构，主要包含以下几个部分：

1. API层：处理HTTP请求和响应
2. 服务层：处理业务逻辑
3. 数据层：与数据库交互

### 目录结构

```
frontend/packages/api/src/routes/v1/agent/sales/referral/
├── schemas.ts - 请求和响应的数据结构验证
├── types.ts - TypeScript类型和错误码
├── router.ts - 路由配置
├── create-referral.ts - 创建推荐接口
├── get-referral-list.ts - 获取推荐列表接口
└── get-referral-detail.ts - 获取推荐详情接口
```

### 前端组件

```
frontend/apps/web/modules/saas/agent/sales/components/referral/
├── hooks/
│   ├── useReferrals.ts - 获取推荐列表Hook
│   ├── useReferralDetail.ts - 获取推荐详情Hook
│   └── useCreateReferral.ts - 创建推荐Hook
├── ReferralForm.tsx - 推荐表单组件
├── ReferralList.tsx - 推荐列表组件
├── ReferralDetail.tsx - 推荐详情组件
├── ReferralStats.tsx - 推荐统计组件
└── ReferralPolicy.tsx - 推荐政策组件
```

## 关键功能实现

### 1. 创建推荐

- 验证请求参数
- 检查代理商权限
- 验证团队名额
- 创建推荐记录
- 返回创建结果

### 2. 获取推荐列表

- 支持分页查询
- 支持状态筛选
- 支持关键词搜索
- 支持日期范围筛选
- 返回列表和总数

### 3. 获取推荐详情

- 验证访问权限
- 获取完整信息
- 包含奖励信息
- 返回详细数据

## 错误处理

采用统一的错误处理机制：

1. 定义错误码和错误消息
2. 使用try-catch捕获异常
3. 记录详细的错误日志
4. 返回友好的错误提示

## 日志记录

使用结构化日志记录关键操作：

1. 请求开始和结束
2. 关键步骤执行
3. 错误和异常
4. 性能指标

## UI交互优化

1. 加载状态
   - 使用骨架屏
   - 添加加载动画

2. 错误提示
   - 使用toast提示
   - 显示友好的错误信息

3. 动画效果
   - 使用Framer Motion
   - 添加过渡动画

## 注意事项

1. 推荐记录有效期为3天
2. 不同角色需要不同数量的团队名额
3. 推荐创建后状态为PENDING
4. 推荐奖励会在审核通过后自动创建

## 后续优化

1. 添加缓存机制
2. 优化查询性能
3. 添加更多的数据统计
4. 改进UI交互体验 