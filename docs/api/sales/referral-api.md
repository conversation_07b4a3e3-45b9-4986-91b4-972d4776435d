# 销售代理推荐系统API文档

## 概述

本文档描述了销售代理推荐系统的API接口实现。系统支持销售代理创建推荐、查询推荐列表和查看推荐详情等功能。

## API路由

基础路径: `/api/v1/agent/sales/referral`

### 1. 创建推荐

- 路径: `/`
- 方法: `POST`
- 描述: 创建新的推荐记录
- 请求体:
  ```json
  {
    "phone": "13800138000",
    "targetRole": "SALES",
    "note": "推荐备注"
  }
  ```
- 响应:
  ```json
  {
    "code": 200,
    "data": {
      "id": "referral_xxx"
    }
  }
  ```

### 2. 获取推荐列表

- 路径: `/list`
- 方法: `GET`
- 描述: 获取当前代理商的推荐记录列表
- 查询参数:
  - `page`: 页码 (默认: 1)
  - `pageSize`: 每页数量 (默认: 10)
  - `keyword`: 搜索关键词
  - `status`: 推荐状态
  - `targetRole`: 目标角色
  - `startDate`: 开始日期
  - `endDate`: 结束日期
- 响应:
  ```json
  {
    "code": 200,
    "data": {
      "total": 100,
      "items": [
        {
          "id": "referral_xxx",
          "referrerId": "agent_xxx",
          "referrerName": "张三",
          "phone": "13800138000",
          "targetRole": "SALES",
          "status": "PENDING",
          "quotaRequired": 1,
          "createdAt": "2024-02-28T10:00:00Z",
          "processedAt": null,
          "note": "推荐备注",
          "rewards": []
        }
      ]
    }
  }
  ```

### 3. 获取推荐详情

- 路径: `/:id`
- 方法: `GET`
- 描述: 获取指定推荐记录的详细信息
- 路径参数:
  - `id`: 推荐记录ID
- 响应:
  ```json
  {
    "code": 200,
    "data": {
      "id": "referral_xxx",
      "referrerId": "agent_xxx",
      "referrerName": "张三",
      "phone": "13800138000",
      "targetRole": "SALES",
      "status": "PENDING",
      "quotaRequired": 1,
      "quotaUsed": false,
      "note": "推荐备注",
      "processNote": null,
      "createdAt": "2024-02-28T10:00:00Z",
      "expiresAt": "2024-03-02T10:00:00Z",
      "processedAt": null,
      "escalatedAt": null,
      "rewards": []
    }
  }
  ```

## 错误码

| 错误码 | 描述 |
|--------|------|
| INVALID_PARAMS | 无效的请求参数 |
| QUOTA_REQUIRED | 需要团队名额 |
| INSUFFICIENT_QUOTA | 团队名额不足 |
| UNAUTHORIZED | 无权限执行此操作 |
| NOT_FOUND | 推荐记录不存在 |
| INTERNAL_ERROR | 服务器内部错误 |

## 实现说明

1. 代码结构遵循轻量级服务架构，包含路由层和服务层
2. 使用Zod进行请求参数验证
3. 使用Prisma进行数据库操作
4. 实现了详细的日志记录
5. 遵循RESTful API设计规范
6. 支持分页和多条件查询
7. 包含完整的错误处理机制

## 注意事项

1. 创建推荐时会自动生成临时用户ID
2. 推荐记录有效期为3天
3. 不同角色需要不同数量的团队名额：
   - 超级个体(SALES): 1个名额
   - 合伙人(PARTNER): 2个名额
   - 联席董事(DIRECTOR): 3个名额
   - 分公司(BRANCH): 5个名额
4. 推荐创建后状态为PENDING，需要等待审核
5. 推荐奖励会在审核通过后自动创建 