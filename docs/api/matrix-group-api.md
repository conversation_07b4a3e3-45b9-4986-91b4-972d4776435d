# 媒体账号分组 (Matrix Group) API 文档

## 概述

本API用于管理媒体账号的分组信息，包括创建、获取、更新和删除分组。

**基础路径**: `/api/v1/ecosystem/matrix`

**通用响应结构**:

```json
{
  "code": 200, // HTTP状态码或业务状态码
  "message": "操作成功", // 可选，操作结果描述
  "error": "ERROR_CODE", // 可选，错误码
  "data": { ... } // 实际响应数据
}
```

**通用错误响应**:

- `400 Bad Request`: 请求参数错误或业务逻辑不允许操作。
- `404 Not Found`: 资源未找到。
- `500 Internal Server Error`: 服务器内部错误。

## API 端点

---


### 1. 创建媒体账号分组

- **Endpoint**: `POST /group/createGroup`
- **Summary**: 创建媒体账号分组
- **Description**: 创建一个新的媒体账号分组。
- **Tags**: `Ecosystem/Matrix/Group`

**请求体** (`application/json`):

| 参数名      | 类型    | 是否必须 | 默认值 | 描述                         |
| ----------- | ------- | -------- | ------ | ---------------------------- |
| `name`      | string  | 是       |        | 分组名称 (1-50个字符)        |
| `description`| string  | 否       |        | 分组描述 (最大200个字符)      |
| `priority`  | number  | 否       | 0      | 排序优先级 (数字越小越靠前)    |
| `isDefault` | boolean | 否       | false  | 是否为默认分组               |

**示例请求**:

```json
{
  "name": "默认分组",
  "description": "这是我的默认媒体账号分组",
  "priority": 1,
  "isDefault": true
}
```

**响应**:

- **`200 OK`**: 创建成功
  ```json
  {
    "code": 200,
    "data": {
      "group": {
        "id": "clxkv6x7v0000u0qk3f7h2c9a",
        "name": "默认分组",
        "description": "这是我的默认媒体账号分组",
        "priority": 1,
        "isDefault": true,
        "createdAt": "2023-05-15T10:00:00.000Z",
        "updatedAt": "2023-05-15T10:00:00.000Z"
      }
    }
  }
  ```
- **`400 Bad Request`**:
  - `GROUP_NAME_EXISTS`: 分组名称已存在
  ```json
  {
    "code": 400,
    "error": "GROUP_NAME_EXISTS",
    "message": "分组名称已存在"
  }
  ```
- **`500 Internal Server Error`**:
  ```json
  {
    "code": 500,
    "error": "INTERNAL_ERROR",
    "message": "服务器内部错误"
  }
  ```

---

### 2. 获取媒体账号分组列表

- **Endpoint**: `POST /group/groups`
- **Summary**: 获取媒体账号分组列表
- **Description**: 获取当前用户的媒体账号分组列表，支持分页、筛选和排序。
- **Tags**: `Ecosystem/Matrix/Group`

**请求体** (`application/json`):

| 参数名                | 类型    | 是否必须 | 默认值 | 描述                         |
| --------------------- | ------- | -------- | ------ | ---------------------------- |
| `page`                | number  | 否       | 1      | 页码                         |
| `pageSize`            | number  | 否       | 20     | 每页数量                     |
| `keyword`             | string  | 否       |        | 搜索关键词 (匹配名称、描述)  |
| `sortOrder`           | string  | 否       | "asc"  | 优先级排序方向 (`asc`, `desc`) |
| `includeAccountCount` | boolean | 否       | false  | 是否包含每个分组下的账号数量   |

**示例请求**:

```json
POST /api/v1/ecosystem/matrix/group/groups
Content-Type: application/json

{
  "page": 1,
  "pageSize": 10,
  "keyword": "默认",
  "sortOrder": "desc",
  "includeAccountCount": true
}
```

**响应**:

- **`200 OK`**: 成功获取分组列表
  ```json
  {
    "code": 200,
    "data": {
      "total": 1,
      "items": [
        {
          "id": "clxkv6x7v0000u0qk3f7h2c9a",
          "name": "默认分组",
          "description": "这是我的默认媒体账号分组",
          "priority": 1,
          "isDefault": true,
          "accountCount": 5,
          "createdAt": "2023-05-15T10:00:00.000Z",
          "updatedAt": "2023-05-15T10:00:00.000Z"
        }
      ]
    }
  }
  ```
- **`500 Internal Server Error`**:
  ```json
  {
    "code": 500,
    "error": "INTERNAL_ERROR",
    "message": "服务器内部错误"
  }
  ```

---

### 3. 更新媒体账号分组

- **Endpoint**: `PUT /group/updateGroup`
- **Summary**: 更新媒体账号分组
- **Description**: 更新指定的媒体账号分组信息。
- **Tags**: `Ecosystem/Matrix/Group`

**请求体** (`application/json`):

| 参数名      | 类型    | 是否必须 | 描述                         |
| ----------- | ------- | -------- | ---------------------------- |
| `id`        | string  | 是       | 要更新的分组ID               |
| `name`      | string  | 否       | 新的分组名称 (1-50个字符)      |
| `description`| string  | 否       | 新的分组描述 (最大200个字符)    |
| `priority`  | number  | 否       | 新的排序优先级               |
| `isDefault` | boolean | 否       | 是否设置为新的默认分组         |

**示例请求**:

```json
{
  "id": "clxkv6x7v0000u0qk3f7h2c9a",
  "name": "我的主力分组",
  "priority": 0,
  "isDefault": true
}
```

**响应**:

- **`200 OK`**: 更新成功
  ```json
  {
    "code": 200,
    "data": {
      "group": {
        "id": "clxkv6x7v0000u0qk3f7h2c9a",
        "name": "我的主力分组",
        "description": "这是我的默认媒体账号分组", // 假设描述未修改
        "priority": 0,
        "isDefault": true,
        "createdAt": "2023-05-15T10:00:00.000Z",
        "updatedAt": "2023-05-15T10:05:00.000Z"
      }
    }
  }
  ```
- **`400 Bad Request`**:
  - `GROUP_NAME_EXISTS`: 分组名称已存在 (如果修改了名称且新名称已存在)
- **`404 Not Found`**:
  - `GROUP_NOT_FOUND`: 分组不存在
  ```json
  {
    "code": 404,
    "error": "GROUP_NOT_FOUND",
    "message": "分组不存在"
  }
  ```
- **`500 Internal Server Error`**:
  ```json
  {
    "code": 500,
    "error": "INTERNAL_ERROR",
    "message": "服务器内部错误"
  }
  ```

---

### 4. 删除媒体账号分组

- **Endpoint**: `POST /group/deleteGroup`
- **Summary**: 删除媒体账号分组
- **Description**: 删除指定的媒体账号分组。注意：默认分组不能被删除。如果分组内有账号，需要使用 `force` 参数强制删除，这会将组内账号的分组ID设为null。
- **Tags**: `Ecosystem/Matrix/Group`

**请求体** (`application/json`):

| 参数名  | 类型    | 是否必须 | 默认值 | 描述                                   |
| ------- | ------- | -------- | ------ | -------------------------------------- |
| `id`    | string  | 是       |        | 要删除的分组ID                         |
| `force` | boolean | 否       | false  | 是否强制删除 (如果分组非空，则必须为true) |

**示例请求**:

```json
{
  "id": "clxkv6x7v0000u0qk3f7h2c9a",
  "force": false
}
```

**响应**:

- **`200 OK`**: 删除成功
  ```json
  {
    "code": 200,
    "message": "分组删除成功"
  }
  ```
- **`400 Bad Request`**:
  - `DELETE_DEFAULT_GROUP`: 不能删除默认分组
  - `DELETE_NON_EMPTY_GROUP`: 不能删除非空分组 (当 `force` 为 `false` 且分组内有账号时)
  ```json
  {
    "code": 400,
    "error": "DELETE_DEFAULT_GROUP", // 或 DELETE_NON_EMPTY_GROUP
    "message": "不能删除默认分组" // 或 "不能删除非空分组，请先移除分组中的账号"
  }
  ```
- **`404 Not Found`**:
  - `GROUP_NOT_FOUND`: 分组不存在
- **`500 Internal Server Error`**:
  ```json
  {
    "code": 500,
    "error": "INTERNAL_ERROR",
    "message": "服务器内部错误"
  }
  ```

</rewritten_file> 