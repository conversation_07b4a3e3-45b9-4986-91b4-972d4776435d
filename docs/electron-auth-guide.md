# Electron桌面应用认证与API请求指南

本文档旨在指导Electron桌面应用如何集成后端的认证系统（基于`better-auth`和Bearer Token），包括用户登录、Token管理、会话有效性检查以及如何使用Token访问受保护的API资源。

## 1. 概述

桌面应用将使用 **Bearer Token** 认证机制与后端API进行通信。这意味着：
1.  用户通过特定凭证（如手机号+验证码/密码）登录。
2.  成功登录后，后端返回一个 **Access Token** 和一个可选的 **Refresh Token**。
3.  桌面应用安全地存储这些Token。
4.  在每次向后端API发送请求时，桌面应用都会在`Authorization` HTTP头部附带Access Token。
5.  Access Token具有一定的有效期，过期后需要使用Refresh Token（如果可用）获取新的Access Token，或提示用户重新登录。

## 2. 用户登录与Token获取

你的后端集成了`better-auth`及其`phoneAuthPlugin`，因此桌面应用可以通过手机号进行登录。

### 2.1. 登录方式

根据 `frontend/packages/auth/plugins/phone-auth/client.ts` 和 `frontend/packages/auth/plugins/phone-auth/server.ts` 的实现，主要支持以下手机号登录方式：

*   **手机号 + 验证码 (OTP) 登录**
*   **手机号 + 密码登录**

### 2.2. API端点

你需要查阅项目自动生成的API文档（通常在`/api/docs`）来获取确切的API端点路径。以下是基于插件代码推测的可能端点（实际路径可能带有`/api/auth`前缀，例如`/api/auth/phone/send-otp`）：

*   **发送OTP验证码**: `POST /phone/send-otp`
    *   请求体: `{ "phoneCountryCode": string, "phoneNumber": string }`
    *   响应: 成功或失败信息。
*   **验证OTP并登录**: `POST /phone/sign-in-by-otp`
    *   请求体: `{ "phoneCountryCode": string, "phoneNumber": string, "code": string }`
    *   响应 (成功时):
        ```json
        {
          "success": true,
          "data": {
            "user": { /* 用户信息 */ },
            "session": { /* 会话信息 */ },
            "tokens": {
              "accessToken": "your_access_token_here",
              "refreshToken": "your_refresh_token_here", // 可选
              "expiresIn": 3600 // Access Token有效期（秒）
            }
          }
        }
        ```
*   **手机号密码登录**: `POST /phone/sign-in-by-password`
    *   请求体: `{ "phoneCountryCode": string, "phoneNumber": string, "password": string }`
    *   响应 (成功时): 同上，包含`user`, `session`, `tokens`。

### 2.3. 客户端实现步骤 (示例：手机号+验证码登录)

1.  **用户输入手机号**。
2.  **调用发送OTP接口**：
    *   向后端 `POST /phone/send-otp` 发送请求，包含 `phoneCountryCode` 和 `phoneNumber`。
    *   处理响应，提示用户验证码已发送。
3.  **用户输入收到的验证码**。
4.  **调用OTP验证并登录接口**：
    *   向后端 `POST /phone/sign-in-by-otp` 发送请求，包含 `phoneCountryCode`, `phoneNumber`, 和 `code`。
    *   如果响应成功 (HTTP 200 OK 和 `success: true`)：
        *   从响应的 `data.tokens` 中提取 `accessToken` 和 `refreshToken` (如果有)。
        *   安全地存储这些Token (例如，使用Electron的`safeStorage`或操作系统的钥匙串服务)。
        *   导航到应用主界面。
    *   如果失败，向用户显示错误信息。

## 3. Token存储

在Electron应用中，必须安全地存储获取到的Token：

*   **Access Token**: 用于后续的API请求。
*   **Refresh Token**: 用于在Access Token过期后静默获取新的Access Token。

**推荐的存储方式**：
*   使用Electron的 `safeStorage` API (如果可用，它使用操作系统的加密功能)。
*   或者，使用特定于操作系统的钥匙串/凭据管理器库 (例如 `keytar`)。
*   **避免**使用`localStorage`或明文文件存储Token，因为它们不安全。

## 4. 检查Token（会话）有效性

在应用启动时或执行关键操作前，你可能需要检查当前存储的Access Token是否仍然有效。

**推荐接口**: `GET /api/v1/shared/user/profile` (如 `frontend/packages/api/src/routes/v1/shared/user/get-profile.ts` 中定义)

**步骤**:
1.  从安全存储中读取Access Token。
2.  如果Token不存在，则用户未登录，跳转到登录界面。
3.  如果Token存在，向 `GET /api/v1/shared/user/profile` 发起请求，并在HTTP头部设置：
    `Authorization: Bearer <YOUR_ACCESS_TOKEN>`
4.  **处理响应**:
    *   **HTTP 200 OK**: Token有效。你可以使用返回的用户数据更新UI。
    *   **HTTP 401 Unauthorized**: Token无效或已过期。
        *   尝试使用Refresh Token获取新的Access Token (见第6节)。
        *   如果刷新失败或没有Refresh Token，则清除本地存储的无效Token，并引导用户重新登录。
    *   其他错误码: 根据情况处理。

## 5. 使用Token发起API请求

当需要从后端获取数据或执行操作时：

1.  从安全存储中读取Access Token。
2.  如果Token不存在，处理未登录状态（例如，提示用户登录）。
3.  构建你的API请求 (例如，`GET /api/v1/some-data-endpoint`)。
4.  在请求的HTTP头部添加:
    `Authorization: Bearer <YOUR_ACCESS_TOKEN>`
5.  发送请求并处理响应。
6.  如果收到 **HTTP 401 Unauthorized**，表明Access Token可能已过期。尝试刷新Token (见第6节)。如果刷新成功，用新的Access Token重试原始请求。如果刷新失败，引导用户重新登录。

## 6. 刷新Access Token

如果后端在登录时返回了`refreshToken`，并且`better-auth`配置启用了Token刷新 (`autoRefresh` 和 `refreshTokenExpiresIn`)，你可以用它来获取新的`accessToken`。

**API端点**: 通常是 `/api/auth/token/refresh` (需要查阅API文档确认)。

**步骤**:
1.  当收到API请求的`401 Unauthorized`响应，或主动检测到Access Token即将过期时。
2.  从安全存储中读取`refreshToken`。
3.  如果`refreshToken`存在，向后端的Token刷新接口 (`POST /api/auth/token/refresh`) 发起请求。请求体通常需要包含：
    ```json
    {
      "refreshToken": "your_refresh_token_here"
    }
    ```
4.  **处理响应**:
    *   **HTTP 200 OK**: 刷新成功。响应体将包含新的`accessToken` (可能还有新的`refreshToken`和新的`expiresIn`)。
        *   用新的Token更新安全存储中的旧Token。
        *   使用新的`accessToken`重试之前失败的API请求。
    *   **HTTP 401 Unauthorized (或其他错误)**: Refresh Token无效或已过期。
        *   清除本地存储的所有Token (access和refresh)。
        *   引导用户重新登录。

## 7. 用户登出

当用户选择登出时：

1.  **可选 (推荐)**: 调用后端的Token撤销接口（如果`better-auth`提供了此功能，通常是类似 `/api/auth/token/revoke` 的端点），将当前的Access Token或Refresh Token发送给后端，使其立即失效。
2.  从桌面应用的安全存储中删除`accessToken`和`refreshToken`。
3.  将用户导航回登录界面。

## 8. 关于微信登录 (`wechat.ts`, `wechat-client.ts`, `QRCodeLogin.tsx`)

微信登录，特别是PC端的扫码登录，通常用于Web环境，因为它依赖OAuth2的重定向和状态轮询机制。在Electron桌面应用中实现此流程有几种方式：

### 8.1. 理解Web端微信扫码登录流程

在你现有的Web应用中：
*   `frontend/apps/web/modules/saas/auth/components/login/index.tsx` 包含一个切换到微信扫码登录的按钮。
*   `frontend/apps/web/modules/saas/auth/components/login/QRCodeLogin.tsx` 用于展示微信登录二维码的界面，并提示用户扫码。
*   后端的 `frontend/packages/auth/plugins/social/wechat.ts` (`wechatPlugin`) 负责：
    1.  提供一个API端点，让前端调用以获取微信授权URL（此URL最终会导向一个显示二维码的页面，或者直接返回二维码所需的数据）。
    2.  处理微信服务器在用户扫码授权后的回调，并与 `better-auth` 系统交互以创建会话和生成Token。
*   前端 `frontend/packages/auth/plugins/social/wechat-client.ts` 提供了客户端调用后端获取授权URL的方法。

**典型的Web端扫码流程一般是：**
1.  用户点击"微信登录"。
2.  前端调用后端接口 (e.g., `/api/auth/wechat/authorize`) 获取一个用于展示二维码的URL或二维码本身的数据。
3.  前端展示二维码 (`QRCodeLogin.tsx` 承担此角色)。
4.  前端开始**轮询**后端另一个特定接口 (e.g., `/api/auth/wechat/check-login-status?ticket=xxx` 或类似机制)，检查用户是否已扫码并授权。
5.  用户在手机微信上扫码并确认登录。
6.  微信服务器通知后端的回调URL。
7.  后端回调处理成功后，更新扫码状态。
8.  前端轮询接口检测到状态变化，获取到登录成功信息和Token。

1.  **获取授权信息**:
    *   Electron应用内的UI（例如一个按钮）触发微信登录。
    *   调用后端 `wechatPlugin` 暴露的接口 (如 `/api/auth/wechat/authorize`) 来获取微信二维码的URL或生成二维码所需的数据。
    *   `authClient.wechat.getAuthorizationUrl()` 客户端方法可以用于此目的。
2.  **展示二维码**:
    *   Electron应用在其原生UI中展示获取到的二维码。你可以创建一个类似于 `QRCodeLogin.tsx` 的原生Electron组件，或者一个简单的图片视图来显示二维码。
    *   同时，后端在返回二维码信息时，通常会附带一个唯一的 `ticket` 或 `scene_id`，用于后续轮询。
3.  **轮询检查登录状态**:
    *   Electron应用开始定期（例如每隔几秒）向后端的一个新接口 (e.g., `/api/auth/wechat/poll-status?ticket=xxx`) 发起请求。
    *   这个轮询接口需要后端实现，用于查询对应 `ticket` 的扫码登录状态。
4.  **用户扫码授权**: 用户使用手机微信扫描二维码并确认登录。
5.  **后端处理回调**: 微信服务器通知你在 `wechatPlugin` 中配置的后端回调URL。后端处理回调，验证用户，并在数据库或缓存中记录该 `ticket` 已成功登录，并生成Token。
6.  **轮询成功，获取Token**:
    *   Electron应用的轮询请求最终会从后端获取到成功的状态以及 `accessToken` 和 `refreshToken`。
    *   停止轮询。
7.  **存储和使用Token**: 按照本文档第3、4、5节描述的方式进行。

**注意事项**:
*   **轮询接口**: 方式二中的轮询接口需要后端额外开发。
*   **安全性**: 对于 `window.postMessage`，务必校验 `event.origin`。
*   **用户体验**: 轮询时要有超时机制和适当的用户提示。

考虑到复用性和开发效率，**方式一 (WebView内嵌)** 通常是为Electron应用添加复杂OAuth流程（如微信扫码登录）时更推荐的选择。

## 9. 注意事项

*   **错误处理**: 对所有API请求和Token操作进行健壮的错误处理。
*   **用户体验**: 在Token过期或刷新时，提供清晰的用户反馈。避免让用户频繁重新登录。
*   **安全性**: 始终通过HTTPS与后端通信。Token的存储和传输必须安全。
*   **API文档**: 仔细阅读后端`better-auth`和你的应用生成的API文档，以获取准确的端点路径、请求/响应格式。

---
希望这份文档能帮助你顺利在Electron应用中实现用户认证！ 