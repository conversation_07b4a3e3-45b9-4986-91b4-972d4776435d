# API 联调逻辑说明文档

本文档旨在阐述 9000AI 项目中，前端（Next.js 应用）与后端（Hono API 服务）之间的 API 调用和处理流程。

## 核心组件

1.  **Hono API 服务 (`frontend/packages/api/src/app.ts`)**:
    *   使用 Hono 框架构建，负责定义和实现核心 API 逻辑。
    *   设置了全局基础路径 `/api`。
    *   包含全局中间件（如日志、CORS）。
    *   组织了多个业务路由模块（如认证、上传、健康检查、V1 版本业务 API 等）。
    *   集成了 OpenAPI 规范生成和 Scalar API 文档展示。
    *   包含 RabbitMQ 的初始化和优雅关停逻辑，表明 API 服务可能与消息队列交互。
    *   **API 安全性**: Hono 应用内部应包含自身的认证和授权中间件来保护 API 端点。

2.  **Next.js API 路由暴露点 (`frontend/apps/web/app/api/[[...rest]]/route.ts`)**:
    *   作为 Next.js 应用中的一个动态路由 (`/app/api/[[...rest]]/route.ts`)。
    *   通过 `hono/vercel` 的 `handle` 函数，将导入的 Hono 应用实例桥接到 Next.js 的请求处理机制。
    *   捕获所有以 `/app/api/` 开头的 HTTP 请求，并将它们转发给 Hono 应用进行处理。

3.  **Next.js 中间件 (`frontend/apps/web/middleware.ts`)**:
    *   在 Next.js 请求处理流程中，早于 API 路由处理器执行。
    *   **关键行为**: 对于路径匹配 `/api/...` 的请求，此中间件会直接调用 `NextResponse.next()`，意味着它不会对 API 请求执行会话检查、角色检查或重定向等操作。API 的安全与访问控制完全交由 Hono 应用内部处理。
    *   主要负责非 API 路径（如应用页面 `/app/...`）的认证、授权、国际化和重定向逻辑。

## 请求处理流程

当客户端向 `/app/api/{some_path}` (例如: `/app/api/v1/users`) 发起一个 API 请求时，处理流程如下：

1.  **请求到达 Next.js**: 客户端的 HTTP 请求首先被 Next.js 服务器接收。

2.  **Next.js 中间件 (`middleware.ts`) 执行**:
    *   请求路径被 `middleware.ts` 捕获。
    *   由于请求路径以 `/api` 开头 (e.g., `/app/api/v1/users`), 中间件的豁免规则 `pathname.match(/^\/(api|...)/)` 被触发。
    *   中间件执行 `NextResponse.next()`，将请求原样传递到下一处理环节，不进行任何身份验证或页面级的访问控制。

3.  **Next.js API 路由 (`route.ts`) 匹配**:
    *   请求被 `frontend/apps/web/app/api/[[...rest]]/route.ts` 文件捕获。
    *   `[[...rest]]` 动态路由段匹配了 `/v1/users` 部分。
    *   `route.ts` 中的 `handle(app)` 函数被调用。此函数是由 `hono/vercel` 提供，用于将 Hono 应用适配到 Next.js (Vercel) 的运行时环境。

4.  **Hono 应用 (`app.ts`) 处理**:
    *   `handle(app)` 将请求（通常是移除了 Next.js 自身的 `/app` 前缀，保留了 Hono 应用关心的 `/api/v1/users` 路径）传递给从 `@repo/api` 包导入的 Hono `app` 实例。
    *   Hono 应用的全局中间件（例如 `loggerMiddleware`, `corsMiddleware`）首先执行。
    *   Hono 的路由系统根据其自身配置的 `basePath("/api")` 和内部定义的路由规则 (e.g., `.route("/", v1Router)` 然后 `v1Router` 内部的 `/users` 路由) 来匹配并执行相应的处理函数。
    *   **API 内部认证/授权**: 在 Hono 路由的处理函数执行前，通常会经过该 API 端点特有的 Hono 中间件，例如用于验证 JWT token 的认证中间件、检查用户权限的授权中间件等。这些是保护 API 安全的核心。
    *   匹配到的 Hono 路由处理函数执行核心业务逻辑，与数据库、消息队列或其他服务交互，并准备响应数据。

5.  **响应返回**:
    *   Hono 处理函数返回的响应，通过 `hono/vercel` 的 `handle` 函数转换成 Next.js 能够理解的格式。
    *   Next.js 将此响应发送回客户端。

## 关键注意事项

*   **API 安全性**: API 的安全（认证、授权）必须在 Hono 应用内部 (`@repo/api`) 实现。Next.js 中间件 (`middleware.ts`) 不会为 `/api/...` 路径提供安全保障。
*   **路径解析**:
    *   客户端请求的完整路径是例如 `https://yourdomain.com/app/api/v1/example`。
    *   Next.js 的 `[[...rest]]` 路由捕获 `/app/api/` 之后的部分。
    *   Hono 应用配置了 `basePath("/api")`，所以它内部看到的路径是 `/api/v1/example`，并基于此进行路由匹配。
*   **关注点分离**:
    *   Next.js `middleware.ts`: 主要负责页面级导航、会话管理和非 API 路由的访问控制。
    *   Next.js `app/api/[[...rest]]/route.ts`: 作为桥梁，将 API 请求转发给 Hono。
    *   Hono `app.ts` (`@repo/api`): 负责所有 API 的业务逻辑实现、API 内部的中间件处理（包括安全）、以及与后端服务的交互。

---

本文档记录了当前 API 联调的主要逻辑。随着项目的迭代，具体实现细节可能会有所更新，请开发人员注意保持文档的同步。 