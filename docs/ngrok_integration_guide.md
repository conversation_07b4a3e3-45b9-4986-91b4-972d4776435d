# Ngrok内网穿透集成指南

## 简介

本文档介绍如何在9000AI项目中使用Ngrok实现内网穿透，使本地运行的API服务可以通过公网访问。特别是，我们将详细说明如何获取并配置固定域名，以便API服务有一个稳定的公网访问地址。

## 前提条件

- 已安装ngrok客户端 (v3.x版本)
- 已注册ngrok账号并获取authtoken
- 9000AI项目已正确配置

## Ngrok固定域名配置步骤

### 1. 获取固定域名

即使使用免费版的ngrok，每个账户也可以申请一个免费的固定域名：

1. 登录[Ngrok控制台](https://dashboard.ngrok.com)
2. 导航到"Domains"部分
3. 点击"New Domain"按钮
4. 选择免费域名（格式为 xx-xx-xx.ngrok-free.app）

也可以通过命令行查看或申请固定域名：
```bash
ngrok http --url=your-reserved-domain.ngrok-free.app 80
```

### 2. 配置环境变量

在项目根目录的`.env`文件中添加以下配置：

```
# Ngrok内网穿透配置
# 启用Ngrok内网穿透
ENABLE_NGROK=1

# Ngrok认证令牌
NGROK_AUTH_TOKEN=your_auth_token_here

# Ngrok固定域名
NGROK_DOMAIN=your-domain.ngrok-free.app

# Ngrok地区，选择最近的地区以获得更低延迟
NGROK_REGION=us
```

### 3. 启动服务

使用项目提供的启动脚本：

```powershell
# Windows
.\backend\start_with_ngrok.ps1

# Linux/Mac
./backend/start_with_ngrok.sh
```

启动后，服务将自动使用配置的固定域名创建隧道，并显示公网访问地址。

## 工作原理

我们的ngrok集成模块(`backend/src/backend/utils/ngrok_service.py`)实现了以下功能：

1. 从环境变量读取配置，包括认证令牌、区域和固定域名
2. 使用pyngrok库启动隧道，绑定到指定的本地端口
3. 如果配置了固定域名，则使用该域名创建隧道
4. 提供隧道状态监控和管理功能

## 故障排除

### 常见问题

1. **无法连接到ngrok服务器**
   - 检查网络连接
   - 确认防火墙未阻止ngrok
   - 尝试更换区域(NGROK_REGION)

2. **固定域名不生效**
   - 确认环境变量NGROK_DOMAIN正确设置
   - 检查域名是否已被占用
   - 确认ngrok authtoken已正确设置

3. **连接超时或断开**
   - 免费版ngrok有连接数和带宽限制
   - 重启隧道可以解决大多数临时问题

### 查看日志

启动时会显示详细的ngrok日志，包括：
- 环境变量读取情况
- 隧道创建过程
- 公网URL信息

## 进阶配置

### 使用自定义域名(需付费账户)

付费账户可以使用自己的域名：

1. 在ngrok控制台添加自定义域名
2. 设置DNS CNAME记录指向ngrok
3. 在`.env`文件中设置：
```
NGROK_DOMAIN=api.yourdomain.com
```

### 自定义TLS设置

可以在`ngrok_service.py`中配置更多TLS相关选项，如证书配置等。

## 相关链接

- [Ngrok官方文档](https://ngrok.com/docs)
- [Pyngrok文档](https://pyngrok.readthedocs.io/en/latest/) 