# RabbitMQ 消息队列架构设计文档

## 1. 引言

本文档详细阐述了项目中所采用的 RabbitMQ 消息队列的架构设计方案。目标是构建一个可靠、可扩展、易于维护的消息传递系统，以支持各项异步任务处理和微服务间的解耦通信。

选择 RabbitMQ (或在已选型 RabbitMQ 的前提下)，我们旨在充分利用其成熟的特性，如多样的交换机类型、消息持久化、灵活的路由机制、消费者确认、以及死信队列（DLX）等，来满足复杂的业务需求。

## 2. 核心设计原则

本次 RabbitMQ 架构设计遵循以下核心原则：

*   **基于 Direct Exchange 的精确路由**: 
    *   对于需要将特定类型的任务精确路由到特定处理单元的场景，我们优先选择 `direct` 类型的交换机。生产者通过指定消息的 `routingKey`，交换机将其精确投递到 `routingKey` 完全匹配的队列。
*   **每个逻辑处理单元对应一个专用队列**:
    *   类似于 Kafka 中每个分区服务于特定逻辑，我们将为每个独立的业务处理逻辑或消费者组创建一个专用的队列。这有助于隔离不同任务的处理，方便独立扩展和监控。
*   **强制持久化**: 
    *   **交换机 (Exchanges)**: 所有业务相关的交换机都将声明为持久化 (`durable: true`)，确保 RabbitMQ 服务器重启后交换机依然存在。
    *   **队列 (Queues)**: 所有业务相关的队列也将声明为持久化 (`durable: true`)，确保队列中的消息在服务器重启后不会丢失。
    *   **消息 (Messages)**: 生产者在发送消息时，应将消息标记为持久化 (`deliveryMode: 2` 或 `persistent: true`)，配合持久化队列，最大限度保证消息不丢失。
*   **为每个业务队列配备死信交换机 (DLX) 和死信队列 (DLQ)**:
    *   这是保障消息处理可靠性的关键机制。当消息因为某些原因（如处理逻辑错误、达到最大重试次数、消息格式错误等）无法被正常消费时，它将被发送到预先配置的 DLX，并路由到对应的 DLQ 中。
    *   运维人员或专门的错误处理服务可以监控 DLQ，对死信进行分析、告警、重试或人工干预，防止消息丢失并帮助诊断问题。
    *   我们推荐使用 `@repo/mq` 模块中的 `setupQueueWithDLX` 辅助函数来统一创建业务队列及其关联的 DLX/DLQ。
*   **清晰的命名约定**: 
    *   采用统一且具有描述性的命名规范，方便理解和管理 RabbitMQ 中的各个组件。
        *   **Exchanges**: `exchange.<业务域>.<功能描述>` (e.g., `exchange.dh_video`)
        *   **Queues**: `queue.<业务域>.<具体任务>` (e.g., `queue.dh_video.tts_driven`)
        *   **Routing Keys**: 使用点分格式，清晰表达意图 (e.g., `dh.video.tts_driven`)
        *   **Dead Letter Exchanges**: `dlx.<业务域>.<功能描述>` (e.g., `dlx.dh_video.tts_driven`)
        *   **Dead Letter Queues**: (自动生成，通常为 `主队列名.dlq`, e.g., `queue.dh_video.tts_driven.dlq`)

## 3. 具体业务场景的拓扑结构

以下是我们为核心业务场景设计的 RabbitMQ 拓扑结构。

### 3.1 数字人视频生成 (`digital_human_video`)

*   **业务描述**: 处理与数字人头像相关的视频生成任务，区分 TTS 驱动和音频驱动两种主要类型。
*   **Exchange**: 
    *   名称: `exchange.dh_video`
    *   类型: `direct`
    *   持久化: `true`
*   **场景 1: TTS 驱动的视频生成**
    *   **Queue**: 
        *   名称: `queue.dh_video.tts_driven`
        *   持久化: `true`
    *   **Routing Key (绑定到 Exchange)**: `dh.video.tts_driven`
    *   **DLX/DLQ**: 
        *   DLX Name: `dlx.dh_video.tts_driven` (direct)
        *   DLQ Name: `queue.dh_video.tts_driven.dlq`
        *   DLX Routing Key from Main Queue: (由 `setupQueueWithDLX` 基于 `dlxRoutingKeyPrefix` 和主队列名生成)
*   **场景 2: 音频驱动的视频生成**
    *   **Queue**: 
        *   名称: `queue.dh_video.audio_driven`
        *   持久化: `true`
    *   **Routing Key (绑定到 Exchange)**: `dh.video.audio_driven`
    *   **DLX/DLQ**: 
        *   DLX Name: `dlx.dh_video.audio_driven` (direct)
        *   DLQ Name: `queue.dh_video.audio_driven.dlq`
        *   DLX Routing Key from Main Queue: (同上)

### 3.2 数字人克隆 (`digital_human_clone`)

*   **业务描述**: 处理数字人克隆任务，包括完整克隆、仅音频克隆和仅视频克隆。
*   **Exchange**: 
    *   名称: `exchange.dh_clone`
    *   类型: `direct`
    *   持久化: `true`
*   **场景 1: 完整克隆 或 仅音频克隆**
    *   **Queue**: 
        *   名称: `queue.dh_clone.full_or_audio`
        *   持久化: `true`
    *   **Routing Key (绑定到 Exchange)**: `dh.clone.full_or_audio`
    *   **DLX/DLQ**: 
        *   DLX Name: `dlx.dh_clone.full_or_audio` (direct)
        *   DLQ Name: `queue.dh_clone.full_or_audio.dlq`
*   **场景 2: 仅视频克隆**
    *   **Queue**: 
        *   名称: `queue.dh_clone.video_only`
        *   持久化: `true`
    *   **Routing Key (绑定到 Exchange)**: `dh.clone.video_only`
    *   **DLX/DLQ**: 
        *   DLX Name: `dlx.dh_clone.video_only` (direct)
        *   DLQ Name: `queue.dh_clone.video_only.dlq`

### 3.3 Web 搜索与写作 (`web_search`)

*   **业务描述**: 处理网络搜索任务以及基于搜索结果的写作任务。
*   **Exchange**: 
    *   名称: `exchange.web_search`
    *   类型: `direct`
    *   持久化: `true`
*   **场景 1: 仅搜索**
    *   **Queue**: 
        *   名称: `queue.web_search.search_only`
        *   持久化: `true`
    *   **Routing Key (绑定到 Exchange)**: `web_search.search_only`
    *   **DLX/DLQ**: 
        *   DLX Name: `dlx.web_search.search_only` (direct)
        *   DLQ Name: `queue.web_search.search_only.dlq`
*   **场景 2: 仅写作 (基于提供的搜索结果)**
    *   **Queue**: 
        *   名称: `queue.web_search.write_only`
        *   持久化: `true`
    *   **Routing Key (绑定到 Exchange)**: `web_search.write_only`
    *   **DLX/DLQ**: 
        *   DLX Name: `dlx.web_search.write_only` (direct)
        *   DLQ Name: `queue.web_search.write_only.dlq`
*   **场景 3: 搜索并写作**
    *   **Queue**: 
        *   名称: `queue.web_search.search_and_write`
        *   持久化: `true`
    *   **Routing Key (绑定到 Exchange)**: `web_search.search_and_write`
    *   **DLX/DLQ**: 
        *   DLX Name: `dlx.web_search.search_and_write` (direct)
        *   DLQ Name: `queue.web_search.search_and_write.dlq`

## 4. 生产者和消费者职责概述

*   **生产者 (Producers)**:
    *   必须确保将消息发送到正确的业务场景对应的 **Exchange**。
    *   必须为消息指定正确的 **Routing Key**，以确保消息被路由到预期的队列。
    *   发送的消息内容应遵循预定义的格式 (e.g., JSON)，并携带必要的业务信息。
    *   应将消息标记为 **持久化**。
*   **消费者 (Consumers)**:
    *   应监听其负责处理的特定 **Queue**。
    *   在成功处理消息后，必须发送 **ACK** (acknowledgment) 给 RabbitMQ，以便消息从队列中移除。
    *   如果处理消息失败：
        *   对于可重试的临时性错误，可以考虑 **NACK** 并将消息重新入队 (requeue)，但必须有重试次数限制和退避策略，以避免消息风暴或无限循环。
        *   对于不可恢复的错误或达到最大重试次数，应 **NACK** 并不重新入队，让消息进入配置好的 DLQ。
    *   消费者应具备良好的错误处理和日志记录能力。
    *   考虑设置合理的 `prefetchCount` (QoS) 以控制消费速率，防止自身过载。

## 5. 未来的扩展性考虑

当前架构设计具备良好的可扩展性：

*   **新增业务场景**: 可以通过定义新的 Exchange、Queues 和 Bindings 来轻松集成新的业务流程，而不会影响现有拓扑。
*   **调整处理逻辑**: 如果某个队列的处理逻辑需要细分或合并，可以通过调整队列定义、绑定关系和消费者实现来完成。
*   **消费者扩展**: RabbitMQ 支持多个消费者实例共同消费一个队列中的消息（竞争消费者模式），可以简单地通过增加消费者实例数量来水平扩展处理能力。
*   **更复杂的路由**: 如果未来出现需要更复杂路由逻辑（如基于消息头部内容、通配符匹配等）的场景，可以考虑引入 `topic` 或 `headers` 类型的交换机作为补充。

## 6. 初始化

所有上述定义的 RabbitMQ 拓扑结构（Exchanges, Queues, Bindings, DLX/DLQ）将在 API 服务启动时，通过调用 `frontend/packages/api/src/initializers/rabbitmq.ts` 文件中的 `initializeRabbitMQ()` 函数进行声明和确保存在。该初始化过程设计为幂等操作。

---
*文档版本: 1.0*
*最后更新日期: (自动填写或手动更新)* 