# 9000AI 前端开发指南

## 简介

本文档为9000AI平台前端开发指南，详细说明前端架构、开发规范和核心功能实现。前端采用基于Next.js 14的App Router架构，使用TypeScript、Tailwind CSS和Prisma等现代技术栈。

## 开发环境配置

### 环境要求

- Node.js >= 18.0.0
- PNPM >= 8.0.0
- VSCode推荐（并安装扩展：ESLint, Prettier, Tailwind CSS IntelliSense）

### 本地开发设置

```bash
# 进入前端目录
cd frontend

# 安装依赖
pnpm install

# 开发环境运行
pnpm dev

# 生产构建
pnpm build

# 类型检查
pnpm type-check
```

## 项目架构

### Monorepo结构

项目使用PNPM Workspace和Turborepo管理monorepo结构：

- `apps/`: 包含独立应用
  - `web/`: 主Web应用（Next.js）
- `packages/`: 共享库包
  - `database/`: Prisma数据库模型和操作
  - `api/`: tRPC API定义
  - `auth/`: 认证相关逻辑
  - `ai/`: AI服务集成
  - 其他功能包...

### 关键技术栈

- **框架**: Next.js 14 (App Router)
- **UI库**: React 18
- **样式**: Tailwind CSS + shadcn/ui组件
- **状态管理**: React Context + React Query
- **API通信**: tRPC
- **ORM**: Prisma
- **表单处理**: React Hook Form + Zod验证

## 代码风格与规范

### 命名约定

- 组件文件: PascalCase (如 `Button.tsx`)
- 非组件模块: camelCase (如 `utils.ts`)
- 常量: UPPER_SNAKE_CASE
- React组件: 函数式组件 + TypeScript接口
- CSS类名: 遵循Tailwind CSS约定

### 文件组织

**典型页面结构**:
```
app/(saas)/agent/orders/
├── page.tsx           # 路由页面组件
├── layout.tsx         # 布局组件
├── loading.tsx        # 加载状态
├── error.tsx          # 错误处理
└── components/        # 页面专用组件
    ├── OrderList.tsx
    └── OrderFilter.tsx
```

**典型组件结构**:
```typescript
// 标准组件模板
import React from 'react';
import { cn } from '@/lib/utils';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'primary' | 'danger';
  size?: 'sm' | 'md' | 'lg';
}

export function Button({
  variant = 'default',
  size = 'md',
  className,
  children,
  ...props
}: ButtonProps) {
  return (
    <button
      className={cn(
        'button-base',
        variant === 'primary' && 'button-primary',
        variant === 'danger' && 'button-danger',
        size === 'sm' && 'text-sm py-1 px-2',
        size === 'md' && 'text-base py-2 px-4',
        size === 'lg' && 'text-lg py-3 px-6',
        className
      )}
      {...props}
    >
      {children}
    </button>
  );
}
```

## 核心模块说明

### 认证系统 (`/auth`)

认证系统实现了多角色（代理商、消费者）登录和注册流程:

- 使用JWT + Session认证
- 多因素认证支持
- 手机验证码登录

**关键文件**:
- `apps/web/app/(saas)/auth/login/page.tsx`: 登录页面
- `packages/auth/src/auth-options.ts`: NextAuth配置

### 代理商平台 (`/agent`)

代理商平台为多级代理商提供管理界面:

- 客户管理
- 订单管理
- 团队管理
- 业绩分析

**关键文件**:
- `apps/web/app/(saas)/agent/dashboard/page.tsx`: 仪表盘
- `apps/web/app/(saas)/agent/consumers/page.tsx`: 客户管理
- `apps/web/app/(saas)/agent/orders/page.tsx`: 订单管理

### 生态平台 (`/ecosystem`)

生态平台提供AI创作功能:

- 数字人克隆 (`/avatar`)
- 内容创作 (`/content`)
- 视频处理 (`/video`)

**关键文件**:
- `apps/web/app/(saas)/ecosystem/avatar/page.tsx`: 数字人系统
- `apps/web/app/(saas)/ecosystem/content/page.tsx`: 内容系统
- `apps/web/app/(saas)/ecosystem/video/page.tsx`: 视频系统

## 状态管理

### 全局状态

全局状态使用React Context API管理:

```typescript
// 创建上下文
export const AppContext = createContext<AppContextType | undefined>(undefined);

// 提供者组件
export function AppProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState);
  
  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  );
}

// 在组件中使用
export function useAppContext() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
}
```

### 服务器状态

服务器状态通过React Query管理:

```typescript
// 查询示例
const { data, isLoading, error } = useQuery({
  queryKey: ['orders', filters],
  queryFn: () => fetchOrders(filters),
});

// 变更示例
const mutation = useMutation({
  mutationFn: createOrder,
  onSuccess: () => {
    queryClient.invalidateQueries(['orders']);
    toast.success('订单创建成功');
  },
});
```

## API通信

### tRPC配置

项目使用tRPC实现类型安全的API调用:

**服务器路由定义**:
```typescript
// packages/api/src/router/order.ts
export const orderRouter = router({
  list: protectedProcedure
    .input(z.object({
      status: z.enum(['PENDING', 'PAID', 'COMPLETED']).optional(),
      page: z.number().default(1),
      limit: z.number().default(10),
    }))
    .query(async ({ ctx, input }) => {
      // 实现查询逻辑
      return orders;
    }),
    
  create: protectedProcedure
    .input(createOrderSchema)
    .mutation(async ({ ctx, input }) => {
      // 实现创建逻辑
      return newOrder;
    }),
});
```

**客户端使用**:
```typescript
// 在组件中使用
const { data, isLoading } = api.order.list.useQuery({ 
  status: 'PENDING',
  page: 1 
});

const createOrder = api.order.create.useMutation();

// 调用创建
createOrder.mutate({
  planId: 'plan_123',
  quantity: 1
});
```

## 表单处理

### React Hook Form + Zod

表单处理使用React Hook Form结合Zod验证:

```typescript
// 验证模式
const loginSchema = z.object({
  email: z.string().email('请输入有效的邮箱地址'),
  password: z.string().min(8, '密码至少8个字符'),
});

// 组件中使用
function LoginForm() {
  const form = useForm<z.infer<typeof loginSchema>>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });
  
  function onSubmit(values: z.infer<typeof loginSchema>) {
    // 处理登录
  }
  
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>邮箱</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        {/* 其他字段 */}
        <Button type="submit">登录</Button>
      </form>
    </Form>
  );
}
```

## 国际化

### next-intl

项目使用next-intl处理国际化:

**配置**:
```typescript
// 在中间件中
export default createMiddleware({
  locales: ['zh-CN', 'en'],
  defaultLocale: 'zh-CN',
});
```

**使用**:
```typescript
// 在组件中
import { useTranslations } from 'next-intl';

function MyComponent() {
  const t = useTranslations('Common');
  
  return <h1>{t('welcome')}</h1>;
}
```

## 性能优化

### 前端性能优化策略

1. **代码分割**:
   - 使用动态导入
   - 组件懒加载

2. **图片优化**:
   - 使用Next.js Image组件
   - WebP格式
   - 响应式图片加载

3. **缓存策略**:
   - SWR/React Query缓存
   - 静态生成与ISR

4. **渲染优化**:
   - 使用`React.memo`
   - 避免不必要的渲染
   - 优化列表渲染

## 开发流程

### 新功能开发

1. **功能分支创建**:
   ```bash
   git checkout -b feature/new-feature
   ```

2. **开发步骤**:
   - 编写组件/页面
   - 添加API路由
   - 编写测试
   - 本地验证

3. **提交代码**:
   ```bash
   git add .
   git commit -m "feat: add new feature"
   git push origin feature/new-feature
   ```

4. **审查与合并**:
   - 创建Pull Request
   - 代码审查
   - 合并到主分支

## 常见问题排查

### 构建错误

- **问题**: "Cannot find module..."
- **解决**: 检查依赖安装、清理node_modules并重新安装

### API调用失败

- **问题**: API请求返回错误
- **解决**: 检查API路由、认证状态、CORS配置

### 页面渲染问题

- **问题**: 页面布局异常
- **解决**: 检查CSS类名、响应式设计、浏览器兼容性

## 代码贡献指南

1. **代码风格**:
   - 遵循ESLint配置
   - 运行`pnpm lint`检查代码风格
   - 使用Prettier格式化代码

2. **提交规范**:
   - 使用约定式提交格式
   - 类型: feat, fix, docs, style, refactor, perf, test, chore

3. **测试要求**:
   - 重要组件需要单元测试
   - 关键流程需要集成测试

---

本文档最后更新: [日期] 