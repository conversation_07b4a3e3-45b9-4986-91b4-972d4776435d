import { PostListItem } from "@marketing/product-updates/components/PostListItem";
import { allPosts } from "content-collections";
import { getLocale, getTranslations } from "next-intl/server";

export async function generateMetadata() {
	const t = await getTranslations();
	return {
		title: t("product_updates.title"),
	};
}

export default async function ProductUpdatesListPage() {
	const locale = await getLocale();
	const t = await getTranslations();

	return (
		<div className="container max-w-6xl pt-32 pb-16">
			<div className="mb-12 pt-8 text-center">
				<h1 className="mb-2 font-bold text-5xl">
					{t("product_updates.title")}
				</h1>
				<p className="text-lg opacity-50">
					{t("product_updates.description")}
				</p>
			</div>

			<div className="grid gap-8 md:grid-cols-2">
				{(allPosts as any)
					.filter(
						(post: any) => post.published && locale === post.locale,
					)
					.sort(
						(a: any, b: any) =>
							new Date(b.date).getTime() -
							new Date(a.date).getTime(),
					)
					.map((post: any) => (
						<PostListItem post={post} key={post.path} />
					))}
			</div>
		</div>
	);
}
