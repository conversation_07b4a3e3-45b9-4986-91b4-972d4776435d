import { LocaleLink, localeRedirect } from "@i18n/routing";
import { PostContent } from "@marketing/product-updates/components/PostContent";
import { getBaseUrl } from "@repo/utils";
import { getActivePathFromUrlParam } from "@shared/lib/content";
import { allPosts } from "content-collections";
import { getLocale, setRequestLocale } from "next-intl/server";

type Params = {
	path: string;
	locale: string;
};

export function generateStaticParams() {
	return (allPosts as any).map((post: any) => ({
		path: post.path.split("/"),
		locale: post.locale,
	}));
}

export async function generateMetadata(props: { params: Promise<Params> }) {
	const params = await props.params;

	const { path } = params;

	const locale = await getLocale();
	const activePath = getActivePathFromUrlParam(path);
	const post = (allPosts as any).find(
		(post: any) => post.path === activePath && locale === post.locale,
	);

	return {
		title: post?.title,
		description: post?.excerpt,
		openGraph: {
			title: post?.title,
			description: post?.excerpt,
			images: post?.image
				? [new URL(post?.image ?? "", getBaseUrl()).toString()]
				: [],
		},
	};
}

export default async function ProductUpdatePostPage(props: {
	params: Promise<Params>;
}) {
	const { path, locale } = await props.params;
	setRequestLocale(locale);

	const activePath = getActivePathFromUrlParam(path);
	const post = (allPosts as any).find(
		(post: any) => post.path === activePath && locale === post.locale,
	);

	if (!post) {
		return localeRedirect({ href: "/product-updates", locale });
	}

	const { title, date, tags, body } = post;

	return (
		<div className="bg-[#0e1e13] text-gray-100">
			<div className="container max-w-6xl pt-32 pb-24">
				<div className="mx-auto max-w-2xl">
					<div className="mb-12">
						<LocaleLink
							href="/product-updates"
							className="text-blue-400 hover:text-blue-300"
						>
							&larr; 返回产品动态
						</LocaleLink>
					</div>

					<h1 className="font-bold text-4xl text-white">{title}</h1>

					<div className="mt-4 flex items-center justify-start gap-6">
						<div className="mr-0 ml-auto">
							<p className="text-sm text-gray-400">
								{Intl.DateTimeFormat("zh-CN").format(
									new Date(date),
								)}
							</p>
						</div>

						{tags && (
							<div className="flex flex-1 flex-wrap gap-2">
								{tags.map((tag: any) => (
									<span
										key={tag}
										className="font-semibold text-blue-400 text-xs uppercase tracking-wider"
									>
										#{tag}
									</span>
								))}
							</div>
						)}
					</div>
				</div>

				<div className="pb-8">
					<PostContent content={body} />
				</div>
			</div>
		</div>
	);
}
