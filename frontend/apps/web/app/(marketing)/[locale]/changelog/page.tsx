import { ChangelogSection } from "@marketing/changelog/components/ChangelogSection";
import { getTranslations } from "next-intl/server";

export default async function ChangelogPage() {
	const _t = await getTranslations();

	return (
		<div className="min-h-screen bg-gray-900 text-gray-100 py-32">
			<div
				className="fixed inset-0 bg-cover bg-center bg-no-repeat opacity-20 z-0"
				style={{
					backgroundImage:
						"url('https://cminio.9000aigc.com/static/contactus/formbg.png')",
				}}
			/>

			<div className="container max-w-3xl mx-auto relative z-10">
				<div className="mb-12 text-balance pt-8 text-center">
					<h1 className="mb-2 font-bold text-5xl text-white">
						更新日志
					</h1>
					<p className="text-lg text-gray-300">
						一个更强大的AI增长引擎，现已全面升级。
					</p>
				</div>
				<ChangelogSection
					items={[
						{
							date: "2025-07-13 18:00",
							changes: [
								"🎬 视频创作体验全面升级！优化了数字人视频生成流程，提升创作效率与稳定性。修复了多个影响用户体验的问题，让您的创作之路更加顺畅。",
							],
						},
						{
							date: "2025-06-23",
							changes: [
								"🚀 9000AI v2.0 重磅发布！我们对数字人系统进行了彻底重构，带来了更流畅、更直观的创作流程与交互体验。",
							],
						},
						{
							date: "2025-06-21",
							changes: [
								"🤝 全新【合伙人代理系统】正式上线！为我们的合作伙伴打造专属管理后台，轻松管理客户与收益。",
							],
						},
						{
							date: "2025-06-18",
							changes: [
								"🎥 革命性【直播智能体】登场！深度拆解热门直播间，洞悉爆款逻辑。",
							],
						},
						{
							date: "2025-06-12",
							changes: [
								"🔥 新增【热点追踪】功能！实时捕获全网热门话题与素材，为您的内容创作提供源源不断的灵感。",
							],
						},
						{
							date: "2025-06-10",
							changes: [
								"✨ AI核心模型再次进化！文案生成引擎已升级，能创作出更具吸引力和转化力的营销脚本。",
							],
						},
						{
							date: "2025-06-07",
							changes: [
								"🎤 音频克隆体验升级！现已支持【音频单独克隆】，并增加了【克隆效果预览】功能，在确认前即可试听声音效果。",
							],
						},
						{
							date: "2025-06-04",
							changes: [
								"⚡️ 系统性能极致优化！全面提升了流程响应速度，从素材上传到视频导出，体验如丝般顺滑。",
							],
						},
						{
							date: "2025-06-01",
							changes: [
								"📈 推出【全自动流量系统】（Beta版）！配置好策略后，系统将自动执行内容分发与互动，打造永动流量池。",
							],
						},
						{
							date: "2025-05-29",
							changes: [
								"🐛 修复与优化：修复了若干已知问题，并对UI/UX细节进行了打磨，提升了整体稳定性。",
							],
						},
						{
							date: "2025-05-25",
							changes: [
								"🔮 敬请期待... 更多激动人心的功能正在路上，AI切片、智能图文裂变等即将到来！",
							],
						},
					]}
				/>
			</div>
		</div>
	);
}
