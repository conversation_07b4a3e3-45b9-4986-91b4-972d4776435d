"use client";

import { AgentSystem } from "@marketing/home/<USER>/AgentSystem";
import CardSystem from "@marketing/home/<USER>/CardSystem";
import { DigitalSystem } from "@marketing/home/<USER>/DigitalSystem";
import { <PERSON> } from "@marketing/home/<USER>/Hero";
import { HumanExample } from "@marketing/home/<USER>/HumanExample";
import { ServiceModel } from "@marketing/home/<USER>/ServiceModel";
import { Solution } from "@marketing/home/<USER>/Solution";
import { useEffect, useState } from "react";

export default function Home() {
	const [showDelayedContent, setShowDelayedContent] = useState(false);

	useEffect(() => {
		const timer = setTimeout(() => {
			setShowDelayedContent(true);
		}, 1000);

		return () => clearTimeout(timer);
	}, []);

	return (
		<>
			<Hero />

			{showDelayedContent && (
				<>
					<CardSystem />
					<DigitalSystem />
					<AgentSystem />
					<Solution />
					<ServiceModel />
					<HumanExample />
					{/* <Newsletter /> */}
				</>
			)}
		</>
	);
}
