"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { apiClient } from "@shared/lib/api-client";
import { useMutation } from "@tanstack/react-query";
import { Checkbox } from "@ui/components/checkbox";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Textarea } from "@ui/components/textarea";
import { toast } from "@ui/hooks/use-toast";
import { CheckCircle2 } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

// 定义表单验证模式
const contactFormSchema = z.object({
	name: z.string().min(2, "姓名至少需要2个字符"),
	email: z.string().email("请输入有效的电子邮件地址"),
	phone: z
		.string()
		.min(11, "手机号必须是11位")
		.max(11, "手机号必须是11位")
		.regex(/^1[3-9]\d{9}$/, "请输入正确的手机号格式"),
	company: z.string().optional(),
	industry: z.string().min(1, "请选择所属行业"),
	products: z.array(z.string()).min(1, "请至少选择一个咨询产品"),
	serviceTypes: z.array(z.string()).min(1, "请至少选择一个服务类型"),
	message: z.string().min(10, "留言内容至少需要10个字符"),
});

type ContactFormData = z.infer<typeof contactFormSchema>;

const industryOptions = [
	{ value: "tech", label: "互联网/科技" },
	{ value: "finance", label: "金融/银行/保险" },
	{ value: "education", label: "教育/培训" },
	{ value: "retail", label: "零售/电商" },
	{ value: "manufacturing", label: "制造业" },
	{ value: "realestate", label: "房地产/建筑" },
	{ value: "healthcare", label: "医疗/健康" },
	{ value: "media", label: "文化/媒体/娱乐" },
	{ value: "government", label: "政府/机构" },
	{ value: "service", label: "专业服务" },
	{ value: "tourism", label: "旅游/酒店" },
	{ value: "logistics", label: "物流/运输" },
	{ value: "energy", label: "能源/环保" },
	{ value: "automotive", label: "汽车/出行" },
	{ value: "other", label: "其他行业" },
];

const productOptions = [
	"企业会员",
	"数字人定制",
	"IP 打造",
	"账号授权",
	"线索管理",
];

const serviceTypeOptions = [
	"购买服务",
	"商务合作",
	"技术合作",
	"问题反馈",
	"其他",
];

export default function ContactPage() {
	const [submissionStatus, setSubmissionStatus] = useState<
		"idle" | "success"
	>("idle");
	const form = useForm<ContactFormData>({
		resolver: zodResolver(contactFormSchema) as any,
		defaultValues: {
			name: "",
			email: "",
			phone: "",
			company: "",
			industry: "",
			products: [],
			serviceTypes: [],
			message: "",
		} as ContactFormData,
	});

	const { mutateAsync: submitContactForm, isPending: isSubmitting } =
		useMutation({
			mutationFn: async (data: ContactFormData) => {
				const message = `
				行业：${data.industry}
				咨询产品：${data.products.join(", ")}
				服务类型：${data.serviceTypes.join(", ")}
				具体需求：${data.message}
			`;

				const response = await apiClient.contact.submit.$post({
					form: {
						name: data.name,
						email: data.email,
						phone: data.phone,
						company: data.company,
						message: message,
					},
				});

				if (!response.ok) {
					const errorData = await response.json();
					throw new Error(errorData.error || "提交失败");
				}

				return response;
			},
			onSuccess: () => {
				setSubmissionStatus("success");
				toast({
					title: "提交成功",
					description: "我们会尽快与您联系！",
					variant: "default",
				});
			},
			onError: (error) => {
				toast({
					title: "提交失败",
					description:
						error instanceof Error ? error.message : "请稍后重试",
					variant: "error",
				});
			},
		});

	const onSubmit = async (data: ContactFormData) => {
		await submitContactForm(data);
	};

	const handleReset = () => {
		form.reset();
		setSubmissionStatus("idle");
	};

	return (
		<main
			className="min-h-screen flex flex-col bg-cover bg-center bg-no-repeat"
			style={{
				backgroundImage:
					"url('https://cminio.9000aigc.com/static/contactus/formbg.png')",
				backgroundPosition: "center 30%",
			}}
		>
			<div className="flex-grow flex items-center justify-center bg-black bg-opacity-50">
				<div className="container mx-auto px-4 py-16 max-w-2xl mt-20">
					<h1 className="font-bold text-2xl lg:text-3xl mb-4 text-center text-white">
						「9000AI 增强版数字人」
					</h1>
					<p className="text-lg opacity-80 mb-8 text-center text-white">
						请完善以下您的信息，方便我们为您提供更好的业务方案，或者您对我们的产品有什么问题或合作意向，请联系我们！
					</p>

					{submissionStatus === "success" ? (
						<div className="flex flex-col items-center space-y-6 rounded-lg bg-black/40 p-8 text-center backdrop-blur-sm">
							<CheckCircle2 className="h-16 w-16 animate-pulse text-green-400" />
							<h2 className="text-2xl font-bold text-white">
								提交成功！
							</h2>
							<p className="text-white/80">
								感谢您的垂询，我们的团队会尽快与您联系。
							</p>
							<button
								type="button"
								onClick={handleReset}
								className="mt-4 w-full max-w-xs rounded-md bg-[#d0fa50] py-3 px-4 font-semibold text-gray-800 transition duration-300 hover:bg-[#bfe048] focus:outline-none focus:ring-2 focus:ring-[#d0fa50] focus:ring-opacity-50"
							>
								提交另一条消息
							</button>
						</div>
					) : (
						<Form {...form}>
							<form
								onSubmit={form.handleSubmit(onSubmit)}
								className="space-y-6 bg-black/40 p-8 rounded-lg backdrop-blur-sm"
							>
								<FormField
									control={form.control}
									name="name"
									render={({ field }) => (
										<FormItem>
											<FormLabel className="text-white">
												如何称呼您
											</FormLabel>
											<FormControl>
												<Input
													placeholder="用于我们更好的与您沟通"
													{...field}
													className="bg-transparent text-white placeholder:text-gray-400"
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name="email"
									render={({ field }) => (
										<FormItem>
											<FormLabel className="text-white">
												电子邮箱
											</FormLabel>
											<FormControl>
												<Input
													type="email"
													placeholder="请输入您的电子邮箱"
													{...field}
													className="bg-transparent text-white placeholder:text-gray-400"
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name="phone"
									render={({ field }) => (
										<FormItem>
											<FormLabel className="text-white">
												联系电话
											</FormLabel>
											<FormControl>
												<Input
													type="tel"
													placeholder="请输入11位手机号码"
													{...field}
													className="bg-transparent text-white placeholder:text-gray-400"
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name="industry"
									render={({ field }) => (
										<FormItem>
											<FormLabel className="text-white">
												所属行业
											</FormLabel>
											<Select
												onValueChange={field.onChange}
												defaultValue={field.value}
											>
												<FormControl>
													<SelectTrigger className="bg-transparent text-white">
														<SelectValue placeholder="请选择您的所属行业" />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													{industryOptions.map(
														(option) => (
															<SelectItem
																key={
																	option.value
																}
																value={
																	option.value
																}
															>
																{option.label}
															</SelectItem>
														),
													)}
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name="company"
									render={({ field }) => (
										<FormItem>
											<FormLabel className="text-white">
												企业名称
											</FormLabel>
											<FormControl>
												<Input
													placeholder="请输入企业或团队名称"
													{...field}
													className="bg-transparent text-white placeholder:text-gray-400"
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name="products"
									render={() => (
										<FormItem>
											<FormLabel className="text-white">
												咨询产品
											</FormLabel>
											<div className="grid grid-cols-2 md:grid-cols-3 gap-4">
												{productOptions.map((item) => (
													<FormField
														key={item}
														control={form.control}
														name="products"
														render={({ field }) => {
															return (
																<FormItem
																	key={item}
																	className="flex flex-row items-start space-x-3 space-y-0"
																>
																	<FormControl>
																		<Checkbox
																			checked={field.value?.includes(
																				item,
																			)}
																			onCheckedChange={(
																				checked,
																			) => {
																				return checked
																					? field.onChange(
																							[
																								...(field.value ??
																									[]),
																								item,
																							],
																						)
																					: field.onChange(
																							field.value?.filter(
																								(
																									value,
																								) =>
																									value !==
																									item,
																							),
																						);
																			}}
																		/>
																	</FormControl>
																	<FormLabel className="text-white font-normal">
																		{item}
																	</FormLabel>
																</FormItem>
															);
														}}
													/>
												))}
											</div>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name="serviceTypes"
									render={() => (
										<FormItem>
											<FormLabel className="text-white">
												需求服务类型
											</FormLabel>
											<div className="grid grid-cols-2 md:grid-cols-3 gap-4">
												{serviceTypeOptions.map(
													(item) => (
														<FormField
															key={item}
															control={
																form.control
															}
															name="serviceTypes"
															render={({
																field,
															}) => {
																return (
																	<FormItem
																		key={
																			item
																		}
																		className="flex flex-row items-start space-x-3 space-y-0"
																	>
																		<FormControl>
																			<Checkbox
																				checked={field.value?.includes(
																					item,
																				)}
																				onCheckedChange={(
																					checked,
																				) => {
																					return checked
																						? field.onChange(
																								[
																									...(field.value ??
																										[]),
																									item,
																								],
																							)
																						: field.onChange(
																								field.value?.filter(
																									(
																										value,
																									) =>
																										value !==
																										item,
																								),
																							);
																				}}
																			/>
																		</FormControl>
																		<FormLabel className="text-white font-normal">
																			{
																				item
																			}
																		</FormLabel>
																	</FormItem>
																);
															}}
														/>
													),
												)}
											</div>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name="message"
									render={({ field }) => (
										<FormItem>
											<FormLabel className="text-white">
												其他需求
											</FormLabel>
											<FormControl>
												<Textarea
													placeholder="请输入您的需求"
													className="resize-none bg-transparent text-white placeholder:text-gray-400"
													{...field}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<button
									type="submit"
									disabled={isSubmitting}
									className={`w-full py-3 px-4 bg-[#d0fa50] hover:bg-[#bfe048] rounded-md transition duration-300 text-gray-800 font-semibold focus:outline-none focus:ring-2 focus:ring-[#d0fa50] focus:ring-opacity-50 ${isSubmitting ? "opacity-50 cursor-not-allowed" : ""}`}
								>
									{isSubmitting ? "提交中..." : "提交"}
								</button>
							</form>
						</Form>
					)}
				</div>
			</div>
		</main>
	);
}
