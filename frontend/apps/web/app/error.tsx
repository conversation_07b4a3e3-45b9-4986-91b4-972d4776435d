"use client";

import { Button } from "@ui/components/button";
import { AlertTriangleIcon } from "lucide-react";
import { useEffect } from "react";

export default function GlobalError({
	error,
	reset,
}: {
	error: Error & { digest?: string };
	reset: () => void;
}) {
	useEffect(() => {
		// 可选择将错误记录到错误跟踪服务
		console.error("全局错误:", error);
	}, [error]);

	return (
		<div className="flex h-screen flex-col items-center justify-center bg-black">
			<h1 className="text-[120px] font-bold text-[#ff5e3a] animate-pulse drop-shadow-[0_0_10px_#ff5e3a]">
				500
			</h1>

			<div className="flex items-center gap-3 text-[#ff5e3a] text-4xl mb-10 animate-pulse">
				<AlertTriangleIcon className="size-8" />
				<span className="font-bold tracking-wider">系统错误</span>
				<span className="text-sm opacity-70">服务器需要充电...</span>
			</div>

			<p className="text-[#ff5e3a] text-xl mb-10">
				系统遇到了一点小问题，我们的技术人员正在加班处理
			</p>

			<Button
				onClick={reset}
				className="border border-[#ff5e3a] bg-transparent text-[#ff5e3a] hover:bg-[#ff5e3a20] px-8 py-2 text-lg"
			>
				【 尝试修复问题 】
			</Button>
		</div>
	);
}
