import { cn } from "@ui/lib";
import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import type { PropsWithChildren } from "react";
import "./globals.css";

const sansFont = Poppins({
	subsets: ["latin"],
	weight: ["400", "500", "600", "700"],
	variable: "--font-sans",
	display: "swap",
});

export const metadata: Metadata = {
	title: {
		absolute: "9000AI 增强版数字人",
		default: "9000AI 增强版数字人",
		template: "%s | 9000AI 增强版数字人",
	},
};

export default function RootLayout({ children }: PropsWithChildren) {
	return (
		<html lang="zh-CN" suppressHydrationWarning>
			<body
				suppressHydrationWarning={true}
				className={cn(
					"min-h-screen bg-background font-sans text-foreground antialiased",
					sansFont.variable,
				)}
			>
				{children}
			</body>
		</html>
	);
}
