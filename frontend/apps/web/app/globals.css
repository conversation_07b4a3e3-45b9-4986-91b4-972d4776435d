@import "tailwindcss";
@import "fumadocs-ui/css/neutral.css";
@import "fumadocs-ui/css/preset.css";
@import "@repo/tailwind-config/theme.css";

@custom-variant dark (&:is(.dark *));

@plugin "tailwindcss-animate";

@source "../node_modules/fumadocs-ui/dist/**/*.js";

@variant dark (&:where(.dark, .dark *));

pre.shiki {
	@apply mb-4 rounded-lg p-6;
}

#nd-sidebar {
	@apply bg-card top-[4.5rem] md:h-[calc(100dvh-4.5rem)];

	button[data-search-full] {
		@apply bg-transparent;
	}
}

#nd-page .prose {
	h1,
	h2,
	h3,
	h4,
	h5,
	h6 {
		a {
			@apply no-underline;
		}
	}
}

div[role="tablist"].bg-secondary {
	@apply bg-muted;
}

input[cmdk-input] {
	@apply border-none focus-visible:ring-0;
}

@layer base {
	* {
		@apply border-border outline-ring/50;
	}

	body {
		@apply bg-background text-foreground;
	}
}

/* 星星闪烁动画 */
@keyframes twinkle {
	0%,
	100% {
		opacity: 0.2;
		transform: scale(0.8) translate(0, 0);
	}

	50% {
		opacity: 1;
		transform: scale(1) translate(1px, 1px);
	}
}

/* 边框光束动画 */
@keyframes border-beam {
	0% {
		transform: translateZ(0) rotate(0deg);
	}

	100% {
		transform: translateZ(0) rotate(360deg);
	}
}

/* 图像发光动画 */
@keyframes image-glow {
	0% {
		opacity: 0;
		filter: blur(180px);
	}

	33% {
		opacity: 1;
		filter: blur(180px);
	}

	66% {
		opacity: 0.5;
		filter: blur(180px);
	}

	100% {
		opacity: 0;
		filter: blur(180px);
	}
}

/* 自定义光束效果工具类 */
@layer utilities {
	.glow-animation {
		animation: image-glow 6s ease-in-out infinite;
	}

	.border-beam-animation {
		animation: border-beam 12s linear infinite;
	}

	/* 定义一个叫做 glow 的动画效果 - 使用 CSS 变量 */
	@keyframes glow {
		/* 从弱光晕开始 */
		0% {
			/* var() 函数用于读取 CSS 变量，如果变量未定义，则使用后面的备用值 */
			text-shadow: 0 0 5px var(--glow-color-faint, rgba(2, 240, 168, 0.5));
		}

		/* 中间光晕最强 */
		50% {
			text-shadow:
				0 0 15px var(--glow-color-mid, rgba(2, 240, 168, 0.8)),
				0 0 20px var(--glow-color-faint, rgba(2, 240, 168, 0.5));
		}

		/* 结束时恢复弱光晕 */
		100% {
			text-shadow: 0 0 5px var(--glow-color-faint, rgba(2, 240, 168, 0.5));
		}
	}

	/* 青色变体样式 */
	.ai-item-box-cyan {
		/* 定义青色系的 CSS 变量 */
		--glow-color-faint: rgba(2, 240, 168, 0.5);
		--glow-color-mid: rgba(2, 240, 168, 0.8);
		/* 设置初始的青色外发光 */
		box-shadow: 0 0 20px rgba(2, 240, 168, 0.3);
	}

	/* 青色变体悬停时，增强外发光 */
	.ai-item-box-cyan:hover {
		box-shadow: 0 0 30px rgba(2, 240, 168, 0.5);
	}

	/* 金色变体样式 */
	.ai-item-box-gold {
		/* 定义金色系的 CSS 变量 (使用接近金色的橙黄色) */
		--glow-color-faint: rgba(255, 190, 0, 0.6);
		--glow-color-mid: rgba(255, 190, 0, 0.9);
		/* 设置初始的金色外发光 - 稍微调亮一点透明度 (从 0.3 到 0.4) */
		box-shadow: 0 0 20px rgba(255, 190, 0, 0.4);
	}

	/* 金色变体悬停时，增强外发光 - 对应增强一点 (从 0.5 到 0.6) */
	.ai-item-box-gold:hover {
		box-shadow: 0 0 30px rgba(255, 190, 0, 0.6);
	}

	/* 当鼠标悬停在任意 .ai-item-box 上时 */
	#nd-page .ai-item-box:hover h2 {
		/* 应用 glow 动画，动画会根据父元素设置的 CSS 变量来显示颜色 */
		animation: glow 2s infinite;
	}

	/* 当鼠标悬停在任意 .ai-item-box 上时 */
	.ai-item-box:hover {
		/* 向上移动效果 */
		transform: translateY(-5px);
	}
}

/* Inward Glow Animation for Cards */
@keyframes radiate-glow {
	0% {
		box-shadow:
			inset 0 0 2px 0px white,
			inset 0 0 5px 2px rgba(96, 165, 250, 0.6),
			inset 0 0 10px 5px rgba(59, 130, 246, 0.3);
		opacity: 0.7;
	}

	50% {
		box-shadow:
			inset 0 0 4px 1px white,
			inset 0 0 10px 4px rgba(96, 165, 250, 0.8),
			inset 0 0 20px 10px rgba(59, 130, 246, 0.5);
		opacity: 1;
	}

	100% {
		box-shadow:
			inset 0 0 2px 0px white,
			inset 0 0 5px 2px rgba(96, 165, 250, 0.6),
			inset 0 0 10px 5px rgba(59, 130, 246, 0.3);
		opacity: 0.7;
	}
}

.animate-inward-glow::before {
	content: "";
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	border-radius: inherit;
	/* Match parent's border radius */
	animation: radiate-glow 3s ease-in-out infinite;
	pointer-events: none;
	/* Allow interaction with card content */
	z-index: 0;
	/* Ensure glow is behind content if needed */
}

/* Green Inward Glow Animation */
@keyframes radiate-glow-green {
	0% {
		box-shadow:
			inset 0 0 2px 0px white,
			inset 0 0 5px 2px rgba(74, 222, 128, 0.6), /* green-400/60 */
			inset 0 0 10px 5px rgba(34, 197, 94, 0.3);
		/* green-500/30 */
		opacity: 0.7;
	}

	50% {
		box-shadow:
			inset 0 0 4px 1px white,
			inset 0 0 10px 4px rgba(74, 222, 128, 0.8), /* green-400/80 */
			inset 0 0 20px 10px rgba(34, 197, 94, 0.5);
		/* green-500/50 */
		opacity: 1;
	}

	100% {
		box-shadow:
			inset 0 0 2px 0px white,
			inset 0 0 5px 2px rgba(74, 222, 128, 0.6), /* green-400/60 */
			inset 0 0 10px 5px rgba(34, 197, 94, 0.3);
		/* green-500/30 */
		opacity: 0.7;
	}
}

/* Gold Inward Glow Animation */
@keyframes radiate-glow-gold {
	0% {
		box-shadow:
			inset 0 0 2px 0px white,
			inset 0 0 5px 2px rgba(255, 190, 0, 0.6), /* gold/60 */
			inset 0 0 10px 5px rgba(255, 190, 0, 0.3);
		/* gold/30 */
		opacity: 0.7;
	}

	50% {
		box-shadow:
			inset 0 0 4px 1px white,
			inset 0 0 10px 4px rgba(255, 190, 0, 0.8), /* gold/80 */
			inset 0 0 20px 10px rgba(255, 190, 0, 0.5);
		/* gold/50 */
		opacity: 1;
	}

	100% {
		box-shadow:
			inset 0 0 2px 0px white,
			inset 0 0 5px 2px rgba(255, 190, 0, 0.6), /* gold/60 */
			inset 0 0 10px 5px rgba(255, 190, 0, 0.3);
		/* gold/30 */
		opacity: 0.7;
	}
}

.animate-inward-glow-green::before {
	content: "";
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	border-radius: inherit;
	animation: radiate-glow-green 3s ease-in-out infinite;
	pointer-events: none;
	z-index: 0;
}

.animate-inward-glow-gold::before {
	content: "";
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	border-radius: inherit;
	animation: radiate-glow-gold 3s ease-in-out infinite;
	pointer-events: none;
	z-index: 0;
}

/* 新版 Orange/Red Inward Glow Animation - 尝试叠加更多颜色 */
@keyframes radiate-glow-orange-red {
	0% {
		/* 较弱状态: 外红(#e0360b) -> 内橙红(#e9522a) */
		box-shadow:
			inset 0 0 2px 0px white,
			inset 0 0 5px 2px rgba(224, 54, 11, 0.4), /* #e0360b at 40% */
			inset 0 0 8px 4px rgba(233, 82, 42, 0.2);
		/* #e9522a at 20% */
		opacity: 0.7;
	}

	50% {
		/* 较强状态: 外橙(#fd7841) -> 内黄(#ffe501) */
		box-shadow:
			inset 0 0 4px 1px white,
			inset 0 0 10px 4px rgba(253, 120, 65, 0.7), /* #fd7841 at 70% */
			inset 0 0 18px 8px rgba(255, 229, 1, 0.5);
		/* #ffe501 at 50% */
		opacity: 1;
	}

	100% {
		/* 同 0% */
		box-shadow:
			inset 0 0 2px 0px white,
			inset 0 0 5px 2px rgba(224, 54, 11, 0.4),
			inset 0 0 8px 4px rgba(233, 82, 42, 0.2);
		opacity: 0.7;
	}
}

/* 新版 Yellow/Blue/Purple Inward Glow Animation - 尝试叠加更多颜色 */
@keyframes radiate-glow-yellow-blue-purple {
	0% {
		/* 较弱状态: 外深紫(#4b40be) -> 内紫(#886492) */
		box-shadow:
			inset 0 0 2px 0px white,
			inset 0 0 5px 2px rgba(75, 64, 190, 0.4), /* #4b40be at 40% */
			inset 0 0 8px 4px rgba(136, 100, 146, 0.2);
		/* #886492 at 20% */
		opacity: 0.7;
	}

	50% {
		/* 较强状态: 外蓝(#20a0de) -> 内黄(#fcea3c) */
		box-shadow:
			inset 0 0 4px 1px white,
			inset 0 0 10px 4px rgba(32, 160, 222, 0.7), /* #20a0de at 70% */
			inset 0 0 18px 8px rgba(252, 234, 60, 0.5);
		/* #fcea3c at 50% */
		opacity: 1;
	}

	100% {
		/* 同 0% */
		box-shadow:
			inset 0 0 2px 0px white,
			inset 0 0 5px 2px rgba(75, 64, 190, 0.4),
			inset 0 0 8px 4px rgba(136, 100, 146, 0.2);
		opacity: 0.7;
	}
}

/* 应用 Orange/Red 动画 */
.animate-inward-glow-orange-red::before {
	content: "";
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	border-radius: inherit;
	animation: radiate-glow-orange-red 3s ease-in-out infinite;
	pointer-events: none;
	z-index: 0;
}

/* 应用 Yellow/Blue/Purple 动画 */
.animate-inward-glow-yellow-blue-purple::before {
	content: "";
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	border-radius: inherit;
	animation: radiate-glow-yellow-blue-purple 3s ease-in-out infinite;
	pointer-events: none;
	z-index: 0;
}
