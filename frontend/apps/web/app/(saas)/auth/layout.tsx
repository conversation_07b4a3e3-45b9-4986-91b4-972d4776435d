import { SessionProvider } from "@saas/auth/components/SessionProvider";
import { getTranslations } from "next-intl/server";

export const dynamic = "force-dynamic";
export const revalidate = 0;

export async function generateMetadata() {
	const t = await getTranslations();

	return {
		title: t("auth.login.title"),
	};
}

export default function AuthLayout({
	children,
}: {
	children: React.ReactNode;
}) {
	return (
		<SessionProvider>
			<div className="flex min-h-screen flex-col">
				<main className="flex-1">{children}</main>
			</div>
		</SessionProvider>
	);
}
