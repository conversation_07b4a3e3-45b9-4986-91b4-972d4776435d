"use client";

import { motion } from "framer-motion";

export default function ContentSystemPage() {
	return (
		<div className="container mx-auto p-8">
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.5 }}
				className="space-y-6"
			>
				<h1 className="text-4xl font-bold bg-gradient-to-r from-yellow-300 to-green-400 bg-clip-text text-transparent">
					AI 图文系统
				</h1>
				<p className="text-yellow-200/70 text-lg">
					智能创作图文内容，实现裂变式传播
				</p>
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
					{["智能创作", "图文编辑", "一键分发", "效果分析"].map(
						(feature, index) => (
							<motion.div
								key={feature}
								initial={{ opacity: 0, y: 20 }}
								animate={{ opacity: 1, y: 0 }}
								transition={{
									duration: 0.5,
									delay: index * 0.1,
								}}
								className="p-6 rounded-xl bg-white/5 hover:bg-white/10 transition-all"
							>
								<h3 className="text-xl font-semibold text-yellow-100 mb-2">
									{feature}
								</h3>
								<p className="text-yellow-200/70">即将推出</p>
							</motion.div>
						),
					)}
				</div>
			</motion.div>
		</div>
	);
}
