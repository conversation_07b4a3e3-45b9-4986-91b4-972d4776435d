import { Metadata } from "next";
import { WorkflowRunView } from "@saas/ecosystem/superbrain/workflow/viewer/WorkflowRunView";
import { redirect } from "next/navigation";

export const metadata: Metadata = {
  title: "运行工作流 - 9000AI超级大脑",
  description: "为不同工作流提供基础运行与结果展示界面",
};

export default function Page({ searchParams }: { searchParams?: { app?: string; mode?: string; runId?: string } }) {
  const appId = searchParams?.app;
  if (!appId) {
    // 缺少 appId 时返回到工作流首页
    redirect("/app/ecosystem/superbrain/workflow");
  }
  return <WorkflowRunView appId={appId!} />;
}

