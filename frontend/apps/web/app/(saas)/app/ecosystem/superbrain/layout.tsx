"use client";

import { SuperBrainWrapper } from "@saas/ecosystem/superbrain/layout/SuperBrainWrapper";
import { usePathname } from "next/navigation";
import { ReactNode } from "react";

interface SuperBrainLayoutProps {
  children: ReactNode;
}

export default function SuperBrainLayout({ children }: SuperBrainLayoutProps) {
  const pathname = usePathname();
  const showHeader = true;
  // 聊天/运行等全屏视图隐藏侧边栏
  const showSidebar = !(
    pathname?.includes("/workflow/agent") ||
    pathname?.includes("/workflow/view") ||
    pathname?.includes("/workflow/run")
  );
  const isFullScreen = !showSidebar; // 与上方规则一致：这些页面全屏展示
  
  if (isFullScreen) {
    return <>{children}</>;
  }

  return (
    <SuperBrainWrapper showHeader={showHeader} showSidebar={showSidebar} currentPath={pathname}>
      {children}
    </SuperBrainWrapper>
  );
}