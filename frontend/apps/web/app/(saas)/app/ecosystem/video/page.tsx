"use client";

import { motion } from "framer-motion";

export default function VideoSystemPage() {
	return (
		<div className="container mx-auto p-8">
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.5 }}
				className="space-y-6"
			>
				<h1 className="text-4xl font-bold bg-gradient-to-r from-yellow-300 to-green-400 bg-clip-text text-transparent">
					AI 视频系统
				</h1>
				<p className="text-yellow-200/70 text-lg">
					智能生成营销视频，提升传播效果
				</p>
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
					{["智能剪辑", "特效制作", "批量生成", "数据追踪"].map(
						(feature, index) => (
							<motion.div
								key={feature}
								initial={{ opacity: 0, y: 20 }}
								animate={{ opacity: 1, y: 0 }}
								transition={{
									duration: 0.5,
									delay: index * 0.1,
								}}
								className="p-6 rounded-xl bg-white/5 hover:bg-white/10 transition-all"
							>
								<h3 className="text-xl font-semibold text-yellow-100 mb-2">
									{feature}
								</h3>
								<p className="text-yellow-200/70">即将推出</p>
							</motion.div>
						),
					)}
				</div>
			</motion.div>
		</div>
	);
}
