import { AgentAuthGuard } from "@saas/auth/components/AgentAuthGuard";
import { AgentWrapper } from "@saas/agent/components/layout/AgentWrapper";
import { AgentSystemType } from "@saas/agent/config/types";
import { SessionProvider } from "@saas/auth/components/SessionProvider";
import { sessionQueryKey } from "@saas/auth/lib/api";
import { getSession } from "@saas/auth/lib/server";
import { ConfirmationAlertProvider } from "@saas/shared/agent/components/ConfirmationAlertProvider";
import { getQueryClient } from "@shared/lib/server";
import { dehydrate, HydrationBoundary } from "@tanstack/react-query";
import type { PropsWithChildren } from "react";

export const dynamic = "force-dynamic";
export const revalidate = 0;

export default async function Layout({ children }: PropsWithChildren) {
	const session = await getSession();
	const queryClient = getQueryClient();

	await queryClient.prefetchQuery({
		queryKey: sessionQueryKey,
		queryFn: () => session,
	});

	// 获取系统类型
	let systemType = AgentSystemType.MERCHANT; // 默认为代理商系统

	// 管理员角色使用管理员系统类型
	if (session?.user?.agentRole === "ADMIN") {
		systemType = AgentSystemType.ADMIN;
	}
	// 销售角色使用销售系统类型
	else if (session?.user?.agentRole === "SALES") {
		systemType = AgentSystemType.SALES;
	}

	return (
		<SessionProvider>
			<HydrationBoundary state={dehydrate(queryClient)}>
				<ConfirmationAlertProvider>
					<AgentAuthGuard>
						<AgentWrapper systemType={systemType}>
							{children}
						</AgentWrapper>
					</AgentAuthGuard>
				</ConfirmationAlertProvider>
			</HydrationBoundary>
		</SessionProvider>
	);
}
