import { SessionProvider } from "@saas/auth/components/SessionProvider";
import { sessionQuery<PERSON>ey } from "@saas/auth/lib/api";
import { getSession } from "@saas/auth/lib/server";
import { ConfirmationAlertProvider } from "@saas/shared/agent/components/ConfirmationAlertProvider";
import { getQueryClient } from "@shared/lib/server";
import { dehydrate, HydrationBoundary } from "@tanstack/react-query";
import type { PropsWithChildren } from "react";

export const dynamic = "force-dynamic";
export const revalidate = 0;

export default async function Layout({ children }: PropsWithChildren) {
	const session = await getSession();

	const queryClient = getQueryClient();

	await queryClient.prefetchQuery({
		queryKey: sessionQueryKey,
		queryFn: () => session,
	});

	return (
		<HydrationBoundary state={dehydrate(queryClient)}>
			<SessionProvider>
				<ConfirmationAlertProvider>
					{children}
				</ConfirmationAlertProvider>
			</SessionProvider>
		</HydrationBoundary>
	);
}
