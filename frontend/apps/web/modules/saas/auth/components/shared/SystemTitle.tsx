"use client";

import { motion } from "framer-motion";

interface SystemTitleProps {
	title: string;
	subtitle: string;
}

export function SystemTitle({ title, subtitle }: SystemTitleProps) {
	return (
		<motion.div
			className="text-center mb-4 md:mb-6"
			initial={{ opacity: 0, y: -20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.5, delay: 0.2 }}
		>
			<motion.div
				initial={{ opacity: 0, scale: 0.9 }}
				animate={{ opacity: 1, scale: 1 }}
				transition={{ duration: 0.5, delay: 0.3 }}
			>
				<h1 className="text-3xl md:text-4xl lg:text-5xl font-bold bg-gradient-to-r from-yellow-100 via-yellow-200 to-yellow-100 bg-clip-text text-transparent mb-2">
					{title}
				</h1>
			</motion.div>
			<motion.p
				className="text-base md:text-lg text-yellow-200/80 max-w-2xl mx-auto"
				initial={{ opacity: 0 }}
				animate={{ opacity: 1 }}
				transition={{ duration: 0.5, delay: 0.4 }}
			>
				{subtitle}
			</motion.p>
		</motion.div>
	);
}
