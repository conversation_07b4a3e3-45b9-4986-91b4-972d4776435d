import { useVerificationCode } from "@saas/auth/hooks/useVerificationCode";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { Loader2 } from "lucide-react";

interface VerificationCodeProps {
	onSendCode: () => Promise<void>;
	value: string;
	onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
	disabled?: boolean;
	error?: boolean;
	errorMessage?: string;
	isValidPhone?: boolean;
	className?: string;
	id?: string;
}

const gradientButtonStyle = {
	backgroundImage: "linear-gradient(135deg, #f0f0f0,#fff9ab)",
	color: "#60533d",
	border: "none",
	fontWeight: "bold",
	fontSize: "1rem",
	letterSpacing: "0.05em",
} as const;

const disabledButtonStyle = {
	backgroundColor: "#2c302b",
	color: "#a0aec0",
	border: "none",
	fontWeight: "bold",
	fontSize: "1rem",
	letterSpacing: "0.05em",
	cursor: "not-allowed",
} as const;

export function VerificationCode({
	onSendCode,
	value,
	onChange,
	disabled = false,
	error = false,
	errorMessage,
	isValidPhone = false,
	className,
	id,
}: VerificationCodeProps) {
	const { isSending, countdown, handleSendCode } =
		useVerificationCode(onSendCode);

	return (
		<div className="flex space-x-4">
			<motion.div
				className="flex-grow"
				whileHover={{ scale: 1.01 }}
				whileTap={{ scale: 0.99 }}
				transition={{ type: "spring", stiffness: 300 }}
				tabIndex={-1}
			>
				<Input
					id={id}
					value={value}
					onChange={onChange}
					type="text"
					placeholder="请输入6位验证码"
					maxLength={6}
					disabled={disabled || !isValidPhone}
					className={cn(
						"!placeholder-yellow-200/50 flex h-12 w-full rounded-xl border-none bg-white/5 px-6 py-3 text-gray-200 text-lg transition-all duration-300 ease-in-out hover:bg-white/10 focus:bg-white/20 focus:ring-2 focus:ring-yellow-100 disabled:opacity-50",
						error && "ring-2 ring-red-500/50",
						className,
					)}
					aria-label="验证码输入框"
				/>
				{errorMessage && (
					<motion.div
						initial={{ opacity: 0, x: -10 }}
						animate={{ opacity: 1, x: 0 }}
						className="mt-1 text-sm text-red-400"
					>
						{errorMessage}
					</motion.div>
				)}
			</motion.div>
			<motion.div
				whileHover={{ scale: 1.05 }}
				whileTap={{ scale: 0.95 }}
				transition={{ type: "spring", stiffness: 400 }}
				className="flex items-end"
				tabIndex={-1}
			>
				<Button
					type="button"
					onClick={handleSendCode}
					disabled={
						isSending || countdown > 0 || !isValidPhone || disabled
					}
					className={`h-12 min-w-[170px] rounded-xl px-4 text-base transition-all duration-300 ease-in-out ${
						isSending || countdown > 0 || !isValidPhone || disabled
							? "opacity-70"
							: "hover:opacity-90 hover:shadow-lg"
					}`}
					style={
						isSending || countdown > 0 || !isValidPhone || disabled
							? disabledButtonStyle
							: gradientButtonStyle
					}
					aria-label="获取验证码按钮"
				>
					{isSending ? (
						<div className="flex items-center space-x-2">
							<Loader2 className="h-4 w-4 animate-spin" />
							<span>发送中...</span>
						</div>
					) : countdown > 0 ? (
						`${countdown}秒后重新获取`
					) : (
						"获取验证码"
					)}
				</Button>
			</motion.div>
		</div>
	);
}
