import { CountryCodeSelect } from "@saas/auth/components/CountryCodeSelect";
import { PasswordInput } from "@saas/auth/components/shared/PasswordInput";
import { Button } from "@ui/components/button";
import { FormControl, FormField, FormItem } from "@ui/components/form";
import { Input } from "@ui/components/input";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { Loader2 } from "lucide-react";
import { useCallback, useId } from "react";
import type { UseFormReturn } from "react-hook-form";

interface LoginByPasswordProps {
	form: UseFormReturn<any>;
	isLoading: boolean;
	isCheckingPhone: boolean;
	isValidPhone: boolean;
	onSubmit: () => void;
	validatePhone: (
		phoneNumber: string,
		phoneCountryCode: string,
	) => Promise<void>;
}

const gradientButtonStyle = {
	backgroundImage: "linear-gradient(100deg,#f0f0f0,#fff9ab)",
	color: "#60533d",
	border: "none",
	fontWeight: "bold",
	fontSize: "1rem",
	letterSpacing: "0.05em",
} as const;

const disabledButtonStyle = {
	backgroundColor: "#4a5568",
	color: "#a0aec0",
	border: "none",
	fontWeight: "bold",
	fontSize: "1rem",
	letterSpacing: "0.05em",
	cursor: "not-allowed",
} as const;

export function LoginByPassword({
	form,
	isLoading,
	isCheckingPhone,
	isValidPhone,
	onSubmit,
	validatePhone,
}: LoginByPasswordProps) {
	const handlePhoneBlur = useCallback(async () => {
		const phoneNumber = form.getValues("phoneNumber");
		const phoneCountryCode = form.getValues("phoneCountryCode");
		if (phoneNumber && phoneCountryCode) {
			await validatePhone(phoneNumber, phoneCountryCode);
		}
	}, [form, validatePhone]);
	const id = useId();

	return (
		<div className="space-y-5">
			<motion.div className="flex space-x-4">
				{/* 区号选择 */}
				<motion.div className="w-[140px]">
					<FormField
						control={form.control}
						name="phoneCountryCode"
						render={({ field }) => (
							<FormItem className="space-y-2">
								<label
									htmlFor="phoneCountryCode"
									className="block font-medium text-base text-yellow-100"
								>
									区号
								</label>
								<FormControl>
									<CountryCodeSelect
										value={field.value}
										onChange={(value) => {
											field.onChange(value);
											form.setValue("phoneNumber", "");
											form.clearErrors("phoneNumber");
										}}
										disabled={isLoading || isCheckingPhone}
									/>
								</FormControl>
							</FormItem>
						)}
					/>
				</motion.div>

				{/* 手机号输入框 */}
				<motion.div className="flex-1">
					<FormField
						control={form.control}
						name="phoneNumber"
						render={({ field }) => (
							<FormItem>
								<label
									htmlFor={`${id}-phoneNumber`}
									className="block font-medium text-base text-yellow-100"
								>
									手机号
								</label>
								<FormControl>
									<Input
										{...field}
										id={`${id}-phoneNumber`}
										type="tel"
										placeholder="请输入手机号码"
										disabled={isLoading || isCheckingPhone}
										className={cn(
											"!placeholder-yellow-200/50 flex h-12 w-full rounded-xl border-none bg-white/5 px-6 py-3 text-gray-200 text-lg transition-all duration-300 ease-in-out hover:bg-white/10 focus:bg-white/20 focus:ring-2 focus:ring-yellow-100 disabled:opacity-50",
											form.formState.errors.phoneNumber &&
												"ring-2 ring-red-500/50",
										)}
										onBlur={handlePhoneBlur}
										aria-label="手机号码输入框"
									/>
								</FormControl>
							</FormItem>
						)}
					/>
				</motion.div>
			</motion.div>

			<FormField
				control={form.control}
				name="password"
				render={({ field }) => (
					<FormItem>
						<label
							htmlFor={`${id}-password`}
							className="mb-2 block font-medium text-base text-yellow-100"
						>
							登录密码
						</label>
						<FormControl>
							<PasswordInput
								id={`${id}-password`}
								value={field.value}
								onChange={(e) => {
									field.onChange(e);
									if (
										e.target.value &&
										form.formState.errors.password
									) {
										form.clearErrors("password");
									}
								}}
								placeholder="请输入登录密码"
								disabled={isLoading || !isValidPhone}
								error={!!form.formState.errors.password}
								errorMessage={
									form.formState.errors.password
										?.message as string
								}
								aria-label="密码输入框"
							/>
						</FormControl>
					</FormItem>
				)}
			/>

			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.5, delay: 0.7 }}
				whileHover={{ scale: 1.02 }}
				whileTap={{ scale: 0.98 }}
			>
				<Button
					type="submit"
					onClick={onSubmit}
					disabled={
						isLoading ||
						!isValidPhone ||
						!form.getValues("password") ||
						isCheckingPhone ||
						!!form.formState.errors.phoneNumber
					}
					className={`h-14 w-full rounded-xl font-semibold text-xl tracking-wide transition-all duration-300 ${
						isLoading ||
						!isValidPhone ||
						!form.getValues("password") ||
						isCheckingPhone ||
						!!form.formState.errors.phoneNumber
							? "cursor-not-allowed opacity-70"
							: "hover:opacity-90 hover:shadow-lg"
					}`}
					style={
						isLoading ||
						!isValidPhone ||
						!form.getValues("password") ||
						isCheckingPhone ||
						!!form.formState.errors.phoneNumber
							? disabledButtonStyle
							: gradientButtonStyle
					}
					aria-label="登录按钮"
				>
					{isLoading ? (
						<div className="flex items-center justify-center space-x-2">
							<Loader2 className="h-5 w-5 animate-spin" />
							<span>登录中...</span>
						</div>
					) : isCheckingPhone ? (
						<div className="flex items-center justify-center space-x-2">
							<Loader2 className="h-5 w-5 animate-spin" />
							<span>验证手机号...</span>
						</div>
					) : form.formState.errors.phoneNumber ? (
						<span>
							{
								form.formState.errors.phoneNumber
									.message as string
							}
						</span>
					) : !isValidPhone ? (
						<span>请输入正确的手机号</span>
					) : (
						"立即登录"
					)}
				</Button>
			</motion.div>
		</div>
	);
}
