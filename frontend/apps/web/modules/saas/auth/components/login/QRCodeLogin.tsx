import { authClient } from "@repo/auth/client";
import type { WechatOfficialQrInitiateResponse } from "@repo/auth/plugins/social/endpoints/wechatMP/wechat-official-qr-initiate";
import type { WechatOfficialQrStatusResponse } from "@repo/auth/plugins/social/endpoints/wechatMP/wechat-official-qr-status";
// import { logger } from "@repo/logs";
import { motion } from "framer-motion";
import {
	AlertTriangleIcon,
	CheckCircleIcon,
	ClockIcon,
	Loader2Icon,
	XCircleIcon,
} from "lucide-react";
import Image from "next/image";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import { useSession } from "../../hooks/use-session";
import { WechatPostScanActions } from "./WechatPostScanActions";

interface QRCodeLoginProps {
	// onWechatRegisterRequest?: (sceneId: string) => void; // May become obsolete or change meaning
	onClose?: () => void;
	// onWechatBindSuccess?: (message?: string) => void; // Obsolete with new flow
	onLoginSuccess?: () => void; // Added to handle overall login success
}

export function QRCodeLogin({
	// onWechatRegisterRequest, // Temporarily commenting out as its role changes
	onClose,
	// onWechatBindSuccess,
	onLoginSuccess, // Destructure the new prop
}: QRCodeLoginProps) {
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [qrCodeImageUrl, setQrCodeImageUrl] = useState<string | null>(null);
	const [initialSceneId, setInitialSceneId] = useState<string | null>(null);
	const [pollingStatus, setPollingStatus] = useState<
		WechatOfficialQrStatusResponse["status"] | null
	>(null);
	const [pollingMessage, setPollingMessage] = useState<string | null>(null);
	const [isRegistered, setIsRegistered] = useState<boolean | undefined>(
		undefined,
	);
	const [retainedSceneId, setRetainedSceneId] = useState<string | null>(null);
	const [qrFlowState, setQrFlowState] = useState<
		"idle" | "awaitingScan" | "awaitingAction" | "success" | "error"
	>("idle");
	const isFetchingQrCodeRef = useRef(false);

	const { reloadSession, session } = useSession();
	const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
	const currentSessionUserId = useRef<string | undefined>(undefined);
	const currentFlowIdRef = useRef(0); // To track the current QR flow generation

	useEffect(() => {
		currentSessionUserId.current = session?.user?.id;
	}, [session?.user?.id]);

	// Function to fetch a new QR code and reset states
	const initiateNewQrCodeFlow = async () => {
		if (isFetchingQrCodeRef.current) {
			// logger.info("[initiateNewQrCodeFlow] Already fetching, returning.");
			return;
		}
		isFetchingQrCodeRef.current = true;

		currentFlowIdRef.current += 1; // Increment for a new flow
		const flowIdForThisInitiation = currentFlowIdRef.current; // Capture for use in this initiation
		// logger.info(
		// 	`[initiateNewQrCodeFlow] Starting new flow with ID: ${flowIdForThisInitiation}`,
		// );

		// Clear any existing polling interval
		if (pollingIntervalRef.current) {
			clearInterval(pollingIntervalRef.current);
			pollingIntervalRef.current = null;
		}

		// Reset all relevant states
		setIsLoading(true);
		setError(null);
		setQrCodeImageUrl(null);
		setInitialSceneId(null); // Crucial for stopping old polling and starting new
		setPollingStatus(null);
		setPollingMessage(null);
		setIsRegistered(undefined);
		setRetainedSceneId(null); // Clear retained sceneId as well
		setQrFlowState("idle"); // Start from idle, will move to awaitingScan upon success

		try {
			const response: WechatOfficialQrInitiateResponse =
				await authClient.wechat.initiateOfficialQrLogin();
			if (
				response.success &&
				response.qrCodeImageUrl &&
				response.sceneId
			) {
				// Check if a newer flow started during the await for QR code
				if (flowIdForThisInitiation !== currentFlowIdRef.current) {
					// logger.warn(
					// 	`[initiateNewQrCodeFlow] Flow ${flowIdForThisInitiation} became outdated during QR fetch (current: ${currentFlowIdRef.current}). Aborting state update.`,
					// );
					isFetchingQrCodeRef.current = false; // Ensure this is reset
					return;
				}
				setQrCodeImageUrl(response.qrCodeImageUrl);
				setInitialSceneId(response.sceneId); // Set the new sceneId to start polling for
				setPollingMessage("二维码已生成，等待用户扫码...");
				setQrFlowState("awaitingScan");
			} else {
				throw new Error(response.message || "获取公众号二维码失败");
			}
		} catch (err) {
			// logger.error("获取公众号二维码时出错:", err);
			const errorMessage =
				err instanceof Error
					? err.message
					: "获取公众号二维码时发生未知错误";
			setError(errorMessage);
			toast.error("加载二维码失败", { description: errorMessage });
			setQrFlowState("error");
		} finally {
			setIsLoading(false);
			isFetchingQrCodeRef.current = false;
		}
	};

	// 获取公众号二维码 (on mount)
	useEffect(() => {
		initiateNewQrCodeFlow();

		// Component unmount cleanup
		return () => {
			if (pollingIntervalRef.current) {
				clearInterval(pollingIntervalRef.current);
				pollingIntervalRef.current = null;
			}
		};
	}, []); // Empty dependency array ensures this runs only on mount and unmount

	// 轮询状态
	useEffect(() => {
		const flowIdWhenEffectHookRan = currentFlowIdRef.current; // Capture flow ID for this effect setup/run

		if (
			!initialSceneId ||
			qrFlowState === "success" ||
			qrFlowState === "awaitingAction" ||
			qrFlowState === "error"
		) {
			if (pollingIntervalRef.current) {
				clearInterval(pollingIntervalRef.current);
				pollingIntervalRef.current = null;
			}
			if (qrFlowState === "success" || qrFlowState === "error") {
				onClose?.(); // Close if success or error and polling stops, unless it's awaiting action.
			}
			return;
		}

		const pollStatus = async () => {
			// Early exit if flow ID has changed since this pollStatus function was defined by the setInterval
			if (flowIdWhenEffectHookRan !== currentFlowIdRef.current) {
				// logger.info(
				// 	`[pollStatus] Flow ID mismatch (poll created for ${flowIdWhenEffectHookRan}, current is ${currentFlowIdRef.current}). Aborting poll action.`,
				// );
				// No need to clear interval here, the new effect run will do it or it's already done.
				return;
			}

			if (!initialSceneId) {
				// This case should ideally be caught by the outer useEffect's !initialSceneId check
				// logger.warn(
				// 	"[pollStatus] Attempted to poll with no initialSceneId.",
				// );
				setQrFlowState("error");
				setError("轮询错误: 场景ID丢失。");
				if (pollingIntervalRef.current) {
					clearInterval(pollingIntervalRef.current);
				}
				return;
			}

			try {
				// logger.debug(
				// 	`[pollStatus] Polling for sceneId: ${initialSceneId}, flowId: ${flowIdWhenEffectHookRan}`,
				// );
				// @ts-expect-error TODO: 为此API调用定义类型化的方法
				const apiResponse = (await authClient.wechat[
					"official-qr-status"
				]({
					query: { sceneId: initialSceneId },
				})) as {
					data: WechatOfficialQrStatusResponse;
					error: any | null;
					success?: boolean;
				}; // 假设一个通用的包装器

				if (!apiResponse || !apiResponse.data) {
					setQrFlowState("error"); // Set to error to stop polling and potentially close
					return;
				}

				const statusResponse: WechatOfficialQrStatusResponse =
					apiResponse.data;

				// CRITICAL CHECK: Ensure flow hasn't changed *after* await and *before* setting state
				if (flowIdWhenEffectHookRan !== currentFlowIdRef.current) {
					// logger.warn(
					// 	`[pollStatus] Flow ID ${flowIdWhenEffectHookRan} became outdated after API call for sceneId ${initialSceneId} (current: ${currentFlowIdRef.current}). Aborting state update.`,
					// );
					return;
				}

				setPollingStatus(statusResponse.status);
				setPollingMessage(statusResponse.message || null);
				setIsRegistered(statusResponse.isRegistered);

				if (statusResponse.status === "SUCCESS") {
					if (pollingIntervalRef.current) {
						clearInterval(pollingIntervalRef.current);
						pollingIntervalRef.current = null;
					}
					if (statusResponse.isRegistered) {
						toast.success(
							statusResponse.message ||
								`欢迎回来，${statusResponse.user?.name || "用户"}！`,
						);
						await reloadSession(); // Reload session first
						onLoginSuccess?.(); // Then call the overall success handler
						setQrFlowState("success"); // This will trigger onClose in useEffect to close modal
					} else {
						setRetainedSceneId(statusResponse.sceneId || null);
						setQrFlowState("awaitingAction"); // This state shows WechatPostScanActions
						toast.info(
							statusResponse.message ||
								"微信授权成功，请完成后续操作。",
						);
					}
				} else if (
					statusResponse.status === "FAILED" ||
					statusResponse.status === "EXPIRED" ||
					statusResponse.status === "CANCELLED"
				) {
					if (pollingIntervalRef.current) {
						clearInterval(pollingIntervalRef.current);
						pollingIntervalRef.current = null;
					}
					toast.error(
						statusResponse.message || "登录失败或已取消/过期",
					);
					setQrFlowState("error"); // This will trigger onClose in useEffect
				}
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : "状态查询异常";
				toast.error("轮询失败", { description: errorMessage });
				if (pollingIntervalRef.current) {
					// logger.error(
					// 	"[pollStatus] 错误：因错误清除 interval。ID:",
					// 	pollingIntervalRef.current,
					// );
					clearInterval(pollingIntervalRef.current);
					pollingIntervalRef.current = null;
				}
				// Check flow ID before setting error state too
				if (flowIdWhenEffectHookRan !== currentFlowIdRef.current) {
					// logger.warn(
					// 	`[pollStatus] Flow ID ${flowIdWhenEffectHookRan} outdated in catch block. Aborting error state update.`,
					// );
					return;
				}
				setQrFlowState("error"); // This will trigger onClose in useEffect
				setError(errorMessage);
			}
		};

		if (pollingIntervalRef.current) {
			clearInterval(pollingIntervalRef.current);
			pollingIntervalRef.current = null;
		}

		pollingIntervalRef.current = setInterval(pollStatus, 3000);

		return () => {
			if (pollingIntervalRef.current) {
				clearInterval(pollingIntervalRef.current);
				pollingIntervalRef.current = null;
			}
		};
	}, [initialSceneId, qrFlowState, reloadSession, onClose, onLoginSuccess]); // Added onClose and onLoginSuccess to dependencies of useEffect

	// 登录成功后的副作用 (例如，用户ID发生变化)
	useEffect(() => {
		if (
			qrFlowState === "success" &&
			session?.user?.id &&
			session.user.id !== currentSessionUserId.current
		) {
			toast.success(`检测到 ${session.user.name || "用户"} 登录，欢迎！`);
			// onClose?.(); // Already handled by the main polling useEffect when qrFlowState becomes "success"
		}
	}, [session, qrFlowState, currentSessionUserId]);

	// UI Rendering based on qrFlowState
	if (qrFlowState === "awaitingAction") {
		return (
			// This wrapper ensures WechatPostScanActions can effectively use w-full and max-w-xl
			// if QRCodeLogin's own root div is using items-center or other constraining flex properties.
			// We want WechatPostScanActions to behave like the main login form in terms of width.
			<div className="w-full flex justify-center">
				<WechatPostScanActions
					retainedSceneId={retainedSceneId}
					onAuthComplete={async () => {
						toast.success("微信授权流程完成，手机号已绑定！");
						await reloadSession(); // Reload session first
						onLoginSuccess?.(); // Then call the overall success handler
						setQrFlowState("success"); // Set state to trigger modal close
					}}
					onCancelActions={initiateNewQrCodeFlow}
				/>
			</div>
		);
	}

	// If loading, or error, or awaitingScan, or if it's success (which should lead to onClose), render the QR display area.
	// The actual closure will be handled by the onClose prop via the useEffect watching qrFlowState.
	return (
		<motion.div
			initial={{ opacity: 0, scale: 0.9 }}
			animate={{ opacity: 1, scale: 1 }}
			exit={{ opacity: 0, scale: 0.9 }}
			className="flex flex-col items-center space-y-8"
		>
			<div className="text-center">
				<h1 className="bg-gradient-to-r from-[#f0f0f0] to-[#fff9ab] bg-clip-text font-extrabold text-4xl text-transparent">
					微信扫码登录 (公众号)
				</h1>
				<p className="mt-4 text-lg text-yellow-200">
					使用微信扫一扫，安全便捷登录
				</p>
			</div>

			<div className="relative h-72 w-72 rounded-2xl bg-white/5 p-2 shadow-lg flex flex-col items-center justify-center text-yellow-200">
				{isLoading && (
					<div className="text-center">
						<Loader2Icon className="mx-auto mb-4 h-12 w-12 animate-spin" />
						<span className="text-lg">正在加载二维码...</span>
					</div>
				)}
				{error && !isLoading && (
					<div className="text-center text-red-400">
						<AlertTriangleIcon className="mx-auto mb-4 h-12 w-12" />
						<p className="text-lg font-semibold">加载失败</p>
						<span className="text-sm">{error}</span>
					</div>
				)}
				{!isLoading && !error && qrCodeImageUrl && (
					<Image
						src={qrCodeImageUrl}
						alt="微信公众号登录二维码"
						className="h-64 w-64 object-contain"
						width={256}
						height={256}
					/>
				)}
				{!isLoading && !error && !qrCodeImageUrl && (
					<p>未能获取二维码图片，请重试。</p>
				)}

				{/* 显示轮询状态 */}
				{initialSceneId &&
					qrFlowState === "awaitingScan" &&
					!isLoading &&
					!error &&
					pollingMessage && (
						<div className="absolute bottom-2 left-2 right-2 text-center text-xs bg-black/30 p-1 rounded">
							{pollingStatus === "PENDING" && (
								<ClockIcon className="inline h-3 w-3 mr-1 animate-pulse" />
							)}
							{pollingStatus === "SUCCESS" && (
								<CheckCircleIcon className="inline h-3 w-3 mr-1 text-green-400" />
							)}
							{(pollingStatus === "FAILED" ||
								pollingStatus === "EXPIRED" ||
								pollingStatus === "CANCELLED") && (
								<XCircleIcon className="inline h-3 w-3 mr-1 text-red-400" />
							)}
							{pollingMessage}
							{/* 简化条件：如果扫码成功但未注册，则显示消息。这是在 qrFlowState 变为 'awaitingAction' 之前的短暂时期 */}
							{pollingStatus === "SUCCESS" &&
								isRegistered === false &&
								" (请完成后续操作)"}
						</div>
					)}
			</div>
			{!isLoading &&
				!error &&
				qrCodeImageUrl &&
				qrFlowState === "awaitingScan" && (
					<div className="text-center">
						<p className="font-medium text-lg text-yellow-200">
							请打开微信，扫一扫上方二维码
						</p>
						{pollingStatus !== "SUCCESS" &&
							pollingStatus !== "FAILED" &&
							pollingStatus !== "EXPIRED" &&
							pollingStatus !== "CANCELLED" && (
								<p className="mt-2 text-yellow-200/70">
									扫码后将自动处理登录状态
								</p>
							)}
					</div>
				)}

			{/* Add a Back button here if not loading and not in success state */}
			{!isLoading && qrFlowState !== "success" && onClose && (
				<motion.button
					onClick={() => {
						// Clear polling interval if it exists when explicitly going back
						if (pollingIntervalRef.current) {
							clearInterval(pollingIntervalRef.current);
							pollingIntervalRef.current = null;
							// logger.info(
							// 	"[QRCodeLogin] Back button clicked, cleared polling interval.",
							// );
						}
						onClose();
					}}
					className="mt-8 flex items-center space-x-3 rounded-xl bg-white/5 px-8 py-3 text-yellow-200/80 transition-all hover:bg-white/10 hover:text-yellow-100"
					whileHover={{
						scale: 1.05,
					}}
					whileTap={{
						scale: 0.95,
					}}
				>
					<span className="text-base">返回手机号登录</span>
				</motion.button>
			)}
		</motion.div>
	);
}
