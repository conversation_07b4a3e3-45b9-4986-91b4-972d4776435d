import { motion } from "framer-motion";
import { LockIcon } from "lucide-react";

interface LoginFooterProps {
	loginType: "code" | "password";
	onToggleLoginType: () => void;
	onToggleMode: () => void;
}

export function LoginFooter({
	loginType,
	onToggleLoginType,
	onToggleMode,
}: LoginFooterProps) {
	return (
		<motion.div
			className="mt-8 flex items-center justify-between text-base"
			initial={{ opacity: 0 }}
			animate={{ opacity: 1 }}
			transition={{ duration: 0.5, delay: 0.8 }}
		>
			<motion.button
				type="button"
				onClick={onToggleLoginType}
				className="flex items-center space-x-1 text-yellow-300 transition-colors duration-200 hover:text-yellow-100"
				whileHover={{ scale: 1.05 }}
				whileTap={{ scale: 0.95 }}
			>
				{loginType === "code" ? (
					<>
						<LockIcon className="h-4 w-4" />
						<span>使用密码登录</span>
					</>
				) : (
					<span>使用验证码登录</span>
				)}
			</motion.button>
			<motion.div
				whileHover={{ scale: 1.05 }}
				className="flex items-center space-x-1"
			>
				<span className="text-yellow-200/70">还没有账号？</span>
				<motion.button
					type="button"
					onClick={onToggleMode}
					whileHover={{ scale: 1.05 }}
					whileTap={{ scale: 0.95 }}
					className="text-yellow-300 transition-colors duration-200 hover:text-yellow-100"
				>
					立即注册
				</motion.button>
			</motion.div>
		</motion.div>
	);
}
