import { CountryCodeSelect } from "@saas/auth/components/CountryCodeSelect";
import { VerificationCode } from "@saas/auth/components/shared/VerificationCode";
import { Button } from "@ui/components/button";
import { FormControl, FormField, FormItem } from "@ui/components/form";
import { Input } from "@ui/components/input";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { Loader2 } from "lucide-react";
import { useCallback } from "react";
import type { ControllerRenderProps, UseFormReturn } from "react-hook-form";

interface LoginByCodeProps {
	form: UseFormReturn<any>;
	isLoading: boolean;
	isCheckingPhone: boolean;
	isValidPhone: boolean;
	onSendCode: () => Promise<void>;
	onSubmit: () => void;
	validatePhone: (
		phoneNumber: string,
		phoneCountryCode: string,
	) => Promise<void>;
	inputLength?: { current: number; required: number };
}

interface PhoneInputWithErrorProps {
	field: ControllerRenderProps<any, "phoneNumber">;
	isLoading: boolean;
	isCheckingPhone: boolean;
	form: UseFormReturn<any>;
	onBlur: () => void;
}

function PhoneInputWithError({
	field,
	isLoading,
	isCheckingPhone,
	form,
	onBlur,
}: PhoneInputWithErrorProps) {
	return (
		<div className="relative">
			<Input
				{...field}
				type="tel"
				placeholder="请输入手机号码"
				disabled={isLoading || isCheckingPhone}
				className={cn(
					"!placeholder-yellow-200/50 flex h-12 w-full rounded-xl border-none bg-white/5 px-6 py-3 text-gray-200 text-lg transition-all duration-300 ease-in-out hover:bg-white/10 focus:bg-white/20 focus:ring-2 focus:ring-yellow-100 disabled:opacity-50",
					form.formState.errors.phoneNumber &&
						"ring-2 ring-red-500/50",
				)}
				onBlur={onBlur}
				aria-label="手机号码输入框"
			/>
			{form.formState.errors.phoneNumber && (
				<motion.div
					initial={{ opacity: 0, x: -10 }}
					animate={{ opacity: 1, x: 0 }}
					className="mt-1 text-sm text-red-400"
				>
					{String(form.formState.errors.phoneNumber.message)}
				</motion.div>
			)}
		</div>
	);
}

const gradientButtonStyle = {
	backgroundImage: "linear-gradient(100deg, #f0f0f0,#fff9ab)",
	color: "#60533d",
	border: "none",
	fontWeight: "bold",
	fontSize: "1rem",
	letterSpacing: "0.05em",
} as const;

const disabledButtonStyle = {
	backgroundColor: "#2c302b",
	color: "#a0aec0",
	border: "none",
	fontWeight: "bold",
	fontSize: "1rem",
	letterSpacing: "0.05em",
	cursor: "not-allowed",
} as const;

export function LoginByCode({
	form,
	isLoading,
	isCheckingPhone,
	isValidPhone,
	onSendCode,
	onSubmit,
	validatePhone,
}: LoginByCodeProps) {
	const handlePhoneBlur = useCallback(async () => {
		const phoneNumber = form.getValues("phoneNumber");
		const phoneCountryCode = form.getValues("phoneCountryCode");
		if (phoneNumber && phoneCountryCode) {
			await validatePhone(phoneNumber, phoneCountryCode);
		}
	}, [form, validatePhone]);

	return (
		<div className="space-y-5">
			<motion.div className="flex space-x-4">
				{/* 区号选择 */}
				<motion.div className="w-[140px]">
					<FormField
						control={form.control}
						name="phoneCountryCode"
						render={({ field }) => (
							<FormItem className="space-y-2">
								<label
									htmlFor="phoneCountryCode"
									className="block font-medium text-base text-yellow-100"
								>
									区号
								</label>
								<FormControl>
									<CountryCodeSelect
										value={field.value}
										onChange={(value) => {
											field.onChange(value);
											form.setValue("phoneNumber", "");
											form.clearErrors("phoneNumber");
										}}
										disabled={isLoading || isCheckingPhone}
									/>
								</FormControl>
							</FormItem>
						)}
					/>
				</motion.div>

				{/* 手机号输入框 */}
				<motion.div className="flex-1">
					<FormField
						control={form.control}
						name="phoneNumber"
						render={({ field }) => (
							<FormItem>
								<label
									htmlFor="phoneNumber"
									className="block font-medium text-base text-yellow-100"
								>
									手机号
								</label>
								<FormControl>
									<PhoneInputWithError
										field={field}
										isLoading={isLoading}
										isCheckingPhone={isCheckingPhone}
										form={form}
										onBlur={handlePhoneBlur}
									/>
								</FormControl>
							</FormItem>
						)}
					/>
				</motion.div>
			</motion.div>

			<FormField
				control={form.control}
				name="code"
				render={({ field }) => (
					<FormItem>
						<label
							htmlFor="code"
							className="mb-2 block font-medium text-base text-yellow-100"
						>
							验证码
						</label>
						<FormControl>
							<VerificationCode
								value={field.value}
								onChange={field.onChange}
								onSendCode={onSendCode}
								disabled={isLoading}
								error={!!form.formState.errors.code}
								isValidPhone={isValidPhone}
							/>
						</FormControl>
					</FormItem>
				)}
			/>

			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.5, delay: 0.7 }}
				whileHover={{ scale: 1.02 }}
				whileTap={{ scale: 0.98 }}
			>
				<Button
					type="submit"
					onClick={onSubmit}
					disabled={
						isLoading ||
						!isValidPhone ||
						!form.getValues("code") ||
						isCheckingPhone ||
						!!form.formState.errors.phoneNumber
					}
					className={`h-14 w-full rounded-xl font-semibold text-xl tracking-wide transition-all duration-300 ${
						isLoading ||
						!isValidPhone ||
						!form.getValues("code") ||
						isCheckingPhone ||
						!!form.formState.errors.phoneNumber
							? "cursor-not-allowed opacity-70"
							: "hover:opacity-90 hover:shadow-lg"
					}`}
					style={
						isLoading ||
						!isValidPhone ||
						!form.getValues("code") ||
						isCheckingPhone ||
						!!form.formState.errors.phoneNumber
							? disabledButtonStyle
							: gradientButtonStyle
					}
					aria-label="登录按钮"
				>
					{isLoading ? (
						<div className="flex items-center justify-center space-x-2">
							<Loader2 className="h-5 w-5 animate-spin" />
							<span>登录中...</span>
						</div>
					) : isCheckingPhone ? (
						<div className="flex items-center justify-center space-x-2">
							<Loader2 className="h-5 w-5 animate-spin" />
							<span>验证手机号...</span>
						</div>
					) : form.formState.errors.phoneNumber ? (
						<span>
							{
								form.formState.errors.phoneNumber
									.message as string
							}
						</span>
					) : !isValidPhone ? (
						<span>请输入正确的手机号</span>
					) : (
						"立即登录"
					)}
				</Button>
			</motion.div>
		</div>
	);
}
