"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { authClient } from "@repo/auth/client";
import { CountryCodeSelect } from "@saas/auth/components/CountryCodeSelect";
import { VerificationCode } from "@saas/auth/components/shared/VerificationCode";
import { Button } from "@ui/components/button";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormMessage,
} from "@ui/components/ecosystem/form";
import { Input } from "@ui/components/ecosystem/input";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { Loader2, LogInIcon } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { useSession } from "../../hooks/use-session";

interface WechatPostScanActionsProps {
	retainedSceneId: string | null;
	onAuthComplete: () => void;
	onCancelActions: () => void;
}

const phoneAuthSchema = z.object({
	phoneCountryCode: z.string().min(1, "请选择国家/地区代码"),
	phoneNumber: z
		.string()
		.min(1, "请输入手机号码")
		.regex(/^1[3-9]\d{9}$/, "请输入正确的手机号格式"),
	code: z
		.string()
		.min(6, "验证码必须是6位数字")
		.max(6, "验证码必须是6位数字"),
});
type PhoneAuthFormValues = z.infer<typeof phoneAuthSchema>;

const gradientButtonStyle = {
	backgroundImage: "linear-gradient(100deg, #f0f0f0,#fff9ab)",
	color: "#60533d",
	border: "none",
	fontWeight: "bold",
	fontSize: "1rem",
	letterSpacing: "0.05em",
} as const;

const disabledButtonStyle = {
	backgroundColor: "#2c302b",
	color: "#a0aec0",
	border: "none",
	fontWeight: "bold",
	fontSize: "1rem",
	letterSpacing: "0.05em",
	cursor: "not-allowed",
} as const;

const Header = () => (
	<div className="text-center mb-8 md:mb-10">
		<motion.h1
			className="bg-gradient-to-r from-[#f0f0f0] to-[#fff9ab] bg-clip-text font-extrabold text-3xl md:text-4xl text-transparent"
			initial={{ opacity: 0, y: -10 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.5, delay: 0.1 }}
		>
			微信授权成功
		</motion.h1>
		<motion.p
			className="mt-3 text-lg md:text-xl text-[#f8f3b0]"
			initial={{ opacity: 0, y: -5 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.5, delay: 0.2 }}
		>
			请验证您的手机号以继续
		</motion.p>
	</div>
);

export function WechatPostScanActions({
	retainedSceneId,
	onAuthComplete,
	onCancelActions,
}: WechatPostScanActionsProps) {
	const [isLoading, setIsLoading] = useState(false);
	const [isSendingCode, setIsSendingCode] = useState(false);
	useSession();

	const form = useForm<PhoneAuthFormValues>({
		resolver: zodResolver(phoneAuthSchema) as any,
		defaultValues: {
			phoneCountryCode: "+86",
			phoneNumber: "",
			code: "",
		},
		mode: "onChange",
	});

	const handleSendCode = async () => {
		const phoneCountryCode = form.getValues("phoneCountryCode");
		const phoneNumber = form.getValues("phoneNumber");
		if (!form.getValues("phoneNumber")) {
			form.setError("phoneNumber", { message: "请输入手机号码" });
			return;
		}
		const isValidPhoneNumber = await form.trigger("phoneNumber");
		if (!isValidPhoneNumber) {
			toast.error("手机号格式不正确");
			return;
		}

		setIsSendingCode(true);
		try {
			const response = await authClient.phoneAuth.sendOTP(
				phoneCountryCode,
				phoneNumber,
			);
			if (response.success) {
				toast.success("验证码已发送");
			} else {
				toast.error(
					response.message ||
						response.data?.message ||
						"验证码发送失败",
				);
			}
		} catch (error) {
			toast.error((error as Error).message || "发送验证码时发生错误");
		} finally {
			setIsSendingCode(false);
		}
	};

	const handleSubmitPhoneAuth = async (values: PhoneAuthFormValues) => {
		if (!retainedSceneId) {
			toast.error("操作失败", {
				description: "缺少关键场景信息 (Scene ID)。",
			});
			return;
		}
		setIsLoading(true);
		try {
			const response = await authClient.phoneAuth.signInByOTP(
				values.phoneCountryCode,
				values.phoneNumber,
				values.code,
				retainedSceneId,
			);

			if (response.success && response.data?.success) {
				toast.success(response.data.message || "手机号验证成功！");
				onAuthComplete();
			} else {
				toast.error("操作失败", {
					description:
						response.data?.message ||
						response.message ||
						"手机号验证时发生未知错误",
				});
			}
		} catch (error) {
			toast.error("操作失败", {
				description:
					(error as Error).message || "手机号验证时发生严重错误",
			});
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			exit={{ opacity: 0, y: -20 }}
			transition={{ duration: 0.3 }}
			className="w-full max-w-xl text-card-foreground"
		>
			<Header />

			<Form {...form}>
				<form
					onSubmit={form.handleSubmit(handleSubmitPhoneAuth)}
					className="space-y-5 md:space-y-6 py-8 md:py-8"
				>
					<div className="flex space-x-4">
						<div className="w-[120px]">
							<FormField
								control={form.control as any}
								name="phoneCountryCode"
								render={({ field }) => (
									<FormItem>
										<FormControl>
											<CountryCodeSelect
												value={field.value}
												onChange={(value) => {
													field.onChange(value);
													form.setValue(
														"phoneNumber",
														"",
													);
													form.clearErrors(
														"phoneNumber",
													);
												}}
												disabled={isLoading}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>
						<div className="flex-1">
							<FormField
								control={form.control as any}
								name="phoneNumber"
								render={({ field }) => (
									<FormItem>
										<FormControl>
											<Input
												{...field}
												type="tel"
												placeholder="请输入手机号码"
												disabled={isLoading}
												className={cn(
													"!placeholder-yellow-200/50 flex h-12 w-full rounded-xl border-none bg-white/5 px-6 py-3 text-gray-200 text-lg transition-all duration-300 ease-in-out hover:bg-white/10 focus:bg-white/20 focus:ring-2 focus:ring-yellow-100 disabled:opacity-50",
													form.formState.errors
														.phoneNumber &&
														"ring-2 ring-red-500/50",
												)}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>
					</div>

					<FormField
						control={form.control as any}
						name="code"
						render={({ field }) => (
							<FormItem>
								<FormControl>
									<VerificationCode
										value={field.value}
										onChange={field.onChange}
										onSendCode={handleSendCode}
										disabled={
											isLoading ||
											isSendingCode ||
											!form.watch("phoneNumber") ||
											!!form.formState.errors.phoneNumber
										}
										error={!!form.formState.errors.code}
										isValidPhone={
											!form.formState.errors
												.phoneNumber &&
											!!form.watch("phoneNumber")
										}
									/>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
					<motion.div
						whileHover={{ scale: 1.02 }}
						whileTap={{ scale: 0.98 }}
					>
						<Button
							type="submit"
							disabled={isLoading || !form.formState.isValid}
							className="w-full h-14 md:h-16 rounded-xl text-base md:text-lg font-semibold flex items-center justify-center px-6 transition-all duration-300 shadow-lg hover:shadow-yellow-300/20"
							style={
								isLoading || !form.formState.isValid
									? disabledButtonStyle
									: gradientButtonStyle
							}
						>
							{isLoading ? (
								<Loader2 className="mr-2 h-5 w-5 animate-spin" />
							) : (
								<LogInIcon className="mr-2 h-5 w-5" />
							)}
							绑定手机号
						</Button>
					</motion.div>
				</form>
			</Form>

			<div className="px-6 md:px-10 pb-8 md:pb-8">
				<Button
					variant="ghost"
					onClick={onCancelActions}
					className="w-full mt-8 text-yellow-200/70 hover:text-yellow-100"
					disabled={isLoading}
				>
					返回重新扫码
				</Button>
			</div>
		</motion.div>
	);
}
