import { motion } from "framer-motion";

interface LoginHeaderProps {
	loginType: "code" | "password";
}

export function LoginHeader({ loginType }: LoginHeaderProps) {
	return (
		<div className="text-center">
			<motion.h1
				className="bg-gradient-to-r from-[#f0f0f0] to-[#fff9ab] bg-clip-text font-extrabold text-4xl text-transparent"
				initial={{ opacity: 0, y: -10 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.5, delay: 0.3 }}
			>
				欢迎登录
			</motion.h1>
			<motion.p
				className="mt-4 text-lg text-[#f8f3b0]"
				initial={{ opacity: 0, y: -5 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.5, delay: 0.4 }}
			>
				{loginType === "code"
					? "请输入手机号和验证码登录"
					: "请输入手机号和密码登录"}
			</motion.p>
		</div>
	);
}
