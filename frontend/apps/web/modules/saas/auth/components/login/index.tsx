import { zodResolver } from "@hookform/resolvers/zod";
import { authClient } from "@repo/auth/client";
import type {
	ApiResponse,
	OTPVerifyResponse,
	PhoneLoginResponse,
} from "@repo/auth/plugins/phone-auth";
import { PhoneNumberCheckType } from "@repo/auth/plugins/phone-auth";
import { Form } from "@ui/components/ecosystem/form";
import { motion } from "framer-motion";
import { QrCodeIcon } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { useSession } from "../../hooks/use-session";
import { usePhoneValidation } from "../../hooks/usePhoneValidation";
import { ErrorDialog } from "../shared/ErrorDialog";
import { LoginByCode } from "./LoginByCode";
import { LoginByPassword } from "./LoginByPassword";
import { LoginFooter } from "./LoginFooter";
import { LoginHeader } from "./LoginHeader";
import { QRCodeLogin } from "./QRCodeLogin";

// 登录表单验证 Schema - 使用更简单的对象结构
const loginFormSchema = z
	.object({
		loginType: z.enum(["code", "password"]),
		phoneCountryCode: z.string().min(1, "请选择国家/地区代码"),
		phoneNumber: z.string().min(1, "请输入手机号码"),
		code: z.string().optional(),
		password: z.string().optional(),
	})
	.superRefine((data, ctx) => {
		// 根据登录类型进行条件验证
		if (data.loginType === "code") {
			if (!data.code || data.code.length < 6 || data.code.length > 6) {
				ctx.addIssue({
					code: z.ZodIssueCode.custom,
					message: "验证码必须是6位数字",
					path: ["code"],
				});
			}
		} else if (data.loginType === "password") {
			if (
				!data.password ||
				data.password.length < 6 ||
				data.password.length > 20
			) {
				ctx.addIssue({
					code: z.ZodIssueCode.custom,
					message: data.password
						? "密码长度应在6-20位之间"
						: "请输入密码",
					path: ["password"],
				});
			}
		}
	});

type LoginFormValues = z.infer<typeof loginFormSchema>;

interface LoginFormProps {
	onToggleMode: () => void;
	onLoginSuccess?: () => void;
}

export function LoginForm({ onToggleMode, onLoginSuccess }: LoginFormProps) {
	const [isLoading, setIsLoading] = useState(false);
	const [loginType, setLoginType] = useState<"code" | "password">("code");
	const [showQRCode, setShowQRCode] = useState(false);
	const [showErrorDialog, setShowErrorDialog] = useState(false);
	const [errorMessage, setErrorMessage] = useState("");
	const { reloadSession } = useSession();
	const formRef = useRef<HTMLFormElement>(null);

	const form = useForm<LoginFormValues>({
		resolver: zodResolver(loginFormSchema) as any,
		defaultValues: {
			loginType: "code" as const,
			phoneCountryCode: "+86",
			phoneNumber: "",
			code: "",
			password: "",
		} as LoginFormValues,
		mode: "onBlur",
		shouldUnregister: false,
		criteriaMode: "firstError",
	});

	const { isValidPhone, isCheckingPhone, validatePhone } = usePhoneValidation(
		{
			type: PhoneNumberCheckType.LOGIN,
			onError: (message) => {
				form.setError("phoneNumber", {
					type: "manual",
					message,
				});
			},
		},
	);

	// 切换登录方式
	const toggleLoginType = () => {
		const newType = loginType === "code" ? "password" : "code";

		// 保存当前的手机号和区号
		const currentPhoneCountryCode = form.getValues("phoneCountryCode");
		const currentPhoneNumber = form.getValues("phoneNumber");

		// 先清除所有错误
		form.clearErrors();

		// 使用 setTimeout 确保状态更新不会冲突
		setTimeout(() => {
			setLoginType(newType);

			// 设置新的登录类型并清除相关字段
			form.setValue("loginType", newType);
			form.setValue("phoneCountryCode", currentPhoneCountryCode);
			form.setValue("phoneNumber", currentPhoneNumber);

			if (newType === "code") {
				form.setValue("code", "");
				form.setValue("password", "");
			} else {
				form.setValue("password", "");
				form.setValue("code", "");
			}
		}, 0);
	};

	// 组件卸载时清理
	useEffect(() => {
		return () => {
			// 清理表单状态
			form.reset();
		};
	}, [form]);

	// 发送验证码
	const handleSendCode = async () => {
		const phoneNumber = form.getValues("phoneNumber");
		const phoneCountryCode = form.getValues("phoneCountryCode");

		if (!phoneNumber || !isValidPhone) {
			toast.error("手机号错误", {
				description: "请输入正确的手机号",
			});
			return;
		}

		try {
			const response = await authClient.phoneAuth.sendOTP(
				phoneCountryCode,
				phoneNumber,
			);

			if (!response.success) {
				toast.error("发送失败", {
					description:
						response.data?.message ||
						response.message ||
						"验证码发送失败",
				});
				return;
			}

			toast.success("验证码已发送", {
				description: "请注意查收短信",
			});
		} catch (_error) {
			toast.error("发送失败", {
				description: "验证码发送失败，请稍后重试",
			});
		}
	};

	// 提交登录
	const onSubmit = async (values: LoginFormValues) => {
		if (!isValidPhone) {
			setErrorMessage("请输入正确的手机号");
			setShowErrorDialog(true);
			return;
		}

		setIsLoading(true);
		try {
			if (values.loginType === "code") {
				const response: ApiResponse<OTPVerifyResponse> =
					await authClient.phoneAuth.signInByOTP(
						values.phoneCountryCode,
						values.phoneNumber,
						values.code || "",
					);

				if (!response.success) {
					toast.error("登录失败", {
						description: response.message || "登录失败，请重试",
					});
					return;
				}

				toast.success("登录成功");
				// 登录成功后刷新session
				await reloadSession();
				// 清理表单状态
				form.reset();
				onLoginSuccess?.();
				return;
			}

			// 密码登录逻辑
			const response: ApiResponse<PhoneLoginResponse> =
				await authClient.phoneAuth.signInByPassword(
					values.phoneCountryCode,
					values.phoneNumber,
					values.password || "",
				);

			if (!response.success) {
				// 显示错误信息并设置密码字段错误
				toast.error("登录失败", {
					description: response.message || "密码错误或账号不存在",
				});

				// 当登录失败时，立即清空密码输入框并设置明显的错误信息
				// 无论错误类型是什么，都清空密码并显示错误
				form.setValue("password", "");

				form.setError("password", {
					type: "manual",
					message: "密码错误，请重新输入",
				});

				return;
			}

			toast.success("登录成功");
			// 登录成功后刷新session
			await reloadSession();
			// 清理表单状态
			form.reset();
			onLoginSuccess?.();
		} catch (error: unknown) {
			toast.error("登录失败", {
				description:
					error instanceof Error
						? error.message
						: "系统繁忙，请稍后重试",
			});
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<>
			<ErrorDialog
				open={showErrorDialog}
				onOpenChange={setShowErrorDialog}
				message={errorMessage}
			/>

			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				exit={{ opacity: 0, y: -20 }}
				transition={{ duration: 0.3 }}
				className="relative"
			>
				<div className="w-full max-w-xl rounded-lg text-card-foreground">
					<div>
						<motion.div
							initial={{ opacity: 0 }}
							animate={{ opacity: 1 }}
							transition={{ duration: 0.5, delay: 0.2 }}
						>
							{showQRCode ? (
								<QRCodeLogin
									onClose={() => setShowQRCode(false)}
									onLoginSuccess={onLoginSuccess}
								/>
							) : (
								<>
									<LoginHeader loginType={loginType} />

									<Form {...form}>
										<form
											ref={formRef}
											onSubmit={form.handleSubmit(
												onSubmit as any,
											)}
											className="mt-8 space-y-6"
										>
											<div>
												{loginType === "code" ? (
													<LoginByCode
														form={form as any}
														isLoading={isLoading}
														isCheckingPhone={
															isCheckingPhone
														}
														isValidPhone={
															isValidPhone
														}
														onSendCode={
															handleSendCode
														}
														onSubmit={() =>
															form.handleSubmit(
																onSubmit as any,
															)()
														}
														validatePhone={
															validatePhone
														}
													/>
												) : (
													<LoginByPassword
														form={form as any}
														isLoading={isLoading}
														isCheckingPhone={
															isCheckingPhone
														}
														isValidPhone={
															isValidPhone
														}
														onSubmit={() =>
															form.handleSubmit(
																onSubmit as any,
															)()
														}
														validatePhone={
															validatePhone
														}
													/>
												)}
											</div>

											<LoginFooter
												loginType={loginType}
												onToggleLoginType={
													toggleLoginType
												}
												onToggleMode={onToggleMode}
											/>

											{/* 分割线和其他登录方式 */}
											<div className="mt-8">
												<div className="relative">
													<div className="absolute inset-0 flex items-center">
														<div className="w-full border-yellow-200/20 border-t" />
													</div>
													<div className="relative flex justify-center text-sm">
														<span className=" px-4 text-yellow-200/70">
															其他登录方式
														</span>
													</div>
												</div>
												<motion.div
													className="mt-6 flex justify-center"
													initial={{
														opacity: 0,
														y: 10,
													}}
													animate={{
														opacity: 1,
														y: 0,
													}}
													transition={{
														duration: 0.5,
														delay: 0.9,
													}}
												>
													<motion.button
														onClick={() =>
															setShowQRCode(true)
														}
														className="flex items-center space-x-3 rounded-xl bg-white/5 px-8 py-4 text-yellow-200 transition-all hover:bg-white/10"
														whileHover={{
															scale: 1.05,
														}}
														whileTap={{
															scale: 0.95,
														}}
													>
														<QrCodeIcon className="h-6 w-6" />
														<span className="text-lg">
															微信扫码登录
														</span>
													</motion.button>
												</motion.div>
											</div>
										</form>
									</Form>
								</>
							)}
						</motion.div>
					</div>
				</div>
			</motion.div>
		</>
	);
}
