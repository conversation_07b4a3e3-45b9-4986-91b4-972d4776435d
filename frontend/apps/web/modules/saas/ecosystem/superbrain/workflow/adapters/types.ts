// Adapters/Types – normalized view models and renderer contracts

import type { ReactNode } from "react";

// Cost/latency usage summary (aligned with docs)
export type Usage = {
  total_tokens?: number;
  total_price?: string | number;
  currency?: string;
  latency?: number;
  prompt_tokens?: number;
  completion_tokens?: number;
};

// Discriminated union for normalized results
export type NormalizedResult =
  | { type: "topics"; items: Array<{ topic_direction: string; angle_keywords?: string[]; target_user?: string; formula_cn?: string }>; matrices?: any; usage?: Usage }
  | { type: "entity_burst"; openings: string[]; reasons: string[]; ctas: string[]; outputs: string[]; usage?: Usage }
  | { type: "script_compose"; segments: Array<{ segment_number: number; emotion?: string; visuals?: string; dialogue: string }>; text?: string; usage?: Usage }
  | { type: "opening"; openings: Array<{ text: string }>; usage?: Usage }
  | { type: "private_marketing"; posts: Array<{ post_content: string; strategy_used?: string; image_suggestion?: string; insight?: { title: string; content: string } }>; usage?: Usage }
  | { type: "material_index"; products: any[]; tags: string[]; assets?: string[]; usage?: Usage }
  | { type: "persona"; profile: any; usage?: Usage }
  | { type: "unknown"; raw: any; usage?: Usage };

export type WorkflowKind =
  | "topic_generation"
  | "entity_burst"
  | "script_compose"
  | "opening"
  | "private_marketing"
  | "material_index"
  | "persona"
  | "chat";

export interface RendererProps<T extends NormalizedResult = NormalizedResult> { data: T }

export interface RendererConfig<T extends NormalizedResult = NormalizedResult> {
  kind: WorkflowKind;
  appIds?: string[];            // optional bind to concrete dify app ids
  normalize: (raw: any) => T;   // adapter: raw json → normalized view model
  Component: (props: RendererProps<T>) => ReactNode; // renderer component
  meta: { title: string; icon?: any; theme?: "purple" | "green" | "cyan" };
}

