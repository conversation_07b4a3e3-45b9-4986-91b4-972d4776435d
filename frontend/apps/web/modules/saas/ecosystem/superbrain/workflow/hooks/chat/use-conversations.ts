import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState, useEffect, useRef } from "react";
import { toast } from "sonner";
import type { Conversation } from "../components/chat/types";

interface ConversationQuery {
  page?: number;
  limit?: number;
  sort_by?: string;
  last_id?: string;
  first_id?: string;
}

interface ConversationsResponse {
  success: boolean;
  data: Conversation[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    has_more: boolean;
  };
}

interface UseConversationsOptions {
  agentId: string;
  autoFetch?: boolean;
}

export function useConversations({ agentId, autoFetch = true }: UseConversationsOptions) {
  const [loading, setLoading] = useState(false);
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(false);
  
  // 请求去重：保存当前请求的AbortController
  const abortControllerRef = useRef<AbortController | null>(null);

  // 获取会话列表
  const fetchConversations = useCallback(async (query: ConversationQuery = {}) => {
    if (!agentId) {
      console.warn("agentId is required");
      return;
    }

    // 取消之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    // 创建新的AbortController
    const abortController = new AbortController();
    abortControllerRef.current = abortController;
    
    setLoading(true);
    setError(null);

    try {
      // 构建查询参数
      const queryParams: Record<string, string> = {
        limit: String(query.limit || 50),
        page: String(query.page || 1),
        sort_by: query.sort_by || "-updated_at",
      };

      if (query.last_id) queryParams.last_id = query.last_id;
      if (query.first_id) queryParams.first_id = query.first_id;

      console.log("Fetching conversations for agent:", agentId, "with query:", queryParams);

      // 根据后端路由定义，需要传递路径参数和查询参数
      const response = await (apiClient.v1.ecosystem.superbrain.agents[":id"].conversations as any).$get({
        param: { id: agentId },
        query: queryParams,
      });
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json() as ConversationsResponse;

      if (result.success) {
        if (!Array.isArray(result.data)) {
          throw new Error("响应数据格式不正确: data应该是数组");
        }

        setConversations(result.data);
        setHasMore(result.pagination?.has_more || false);
        
        console.log(`获取到 ${result.data.length} 个会话`);
      } else {
        throw new Error("获取会话列表失败");
      }
    } catch (error) {
      // 如果是取消的请求，不处理错误
      if (error instanceof Error && error.name === 'AbortError') {
        return;
      }
      
      const errorMessage = error instanceof Error ? error.message : String(error);
      setError(errorMessage);
      toast.error(`获取会话列表失败: ${errorMessage}`);
      
      // 重置数据
      setConversations([]);
      setHasMore(false);
    } finally {
      // 只有当前请求没有被取消时才设置loading为false
      if (abortController.signal.aborted === false) {
        setLoading(false);
      }
    }
  }, [agentId]);

  // 刷新会话列表
  const refresh = useCallback(() => {
    return fetchConversations();
  }, [fetchConversations]);

  // 自动获取数据
  useEffect(() => {
    if (autoFetch && agentId) {
      fetchConversations();
    }
  }, [agentId, autoFetch, fetchConversations]);

  // 清理函数：组件卸载时取消请求
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    conversations,
    loading,
    error,
    hasMore,
    fetchConversations,
    refresh,
  };
}