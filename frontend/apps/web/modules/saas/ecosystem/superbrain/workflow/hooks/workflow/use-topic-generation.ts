import { useState, useRef, useCallback } from "react";
import { toast } from "sonner";
import { apiClient } from "@shared/lib/api-client";

export interface TopicGenerationRunParams {
  appId: string;
  topic_type?: "教知识" | "讲故事" | "聊观点" | "讲产品" | "晒过程";
  number?: number;
  titled?: string[];
  additional?: string;
  product_info?: string;
}

export interface WorkflowSSEvent {
  event?: string;
  task_id?: string;
  workflow_run_id?: string;
  conversation_id?: string;
  data?: any;
  answer?: string;
  [k: string]: any;
}

export function useTopicGenerationWorkflow() {
  const [loading, setLoading] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [events, setEvents] = useState<WorkflowSSEvent[]>([]);
  const [lastEvent, setLastEvent] = useState<WorkflowSSEvent | null>(null);
  const [workflowRunId, setWorkflowRunId] = useState<string | undefined>();
  const [taskId, setTaskId] = useState<string | undefined>();

  const abortControllerRef = useRef<AbortController | null>(null);

  const stop = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    setIsStreaming(false);
    setLoading(false);
  }, []);

  const run = useCallback(async (params: TopicGenerationRunParams) => {
    const { appId, topic_type, number, titled, additional, product_info } = params;
    if (!appId) {
      toast.error("缺少 appId");
      return;
    }

    // 取消前一个请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    const abortController = new AbortController();
    abortControllerRef.current = abortController;

    setLoading(true);
    setIsStreaming(true);
    setError(null);
    setEvents([]);
    setLastEvent(null);
    setWorkflowRunId(undefined);
    setTaskId(undefined);

    try {
      // 使用 hono apiClient 规范化 POST 请求（SSE 流）
      const res = await apiClient.v1.ecosystem.superbrain.workflows["topic-generation"].run.$post({
        json: {
          app_id: appId,
          topic_type,
          number,
          titled,
          additional,
          product_info,
        },
      }) as Response;

      if (!res.ok) {
        throw new Error(`HTTP ${res.status}: ${res.statusText}`);
      }

      const reader = res.body?.getReader();
      if (!reader) throw new Error("无法读取响应流");

      const decoder = new TextDecoder();
      let buffer = "";

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split("\n");
        buffer = lines.pop() || "";
        for (const line of lines) {
          if (!line.startsWith("data: ")) continue;
          const eventData = line.slice(6).trim();
          if (!eventData) continue;
          if (eventData === "[DONE]") {
            setIsStreaming(false);
            setLoading(false);
            return;
          }
          try {
            const evt: WorkflowSSEvent = JSON.parse(eventData);
            // 记录关键ID
            if (evt.workflow_run_id) setWorkflowRunId(evt.workflow_run_id);
            if (evt.task_id) setTaskId(evt.task_id);
            // 追加事件
            setEvents(prev => [...prev, evt]);
            setLastEvent(evt);
          } catch (e) {
            console.warn("解析SSE事件失败:", e, "原始:", eventData);
          }
        }
      }
    } catch (err) {
      if (err instanceof Error && err.name === "AbortError") return;
      const msg = err instanceof Error ? err.message : String(err);
      setError(msg);
      toast.error(`执行失败: ${msg}`);
    } finally {
      setIsStreaming(false);
      setLoading(false);
    }
  }, []);

  return {
    // 状态
    loading,
    isStreaming,
    error,
    events,
    lastEvent,
    workflowRunId,
    taskId,
    // 操作
    run,
    stop,
  } as const;
}

