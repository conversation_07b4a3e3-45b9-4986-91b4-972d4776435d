"use client";

import { motion } from "framer-motion";
import styles from "../../sb-theme.module.css";
import { useRouter, useSearchParams } from "next/navigation";
import { TopBar } from "./TopBar";
import { pickRendererForRaw } from "../renderers/registry";
import { TopicRenderer } from "../renderers/TopicRenderer";


interface WorkflowRunViewProps {
  appId: string;
}

export function WorkflowRunView({ appId }: WorkflowRunViewProps) {
  const router = useRouter();
  // debug trace which renderer is used
  if (typeof window !== "undefined") {
    // eslint-disable-next-line no-console
    console.info("[WorkflowRunView] appId:", appId);
  }


  return (
    <motion.div className={`${styles.sbTheme} min-h-screen bg-gradient-to-br from-[#01030c] via-[#0e1018] to-[#01030c] relative overflow-hidden`} initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.35 }}>
      <TopBar title="工作流 · 运行" onBack={() => router.push("/app/ecosystem/superbrain/workflow")} />

      {/* 基础页面：只保留最小外壳，输入与结果展示由各工作流渲染器自行实现 */}
      <div className="min-h-screen flex flex-col items-start justify-start pt-[10vh] px-4">
        <div className="w-full max-w-4xl mx-auto">
          <div className="text-center mb-6">
            <h1 className="text-white text-2xl font-semibold">工作流 · 运行</h1>
            <p className="text-[#a0a0a0] text-sm mt-2">appId: {appId}</p>
          </div>

          {/* 专属渲染区：优先按 appId 选择已注册的专属渲染器 */}
          {(() => {
            const { Component, data } = pickRendererForRaw({});
            const Comp = Component as any;
            if (typeof window !== "undefined") {
              // eslint-disable-next-line no-console
              console.info("[WorkflowRunView] Using fallback renderer", { CompName: Comp?.name });
            }
            return (
              <motion.div initial={{ opacity: 0, y: 8 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.3 }}>
                <Comp data={data as any} appId={appId} />
              </motion.div>
            );
          })()}
        </div>
      </div>
    </motion.div>
  );
}
