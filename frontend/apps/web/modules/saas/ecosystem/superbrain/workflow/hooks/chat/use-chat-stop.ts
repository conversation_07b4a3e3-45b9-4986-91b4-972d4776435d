import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";

interface StopChatRequest {
  task_id: string;
}

interface StopChatResponse {
  success: boolean;
  data: {
    result: string;
  };
}

interface UseChatStopOptions {
  agentId: string;
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export function useChatStop({ agentId, onSuccess, onError }: UseChatStopOptions) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 停止聊天响应
  const stopChat = useCallback(async (taskId: string) => {
    if (!agentId) {
      const errorMsg = "智能体ID不能为空";
      setError(errorMsg);
      onError?.(errorMsg);
      return;
    }

    if (!taskId?.trim()) {
      const errorMsg = "任务ID不能为空";
      setError(errorMsg);
      onError?.(errorMsg);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      console.log("Stopping chat message:", {
        agentId,
        taskId
      });

      const response = await (apiClient.v1.ecosystem.superbrain.agents[":id"].chat.stop as any).$post({
        param: { id: agentId },
        json: { task_id: taskId }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json() as StopChatResponse;

      if (result.success) {
        console.log("Chat stopped successfully:", result.data);
        toast.success("已停止响应");
        
        if (onSuccess) {
          onSuccess();
        }
        
        return result.data;
      } else {
        throw new Error("停止响应失败");
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      setError(errorMessage);
      toast.error(`停止响应失败: ${errorMessage}`);
      
      if (onError) {
        onError(errorMessage);
      }
      
      throw error;
    } finally {
      setLoading(false);
    }
  }, [agentId, onSuccess, onError]);

  // 重置错误状态
  const resetError = useCallback(() => {
    setError(null);
  }, []);

  return {
    loading,
    error,
    stopChat,
    resetError,
  };
}