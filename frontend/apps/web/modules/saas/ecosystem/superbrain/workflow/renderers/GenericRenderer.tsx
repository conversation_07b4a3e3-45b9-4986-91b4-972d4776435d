"use client";

import { RendererProps } from "../adapters/types";
import styles from "../../sb-theme.module.css";

  // debug trace
  if (typeof window !== "undefined") {
    // eslint-disable-next-line no-console
    console.info("[Renderer] GenericRenderer mounted");
  }

export function GenericRenderer({ data }: RendererProps<{ type: "unknown"; raw: any }>) {
  return (
    <div className={`rounded-2xl border ${styles.glass} p-5`}>
      <div className="text-white font-semibold mb-2">通用渲染器（未注册类型）</div>
      <div className="text-[#a0a0a0] text-sm mb-3">直接显示结构化 JSON 结果与用量。</div>

      {data?.raw ? (
        <pre className="text-xs text-[#eef3ff] bg-[#0e1018]/50 p-3 rounded-md border border-[#8394fb]/25 overflow-x-auto max-h-[480px]">
{JSON.stringify(data.raw, null, 2)}
        </pre>
      ) : (
        <div className="text-[#a0a0a0]">无原始数据</div>
      )}
    </div>
  );
}

