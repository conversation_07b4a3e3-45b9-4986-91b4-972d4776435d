import { GenericRenderer } from "./GenericRenderer";
import { TopicRenderer } from "./TopicRenderer";
import type { NormalizedResult, RendererConfig, WorkflowKind } from "../adapters/types";

// minimal registry; add entries per workflow kind
const REGISTRY: Array<RendererConfig<any>> = [
  {
    kind: "topic_generation",
    meta: { title: "营销选题" },
    appIds: [],
    normalize: (raw: any) => {
      // try to map dify outputs → normalized topics; fallback to unknown
      const outputs = raw?.data?.outputs || raw?.outputs;
      if (outputs?.topics || outputs?.items) {
        const items = outputs.topics || outputs.items;
        return { type: "topics", items } as NormalizedResult;
      }
      // 有些情况下，结果在事件里（node_finished 输出中）
      const events: any[] | undefined = raw?.events;
      if (Array.isArray(events)) {
        const wfFinished = events.find((e) => e?.event === "workflow_finished");
        const evOutputs = wfFinished?.data?.outputs;
        if (evOutputs?.topics || evOutputs?.items) {
          return { type: "topics", items: evOutputs.topics || evOutputs.items } as NormalizedResult;
        }
      }
      return { type: "unknown", raw } as NormalizedResult;
    },
    Component: TopicRenderer as any,
  },


// end registry array
];

export function findRendererByKind(kind?: WorkflowKind) {
  return REGISTRY.find(r => r.kind === kind);
}

export function getDefaultRenderer() {
  return {
    normalize: (raw: any) => ({ type: "unknown", raw } as NormalizedResult),
    Component: GenericRenderer,
  };
}

// Try registry entries on a given raw object; if any normalize() returns non-unknown, use it; else default
export function pickRendererForRaw(raw: any) {
  for (const r of REGISTRY) {
    try {
      const data = r.normalize(raw) as NormalizedResult;
      if ((data as any)?.type && (data as any).type !== "unknown") {
        return { Component: r.Component, data };
      }
    } catch (_) {
      // ignore and continue
    }
  }
  const def = getDefaultRenderer();
  return { Component: def.Component, data: def.normalize(raw) };
}

