import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";

interface RenameConversationRequest {
  name?: string;
  auto_generate?: boolean;
}

interface RenameConversationResponse {
  success: boolean;
  data: {
    id: string;
    name: string;
    inputs: Record<string, any>;
    status: string;
    introduction: string | null;
    created_at: number;
    updated_at: number;
  };
}

interface DeleteConversationResponse {
  success: boolean;
  data: {
    result: string;
  };
}

interface UseConversationActionsOptions {
  agentId: string;
  onRenameSuccess?: (conversation: any) => void;
  onDeleteSuccess?: (conversationId: string) => void;
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export function useConversationActions({ 
  agentId, 
  onRenameSuccess, 
  onDeleteSuccess, 
  onSuccess,
  onError 
}: UseConversationActionsOptions) {
  const [renaming, setRenaming] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 重命名会话
  const renameConversation = useCallback(async (
    conversationId: string, 
    data: RenameConversationRequest
  ) => {
    if (!agentId) {
      const errorMsg = "智能体ID不能为空";
      setError(errorMsg);
      onError?.(errorMsg);
      return;
    }

    if (!conversationId?.trim()) {
      const errorMsg = "会话ID不能为空";
      setError(errorMsg);
      onError?.(errorMsg);
      return;
    }

    if (!data.name && !data.auto_generate) {
      const errorMsg = "必须提供会话名称或启用自动生成";
      setError(errorMsg);
      onError?.(errorMsg);
      return;
    }

    setRenaming(true);
    setError(null);

    try {
      console.log("Renaming conversation:", {
        agentId,
        conversationId,
        data
      });

      const response = await (apiClient.v1.ecosystem.superbrain.agents[":id"].conversations.name as any).$put({
        param: { id: agentId },
        query: { conversation_id: conversationId },
        json: data
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json() as RenameConversationResponse;

      if (result.success) {
        console.log("Conversation renamed successfully:", result.data);
        toast.success("会话重命名成功");
        
        if (onRenameSuccess) {
          onRenameSuccess(result.data);
        }
        if (onSuccess) {
          onSuccess();
        }
        
        return result.data;
      } else {
        throw new Error("重命名会话失败");
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      setError(errorMessage);
      toast.error(`重命名会话失败: ${errorMessage}`);
      
      if (onError) {
        onError(errorMessage);
      }
      
      throw error;
    } finally {
      setRenaming(false);
    }
  }, [agentId, onRenameSuccess, onError]);

  // 删除会话
  const deleteConversation = useCallback(async (conversationId: string) => {
    if (!agentId) {
      const errorMsg = "智能体ID不能为空";
      setError(errorMsg);
      onError?.(errorMsg);
      return;
    }

    if (!conversationId?.trim()) {
      const errorMsg = "会话ID不能为空";
      setError(errorMsg);
      onError?.(errorMsg);
      return;
    }

    setDeleting(true);
    setError(null);

    try {
      console.log("Deleting conversation:", {
        agentId,
        conversationId
      });

      const response = await (apiClient.v1.ecosystem.superbrain.agents[":id"].conversations as any).$delete({
        param: { id: agentId },
        query: { conversation_id: conversationId }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json() as DeleteConversationResponse;

      if (result.success) {
        console.log("Conversation deleted successfully:", result.data);
        toast.success("会话删除成功");
        
        if (onDeleteSuccess) {
          onDeleteSuccess(conversationId);
        }
        if (onSuccess) {
          onSuccess();
        }
        
        return result.data;
      } else {
        throw new Error("删除会话失败");
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      setError(errorMessage);
      toast.error(`删除会话失败: ${errorMessage}`);
      
      if (onError) {
        onError(errorMessage);
      }
      
      throw error;
    } finally {
      setDeleting(false);
    }
  }, [agentId, onDeleteSuccess, onError]);

  // 手动设置会话名称
  const setConversationName = useCallback(async (
    conversationId: string, 
    name: string
  ) => {
    return renameConversation(conversationId, { name });
  }, [renameConversation]);

  // 自动生成会话名称
  const generateConversationName = useCallback(async (conversationId: string) => {
    return renameConversation(conversationId, { auto_generate: true });
  }, [renameConversation]);

  // 重置错误状态
  const resetError = useCallback(() => {
    setError(null);
  }, []);

  return {
    renaming,
    deleting,
    loading: renaming || deleting,
    error,
    renameConversation,
    deleteConversation,
    setConversationName,
    generateConversationName,
    resetError,
  };
}