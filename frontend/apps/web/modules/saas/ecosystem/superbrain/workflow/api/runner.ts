import { apiClient } from "@shared/lib/api-client";

export type RunOptions = {
  appId: string;
  kind?: string; // workflow kind hint
  inputs: Record<string, any>;
  response_mode?: "streaming" | "blocking";
};

export type StreamHandler = {
  onEvent?: (evt: any) => void;
  onError?: (err: any) => void;
  onDone?: () => void;
};

// Minimal runner for streaming workflows via our backend
export async function runWorkflowStream(opts: RunOptions, handlers: StreamHandler = {}) {
  const { appId, inputs, response_mode = "streaming" } = opts;
  const res = await (apiClient.v1.ecosystem.superbrain.workflows["topic-generation"].run as any).$post({
    json: { app_id: appId, ...inputs, response_mode },
  }) as Response;
  if (!res.ok) throw new Error(`HTTP ${res.status}: ${res.statusText}`);
  const reader = res.body?.getReader();
  if (!reader) throw new Error("无法读取响应流");
  const decoder = new TextDecoder();
  let buffer = "";
  while (true) {
    const { done, value } = await reader.read();
    if (done) break;
    buffer += decoder.decode(value, { stream: true });
    const lines = buffer.split("\n");
    buffer = lines.pop() || "";
    for (const line of lines) {
      if (!line.startsWith("data: ")) continue;
      const eventData = line.slice(6).trim();
      if (!eventData) continue;
      if (eventData === "[DONE]") { handlers.onDone?.(); return; }
      try { handlers.onEvent?.(JSON.parse(eventData)); } catch (e) { handlers.onError?.(e); }
    }
  }
  handlers.onDone?.();
}

