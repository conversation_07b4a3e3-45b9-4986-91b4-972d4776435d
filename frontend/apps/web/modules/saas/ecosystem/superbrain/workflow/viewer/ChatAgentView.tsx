"use client";

import { motion } from "framer-motion";
import styles from "../../sb-theme.module.css";
import { useRouter } from "next/navigation";
import { useSession } from "@saas/auth/hooks/use-session";
import { ArrowLeft, Plus, Send, Search } from "lucide-react";
import { useMemo, useState } from "react";

interface ChatAgentViewProps {
  agentId: string;
}

export function ChatAgentView({ agentId }: ChatAgentViewProps) {
  const router = useRouter();
  const { user } = useSession();
  const mockConversations = useMemo(
    () => [
      { id: "conv_1", name: "最近对话", last: "帮我优化黄金三秒…" },
      { id: "conv_2", name: "活动策划灵感", last: "给 10 个主题" },
      { id: "conv_3", name: "爆款话术打磨", last: "CTA 怎么写" },
    ],
    []
  );

  const mockMessages = useMemo(
    () => [
      { id: "m1", role: "user" as const, content: "你好，帮我优化这段开场。" },
      { id: "m2", role: "assistant" as const, content: "可以，从反差和场景感入手更好。" },
      {
        id: "m3",
        role: "user" as const,
        content:
          "这是一段很长很长的用户输入，用于测试消息框的折叠与展开行为。我们希望当文字很多的时候默认不展示成很高的消息框，而是保持一个适中的高度，用户可以通过点击‘展开’按钮来查看完整的内容，点击‘收起’按钮再回到折叠状态。该段文案会超过多行，用来验证在折叠态下的溢出隐藏与在展开态下的完整展示效果。",
      },
    ],
    []
  );

  const [expanded, setExpanded] = useState<string[]>([]);
  const [inputValue, setInputValue] = useState("");
  const toggleExpand = (id: string) =>
    setExpanded((prev) => (prev.includes(id) ? prev.filter((x) => x !== id) : [...prev, id]));

  return (
    <motion.div className={`${styles.sbTheme} min-h-screen bg-gradient-to-br from-[#01030c] via-[#0e1018] to-[#01030c] relative overflow-hidden`} initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.35 }}>

      {/* 主体布局：左侧会话 + 右侧聊天 */}
      <div className="h-screen flex overflow-hidden">
        {/* 左侧：历史会话列表（侧边栏） */}
        <motion.aside
          className={`w-72 h-full flex-shrink-0 p-3 ${styles.glass} border border-[#8394fb]/20 rounded-none rounded-r-2xl flex flex-col`}
          initial={{ opacity: 0, x: -12 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.35, ease: "easeOut", delay: 0.05 }}
        >
          <div className="flex items-center justify-between mb-3">
            <div className="text-sm text-[#eef3ff]">会话</div>
            <button
              className="group inline-flex items-center justify-center h-8 w-8 rounded-lg border border-[#8394fb]/30 text-[#c9d4ff] hover:border-[#c9d4ff]/60 hover:bg-[#0e1018]/60 hover:ring-1 hover:ring-[#c9d4ff]/40 hover:shadow-[0_0_12px_rgba(169,181,255,0.25)] active:scale-95 transition-all duration-200"
              aria-label="搜索会话"
            >
              <Search className="w-4 h-4 transition-colors duration-200 group-hover:text-white" />
            </button>
          </div>
          {/* 新建对话：长按钮样式 */}
          <button
            className={`group w-full mb-3 flex items-center gap-3 p-3 rounded-xl border ${styles.glass} text-left hover:border-[#c9d4ff]/55 hover:bg-[#0e1018]/60 hover:shadow-[0_0_16px_rgba(131,148,251,0.18)] hover:-translate-y-[1px] active:scale-[0.985] transition-all duration-200`}
          >
            <div className="inline-flex h-7 w-7 items-center justify-center rounded-lg bg-[#8394fb]/20 border border-[#aab6fd]/30 transition-colors duration-200 group-hover:border-[#c9d4ff]/60">
              <Plus className="w-4 h-4 text-[#c9d4ff] group-hover:text-white transition-colors" />
            </div>
            <div className="text-[13px] text-[#eef3ff]">新建对话</div>
          </button>
          <div className="flex-1 overflow-y-auto space-y-2">
            {mockConversations.map((c) => (
              <div
                key={c.id}
                className={`p-3 rounded-xl border ${styles.glass} cursor-pointer hover:border-[#c9d4ff]/55 hover:bg-[#0e1018]/60 hover:shadow-[0_0_14px_rgba(131,148,251,0.15)] transition-all duration-200 hover:translate-x-[1px]`}
              >
                <div className="text-[13px] text-white truncate">{c.name}</div>
                <div className="text-[12px] text-[#a0a0a0] truncate mt-1">{c.last}</div>
              </div>
            ))}
          </div>
          {/* 返回：长按钮样式，位于底部用户卡片上方 */}
          <div className="pt-3">
            <button
              type="button"
              onClick={() => router.push("/app/ecosystem/superbrain/workflow")}
              className={`group w-full flex items-center gap-3 p-3 rounded-xl border ${styles.glass} text-left hover:border-[#c9d4ff]/55 hover:bg-[#0e1018]/60 hover:shadow-[0_0_16px_rgba(131,148,251,0.18)] hover:-translate-y-[1px] active:scale-[0.985] transition-all duration-200`}
            >
              <div className="inline-flex h-7 w-7 items-center justify-center rounded-lg bg-[#8394fb]/20 border border-[#aab6fd]/30 transition-colors duration-200 group-hover:border-[#c9d4ff]/60">
                <ArrowLeft className="w-4 h-4 text-[#c9d4ff] group-hover:text-white transition-colors" />
              </div>
              <div className="text-[13px] text-[#eef3ff]">返回工作流</div>
            </button>
          </div>
          {/* 底部用户信息卡片（侧边栏内部） */}
          <div className="pt-3">
            <button
              type="button"
              onClick={() => router.push("/app/account")}
              className={`w-full flex items-center gap-3 px-3 py-3 rounded-xl border border-[#8394fb]/25 bg-[#0e1018]/80 backdrop-blur-sm hover:border-[#aab6fd]/35 hover:bg-[#0e1018]/95 transition-all ${styles.glass}`}
            >
              <div className="relative w-10 h-10 rounded-full bg-gradient-to-br from-[#8394fb] via-[#aab6fd] to-[#d7e0fc] p-[2px]">
                <div className="w-full h-full rounded-full bg-[#0e1018] flex items-center justify-center overflow-hidden">
                  {user?.avatar ? (
                    // eslint-disable-next-line @next/next/no-img-element
                    <img src={user?.avatar ?? undefined} alt={(user?.name ?? "用户")} className="w-full h-full object-cover" />
                  ) : (
                    <span className="text-[#8394fb] text-sm font-bold">{(user?.name || "用").slice(0, 1)}</span>
                  )}
                </div>
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium text-white truncate">{user?.name || "超级大脑用户"}</div>
                <div className="text-xs text-[#a0a0a0] truncate">9000AI SuperBrain</div>
              </div>
              <div className="text-[10px] px-2 py-1 rounded-md bg-[#aab6fd]/15 text-[#d7e0fc] border border-[#aab6fd]/30">资料</div>
            </button>
          </div>
        </motion.aside>

        {/* 右侧：聊天区域 */}
        <motion.section
          className="flex-1 min-w-0 flex flex-col px-8 lg:px-12"
          initial={{ opacity: 0, x: 12 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.4, ease: "easeOut", delay: 0.08 }}
        >
          {/* 消息列表（与输入框宽度保持一致） */}
          <div className="flex-1 overflow-y-auto py-6 space-y-6">
            <div className="max-w-4xl mx-auto w-full">
              {mockMessages.map((m) => {
                if (m.role === "user") {
                  return (
                    <div key={m.id} className={`max-w-[50%] ml-auto mb-6`}>
                      <div className={`relative px-7 lg:px-9 py-3 rounded-2xl border bg-[#8394fb]/15 border-[#aab6fd]/30 text-[#eef3ff]`}>
                        <div className={`${expanded.includes(m.id) ? "" : "line-clamp-3"}`}>
                          {m.content}
                        </div>
                      </div>
                      {m.content.length > 90 && (
                        <div className="mt-1 text-right">
                          <button
                            className="text-[12px] text-[#c9d4ff] hover:text-white transition-colors"
                            onClick={() => toggleExpand(m.id)}
                          >
                            {expanded.includes(m.id) ? "收起" : "展开"}
                          </button>
                        </div>
                      )}
                    </div>
                  );
                } else {
                  return (
                    <div key={m.id} className="max-w-4xl mx-auto w-full mb-6">
                      <div className="flex items-start gap-3">
                        {/* AI 图标 */}
                        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br from-[#8394fb] to-[#c9d4ff] flex items-center justify-center">
                          <div className="text-white text-sm font-bold">AI</div>
                        </div>
                        {/* AI 回复内容 */}
                        <div className="flex-1 text-white leading-relaxed">
                          {m.content}
                        </div>
                      </div>
                    </div>
                  );
                }
              })}
            </div>
          </div>

          {/* 输入框 */}
          <div className="px-4 pt-3 pb-8 bg-[#0e1018]/70 backdrop-blur-md">
            <div className="max-w-4xl mx-auto relative">
              <textarea
                placeholder="输入消息，与 AI 对话…"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                className="w-full min-h-[60px] max-h-32 px-4 py-3 pr-12 rounded-xl bg-[#0e1018]/70 text-[#eef3ff] placeholder-[#a0a0a0] focus:outline-none focus:ring-1 focus:ring-[#c9d4ff]/60 border border-[#8394fb]/30 resize-none"
                style={{ lineHeight: '1.5' }}
              />
              <button
                disabled={!inputValue.trim()}
                className={`absolute bottom-3 right-3 p-2 rounded-lg transition-all duration-200 ${inputValue.trim()
                    ? 'bg-[#8394fb] hover:bg-[#7a8bf0] text-white cursor-pointer hover:scale-105'
                    : 'bg-[#2a2d3a] text-[#6b7280] cursor-not-allowed'
                  }`}
              >
                <Send className="w-4 h-4" />
              </button>
            </div>
          </div>
        </motion.section>
      </div>

      {/* 右侧：聊天区域添加淡入动画容器 */}

    </motion.div>
  );
}


