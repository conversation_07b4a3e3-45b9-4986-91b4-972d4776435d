/**
 * 流式聊天Hook
 * 基于2025年最佳实践，优化消息累积和实时显示
 */

import { useState, useCallback, useRef, useEffect } from "react";
import { toast } from "sonner";
import type { ChatMessage, StreamingState, SSEEvent, ChatConfig } from "../../types";

interface UseStreamingChatOptions extends ChatConfig {
  onMessageUpdate?: (message: ChatMessage) => void;
  onStreamComplete?: (message: ChatMessage) => void;
  onError?: (error: string) => void;
}

export function useStreamingChat(options: UseStreamingChatOptions) {
  const {
    agentId,
    maxMessages = 100,
    retryAttempts = 3,
    onMessageUpdate,
    onStreamComplete,
    onError,
  } = options;

  // 消息状态管理
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [streamingState, setStreamingState] = useState<StreamingState>({
    isStreaming: false,
    accumulatedContent: "",
    taskId: undefined,
  });
  
  // 请求控制
  const [loading, setLoading] = useState(false);
  const abortControllerRef = useRef<AbortController | null>(null);
  const currentConversationId = useRef<string | null>(null);

  // 生成唯一消息ID
  const generateMessageId = useCallback((prefix: string) => {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  // 添加用户消息
  const addUserMessage = useCallback((content: string, conversationId?: string): ChatMessage => {
    const userMessage: ChatMessage = {
      id: generateMessageId("user"),
      role: "user",
      content,
      timestamp: new Date(),
      status: "sent",
      conversationId,
    };

    setMessages(prev => {
      const updated = [...prev, userMessage];
      // 限制消息数量
      return updated.length > maxMessages ? updated.slice(-maxMessages) : updated;
    });

    return userMessage;
  }, [maxMessages, generateMessageId]);

  // 开始流式响应
  const startStreaming = useCallback((messageId: string) => {
    setStreamingState({
      isStreaming: true,
      currentMessageId: messageId,
      taskId: undefined,
      accumulatedContent: "",
      error: undefined,
    });
  }, []);

  // 累积流式内容
  const appendStreamContent = useCallback((content: string) => {
    setStreamingState(prev => {
      if (!prev.isStreaming) return prev;
      
      const newContent = prev.accumulatedContent + content;
      
      // 创建临时消息用于实时显示
      const tempMessage: ChatMessage = {
        id: prev.currentMessageId || `assistant_${Date.now()}`,
        role: "assistant",
        content: newContent,
        timestamp: new Date(),
        conversationId: currentConversationId.current || undefined,
      };

      // 通知上层组件更新
      onMessageUpdate?.(tempMessage);

      return {
        ...prev,
        accumulatedContent: newContent,
      };
    });
  }, [onMessageUpdate]);

  // 完成流式响应
  const completeStreaming = useCallback((finalData?: any) => {
    setStreamingState(prev => {
      if (!prev.isStreaming) return prev;

      const finalMessage: ChatMessage = {
        id: prev.currentMessageId || generateMessageId("assistant"),
        role: "assistant",
        content: prev.accumulatedContent,
        timestamp: new Date(),
        status: "sent",
        conversationId: currentConversationId.current || undefined,
        metadata: finalData?.metadata,
      };

      // 将流式消息转为正式消息
      setMessages(prevMessages => {
        // 检查是否已存在相同ID的消息，避免重复添加
        const exists = prevMessages.some(m => m.id === finalMessage.id);
        if (exists) {
          // 如果已存在，替换它
          const updated = prevMessages.map(m => 
            m.id === finalMessage.id ? finalMessage : m
          );
          return updated.length > maxMessages ? updated.slice(-maxMessages) : updated;
        } else {
          // 如果不存在，添加新消息
          const updated = [...prevMessages, finalMessage];
          return updated.length > maxMessages ? updated.slice(-maxMessages) : updated;
        }
      });

      // 通知完成
      onStreamComplete?.(finalMessage);

      return {
        isStreaming: false,
        accumulatedContent: "",
        currentMessageId: undefined,
        taskId: undefined,
        error: undefined,
      };
    });
  }, [maxMessages, onStreamComplete, generateMessageId]);

  // 处理流式错误
  const handleStreamError = useCallback((error: string) => {
    setStreamingState(prev => ({
      ...prev,
      isStreaming: false,
      error,
    }));
    
    setLoading(false);
    onError?.(error);
    toast.error(`发送消息失败: ${error}`);
  }, [onError]);

  // 发送消息主函数
  const sendMessage = useCallback(async (
    content: string,
    conversationId?: string,
    retryCount = 0
  ): Promise<void> => {
    if (!agentId || !content.trim()) return;

    // 取消之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    const abortController = new AbortController();
    abortControllerRef.current = abortController;

    setLoading(true);
    
    try {
      // 添加用户消息
      const userMessage = addUserMessage(content, conversationId);
      currentConversationId.current = conversationId || null;

      // 准备流式响应 - 使用唯一ID
      const assistantMessageId = generateMessageId("assistant");
      startStreaming(assistantMessageId);

      // 发送请求
      const response = await fetch(`/api/v1/ecosystem/superbrain/agents/${agentId}/chat`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          query: content,
          conversation_id: conversationId,
          response_mode: "streaming",
          inputs: {},
        }),
        signal: abortController.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // 处理SSE流
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("无法读取响应流");
      }

      const decoder = new TextDecoder();
      let buffer = "";

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split("\n");
          buffer = lines.pop() || "";

          for (const line of lines) {
            if (line.startsWith("data: ")) {
              const eventData = line.slice(6).trim();
              
              if (eventData === "[DONE]") {
                completeStreaming();
                setLoading(false);
                return;
              }

              try {
                const event: SSEEvent = JSON.parse(eventData);
                
                // 更新会话ID
                if (event.conversation_id) {
                  currentConversationId.current = event.conversation_id;
                }

                // 更新任务ID用于停止功能
                if (event.task_id) {
                  setStreamingState(prev => ({
                    ...prev,
                    taskId: event.task_id
                  }));
                }

                // 处理流式内容
                if (event.event === "agent_message" && event.answer) {
                  appendStreamContent(event.answer);
                }

                // 处理消息结束
                if (event.event === "message_end") {
                  completeStreaming(event);
                  setLoading(false);
                  return;
                }

              } catch (parseError) {
                console.warn("解析SSE事件失败:", parseError, "原始数据:", eventData);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }

    } catch (error) {
      // 处理取消的请求
      if (error instanceof Error && error.name === 'AbortError') {
        return;
      }

      const errorMessage = error instanceof Error ? error.message : String(error);

      // 重试逻辑
      if (retryCount < retryAttempts) {
        console.log(`重试发送消息 (${retryCount + 1}/${retryAttempts})`);
        await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, retryCount)));
        return sendMessage(content, conversationId, retryCount + 1);
      }

      handleStreamError(errorMessage);
    }
  }, [
    agentId,
    retryAttempts,
    addUserMessage,
    startStreaming,
    appendStreamContent,
    completeStreaming,
    handleStreamError,
    generateMessageId,
  ]);

  // 停止流式响应
  const stopStreaming = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    if (streamingState.isStreaming) {
      completeStreaming();
    }
    
    setLoading(false);
  }, [streamingState.isStreaming, completeStreaming]);

  // 清理消息
  const clearMessages = useCallback(() => {
    setMessages([]);
    setStreamingState({
      isStreaming: false,
      accumulatedContent: "",
      taskId: undefined,
    });
    currentConversationId.current = null;
  }, []);

  // 重试消息
  const retryMessage = useCallback((messageId: string) => {
    const messageIndex = messages.findIndex(m => m.id === messageId);
    if (messageIndex > 0) {
      const userMessage = messages[messageIndex - 1];
      if (userMessage.role === "user") {
        // 移除失败的消息
        setMessages(prev => prev.filter(m => m.id !== messageId));
        // 重新发送
        sendMessage(userMessage.content, userMessage.conversationId);
      }
    }
  }, [messages, sendMessage]);

  // 清理函数
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    // 状态
    messages,
    streamingState,
    loading,
    conversationId: currentConversationId.current,
    
    // 操作
    sendMessage,
    stopStreaming,
    clearMessages,
    retryMessage,
    
    // 辅助状态
    isStreaming: streamingState.isStreaming,
    hasError: !!streamingState.error,
    canSend: !loading && !streamingState.isStreaming,
  };
}