"use client";

import { motion } from "framer-motion";
import { ArrowLeft } from "lucide-react";
import styles from "../../sb-theme.module.css";
import { useSession } from "@saas/auth/hooks/use-session";

interface TopBarProps {
  title?: string;
  onBack?: () => void;
}

export function TopBar({ title, onBack }: TopBarProps) {
  const { user } = useSession();
  return (
    <motion.div
      className={`fixed top-0 left-0 right-0 z-40 px-4 py-3 border-b border-[#8394fb]/20 bg-[#0e1018]/80 backdrop-blur-md ${styles.glass}`}
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.25 }}
    >
      <div className="max-w-6xl mx-auto flex items-center justify-between gap-3">
        <div className="flex items-center gap-3">
          <button
            type="button"
            onClick={onBack}
            className="inline-flex items-center justify-center h-10 w-10 rounded-xl border border-[#8394fb]/30 bg-[#0e1018]/80 text-white hover:border-[#aab6fd]/50 hover:bg-[#0e1018]/95 transition-all"
            aria-label="返回"
          >
            <ArrowLeft className="h-5 w-5 text-[#c9d4ff]" />
          </button>
          {title ? (
            <div className="text-white text-sm sm:text-base font-medium">{title}</div>
          ) : null}
        </div>
        {/* 用户信息（内联展示，无卡片边框） */}
        <div className="flex items-center gap-3">
          <div className="relative w-9 h-9 rounded-full bg-gradient-to-br from-[#8394fb] via-[#aab6fd] to-[#d7e0fc] p-[2px]">
            <div className="w-full h-full rounded-full bg-[#0e1018] flex items-center justify-center overflow-hidden">
              {user?.avatar ? (
                // eslint-disable-next-line @next/next/no-img-element
                <img src={user.avatar} alt={user?.name || "用户"} className="w-full h-full object-cover" />
              ) : (
                <span className="text-[#8394fb] text-xs font-bold">{(user?.name || "用").slice(0,1)}</span>
              )}
            </div>
          </div>
          <div className="hidden sm:block min-w-0 text-right">
            <div className="text-xs font-medium text-white truncate">{user?.name || "超级大脑用户"}</div>
            <div className="text-[10px] text-[#a0a0a0] truncate">9000AI SuperBrain</div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}

