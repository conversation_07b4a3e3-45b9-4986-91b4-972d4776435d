import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState, useEffect, useRef } from "react";
import { toast } from "sonner";
import type { Message, RawMessage } from "../components/chat/types";

interface MessageQuery {
  conversation_id: string;
  page?: number;
  limit?: number;
  first_id?: string;
  last_id?: string;
}

interface MessagesResponse {
  success: boolean;
  data: RawMessage[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    has_more: boolean;
  };
}

interface UseMessagesOptions {
  agentId: string;
  conversationId?: string;
  autoFetch?: boolean;
}

// 将后端原始消息转换为前端显示格式
const convertRawMessagesToDisplayMessages = (rawMessages: RawMessage[]): Message[] => {
  const messages: Message[] = [];
  
  rawMessages.forEach((rawMessage) => {
    // 用户消息时间戳 - 稍微早于助手消息
    const userTimestamp = typeof rawMessage.created_at === 'number' 
      ? rawMessage.created_at 
      : new Date(rawMessage.created_at).getTime() / 1000;
    
    // 助手消息时间戳 - 稍微晚于用户消息
    const assistantTimestamp = userTimestamp + 1;
    
    // 添加用户消息
    if (rawMessage.query) {
      messages.push({
        id: `${rawMessage.id}-user`,
        content: rawMessage.query,
        role: "user",
        created_at: userTimestamp,
        conversation_id: rawMessage.conversation_id
      });
    }
    
    // 添加助手消息
    if (rawMessage.answer) {
      messages.push({
        id: `${rawMessage.id}-assistant`,
        content: rawMessage.answer,
        role: "assistant",
        created_at: assistantTimestamp,
        conversation_id: rawMessage.conversation_id
      });
    }
  });
  
  return messages;
};

export function useMessages({ agentId, conversationId, autoFetch = true }: UseMessagesOptions) {
  const [loading, setLoading] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(false);
  
  // 请求去重：保存当前请求的AbortController
  const abortControllerRef = useRef<AbortController | null>(null);

  // 获取消息历史
  const fetchMessages = useCallback(async (query?: Partial<MessageQuery>) => {
    const targetConversationId = query?.conversation_id || conversationId;
    
    if (!agentId || !targetConversationId) {
      console.warn("agentId and conversationId are required");
      setMessages([]);
      return;
    }

    // 取消之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    // 创建新的AbortController
    const abortController = new AbortController();
    abortControllerRef.current = abortController;
    
    setLoading(true);
    setError(null);

    try {
      // 构建查询参数（包含conversation_id作为查询参数）
      const queryParams: Record<string, string> = {
        conversation_id: targetConversationId,
        limit: String(query?.limit || 50),
        page: String(query?.page || 1),
      };

      if (query?.first_id) queryParams.first_id = query.first_id;
      if (query?.last_id) queryParams.last_id = query.last_id;

      console.log("Fetching messages for conversation:", targetConversationId, "with query:", queryParams);

      const response = await (apiClient.v1.ecosystem.superbrain.agents[":id"].conversations.messages as any).$get({
        param: { 
          id: agentId
        },
        query: queryParams
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json() as MessagesResponse;

      if (result.success) {
        if (!Array.isArray(result.data)) {
          throw new Error("响应数据格式不正确: data应该是数组");
        }

        // 按时间排序原始消息（最新的在最后）
        const sortedRawMessages = result.data.sort((a, b) => 
          (typeof a.created_at === 'number' ? a.created_at : new Date(a.created_at).getTime()) - 
          (typeof b.created_at === 'number' ? b.created_at : new Date(b.created_at).getTime())
        );

        // 转换为前端显示格式
        const displayMessages = convertRawMessagesToDisplayMessages(sortedRawMessages);

        setMessages(displayMessages);
        setHasMore(result.pagination?.has_more || false);
        
        console.log(`获取到 ${result.data.length} 条原始消息，转换为 ${displayMessages.length} 条显示消息`);
      } else {
        throw new Error("获取消息历史失败");
      }
    } catch (error) {
      // 如果是取消的请求，不处理错误
      if (error instanceof Error && error.name === 'AbortError') {
        return;
      }
      
      const errorMessage = error instanceof Error ? error.message : String(error);
      setError(errorMessage);
      toast.error(`获取消息历史失败: ${errorMessage}`);
      
      // 重置数据
      setMessages([]);
      setHasMore(false);
    } finally {
      // 只有当前请求没有被取消时才设置loading为false
      if (abortController.signal.aborted === false) {
        setLoading(false);
      }
    }
  }, [agentId, conversationId]);

  // 刷新消息列表
  const refresh = useCallback(() => {
    if (conversationId) {
      return fetchMessages({ conversation_id: conversationId });
    }
  }, [fetchMessages, conversationId]);

  // 清空消息（用于新对话）
  const clearMessages = useCallback(() => {
    setMessages([]);
    setError(null);
    setHasMore(false);
  }, []);

  // 自动获取数据
  useEffect(() => {
    console.log("📥 [useMessages] useEffect触发:", {
      autoFetch,
      agentId,
      conversationId,
      shouldFetch: autoFetch && agentId && conversationId
    });
    
    if (autoFetch && agentId && conversationId) {
      console.log("🔄 [useMessages] 自动获取消息");
      fetchMessages({ conversation_id: conversationId });
    } else if (!conversationId) {
      console.log("🧹 [useMessages] 清空消息（无会话ID）");
      // 如果没有会话ID，清空消息
      clearMessages();
    }
  }, [agentId, conversationId, autoFetch, fetchMessages, clearMessages]);

  // 清理函数：组件卸载时取消请求
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    messages,
    loading,
    error,
    hasMore,
    fetchMessages,
    refresh,
    clearMessages,
  };
}