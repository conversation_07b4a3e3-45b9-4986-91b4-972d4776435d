import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState, useRef } from "react";
import { toast } from "sonner";

interface ChatMessage {
  query: string;
  conversation_id?: string;
  inputs?: Record<string, any>;
  response_mode?: "streaming" | "blocking";
  files?: Array<{
    type: "image";
    transfer_method: "remote_url";
    url: string;
  }>;
}

interface ChatResponse {
  success: boolean;
  data: {
    event: string;
    task_id: string;
    id: string;
    message_id: string;
    conversation_id: string;
    mode: string;
    answer: string;
    metadata: {
      usage?: {
        prompt_tokens: number;
        completion_tokens: number;
        total_tokens: number;
      };
      retriever_resources?: any[];
      agent_thoughts?: any[];
    };
    created_at: number;
  };
}

interface StreamEvent {
  event: string;
  data: any;
  task_id?: string;
  message_id?: string;
  conversation_id?: string;
  answer?: string;
}

interface UseChatOptions {
  agentId: string;
  onMessage?: (event: StreamEvent) => void;
  onError?: (error: string) => void;
  onComplete?: (response: any) => void;
}

export function useChat({ agentId, onMessage, onError, onComplete }: UseChatOptions) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [response, setResponse] = useState<any>(null);
  
  // 保存当前任务ID用于停止操作
  const [currentTaskId, setCurrentTaskId] = useState<string | null>(null);
  
  // 请求去重：保存当前请求的AbortController
  const abortControllerRef = useRef<AbortController | null>(null);

  // 发送聊天消息
  const sendMessage = useCallback(async (message: ChatMessage) => {
    if (!agentId) {
      const errorMsg = "智能体ID不能为空";
      setError(errorMsg);
      onError?.(errorMsg);
      return;
    }

    if (!message.query?.trim()) {
      const errorMsg = "消息内容不能为空";
      setError(errorMsg);
      onError?.(errorMsg);
      return;
    }

    // 取消之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    // 创建新的AbortController
    const abortController = new AbortController();
    abortControllerRef.current = abortController;
    
    setLoading(true);
    setError(null);
    setResponse(null);
    setCurrentTaskId(null);

    try {
      console.log("Sending chat message:", {
        agentId,
        query: message.query,
        conversation_id: message.conversation_id,
        response_mode: message.response_mode || "streaming"
      });

      const requestBody = {
        query: message.query,
        inputs: message.inputs || {},
        response_mode: message.response_mode || "streaming",
        ...(message.conversation_id && { conversation_id: message.conversation_id }),
        ...(message.files && { files: message.files })
      };

      const response = await fetch(`/api/v1/ecosystem/superbrain/agents/${agentId}/chat`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
        signal: abortController.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // 处理流式响应
      if (message.response_mode === "streaming" || !message.response_mode) {
        console.log("🌊 [Stream] 开始处理流式响应");
        
        const reader = response.body?.getReader();
        if (!reader) {
          throw new Error("无法读取响应流");
        }

        const decoder = new TextDecoder();
        let buffer = "";
        let eventCount = 0;

        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) {
              console.log("🌊 [Stream] 流式响应完成，共处理", eventCount, "个事件");
              break;
            }

            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split("\n");
            buffer = lines.pop() || "";

            for (const line of lines) {
              if (line.startsWith("data: ")) {
                const eventData = line.slice(6);
                if (eventData === "[DONE]") {
                  console.log("🌊 [Stream] 收到结束信号 [DONE]");
                  setLoading(false);
                  return;
                }

                try {
                  const event = JSON.parse(eventData);
                  eventCount++;
                  
                  console.log(`🌊 [Stream] 事件 #${eventCount}:`, {
                    event: event.event,
                    task_id: event.task_id,
                    message_id: event.message_id,
                    conversation_id: event.conversation_id,
                    answer_length: event.answer ? event.answer.length : 0
                  });
                  
                  // 保存任务ID
                  if (event.task_id) {
                    setCurrentTaskId(event.task_id);
                    console.log("💾 [Stream] 保存任务ID:", event.task_id);
                  }

                  // 调用消息回调
                  if (onMessage) {
                    console.log("📨 [Stream] 调用消息回调:", event.event);
                    onMessage(event);
                  }

                  // 消息结束时调用完成回调
                  if (event.event === "message_end" && onComplete) {
                    console.log("✅ [Stream] 消息结束，调用完成回调");
                    onComplete(event);
                  }
                } catch (parseError) {
                  console.warn("⚠️ [Stream] 解析SSE事件失败:", parseError, "原始数据:", eventData);
                }
              }
            }
          }
        } finally {
          reader.releaseLock();
        }
      } else {
        // 处理阻塞响应
        const result = await response.json() as ChatResponse;
        
        if (result.success) {
          setResponse(result.data);
          if (onComplete) {
            onComplete(result.data);
          }
        } else {
          throw new Error("发送消息失败");
        }
      }
    } catch (error) {
      // 如果是取消的请求，不处理错误
      if (error instanceof Error && error.name === 'AbortError') {
        return;
      }
      
      const errorMessage = error instanceof Error ? error.message : String(error);
      setError(errorMessage);
      toast.error(`发送消息失败: ${errorMessage}`);
      
      if (onError) {
        onError(errorMessage);
      }
    } finally {
      // 只有当前请求没有被取消时才设置loading为false
      if (!abortController.signal.aborted) {
        setLoading(false);
      }
    }
  }, [agentId, onMessage, onError, onComplete]);

  // 取消当前请求
  const cancel = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      setLoading(false);
      setCurrentTaskId(null);
    }
  }, []);

  // 清理函数
  const cleanup = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    setLoading(false);
    setError(null);
    setResponse(null);
    setCurrentTaskId(null);
  }, []);

  return {
    loading,
    error,
    response,
    currentTaskId,
    sendMessage,
    cancel,
    cleanup,
  };
}