"use client";

import { useMemo, useState } from "react";
import { RendererProps } from "../adapters/types";
import styles from "../../sb-theme.module.css";
import { useTopicGenerationWorkflow } from "../hooks";

// 前端渲染所需的精简数据结构
export interface TopicItem {
  topic_direction: string;
  angle_keywords?: string[];
  target_user?: string;
  formula_cn?: string;
  type?: string;
}

export interface ProductTagMatrix {
  A1_core_tags?: string[];
  A2_features_selling_points?: string[];
  A4_unique_advantages?: string[];
  A5_sensory_experience?: string[];
  A6_service_process_flow?: string[];
  A7_culture_symbol?: string[];
}

export interface UserPersonaItem {
  persona_name: string;
  persona_description?: string;
  persona_quote?: string;
  core_needs?: string[];
  detailed_pain_points?: Partial<Record<"decision_making"|"in_use_experience"|"social_dynamics"|"post_use_concerns"|"identity_conflict", string[]>>;
}

export interface TopicRenderViewModel {
  result: TopicItem[];
  matrices?: {
    product_tag_matrix?: ProductTagMatrix;
    user_persona_matrix?: UserPersonaItem[];
  };
  usage?: { total_tokens?: number; latency?: number };
}

// 将流式事件或 outputs 脱敏提取为前端渲染模型
function extractViewModelFromEvents(events: any[]): TopicRenderViewModel {
  const wfFinished = events.find((e) => e.event === "workflow_finished");
  const outputs = wfFinished?.data?.outputs || {};
  const nodeOutputs = outputs?.node_outputs || outputs?.nodeOutputs || {};
  const result = outputs?.result || outputs?.results || [];

  // 脱敏：仅挑选必要字段
  const items: TopicItem[] = Array.isArray(result)
    ? result.map((r: any) => ({
        topic_direction: r.topic_direction,
        angle_keywords: r.angle_keywords,
        target_user: r.target_user,
        formula_cn: r.formula_cn,
        type: r.type,
      })).filter((x) => !!x.topic_direction)
    : [];

  const product_tag_matrix: ProductTagMatrix | undefined = nodeOutputs?.product_tag_matrix ? {
    A1_core_tags: nodeOutputs.product_tag_matrix.A1_core_tags,
    A2_features_selling_points: nodeOutputs.product_tag_matrix.A2_features_selling_points,
    A4_unique_advantages: nodeOutputs.product_tag_matrix.A4_unique_advantages,
    A5_sensory_experience: nodeOutputs.product_tag_matrix.A5_sensory_experience,
    A6_service_process_flow: nodeOutputs.product_tag_matrix.A6_service_process_flow,
    A7_culture_symbol: nodeOutputs.product_tag_matrix.A7_culture_symbol,
  } : undefined;

  const user_persona_matrix: UserPersonaItem[] | undefined = Array.isArray(nodeOutputs?.user_persona_matrix)
    ? (nodeOutputs.user_persona_matrix as any[]).map((p) => ({
        persona_name: (p as any).persona_name,
        persona_description: (p as any).persona_description,
        persona_quote: (p as any).persona_quote,
        core_needs: (p as any).core_needs,
        detailed_pain_points: (p as any).detailed_pain_points,
      })).filter((p: UserPersonaItem) => !!p.persona_name)
    : undefined;

  const usage = wfFinished?.data ? {
    total_tokens: wfFinished.data.total_tokens ?? wfFinished.data.usage?.total_tokens,
    latency: wfFinished.data.elapsed_time ?? wfFinished.data.usage?.latency,
  } : undefined;

  return { result: items, matrices: { product_tag_matrix, user_persona_matrix }, usage };
}

export function TopicRenderer({ data, ...props }: RendererProps<any> & { appId?: string }) {
  const topic = useTopicGenerationWorkflow();

  // 表单状态（符合需求：移除画像 input）
  const [topicType, setTopicType] = useState<"教知识" | "讲故事" | "聊观点" | "讲产品" | "晒过程">("教知识");
  const [number, setNumber] = useState<number>(20);
  const [titledText, setTitledText] = useState<string>("");
  const [additional, setAdditional] = useState<string>("");
  const [productInfoOpen, setProductInfoOpen] = useState<boolean>(false);
  const [productInfo, setProductInfo] = useState<string>("");

  const viewModel = useMemo<TopicRenderViewModel>(() => extractViewModelFromEvents(topic.events), [topic.events]);

  const handleRun = async () => {
    await topic.run({
      appId: (props as any)?.appId || "",
      topic_type: topicType,
      number,
      titled: titledText.split("\n").map(s => s.trim()).filter(Boolean),
      additional,
      product_info: productInfo || undefined,
    });
  };

  return (
    <div className="space-y-6">
      {/* 表单 */}
      <div className={`rounded-2xl border ${styles.glass} p-5`}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm text-[#eef3ff] mb-2">选题类型</label>
            <select value={topicType} onChange={(e) => setTopicType(e.target.value as any)} className="w-full h-11 px-3 rounded-xl bg-[#0e1018]/70 border border-[#8394fb]/30 text-[#eef3ff]">
              {(["教知识","讲故事","聊观点","讲产品","晒过程"] as const).map(op => <option key={op} value={op}>{op}</option>)}
            </select>
          </div>
          <div>
            <label className="block text-sm text-[#eef3ff] mb-2">生成条数</label>
            <input type="number" min={1} max={50} value={number} onChange={(e) => setNumber(Math.max(1, Math.min(50, Number(e.target.value) || 1)))} className="w-full h-11 px-3 rounded-xl bg-[#0e1018]/70 border border-[#8394fb]/30 text-[#eef3ff]" />
          </div>
          <div className="md:col-span-2">
            <label className="block text-sm text-[#eef3ff] mb-2">历史选题去重（每行一条）</label>
            <textarea rows={4} value={titledText} onChange={(e) => setTitledText(e.target.value)} className="w-full rounded-xl bg-[#0e1018]/70 border border-[#8394fb]/30 text-[#eef3ff] placeholder-[#a0a0a0] p-3" />
          </div>
          <div className="md:col-span-2">
            <label className="block text-sm text-[#eef3ff] mb-2">额外要求</label>
            <input value={additional} onChange={(e) => setAdditional(e.target.value)} className="w-full h-11 px-3 rounded-xl bg-[#0e1018]/70 border border-[#8394fb]/30 text-[#eef3ff]" />
          </div>
        </div>
        {/* 产品信息（可折叠） */}
        <details className="mt-4">
          <summary className="text-[#c9d4ff] cursor-pointer" onClick={() => setProductInfoOpen(v => !v)}>
            产品信息（可选）
          </summary>
          <div className="mt-3">
            <textarea rows={4} placeholder='建议包含{"name":"","description":""}' value={productInfo} onChange={(e) => setProductInfo(e.target.value)} className="w-full rounded-xl bg-[#0e1018]/70 border border-[#8394fb]/30 text-[#eef3ff] p-3" />
          </div>
        </details>
        <div className="mt-5 flex justify-end">
          <button onClick={handleRun} disabled={topic.loading || topic.isStreaming} className="inline-flex items-center gap-2 h-11 px-5 rounded-xl border transition hover:border-[#c9d4ff]/60 border-[#aab6fd]/40 bg-gradient-to-r from-[#8394fb]/30 to-[#c9d4ff]/20 text-white">
            {topic.loading || topic.isStreaming ? "生成中…" : "生成选题"}
          </button>
        </div>
      </div>

      {/* 结果：用量徽章 */}
      {viewModel.usage && (
        <div className="text-xs text-[#a0a0a0]">Token: {viewModel.usage.total_tokens ?? "-"} · 延迟: {viewModel.usage.latency ?? "-"}s</div>
      )}

      {/* 中间产出矩阵 */}
      {viewModel.matrices?.product_tag_matrix && (
        <div className={`rounded-2xl border ${styles.glass} p-5`}>
          <div className="text-white font-semibold mb-3">产品标签矩阵</div>
          <div className="grid md:grid-cols-2 gap-3 text-sm">
            {Object.entries(viewModel.matrices.product_tag_matrix).map(([k, arr]) => (
              <div key={k} className="p-3 rounded-xl border border-[#8394fb]/25 bg-[#0e1018]/60">
                <div className="text-[#c9d4ff] mb-1">{k}</div>
                <div className="text-[#eef3ff]">{(arr || []).join(" / ")}</div>
              </div>
            ))}
          </div>
        </div>
      )}

      {viewModel.matrices?.user_persona_matrix && (
        <div className={`rounded-2xl border ${styles.glass} p-5`}>
          <div className="text-white font-semibold mb-3">用户画像矩阵</div>
          <div className="grid md:grid-cols-2 gap-3">
            {viewModel.matrices.user_persona_matrix.map((p, idx) => (
              <div key={idx} className="p-3 rounded-xl border border-[#8394fb]/25 bg-[#0e1018]/60 text-sm">
                <div className="text-[#eef3ff] font-medium">{p.persona_name}</div>
                {p.persona_description && <div className="text-[#a0a0a0] text-xs mt-1">{p.persona_description}</div>}
                {p.core_needs?.length ? <div className="text-[#c9d4ff] text-xs mt-2">核心需求：{p.core_needs.join(" / ")}</div> : null}
                {p.detailed_pain_points && (
                  <details className="mt-2">
                    <summary className="text-[#c9d4ff] text-xs cursor-pointer">痛点细分</summary>
                    <div className="text-[#a0a0a0] text-xs mt-1 space-y-1">
                      {Object.entries(p.detailed_pain_points).map(([k, arr]) => (
                        <div key={k}>{k}: {(arr || []).join(" / ")}</div>
                      ))}
                    </div>
                  </details>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 选题结果 */}
      {/* 节点进度（实时） */}
      {topic.events.filter(e => e.event === "node_started" || e.event === "node_finished").length > 0 && (
        <div className={`rounded-2xl border ${styles.glass} p-5`}>
          <div className="text-white font-semibold mb-3">节点执行进度</div>
          <div className="space-y-2">
            {topic.events.filter(e => e.event === "node_started" || e.event === "node_finished").map((event, idx) => {
              const isStarted = event.event === "node_started";
              const isFinished = event.event === "node_finished";
              const nodeData = event.data || {} as any;
              return (
                <div key={idx} className="flex items-center gap-3 p-2 rounded-lg bg-[#0e1018]/30">
                  <div className={`w-2 h-2 rounded-full ${
                    isFinished && (nodeData as any)?.status === "succeeded" ? "bg-green-400" :
                    isFinished && (nodeData as any)?.status === "failed" ? "bg-red-400" :
                    isStarted ? "bg-yellow-400" : "bg-gray-400"
                  }`} />
                  <div className="flex-1">
                    <div className="text-[#eef3ff] text-sm">
                      {(nodeData as any)?.title || (nodeData as any)?.node_type || "未知节点"}
                      {typeof (nodeData as any)?.index !== "undefined" && (
                        <span className="text-[#a0a0a0] ml-2">#{(nodeData as any)?.index}</span>
                      )}
                    </div>
                    {isFinished && (nodeData as any)?.elapsed_time && (
                      <div className="text-xs text-[#a0a0a0]">耗时: {(nodeData as any).elapsed_time.toFixed?.(2) ?? (nodeData as any).elapsed_time}s</div>
                    )}
                  </div>
                  <div className="text-xs text-[#a0a0a0]">
                    {isStarted ? "开始" : isFinished ? "完成" : ""}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      <div className={`rounded-2xl border ${styles.glass} p-5`}>
        <div className="text-white font-semibold mb-3">选题结果</div>
        {viewModel.result.length === 0 ? (
          <div className="text-[#a0a0a0] text-sm">暂无结果，请先提交表单生成。</div>
        ) : (
          <div className="grid sm:grid-cols-2 gap-3">
            {viewModel.result.map((it, idx) => (
              <div key={idx} className="p-3 rounded-xl border border-[#8394fb]/25 bg-[#0e1018]/60">
                <div className="text-[#eef3ff]">{it.topic_direction}</div>
                {it.angle_keywords?.length ? (
                  <div className="mt-1 text-xs text-[#a0a0a0]">关键词：{it.angle_keywords.join(" / ")}</div>
                ) : null}
                <div className="mt-2 flex items-center justify-between text-xs text-[#a0a0a0]">
                  <div>{it.target_user || "-"}</div>
                  {it.type && <span className="px-2 py-0.5 rounded bg-[#8394fb]/20 border border-[#8394fb]/40 text-[#c9d4ff]">{it.type}</span>}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

