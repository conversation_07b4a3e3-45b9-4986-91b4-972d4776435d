"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Button } from "@ui/components/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { useCallback, useEffect, useRef, useState } from "react";
import { ConsumerDetailView } from "./components/ConsumerDetail";
import { ConsumerList } from "./components/ConsumerList";
import { useConsumerDetail } from "./hooks/use-consumer-detail";
import type {
	ConsumerListItem,
	ConsumerListQuery,
} from "./hooks/use-consumer-list";
import { useConsumerList } from "./hooks/use-consumer-list";

export interface ConsumerManagementProps {
	className?: string;
}

export function ConsumerManagement({ className }: ConsumerManagementProps) {
	const isFirstRender = useRef(true);

	// 查询参数
	const [query, setQuery] = useState<ConsumerListQuery>({
		page: 1,
		pageSize: 5,
		sortBy: "createdAt",
		sortOrder: "desc",
	});

	// API hooks
	const {
		loading: listLoading,
		consumers,
		total,
		currentPage,
		totalPages,
		fetchConsumers,
	} = useConsumerList();
	const {
		loading: detailLoading,
		consumer: selectedConsumerDetail,
		fetchConsumerDetail,
	} = useConsumerDetail();

	// 选中的消费者
	const [_selectedConsumer, setSelectedConsumer] =
		useState<ConsumerListItem | null>(null);

	// 显示详情对话框
	const [showDetailsDialog, setShowDetailsDialog] = useState(false);

	// 初始化加载
	useEffect(() => {
		if (isFirstRender.current) {
			isFirstRender.current = false;
			fetchConsumers(query);
		}
	}, [fetchConsumers, query]);

	// 处理筛选
	const handleFilter = useCallback(
		(newQuery: ConsumerListQuery) => {
			setQuery(newQuery);
			fetchConsumers(newQuery);
		},
		[fetchConsumers],
	);

	// 处理分页
	const handlePageChange = useCallback(
		(page: number) => {
			const newQuery = { ...query, page };
			setQuery(newQuery);
			fetchConsumers(newQuery);
		},
		[query, fetchConsumers],
	);

	// 处理消费者详情查看
	const handleViewConsumerDetails = useCallback(
		(consumer: ConsumerListItem) => {
			setSelectedConsumer(consumer);
			fetchConsumerDetail(consumer.id);
			setShowDetailsDialog(true);
		},
		[fetchConsumerDetail],
	);

	return (
		<div className={cn("space-y-8", className)}>
			{/* 标题区域 */}
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.1 }}
				className="relative mt-6 mb-8 space-y-1"
			>
				<div className="flex items-center gap-4">
					<div
						className={cn(
							"flex h-14 w-14 shrink-0 items-center justify-center",
							"rounded-xl bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
							"ring-1 ring-[#D4B485]/20",
							"shadow-[0_0_0_8px_rgba(212,180,133,0.03)]",
							"transform-gpu transition-transform duration-300",
							"hover:scale-105 hover:shadow-[0_0_0_10px_rgba(212,180,133,0.05)]",
						)}
					>
						<span className="h-7 w-7 text-[#D4B485]">👤</span>
					</div>
					<div className="space-y-1">
						<h2
							className={cn(
								"font-semibold text-3xl",
								zywhFont.className,
								"leading-none",
								"tracking-[0.05em]",
								"relative",
								"after:absolute after:inset-0",
								"after:bg-[radial-gradient(circle_at_50%_50%,rgba(212,180,133,0.15),transparent_70%)]",
								"after:blur-xl after:-z-10",
							)}
							style={{
								background:
									"linear-gradient(135deg, rgba(229,201,165,0.95) 0%, rgba(212,180,133,0.9) 50%, rgba(176,137,104,0.85) 100%)",
								WebkitBackgroundClip: "text",
								WebkitTextFillColor: "transparent",
								backgroundClip: "text",
								textShadow:
									"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.1)",
								filter: "contrast(1.1) brightness(1.05)",
							}}
						>
							消费者管理
							<span className="ml-3 font-normal text-base text-[#D4B485]/80 tracking-wide">
								查看和管理平台用户
							</span>
						</h2>
						<p className="text-sm leading-relaxed text-white/40 group-hover:text-white/60 transition-colors duration-300">
							查看消费者信息、算力使用情况、套餐订阅和支付记录等
						</p>
					</div>
				</div>
			</motion.div>

			<ConsumerList
				isLoading={listLoading}
				consumers={consumers}
				total={total}
				currentPage={currentPage}
				totalPages={totalPages}
				onPageChange={handlePageChange}
				onFilter={handleFilter}
				onViewConsumerDetails={handleViewConsumerDetails}
			/>

			{/* 消费者详情对话框 */}
			<Dialog
				open={showDetailsDialog}
				onOpenChange={setShowDetailsDialog}
			>
				<DialogContent className="bg-[#1E2023] border-[#D4B485]/20 text-white max-w-3xl">
					<DialogHeader>
						<DialogTitle className="text-[#D4B485]">
							消费者详情
						</DialogTitle>
						<DialogDescription className="text-[#D4B485]/60">
							查看消费者详细信息和相关统计数据
						</DialogDescription>
					</DialogHeader>

					{selectedConsumerDetail ? (
						<ConsumerDetailView
							consumer={selectedConsumerDetail}
							isLoading={detailLoading}
						/>
					) : (
						<div className="py-8 flex items-center justify-center">
							<div className="text-[#D4B485]/60">
								{detailLoading ? "加载中..." : "暂无数据"}
							</div>
						</div>
					)}

					<DialogFooter>
						<Button
							variant="outline"
							className="bg-[#1A1C1E] border-[#D4B485]/20 text-[#D4B485] hover:bg-[#D4B485]/10"
							onClick={() => setShowDetailsDialog(false)}
						>
							关闭
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</div>
	);
}
