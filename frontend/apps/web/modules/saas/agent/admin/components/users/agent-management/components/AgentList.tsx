"use client";

import type { <PERSON><PERSON><PERSON> } from "@prisma/client";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Skeleton } from "@ui/components/skeleton";
import { cn } from "@ui/lib";
import {
	ChevronLeftIcon,
	ChevronRightIcon,
	PlusCircleIcon,
	RefreshCcwIcon,
	SearchIcon,
	SlidersHorizontalIcon,
} from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import type {
	AgentListItem as AgentListItemType,
	AgentListQuery,
} from "../hooks/use-agent-list";
import { AgentFilterBar, type AgentFilterOptions } from "./AgentFilterBar";
import { AgentListItem } from "./AgentListItem";

// 角色名称映射
const roleNameMap: Record<AgentRole, string> = {
	ADMIN: "管理员",
	BRANCH: "分公司",
	DIRECTOR: "联席董事",
	PARTNER: "合伙人",
	SALES: "超级个体",
};

// 状态映射
const statusOptions = [
	{ value: "ALL", label: "全部状态" },
	{ value: "ACTIVE", label: "已激活" },
	{ value: "DISABLED", label: "已禁用" },
	{ value: "PENDING", label: "待审核" },
];

export interface AgentListProps {
	onAddAgent?: () => void;
	onEditAgent?: (agent: AgentListItemType) => void;
	onViewAgentDetails?: (agent: AgentListItemType) => void;
	onViewAgentStructure?: (agent: AgentListItemType) => void;
	onDeleteAgent?: (agent: AgentListItemType) => void;
	onFilter?: (query: AgentListQuery) => void;
	onPageChange?: (page: number) => void;
	isLoading?: boolean;
	agents?: AgentListItemType[];
	total?: number;
	currentPage?: number;
	totalPages?: number;
}

export function AgentList({
	onAddAgent,
	onEditAgent,
	onViewAgentDetails,
	onViewAgentStructure,
	onDeleteAgent,
	onFilter,
	onPageChange,
	isLoading = false,
	agents = [],
	total = 0,
	currentPage = 1,
	totalPages = 1,
}: AgentListProps) {
	const [showFilters, setShowFilters] = useState(false);
	const [searchTerm, setSearchTerm] = useState("");
	const [role, setRole] = useState<string>("ALL");
	const [status, setStatus] = useState<string>("ALL");
	const [debouncedSearch, setDebouncedSearch] = useState("");

	// 处理搜索防抖
	useEffect(() => {
		const timer = setTimeout(() => {
			setDebouncedSearch(searchTerm);
		}, 500);

		return () => clearTimeout(timer);
	}, [searchTerm]);

	// 处理筛选条件变化
	const handleFilterChange = useCallback(() => {
		onFilter?.({
			page: 1,
			pageSize: 5,
			searchTerm: debouncedSearch,
			role: role as AgentRole | "ALL",
			status: status,
		});
	}, [debouncedSearch, onFilter, role, status]);

	// 当防抖搜索词变化时触发筛选
	useEffect(() => {
		handleFilterChange();
	}, [handleFilterChange]);

	// 处理详细筛选条件变化
	const handleDetailedFilterChange = (filters: AgentFilterOptions) => {
		onFilter?.({
			page: 1,
			pageSize: 5,
			searchTerm: filters.searchTerm,
			role: filters.role,
			status: filters.status,
			// 日期范围暂不处理
		});
	};

	// 处理页码变化
	const handlePageChange = (page: number) => {
		onPageChange?.(page);
	};

	// 重置所有筛选
	const handleReset = () => {
		setSearchTerm("");
		setRole("ALL");
		setStatus("ALL");
		onFilter?.({
			page: 1,
			pageSize: 5,
		});
	};

	// 生成页码数组
	const getPageNumbers = () => {
		const pageNumbers = [];
		const maxPagesToShow = 5; // 最多显示的页码数

		if (totalPages <= maxPagesToShow) {
			// 如果总页数小于等于最大显示页码数，显示所有页码
			for (let i = 1; i <= totalPages; i++) {
				pageNumbers.push(i);
			}
		} else {
			// 否则，显示当前页附近的页码
			let startPage = Math.max(
				1,
				currentPage - Math.floor(maxPagesToShow / 2),
			);
			let endPage = startPage + maxPagesToShow - 1;

			if (endPage > totalPages) {
				endPage = totalPages;
				startPage = Math.max(1, endPage - maxPagesToShow + 1);
			}

			for (let i = startPage; i <= endPage; i++) {
				pageNumbers.push(i);
			}
		}

		return pageNumbers;
	};

	return (
		<div className="space-y-6">
			<div className="rounded-lg bg-[#1E2023]/50 p-4">
				<div className="flex flex-wrap items-center gap-4">
					{/* 搜索框 */}
					<div className="relative flex-1">
						<SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-[#D4B485]/40" />
						<Input
							placeholder="搜索代理商名称、邮箱或手机号"
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className={cn(
								"pl-9",
								"bg-[#1E2023]/50",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
								"placeholder:text-[#D4B485]/40",
								"focus:border-[#D4B485]/40",
								"focus:ring-[#D4B485]/20",
							)}
							onKeyDown={(e) => {
								if (e.key === "Enter") {
									handleFilterChange();
								}
							}}
						/>
					</div>

					{/* 角色筛选 */}
					<Select
						value={role}
						onValueChange={(value) => setRole(value)}
					>
						<SelectTrigger
							className={cn(
								"w-[160px]",
								"bg-[#1E2023]/50",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
								"focus:border-[#D4B485]/40",
								"focus:ring-[#D4B485]/20",
							)}
						>
							<SelectValue placeholder="选择角色" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem
								value="ALL"
								className="text-[#D4B485] focus:bg-[#D4B485]/10"
							>
								全部角色
							</SelectItem>
							{Object.entries(roleNameMap).map(
								([value, label]) => (
									<SelectItem
										key={value}
										value={value}
										className="text-[#D4B485] focus:bg-[#D4B485]/10"
									>
										{label}
									</SelectItem>
								),
							)}
						</SelectContent>
					</Select>

					{/* 状态筛选 */}
					<Select
						value={status}
						onValueChange={(value) => setStatus(value)}
					>
						<SelectTrigger
							className={cn(
								"w-[160px]",
								"bg-[#1E2023]/50",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
								"focus:border-[#D4B485]/40",
								"focus:ring-[#D4B485]/20",
							)}
						>
							<SelectValue placeholder="选择状态" />
						</SelectTrigger>
						<SelectContent>
							{statusOptions.map((option) => (
								<SelectItem
									key={option.value}
									value={option.value}
									className="text-[#D4B485] focus:bg-[#D4B485]/10"
								>
									{option.label}
								</SelectItem>
							))}
						</SelectContent>
					</Select>

					{/* 搜索按钮 */}
					<Button
						type="button"
						className={cn(
							"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
							"text-white",
							"border-none",
							"shadow-[0_8px_16px_-4px_rgba(212,180,133,0.3)]",
							"hover:shadow-[0_12px_20px_-4px_rgba(212,180,133,0.4)]",
							"transition-all duration-200",
						)}
						onClick={handleFilterChange}
					>
						搜索
					</Button>

					{/* 刷新按钮 */}
					<Button
						variant="outline"
						size="icon"
						className={cn(
							"bg-[#1E2023]/50 border-[#D4B485]/20",
							"text-[#D4B485] hover:text-[#E5C9A5]",
							"hover:bg-[#D4B485]/10",
							"transition duration-200",
							"w-10 h-10",
							"flex items-center justify-center",
						)}
						onClick={handleReset}
					>
						<RefreshCcwIcon className="h-4 w-4" />
					</Button>

					{/* 高级筛选按钮 */}
					<Button
						variant="outline"
						size="icon"
						className={cn(
							"bg-[#1E2023]/50 border-[#D4B485]/20",
							"text-[#D4B485] hover:text-[#E5C9A5]",
							"hover:bg-[#D4B485]/10",
							"transition duration-200",
							"w-10 h-10",
							showFilters && "bg-[#D4B485]/10",
						)}
						onClick={() => setShowFilters(!showFilters)}
					>
						<SlidersHorizontalIcon className="h-4 w-4" />
					</Button>

					{/* 添加代理商按钮 */}
					<Button
						size="sm"
						className={cn(
							"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
							"text-[#1A1C1E]",
							"hover:from-[#B08968] hover:to-[#D4B485]",
						)}
						onClick={onAddAgent}
					>
						<PlusCircleIcon className="h-4 w-4 mr-2" />
						添加代理商
					</Button>
				</div>
			</div>

			{showFilters && (
				<AgentFilterBar onChange={handleDetailedFilterChange} />
			)}

			<div className="space-y-4">
				{isLoading ? (
					// 加载状态
					[
						"skeleton-1",
						"skeleton-2",
						"skeleton-3",
						"skeleton-4",
						"skeleton-5",
					].map((id) => (
						<Skeleton
							key={id}
							className="h-[92px] w-full rounded-lg bg-[#1E2023]/60"
						/>
					))
				) : agents.length > 0 ? (
					<>
						{/* 显示代理商列表 */}
						{agents.map((agent, index) => (
							<AgentListItem
								key={agent.id}
								agent={{
									id: agent.id,
									name: agent.name,
									role: agent.role,
									email: agent.email,
									phone: agent.phone,
									status: agent.status,
									teamSize: agent.teamSize,
									performance: agent.performance,
									avatar: agent.avatar,
									hasChildren: agent.hasChildren,
								}}
								index={index}
								onEdit={(a) =>
									onEditAgent?.(
										a as unknown as AgentListItemType,
									)
								}
								onViewDetails={(a) =>
									onViewAgentDetails?.(
										a as unknown as AgentListItemType,
									)
								}
								onViewStructure={(a) =>
									onViewAgentStructure?.(
										a as unknown as AgentListItemType,
									)
								}
								onDelete={(a) =>
									onDeleteAgent?.(
										a as unknown as AgentListItemType,
									)
								}
							/>
						))}

						{/* 分页控件 */}
						<div className="flex items-center justify-between pt-4 border-t border-[#D4B485]/10">
							<div className="text-sm text-[#D4B485]/60">
								显示{" "}
								{Math.min(total, (currentPage - 1) * 5 + 1)}-
								{Math.min(total, currentPage * 5)} 条，共{" "}
								{total} 条
							</div>
							<div className="flex items-center space-x-2">
								<Button
									variant="outline"
									size="sm"
									className={cn(
										"bg-[#1A1C1E]",
										"border-[#D4B485]/20",
										"text-[#D4B485]",
										"hover:bg-[#D4B485]/10",
										"h-8 w-8 p-0",
									)}
									disabled={currentPage === 1}
									onClick={() =>
										handlePageChange(currentPage - 1)
									}
								>
									<ChevronLeftIcon className="h-4 w-4" />
								</Button>

								{getPageNumbers().map((page) => (
									<Button
										key={page}
										variant={
											page === currentPage
												? "primary"
												: "outline"
										}
										size="sm"
										className={cn(
											page === currentPage
												? "bg-[#D4B485] text-[#1A1C1E]"
												: "bg-[#1A1C1E] border-[#D4B485]/20 text-[#D4B485] hover:bg-[#D4B485]/10",
											"h-8 w-8 p-0",
										)}
										onClick={() => handlePageChange(page)}
									>
										{page}
									</Button>
								))}

								<Button
									variant="outline"
									size="sm"
									className={cn(
										"bg-[#1A1C1E]",
										"border-[#D4B485]/20",
										"text-[#D4B485]",
										"hover:bg-[#D4B485]/10",
										"h-8 w-8 p-0",
									)}
									disabled={currentPage === totalPages}
									onClick={() =>
										handlePageChange(currentPage + 1)
									}
								>
									<ChevronRightIcon className="h-4 w-4" />
								</Button>
							</div>
						</div>
					</>
				) : (
					// 没有匹配的结果
					<div className="py-20 flex flex-col items-center justify-center text-center">
						<div className="rounded-full bg-[#D4B485]/10 p-4 mb-4">
							<SlidersHorizontalIcon className="h-8 w-8 text-[#D4B485]/40" />
						</div>
						<h3 className="text-lg font-medium text-[#D4B485] mb-1">
							无匹配结果
						</h3>
						<p className="text-[#D4B485]/60 max-w-sm">
							没有找到符合当前筛选条件的代理商。请尝试调整筛选条件或添加新的代理商。
						</p>
					</div>
				)}
			</div>
		</div>
	);
}
