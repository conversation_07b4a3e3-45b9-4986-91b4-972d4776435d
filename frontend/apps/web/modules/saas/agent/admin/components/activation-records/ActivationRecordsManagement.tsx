"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";
import { Calendar, Download } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { OrderPagination } from "../orders/components";
import {
	ActivationFilters,
	ActivationRecordItem,
	ActivationStats,
} from "./components";
import { type ActivationRecordsQuery, useActivationRecords } from "./hooks";

export function ActivationRecordsManagement() {
	const { loading, records, total, stats, fetchRecords } =
		useActivationRecords();
	const [currentPage, setCurrentPage] = useState(1);
	const [pageSize, setPageSize] = useState(10);
	const [filters, setFilters] = useState<Partial<ActivationRecordsQuery>>({});

	// 获取激活记录列表
	const loadRecords = useCallback(() => {
		const query: ActivationRecordsQuery = {
			page: currentPage,
			pageSize,
			...filters,
		};
		fetchRecords(query);
	}, [currentPage, pageSize, filters, fetchRecords]);

	// 初始加载
	useEffect(() => {
		loadRecords();
	}, [loadRecords]);

	// 处理筛选条件变化
	const handleFiltersChange = useCallback(
		(newFilters: Partial<ActivationRecordsQuery>) => {
			setFilters((prev) => ({ ...prev, ...newFilters }));
			setCurrentPage(1); // 重置到第一页
		},
		[],
	);

	// 处理分页变化
	const handlePageChange = useCallback((page: number) => {
		setCurrentPage(page);
	}, []);

	const handlePageSizeChange = useCallback((size: number) => {
		setPageSize(size);
		setCurrentPage(1);
	}, []);

	// 处理记录点击
	const handleRecordClick = useCallback((_recordId: string) => {
		// TODO: 打开激活记录详情弹窗或跳转到详情页
		// console.log("查看激活记录详情:", recordId);
	}, []);

	return (
		<div className="w-full space-y-6 p-6">
			{/* 标题和操作栏 */}
			<div className="flex items-center justify-between">
				<h1
					className={cn("text-2xl font-bold", zywhFont.className)}
					style={{
						background:
							"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
						WebkitBackgroundClip: "text",
						WebkitTextFillColor: "transparent",
						backgroundClip: "text",
						textShadow:
							"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
					}}
				>
					套餐激活记录
				</h1>
				<div className="flex items-center gap-4">
					<div className="flex items-center gap-2">
						<Calendar className="h-4 w-4 text-[#D4B485]/60" />
						<span className="text-[#D4B485]/60">
							{new Date().getFullYear()}-01-01 至{" "}
							{new Date().getFullYear()}
							-12-31
						</span>
					</div>
					<Button
						variant="outline"
						className={cn(
							"gap-2",
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"hover:bg-[#D4B485]/10",
						)}
					>
						<Download className="h-4 w-4" />
						<span>导出数据</span>
					</Button>
				</div>
			</div>

			{/* 统计信息 */}
			<ActivationStats stats={stats} loading={loading} />

			{/* 搜索和筛选 */}
			<ActivationFilters
				onFiltersChange={handleFiltersChange}
				loading={loading}
			/>

			{/* 激活记录列表 */}
			<div className="space-y-4">
				{loading ? (
					// 骨架屏
					<div className="space-y-4">
						{Array.from({ length: pageSize }).map((_, index) => (
							<div
								key={index}
								className={cn(
									"rounded-lg p-6",
									"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
									"border border-[#D4B485]/20",
									"animate-pulse",
								)}
							>
								<div className="grid grid-cols-1 gap-4 lg:grid-cols-4">
									<div className="space-y-3">
										<div className="flex items-center gap-3">
											<div className="h-10 w-10 rounded-lg bg-[#D4B485]/20" />
											<div className="space-y-2">
												<div className="h-4 w-24 bg-[#D4B485]/20 rounded" />
												<div className="h-3 w-16 bg-[#D4B485]/20 rounded" />
											</div>
										</div>
									</div>
									<div className="space-y-2">
										<div className="h-4 w-20 bg-[#D4B485]/20 rounded" />
										<div className="h-3 w-32 bg-[#D4B485]/20 rounded" />
									</div>
									<div className="space-y-2">
										<div className="h-4 w-20 bg-[#D4B485]/20 rounded" />
										<div className="h-3 w-28 bg-[#D4B485]/20 rounded" />
									</div>
									<div className="space-y-2">
										<div className="h-4 w-20 bg-[#D4B485]/20 rounded" />
										<div className="h-3 w-36 bg-[#D4B485]/20 rounded" />
									</div>
								</div>
							</div>
						))}
					</div>
				) : records.length > 0 ? (
					records.map((record, index) => (
						<ActivationRecordItem
							key={record.id}
							record={record}
							index={index}
							onRecordClick={handleRecordClick}
						/>
					))
				) : (
					// 空状态
					<div className="flex flex-col items-center justify-center py-12">
						<div className="text-center">
							<div className="mx-auto h-24 w-24 rounded-full bg-[#D4B485]/10 flex items-center justify-center mb-4">
								<Calendar className="h-12 w-12 text-[#D4B485]/40" />
							</div>
							<h3 className="text-lg font-medium text-[#D4B485] mb-2">
								暂无激活记录
							</h3>
							<p className="text-[#D4B485]/60">
								当前筛选条件下没有找到激活记录
							</p>
						</div>
					</div>
				)}
			</div>

			{/* 分页 */}
			{!loading && records.length > 0 && (
				<OrderPagination
					currentPage={currentPage}
					pageSize={pageSize}
					total={total}
					onPageChange={handlePageChange}
					onPageSizeChange={handlePageSizeChange}
					loading={loading}
				/>
			)}
		</div>
	);
}
