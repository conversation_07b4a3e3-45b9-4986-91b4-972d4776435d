"use client";

import { logger } from "@repo/logs";
import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";

// 消费者列表项
export interface ConsumerListItem {
	id: string;
	name: string;
	role: string;
	email: string;
	phone: string;
	status: string;
	computingPower: number;
	totalComputingPower: number;
	avatar: string | null;
	agentName: string | null;
	planName: string | null;
	planExpireAt: string | null;
	createdAt: string;
}

// API响应类型
interface ApiResponse<T> {
	code?: number;
	message?: string;
	data?: T;
	error?: string;
	items?: ConsumerListItem[];
	total?: number;
	page?: number;
	pageSize?: number;
	totalPages?: number;
}

// 查询参数
export interface ConsumerListQuery {
	page: number;
	pageSize: number;
	searchTerm?: string;
	role?: string;
	status?: string;
	sortBy?: "createdAt" | "name" | "computingPower" | "role";
	sortOrder?: "asc" | "desc";
}

export function useConsumerList() {
	const [loading, setLoading] = useState(false);
	const [consumers, setConsumers] = useState<ConsumerListItem[]>([]);
	const [total, setTotal] = useState(0);
	const [currentPage, setCurrentPage] = useState(1);
	const [totalPages, setTotalPages] = useState(1);

	const fetchConsumers = useCallback(async (query: ConsumerListQuery) => {
		setLoading(true);
		try {
			const response =
				await apiClient.v1.agent.admin.user.consumers.list.$get({
					query: {
						page: query.page,
						pageSize: query.pageSize,
						searchTerm: query.searchTerm,
						role: query.role,
						status: query.status,
						sortBy: query.sortBy,
						sortOrder: query.sortOrder,
					},
				});

			const result = (await response.json()) as ApiResponse<unknown>;

			if (response.ok && result.items) {
				setConsumers(result.items);
				setTotal(result.total || 0);
				setCurrentPage(result.page || 1);
				setTotalPages(result.totalPages || 1);
			} else {
				toast.error("获取消费者列表失败");
				logger.error("[Admin] 获取消费者列表失败", {
					error: result.message || result.error,
				});
			}
		} catch (error) {
			toast.error("获取消费者列表失败");
			logger.error("[Admin] 获取消费者列表失败", {
				error: error instanceof Error ? error.message : "未知错误",
			});
		} finally {
			setLoading(false);
		}
	}, []);

	return {
		loading,
		consumers,
		total,
		currentPage,
		totalPages,
		fetchConsumers,
	};
}
