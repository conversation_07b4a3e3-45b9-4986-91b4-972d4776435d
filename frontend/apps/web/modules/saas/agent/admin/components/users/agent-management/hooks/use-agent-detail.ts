import { logger } from "@repo/logs";
import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";
import { isAgentResponse, isErrorResponse } from "../types";
import type { AgentListItem } from "./use-agent-list";

// 代理商详情
export interface AgentDetail extends AgentListItem {
	userId: string;
	parentId: string | null;
	parent: AgentListItem | null;
	path: string | null;
	joinFee: number | null;
	hourlyRevenue: number | null;
	teamQuota: number;
	usedTeamQuota: number;
	totalSales: number;
	monthSales: number;
	updatedAt: string;
}

export function useAgentDetail() {
	const [loading, setLoading] = useState(false);
	const [agent, setAgent] = useState<AgentDetail | null>(null);

	const fetchAgentDetail = useCallback(async (id: string) => {
		setLoading(true);
		try {
			const response = await apiClient.v1.agent.admin.user.agent.detail[
				":id"
			].$get({
				param: { id },
			});

			// 无论HTTP状态码是什么，都尝试解析响应体
			const responseData = await response.json();

			// 检查是否是成功响应
			if (isAgentResponse(responseData)) {
				setAgent(responseData.agent);
				return responseData.agent;
			}

			// 处理错误响应
			if (isErrorResponse(responseData)) {
				const errorMessage = responseData.message;
				toast.error(errorMessage);

				logger.error("[Admin] 获取代理商详情失败", {
					error: errorMessage,
					errorCode: responseData.error,
					id,
				});

				return null;
			}

			// 处理未预期的响应格式
			toast.error("获取代理商详情失败");
			logger.error("[Admin] 获取代理商详情失败（未知响应格式）", {
				responseData,
				id,
			});

			return null;
		} catch (error) {
			toast.error("获取代理商详情失败");
			logger.error("[Admin] 获取代理商详情失败", {
				error: error instanceof Error ? error.message : "未知错误",
				id,
			});
			return null;
		} finally {
			setLoading(false);
		}
	}, []);

	return {
		loading,
		agent,
		fetchAgentDetail,
	};
}
