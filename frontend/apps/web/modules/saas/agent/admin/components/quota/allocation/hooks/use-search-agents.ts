import { logger } from "@repo/logs";
import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";

interface Agent {
	id: string;
	name: string;
	role: string;
	email: string;
	phone: string;
	teamQuota: number;
	usedTeamQuota: number;
	remainingQuota: number;
	teamSize: number;
	totalSales: number;
	monthSales: number;
	createdAt: string;
}

interface ApiSuccessResponse<T> {
	code: number;
	message?: string;
	data: T;
}

interface ApiErrorResponse {
	code: number;
	error: string;
	message: string;
}

type ApiResponse<T> = ApiSuccessResponse<T> | ApiErrorResponse;

interface SearchAgentsResponseData {
	total: number;
	items: Agent[];
}

export function useSearchAgents() {
	const [agents, setAgents] = useState<Agent[]>([]);
	const [total, setTotal] = useState(0);
	const [loading, setLoading] = useState(false);
	const [currentPage, setCurrentPage] = useState(1);
	const [pageSize, setPageSize] = useState(10);

	const searchAgentsByPhone = useCallback(
		async (params: { phone: string; page?: number; pageSize?: number }) => {
			// 更新当前页码和页面大小
			if (params.page !== undefined) {
				setCurrentPage(params.page);
			}
			if (params.pageSize !== undefined) {
				setPageSize(params.pageSize);
			}
			try {
				setLoading(true);
				logger.info("开始根据手机号搜索代理商", { params });

				const res = await apiClient.v1.agent.admin.quota[
					"search-agents"
				].$get({
					query: params,
				});
				const result =
					(await res.json()) as ApiResponse<SearchAgentsResponseData>;

				logger.info("手机号搜索代理商响应:", result);

				if ("error" in result || !result.data) {
					throw new Error(result.message || "搜索代理商失败");
				}

				setAgents(result.data.items);
				setTotal(result.data.total);

				return {
					items: result.data.items,
					total: result.data.total,
				};
			} catch (error) {
				logger.error("根据手机号搜索代理商失败", { error });
				toast.error("搜索代理商失败", {
					description:
						error instanceof Error ? error.message : "请重试",
				});
				return {
					items: [],
					total: 0,
				};
			} finally {
				setLoading(false);
			}
		},
		[],
	);

	const clearResults = useCallback(() => {
		setAgents([]);
		setTotal(0);
		setCurrentPage(1); // 重置页码
	}, []);

	return {
		agents,
		total,
		loading,
		currentPage,
		pageSize,
		searchAgentsByPhone,
		clearResults,
		setCurrentPage,
		setPageSize,
	};
}
