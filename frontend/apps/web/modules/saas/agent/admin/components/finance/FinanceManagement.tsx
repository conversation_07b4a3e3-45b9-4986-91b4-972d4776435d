"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Button } from "@ui/components/button";
import { Card } from "@ui/components/card";
import { Input } from "@ui/components/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import {
	Calendar,
	CreditCard,
	Download,
	Filter,
	Plus,
	Search,
	Wallet,
} from "lucide-react";

// 类型定义
type SettlementType = "commission" | "refund";
type SettlementStatus = "PENDING" | "PROCESSING" | "COMPLETED" | "FAILED";

const SETTLEMENT_STATUS_LABELS: Record<SettlementStatus, string> = {
	PENDING: "待处理",
	PROCESSING: "处理中",
	COMPLETED: "已完成",
	FAILED: "已失败",
};

const SETTLEMENT_TYPE_LABELS: Record<SettlementType, string> = {
	commission: "佣金结算",
	refund: "退款",
};

// Mock数据
const mockSettlements = [
	{
		id: "ST24020001",
		agent: {
			name: "张三",
			role: "DIRECTOR",
			avatar: null,
		},
		amount: 1299900,
		status: "PENDING" as SettlementStatus,
		type: "commission" as SettlementType,
		bankInfo: {
			bank: "招商银行",
			account: "6225 **** **** 1234",
			name: "张三",
		},
		createdAt: "2024-02-24 10:30:00",
	},
	{
		id: "ST24020002",
		agent: {
			name: "李四",
			role: "PARTNER",
			avatar: null,
		},
		amount: 899900,
		status: "COMPLETED" as SettlementStatus,
		type: "commission" as SettlementType,
		bankInfo: {
			bank: "工商银行",
			account: "6222 **** **** 5678",
			name: "李四",
		},
		createdAt: "2024-02-24 11:15:00",
		completedAt: "2024-02-24 11:16:45",
	},
];

export function FinanceManagement() {
	return (
		<div className="w-full space-y-6 p-6">
			{/* 标题和操作栏 */}
			<div className="flex items-center justify-between">
				<h1
					className={cn("text-2xl font-bold", zywhFont.className)}
					style={{
						background:
							"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
						WebkitBackgroundClip: "text",
						WebkitTextFillColor: "transparent",
						backgroundClip: "text",
						textShadow:
							"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
					}}
				>
					财务管理
				</h1>
				<div className="flex items-center gap-4">
					<div className="flex items-center gap-2">
						<Calendar className="h-4 w-4 text-[#D4B485]/60" />
						<span className="text-[#D4B485]/60">
							2024-01-01 至 2024-12-31
						</span>
					</div>
					<Button
						variant="outline"
						className={cn(
							"gap-2",
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"hover:bg-[#D4B485]/10",
						)}
					>
						<Download className="h-4 w-4" />
						<span>导出数据</span>
					</Button>
				</div>
			</div>

			{/* 搜索和筛选 */}
			<Card
				className={cn(
					"p-4",
					"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
					"border border-[#D4B485]/20",
					"group",
					"hover:border-[#D4B485]/40",
					"transition-all duration-500",
				)}
			>
				<div className="flex items-center gap-4">
					<div className="relative flex-1">
						<Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-[#D4B485]/40" />
						<Input
							placeholder="搜索结算记录..."
							className={cn(
								"pl-10",
								"bg-[#1A1C1E]",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
								"placeholder:text-[#D4B485]/40",
							)}
						/>
					</div>
					<Select defaultValue="all">
						<SelectTrigger
							className={cn(
								"w-[160px]",
								"bg-[#1A1C1E]",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
							)}
						>
							<SelectValue placeholder="结算类型" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="all">全部类型</SelectItem>
							<SelectItem value="commission">佣金结算</SelectItem>
							<SelectItem value="refund">退款</SelectItem>
						</SelectContent>
					</Select>
					<Select defaultValue="all">
						<SelectTrigger
							className={cn(
								"w-[160px]",
								"bg-[#1A1C1E]",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
							)}
						>
							<SelectValue placeholder="结算状态" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="all">全部状态</SelectItem>
							<SelectItem value="PENDING">待处理</SelectItem>
							<SelectItem value="PROCESSING">处理中</SelectItem>
							<SelectItem value="COMPLETED">已完成</SelectItem>
							<SelectItem value="FAILED">已失败</SelectItem>
						</SelectContent>
					</Select>
					<Button
						variant="outline"
						className={cn(
							"gap-2",
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"hover:bg-[#D4B485]/10",
						)}
					>
						<Filter className="h-4 w-4" />
						<span>筛选</span>
					</Button>
					<Button
						variant="outline"
						className={cn(
							"gap-2",
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"hover:bg-[#D4B485]/10",
						)}
					>
						<Plus className="h-4 w-4" />
						<span>高级筛选</span>
					</Button>
				</div>
			</Card>

			{/* 结算列表 */}
			<div className="grid gap-4">
				{mockSettlements.map((settlement, index) => (
					<motion.div
						key={settlement.id}
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ delay: index * 0.1 }}
						className={cn(
							"rounded-lg p-6",
							"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
							"border border-[#D4B485]/20",
							"group",
							"hover:border-[#D4B485]/40",
							"transition-all duration-200",
							"relative overflow-hidden",
						)}
					>
						{/* 背景装饰 */}
						<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
						<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />

						<div className="relative flex items-center gap-4">
							<div
								className={cn(
									"flex h-12 w-12 shrink-0 items-center justify-center rounded-lg",
									"bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
									"ring-1 ring-[#D4B485]/20",
								)}
							>
								{settlement.type === "commission" ? (
									<Wallet className="h-6 w-6 text-[#D4B485]" />
								) : (
									<CreditCard className="h-6 w-6 text-[#D4B485]" />
								)}
							</div>

							<div className="flex-1 min-w-0">
								<div className="flex items-center gap-3">
									<span className="font-medium text-[#D4B485]">
										{settlement.id}
									</span>
									<span
										className={cn(
											"rounded-full px-2 py-0.5 text-xs",
											"bg-[#D4B485]/10",
											"text-[#D4B485]/60",
										)}
									>
										{
											SETTLEMENT_TYPE_LABELS[
												settlement.type
											]
										}
									</span>
									<span
										className={cn(
											"rounded-full px-2 py-0.5 text-xs",
											settlement.status === "COMPLETED"
												? "bg-emerald-500/10 text-emerald-500"
												: settlement.status ===
														"PENDING"
													? "bg-amber-500/10 text-amber-500"
													: "bg-rose-500/10 text-rose-500",
										)}
									>
										{
											SETTLEMENT_STATUS_LABELS[
												settlement.status
											]
										}
									</span>
								</div>
								<div className="mt-2 flex items-center gap-6 text-sm text-[#D4B485]/40">
									<div className="flex items-center gap-2">
										<Avatar
											className={cn(
												"h-6 w-6",
												"border border-[#D4B485]/20",
											)}
										>
											<AvatarImage
												src={
													settlement.agent.avatar ||
													undefined
												}
												alt={settlement.agent.name}
											/>
											<AvatarFallback
												className={cn(
													"text-[#1E2023] text-xs font-semibold",
													"bg-gradient-to-br from-[#D4B485] to-[#B08968]",
												)}
											>
												{settlement.agent.name.slice(
													0,
													1,
												)}
											</AvatarFallback>
										</Avatar>
										<span>{settlement.agent.name}</span>
										<span
											className={cn(
												"rounded-full px-1.5 py-0.5 text-[10px]",
												"bg-[#D4B485]/10",
												"text-[#D4B485]/60",
											)}
										>
											{settlement.agent.role}
										</span>
									</div>
									<div>
										结算金额：
										<span className="font-medium text-[#D4B485]">
											¥
											{(settlement.amount / 100).toFixed(
												2,
											)}
										</span>
									</div>
									<div>
										收款账户：
										<span className="font-medium text-[#D4B485]">
											{settlement.bankInfo.bank} ·{" "}
											{settlement.bankInfo.account}
										</span>
									</div>
									<div>
										申请时间：
										<span className="font-medium text-[#D4B485]">
											{settlement.createdAt}
										</span>
									</div>
									{settlement.completedAt && (
										<div>
											完成时间：
											<span className="font-medium text-[#D4B485]">
												{settlement.completedAt}
											</span>
										</div>
									)}
								</div>
							</div>

							<div className="flex gap-2">
								<Button
									variant="outline"
									className={cn(
										"h-8 px-3",
										"bg-[#1A1C1E]",
										"border-[#D4B485]/20",
										"text-[#D4B485]",
										"hover:bg-[#D4B485]/10",
									)}
								>
									查看详情
								</Button>
								{settlement.status === "PENDING" && (
									<Button
										className={cn(
											"h-8 px-3",
											"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
											"text-white hover:text-white/90",
											"border-none",
											"shadow-[0_8px_16px_-4px_rgba(212,180,133,0.3)]",
											"transition-all duration-300",
											"hover:shadow-[0_12px_20px_-4px_rgba(212,180,133,0.4)]",
											"hover:scale-105",
										)}
									>
										处理
									</Button>
								)}
							</div>
						</div>
					</motion.div>
				))}
			</div>
		</div>
	);
}
