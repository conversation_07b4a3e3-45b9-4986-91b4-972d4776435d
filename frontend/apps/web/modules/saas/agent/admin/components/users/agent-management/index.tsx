"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@ui/components/alert-dialog";
import { Button } from "@ui/components/button";
import { Card, CardContent } from "@ui/components/card";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { useCallback, useEffect, useRef, useState } from "react";
import { AgentForm } from "./components/AgentForm";
import { AgentList } from "./components/AgentList";
import { HierarchyView } from "./components/HierarchyView";
import type { TeamMembersFilterParams } from "./components/TeamMembersList";
import type { CreateAgentParams } from "./hooks/use-agent-create";
import { useAgentCreate } from "./hooks/use-agent-create";
import { useAgentDelete } from "./hooks/use-agent-delete";
import { useAgentDetail } from "./hooks/use-agent-detail";
import type { AgentNode } from "./hooks/use-agent-hierarchy";
import { useAgentHierarchy } from "./hooks/use-agent-hierarchy";
import type { AgentListItem, AgentListQuery } from "./hooks/use-agent-list";
import { useAgentList } from "./hooks/use-agent-list";
import type { UpdateAgentParams } from "./hooks/use-agent-update";
import { useAgentUpdate } from "./hooks/use-agent-update";
import { useTeamMembers } from "./hooks/use-team-members";

export interface AgentManagementProps {
	className?: string;
}

export function AgentManagement({ className }: AgentManagementProps) {
	const isFirstRender = useRef(true);

	// 查询参数
	const [query, setQuery] = useState({
		page: 1,
		pageSize: 5,
	});

	// API hooks
	const {
		loading: listLoading,
		agents,
		total,
		currentPage,
		totalPages,
		fetchAgents,
	} = useAgentList();
	const {
		loading: detailLoading,
		agent: selectedAgentDetail,
		fetchAgentDetail,
	} = useAgentDetail();
	const {
		loading: hierarchyLoading,
		hierarchy,
		fetchHierarchy,
	} = useAgentHierarchy();
	const {
		loading: teamMembersLoading,
		members: teamMembers,
		fetchTeamMembers,
		filterTeamMembers,
		total: teamMembersTotal,
		currentPage: teamMembersPage,
		totalPages: teamMembersTotalPages,
	} = useTeamMembers();
	const { loading: createLoading, createAgent } = useAgentCreate();
	const { loading: updateLoading, updateAgent } = useAgentUpdate();
	const { loading: deleteLoading, deleteAgent } = useAgentDelete();

	// 选中的代理商
	const [selectedAgent, setSelectedAgent] = useState<AgentListItem | null>(
		null,
	);

	// 显示详情对话框
	const [showDetailsDialog, setShowDetailsDialog] = useState(false);

	// 显示层级结构视图
	const [showHierarchyView, setShowHierarchyView] = useState(false);

	// 显示创建/编辑对话框
	const [showFormDialog, setShowFormDialog] = useState(false);

	// 显示删除确认对话框
	const [showDeleteDialog, setShowDeleteDialog] = useState(false);

	// 编辑模式
	const [isEditMode, setIsEditMode] = useState(false);

	// 当前层级结构查看的根节点ID
	const [_currentRootId, setCurrentRootId] = useState<string | undefined>(
		undefined,
	);

	// 初始化加载
	useEffect(() => {
		if (isFirstRender.current) {
			isFirstRender.current = false;
			fetchAgents(query);
		}
	}, [fetchAgents, query]);

	// 处理筛选
	const handleFilter = useCallback(
		(newQuery: AgentListQuery) => {
			const updatedQuery = {
				...newQuery,
				page: 1, // 重置页码
			};
			setQuery(updatedQuery);
			fetchAgents(updatedQuery);
		},
		[fetchAgents],
	);

	// 处理分页
	const handlePageChange = useCallback(
		(page: number) => {
			const newQuery = { ...query, page };
			setQuery(newQuery);
			fetchAgents(newQuery);
		},
		[query, fetchAgents],
	);

	// 处理代理商详情查看
	const handleViewAgentDetails = useCallback(
		(agent: AgentListItem) => {
			setSelectedAgent(agent);
			fetchAgentDetail(agent.id);
			setShowDetailsDialog(true);
		},
		[fetchAgentDetail],
	);

	// 处理代理商结构查看
	const handleViewAgentStructure = useCallback(
		(agent: AgentListItem) => {
			setSelectedAgent(agent);
			setCurrentRootId(agent.id);
			fetchHierarchy(agent.id);
			// 同时加载该代理商下的直接成员
			fetchTeamMembers(agent.id);
			setShowHierarchyView(true);
		},
		[fetchHierarchy, fetchTeamMembers],
	);

	// 处理返回列表视图
	const handleBackToListView = useCallback(() => {
		setShowHierarchyView(false);
		setCurrentRootId(undefined);
	}, []);

	// 处理层级树节点点击
	const handleTreeNodeClick = useCallback(
		(node: AgentNode) => {
			// 更新当前选中的节点
			fetchTeamMembers(node.id);

			// 如果我们想在节点点击时不切换到详情，而只是更新团队成员列表，则注释掉以下代码
			/* 
    // 查找对应的代理商数据
    const agentData = agents.find(a => a.id === node.id);
    if (agentData) {
      setSelectedAgent(agentData);
      fetchAgentDetail(agentData.id);
      setShowHierarchyView(false);
      setShowDetailsDialog(true);
    }
    */
		},
		[fetchTeamMembers],
	);

	// 处理添加代理商
	const handleAddAgent = useCallback(() => {
		setIsEditMode(false);
		setSelectedAgent(null);
		setShowFormDialog(true);
	}, []);

	// 处理编辑代理商
	const handleEditAgent = useCallback(
		(agent: AgentListItem) => {
			setIsEditMode(true);
			setSelectedAgent(agent);
			fetchAgentDetail(agent.id);
			setShowFormDialog(true);
		},
		[fetchAgentDetail],
	);

	// 处理删除代理商
	const handleDeleteAgent = useCallback((agent: AgentListItem) => {
		setSelectedAgent(agent);
		setShowDeleteDialog(true);
	}, []);

	// 处理表单提交
	const handleFormSubmit = useCallback(
		async (data: CreateAgentParams | UpdateAgentParams) => {
			if (isEditMode && "id" in data) {
				// 更新代理商
				const result = await updateAgent(data as UpdateAgentParams);
				if (result.success) {
					setShowFormDialog(false);
					fetchAgents(query); // 刷新列表
				}
			} else {
				// 创建代理商
				const result = await createAgent(data as CreateAgentParams);
				if (result.success) {
					setShowFormDialog(false);
					fetchAgents(query); // 刷新列表
				}
			}
		},
		[isEditMode, createAgent, updateAgent, fetchAgents, query],
	);

	// 处理删除确认
	const handleDeleteConfirm = useCallback(async () => {
		if (selectedAgent) {
			const result = await deleteAgent(selectedAgent.id);
			if (result.success) {
				setShowDeleteDialog(false);
				fetchAgents(query); // 刷新列表
			}
		}
	}, [selectedAgent, deleteAgent, fetchAgents, query]);

	// 处理团队成员筛选
	const handleFilterTeamMembers = useCallback(
		(params: TeamMembersFilterParams) => {
			filterTeamMembers({
				...params,
				includeDescendants: true,
			});
		},
		[filterTeamMembers],
	);

	// 处理团队成员分页
	const handleTeamMembersPageChange = useCallback(
		(page: number) => {
			filterTeamMembers({
				page,
				pageSize: 5,
				includeDescendants: true,
			});
		},
		[filterTeamMembers],
	);

	// 渲染层级结构视图
	if (showHierarchyView) {
		return (
			<HierarchyView
				className={className}
				hierarchy={hierarchy}
				teamMembers={teamMembers}
				teamMembersLoading={teamMembersLoading}
				selectedAgent={selectedAgent}
				hierarchyLoading={hierarchyLoading}
				onBackToList={handleBackToListView}
				onNodeClick={handleTreeNodeClick}
				onViewMemberDetails={handleViewAgentDetails}
				onEditMember={handleEditAgent}
				onDeleteMember={handleDeleteAgent}
				onAddMember={handleAddAgent}
				onFilterTeamMembers={handleFilterTeamMembers}
				onTeamMembersPageChange={handleTeamMembersPageChange}
				teamMembersTotal={teamMembersTotal}
				teamMembersCurrentPage={teamMembersPage}
				teamMembersTotalPages={teamMembersTotalPages}
			/>
		);
	}

	return (
		<div className={cn("space-y-8", className)}>
			{/* 标题区域 */}
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.1 }}
				className="relative mt-6 mb-8 space-y-1"
			>
				<div className="flex items-center gap-4">
					<div
						className={cn(
							"flex h-14 w-14 shrink-0 items-center justify-center",
							"rounded-xl bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
							"ring-1 ring-[#D4B485]/20",
							"shadow-[0_0_0_8px_rgba(212,180,133,0.03)]",
							"transform-gpu transition-transform duration-300",
							"hover:scale-105 hover:shadow-[0_0_0_10px_rgba(212,180,133,0.05)]",
						)}
					>
						<span className="h-7 w-7 text-[#D4B485]">👨‍💼</span>
					</div>
					<div className="space-y-1">
						<h2
							className={cn(
								"font-semibold text-3xl",
								zywhFont.className,
								"leading-none",
								"tracking-[0.05em]",
								"relative",
								"after:absolute after:inset-0",
								"after:bg-[radial-gradient(circle_at_50%_50%,rgba(212,180,133,0.15),transparent_70%)]",
								"after:blur-xl after:-z-10",
							)}
							style={{
								background:
									"linear-gradient(135deg, rgba(229,201,165,0.95) 0%, rgba(212,180,133,0.9) 50%, rgba(176,137,104,0.85) 100%)",
								WebkitBackgroundClip: "text",
								WebkitTextFillColor: "transparent",
								backgroundClip: "text",
								textShadow:
									"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.1)",
								filter: "contrast(1.1) brightness(1.05)",
							}}
						>
							代理商管理
							<span className="ml-3 font-normal text-base text-[#D4B485]/80 tracking-wide">
								高效管理您的代理商团队
							</span>
						</h2>
						<p className="text-sm leading-relaxed text-white/40 group-hover:text-white/60 transition-colors duration-300">
							创建、查看和管理代理商账号，按角色设置权限，查看团队层级结构
						</p>
					</div>
				</div>
			</motion.div>

			<AgentList
				isLoading={listLoading}
				agents={agents}
				total={total}
				currentPage={currentPage}
				totalPages={totalPages}
				onPageChange={handlePageChange}
				onFilter={handleFilter}
				onAddAgent={handleAddAgent}
				onEditAgent={handleEditAgent}
				onViewAgentDetails={handleViewAgentDetails}
				onViewAgentStructure={handleViewAgentStructure}
				onDeleteAgent={handleDeleteAgent}
			/>

			{/* 代理商详情对话框 */}
			<Dialog
				open={showDetailsDialog}
				onOpenChange={setShowDetailsDialog}
			>
				<DialogContent className="bg-[#1E2023] border-[#D4B485]/20 text-white">
					<DialogHeader>
						<DialogTitle className="text-[#D4B485]">
							代理商详情
						</DialogTitle>
						<DialogDescription className="text-[#D4B485]/60">
							查看代理商详细信息和相关统计数据
						</DialogDescription>
					</DialogHeader>

					{detailLoading ? (
						<div className="py-8 flex items-center justify-center">
							<div className="text-[#D4B485]/60">加载中...</div>
						</div>
					) : selectedAgentDetail ? (
						<div className="space-y-4">
							<div className="flex items-center gap-4">
								<div className="h-16 w-16 rounded-full bg-gradient-to-br from-[#D4B485] to-[#B08968] flex items-center justify-center text-[#1E2023] text-xl font-bold">
									{selectedAgentDetail.name.slice(0, 1)}
								</div>
								<div>
									<h3 className="text-lg font-medium text-[#D4B485]">
										{selectedAgentDetail.name}
									</h3>
									<p className="text-sm text-[#D4B485]/60">
										{selectedAgentDetail.email} ·{" "}
										{selectedAgentDetail.phone}
									</p>
								</div>
							</div>

							<div className="grid grid-cols-2 gap-4">
								<Card className="bg-[#1A1C1E] border-[#D4B485]/20">
									<CardContent className="p-4">
										<div className="text-sm text-[#D4B485]/60">
											角色
										</div>
										<div className="text-lg text-[#D4B485]">
											{selectedAgentDetail.role}
										</div>
									</CardContent>
								</Card>
								<Card className="bg-[#1A1C1E] border-[#D4B485]/20">
									<CardContent className="p-4">
										<div className="text-sm text-[#D4B485]/60">
											状态
										</div>
										<div className="text-lg text-[#D4B485]">
											{selectedAgentDetail.status}
										</div>
									</CardContent>
								</Card>
								<Card className="bg-[#1A1C1E] border-[#D4B485]/20">
									<CardContent className="p-4">
										<div className="text-sm text-[#D4B485]/60">
											团队规模
										</div>
										<div className="text-lg text-[#D4B485]">
											{selectedAgentDetail.teamSize}人
										</div>
									</CardContent>
								</Card>
								<Card className="bg-[#1A1C1E] border-[#D4B485]/20">
									<CardContent className="p-4">
										<div className="text-sm text-[#D4B485]/60">
											本月业绩
										</div>
										<div className="text-lg text-[#D4B485]">
											¥
											{(
												selectedAgentDetail.performance ||
												0
											).toLocaleString()}
										</div>
									</CardContent>
								</Card>
							</div>

							<div className="pt-2">
								<h4 className="text-md font-medium text-[#D4B485] mb-3">
									相关操作
								</h4>
								<div className="flex gap-3">
									<Button
										variant="outline"
										className="bg-[#1A1C1E] border-[#D4B485]/20 text-[#D4B485] hover:bg-[#D4B485]/10"
										onClick={() => {
											setShowDetailsDialog(false);
											handleEditAgent(
												selectedAgentDetail,
											);
										}}
									>
										编辑信息
									</Button>
									{selectedAgentDetail.hasChildren && (
										<Button
											variant="outline"
											className="bg-[#1A1C1E] border-[#D4B485]/20 text-[#D4B485] hover:bg-[#D4B485]/10"
											onClick={() => {
												setShowDetailsDialog(false);
												handleViewAgentStructure(
													selectedAgentDetail,
												);
											}}
										>
											查看结构
										</Button>
									)}
									<Button
										variant="outline"
										className="bg-[#1A1C1E] border-[#D4B485]/20 text-red-500 hover:bg-red-500/10"
										onClick={() => {
											setShowDetailsDialog(false);
											handleDeleteAgent(
												selectedAgentDetail,
											);
										}}
									>
										删除
									</Button>
								</div>
							</div>
						</div>
					) : (
						<div className="py-8 flex items-center justify-center">
							<div className="text-[#D4B485]/60">暂无数据</div>
						</div>
					)}

					<DialogFooter>
						<Button
							variant="outline"
							className="bg-[#1A1C1E] border-[#D4B485]/20 text-[#D4B485] hover:bg-[#D4B485]/10"
							onClick={() => setShowDetailsDialog(false)}
						>
							关闭
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* 创建/编辑代理商对话框 */}
			<Dialog open={showFormDialog} onOpenChange={setShowFormDialog}>
				<DialogContent className="bg-[#1E2023] border-[#D4B485]/20 text-white">
					<DialogHeader>
						<DialogTitle className="text-[#D4B485]">
							{isEditMode ? "编辑代理商" : "创建代理商"}
						</DialogTitle>
						<DialogDescription className="text-[#D4B485]/60">
							{isEditMode
								? "修改代理商信息"
								: "填写代理商信息以创建新的代理商"}
						</DialogDescription>
					</DialogHeader>

					{isEditMode && detailLoading ? (
						<div className="py-8 flex items-center justify-center">
							<div className="text-[#D4B485]/60">加载中...</div>
						</div>
					) : (
						<AgentForm
							agent={isEditMode ? selectedAgentDetail : null}
							isLoading={
								isEditMode ? updateLoading : createLoading
							}
							onSubmit={handleFormSubmit}
							onCancel={() => setShowFormDialog(false)}
						/>
					)}
				</DialogContent>
			</Dialog>

			{/* 删除确认对话框 */}
			<AlertDialog
				open={showDeleteDialog}
				onOpenChange={setShowDeleteDialog}
			>
				<AlertDialogContent className="bg-[#1E2023] border-[#D4B485]/20 text-white">
					<AlertDialogHeader>
						<AlertDialogTitle className="text-[#D4B485]">
							确认删除
						</AlertDialogTitle>
						<AlertDialogDescription className="text-[#D4B485]/60">
							您确定要删除代理商 "{selectedAgent?.name}"
							吗？此操作不可撤销。
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel className="bg-[#1A1C1E] border-[#D4B485]/20 text-[#D4B485] hover:bg-[#D4B485]/10">
							取消
						</AlertDialogCancel>
						<AlertDialogAction
							className="bg-red-500 text-white hover:bg-red-600"
							onClick={handleDeleteConfirm}
							disabled={deleteLoading}
						>
							{deleteLoading ? "删除中..." : "确认删除"}
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</div>
	);
}
