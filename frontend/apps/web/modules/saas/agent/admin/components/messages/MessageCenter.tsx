"use client";

import {
	MessageStatus,
	MessageType,
} from "@repo/api/src/routes/v1/agent/messages/types";
import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Button } from "@ui/components/button";
import { Card } from "@ui/components/card";
import { Checkbox } from "@ui/components/checkbox";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { Input } from "@ui/components/input";
import { Skeleton } from "@ui/components/skeleton";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import {
	ArchiveIcon,
	Calendar,
	CheckCircle,
	CheckIcon,
	Download,
	Filter,
	MessageCircle,
	RefreshCwIcon,
	Search,
	Settings,
	Users,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
// 直接从各自的文件导入
import { useMessageBatchStatus } from "./hooks/useMessageBatchStatus";
import { useMessageStatus } from "./hooks/useMessageStatus";
import { useMessages } from "./hooks/useMessages";

// 定义消息类型接口
interface Message {
	id: string;
	userId: string;
	title: string;
	content: string;
	type: MessageType;
	routeLink?: string;
	status: MessageStatus;
	readAt: string | null;
	createdAt: string;
	updatedAt: string;
}

// 消息类型图标组件
const MessageTypeIcon = ({ type }: { type: string }) => {
	const iconClassName = "h-5 w-5";
	const containerClassName = cn(
		"flex h-12 w-12 shrink-0 items-center justify-center rounded-lg",
		"bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
		"ring-1 ring-[#D4B485]/20",
	);

	switch (type) {
		case MessageType.AGENT:
			return (
				<div className={containerClassName}>
					<Users className={cn(iconClassName, "text-[#D4B485]")} />
				</div>
			);
		case MessageType.SYSTEM:
			return (
				<div className={containerClassName}>
					<Settings className={cn(iconClassName, "text-[#D4B485]")} />
				</div>
			);
		case MessageType.QUOTA:
			return (
				<div className={containerClassName}>
					<CheckCircle
						className={cn(iconClassName, "text-[#D4B485]")}
					/>
				</div>
			);
		case MessageType.PERFORMANCE:
			return (
				<div className={containerClassName}>
					<Calendar className={cn(iconClassName, "text-[#D4B485]")} />
				</div>
			);
		default:
			return (
				<div className={containerClassName}>
					<MessageCircle
						className={cn(iconClassName, "text-[#D4B485]")}
					/>
				</div>
			);
	}
};

// 消息列表项组件
const MessageItem = ({
	message,
	isSelected,
	onSelect,
	onStatusChange,
	router,
}: {
	message: Message;
	isSelected: boolean;
	onSelect: (id: string, selected: boolean) => void;
	onStatusChange: (id: string, status: MessageStatus) => void;
	router: ReturnType<typeof useRouter>;
}) => {
	const handleProcess = () => {
		// 点击"处理"按钮时，自动将消息标记为已读状态
		if (message.status === MessageStatus.UNREAD) {
			onStatusChange(message.id, MessageStatus.READ);
		}

		if (message.routeLink) {
			router.push(message.routeLink);
		}
	};

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ delay: 0.1 }}
			className={cn(
				"rounded-lg p-6",
				"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
				"border border-[#D4B485]/20",
				"group",
				"hover:border-[#D4B485]/40",
				"transition-all duration-200",
				"relative overflow-hidden",
				message.status === MessageStatus.UNREAD &&
					"bg-[#D4B485]/5 border-[#D4B485]/30",
			)}
		>
			{/* 背景装饰 */}
			<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
			<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />

			<div className="relative flex items-start gap-4">
				{/* 选择框 */}
				<div className="flex items-center h-8">
					<Checkbox
						checked={isSelected}
						onCheckedChange={(checked) =>
							onSelect(message.id, !!checked)
						}
						className="border-[#D4B485]/40 data-[state=checked]:bg-[#D4B485] data-[state=checked]:text-[#1A1C1E]"
					/>
				</div>

				{/* 消息类型图标 */}
				<MessageTypeIcon type={message.type} />

				<div className="flex-1 min-w-0">
					<div className="flex items-center gap-3">
						<h3 className="font-medium text-[#D4B485]">
							{message.title}
						</h3>
						<span
							className={cn(
								"rounded-full px-2 py-0.5 text-xs",
								"bg-[#D4B485]/10",
								"text-[#D4B485]/60",
							)}
						>
							{getMessageTypeLabel(message.type)}
						</span>
						<span
							className={cn(
								"rounded-full px-2 py-0.5 text-xs",
								message.status === MessageStatus.UNREAD
									? "bg-amber-500/10 text-amber-500"
									: message.status === MessageStatus.READ
										? "bg-blue-500/10 text-blue-500"
										: message.status ===
												MessageStatus.ARCHIVED
											? "bg-emerald-500/10 text-emerald-500"
											: "bg-[#D4B485]/10 text-[#D4B485]/60",
							)}
						>
							{getMessageStatusLabel(message.status)}
						</span>
					</div>
					<p className="mt-2 text-sm text-[#D4B485]/40">
						{message.content}
					</p>
					<div className="mt-3 flex items-center gap-6 text-sm text-[#D4B485]/40">
						<div className="flex items-center gap-2">
							<Avatar className="h-6 w-6">
								<AvatarImage
									src={undefined}
									alt={message.userId}
								/>
								<AvatarFallback
									className={cn(
										"text-[#1E2023] text-xs font-semibold",
										"bg-gradient-to-br from-[#D4B485] to-[#B08968]",
									)}
								>
									{message.userId.slice(0, 1)}
								</AvatarFallback>
							</Avatar>
							<span>{message.userId}</span>
						</div>
						<div>
							创建时间：
							<span className="font-medium text-[#D4B485]">
								{new Date(message.createdAt).toLocaleString(
									"zh-CN",
									{
										year: "numeric",
										month: "2-digit",
										day: "2-digit",
										hour: "2-digit",
										minute: "2-digit",
									},
								)}
							</span>
						</div>
						{message.readAt && (
							<div>
								已读时间：
								<span className="font-medium text-[#D4B485]">
									{new Date(message.readAt).toLocaleString(
										"zh-CN",
										{
											year: "numeric",
											month: "2-digit",
											day: "2-digit",
											hour: "2-digit",
											minute: "2-digit",
										},
									)}
								</span>
							</div>
						)}
					</div>
				</div>

				<div className="flex gap-2">
					<Button
						variant="outline"
						className={cn(
							"h-8 px-3",
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"hover:bg-[#D4B485]/10",
						)}
						onClick={() =>
							router.push(
								`/app/agent/admin/messages/${message.id}`,
							)
						}
					>
						查看详情
					</Button>
					{message.status === MessageStatus.UNREAD && (
						<Button
							className={cn(
								"h-8 px-3",
								"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
								"text-white hover:text-white/90",
								"border-none",
								"shadow-[0_8px_16px_-4px_rgba(212,180,133,0.3)]",
								"transition-all duration-300",
								"hover:shadow-[0_12px_20px_-4px_rgba(212,180,133,0.4)]",
								"hover:scale-105",
							)}
							onClick={handleProcess}
						>
							处理
						</Button>
					)}
				</div>
			</div>
		</motion.div>
	);
};

// 消息列表加载骨架屏
const MessageSkeleton = () => {
	return (
		<div className="rounded-lg p-6 bg-gradient-to-br from-[#1E2023] to-[#1A1C1E] border border-[#D4B485]/20">
			<div className="flex items-start gap-4">
				<Skeleton className="h-8 w-8 rounded-full bg-[#D4B485]/10" />
				<div className="flex-1">
					<Skeleton className="h-6 w-48 mb-2 bg-[#D4B485]/10" />
					<Skeleton className="h-4 w-full mb-2 bg-[#D4B485]/10" />
					<Skeleton className="h-4 w-3/4 mb-2 bg-[#D4B485]/10" />
					<div className="flex justify-between mt-2">
						<Skeleton className="h-4 w-24 bg-[#D4B485]/10" />
						<Skeleton className="h-8 w-24 bg-[#D4B485]/10" />
					</div>
				</div>
			</div>
		</div>
	);
};

// 辅助函数：获取消息类型标签
const getMessageTypeLabel = (type: string): string => {
	const messageTypeLabels: Record<string, string> = {
		[MessageType.AGENT]: "代理商消息",
		[MessageType.SYSTEM]: "系统消息",
		[MessageType.QUOTA]: "配额消息",
		[MessageType.PERFORMANCE]: "性能消息",
		[MessageType.ORDER]: "订单消息",
		[MessageType.PLAN]: "计划消息",
	};
	return messageTypeLabels[type] || "其他消息";
};

// 辅助函数：获取消息状态标签
const getMessageStatusLabel = (status: string): string => {
	const messageStatusLabels: Record<string, string> = {
		[MessageStatus.UNREAD]: "未读",
		[MessageStatus.READ]: "已读",
		[MessageStatus.ARCHIVED]: "已归档",
	};
	return messageStatusLabels[status] || "未知状态";
};

export function MessageCenter() {
	// 状态和钩子
	const [searchTerm, setSearchTerm] = useState("");
	const [selectedMessages, setSelectedMessages] = useState<string[]>([]);
	const [filterStatus, setFilterStatus] = useState<MessageStatus | "">("");
	const [filterType, setFilterType] = useState<MessageType | "">("");

	const { loading, messages, total, fetchMessages } = useMessages();

	const { updateStatus } = useMessageStatus();
	const { batchUpdateStatus } = useMessageBatchStatus();

	const router = useRouter();

	// 初始加载消息
	useEffect(() => {
		fetchMessages();
	}, [fetchMessages]);

	// 过滤消息
	const filteredMessages = messages.filter((message) => {
		const matchesSearch = searchTerm
			? message.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
				message.content.toLowerCase().includes(searchTerm.toLowerCase())
			: true;

		const matchesStatus = filterStatus
			? message.status === filterStatus
			: true;

		const matchesType = filterType ? message.type === filterType : true;

		return matchesSearch && matchesStatus && matchesType;
	});

	// 处理消息选择
	const handleSelectMessage = useCallback((id: string, selected: boolean) => {
		setSelectedMessages((prev) =>
			selected
				? [...prev, id]
				: prev.filter((messageId) => messageId !== id),
		);
	}, []);

	// 处理全选
	const handleSelectAll = useCallback(
		(selected: boolean) => {
			setSelectedMessages(
				selected ? filteredMessages.map((m) => m.id) : [],
			);
		},
		[filteredMessages],
	);

	// 处理单个消息状态更新
	const handleStatusChange = useCallback(
		async (id: string, status: MessageStatus) => {
			await updateStatus(id, status);
			fetchMessages();
		},
		[updateStatus, fetchMessages],
	);

	// 处理批量更新状态
	const handleBatchUpdate = useCallback(
		async (status: MessageStatus) => {
			if (selectedMessages.length === 0) {
				return;
			}

			await batchUpdateStatus(selectedMessages, status);
			setSelectedMessages([]);
			fetchMessages();
		},
		[selectedMessages, batchUpdateStatus, fetchMessages],
	);

	// 当前选中的消息数量
	const selectedCount = selectedMessages.length;
	// 是否全部选中
	const allSelected =
		filteredMessages.length > 0 &&
		selectedCount === filteredMessages.length;

	return (
		<div className="w-full space-y-6 p-6">
			{/* 标题和操作栏 */}
			<div className="flex items-center justify-between">
				<h1
					className={cn("text-2xl font-bold", zywhFont.className)}
					style={{
						background:
							"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
						WebkitBackgroundClip: "text",
						WebkitTextFillColor: "transparent",
						backgroundClip: "text",
						textShadow:
							"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
					}}
				>
					消息中心
				</h1>
				<div className="flex items-center gap-4">
					<div className="flex items-center gap-2">
						<span className="text-[#D4B485]/60">
							共 {total} 条消息
						</span>
					</div>
					<Button
						variant="outline"
						className={cn(
							"gap-2",
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"hover:bg-[#D4B485]/10",
						)}
						onClick={() => fetchMessages()}
					>
						<RefreshCwIcon className="h-4 w-4" />
						<span>刷新</span>
					</Button>
					<Button
						variant="outline"
						className={cn(
							"gap-2",
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"hover:bg-[#D4B485]/10",
						)}
					>
						<Download className="h-4 w-4" />
						<span>导出记录</span>
					</Button>
				</div>
			</div>

			{/* 搜索和筛选 */}
			<Card
				className={cn(
					"p-4",
					"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
					"border border-[#D4B485]/20",
					"group",
					"hover:border-[#D4B485]/40",
					"transition-all duration-500",
				)}
			>
				<div className="flex items-center gap-4">
					<div className="relative flex-1">
						<Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-[#D4B485]/40" />
						<Input
							placeholder="搜索消息..."
							className={cn(
								"pl-10",
								"bg-[#1A1C1E]",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
								"placeholder:text-[#D4B485]/40",
							)}
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
						/>
					</div>

					{/* 消息类型筛选 */}
					<DropdownMenu>
						<DropdownMenuTrigger asChild>
							<Button
								variant="outline"
								className={cn(
									"w-[160px]",
									"bg-[#1A1C1E]",
									"border-[#D4B485]/20",
									"text-[#D4B485]",
									"hover:bg-[#D4B485]/10",
								)}
							>
								{filterType
									? getMessageTypeLabel(filterType)
									: "全部类型"}
							</Button>
						</DropdownMenuTrigger>
						<DropdownMenuContent className="bg-[#1A1C1E] border-[#D4B485]/20">
							<DropdownMenuItem
								className="text-[#D4B485] hover:bg-[#D4B485]/10 cursor-pointer"
								onClick={() => setFilterType("")}
							>
								全部类型
							</DropdownMenuItem>
							<DropdownMenuItem
								className="text-[#D4B485] hover:bg-[#D4B485]/10 cursor-pointer"
								onClick={() => setFilterType(MessageType.AGENT)}
							>
								代理商消息
							</DropdownMenuItem>
							<DropdownMenuItem
								className="text-[#D4B485] hover:bg-[#D4B485]/10 cursor-pointer"
								onClick={() =>
									setFilterType(MessageType.SYSTEM)
								}
							>
								系统消息
							</DropdownMenuItem>
							<DropdownMenuItem
								className="text-[#D4B485] hover:bg-[#D4B485]/10 cursor-pointer"
								onClick={() => setFilterType(MessageType.QUOTA)}
							>
								配额消息
							</DropdownMenuItem>
							<DropdownMenuItem
								className="text-[#D4B485] hover:bg-[#D4B485]/10 cursor-pointer"
								onClick={() =>
									setFilterType(MessageType.PERFORMANCE)
								}
							>
								性能消息
							</DropdownMenuItem>
						</DropdownMenuContent>
					</DropdownMenu>

					{/* 消息状态筛选 */}
					<DropdownMenu>
						<DropdownMenuTrigger asChild>
							<Button
								variant="outline"
								className={cn(
									"w-[160px]",
									"bg-[#1A1C1E]",
									"border-[#D4B485]/20",
									"text-[#D4B485]",
									"hover:bg-[#D4B485]/10",
								)}
							>
								{filterStatus === MessageStatus.UNREAD
									? "未读"
									: filterStatus === MessageStatus.READ
										? "已读"
										: filterStatus ===
												MessageStatus.ARCHIVED
											? "已归档"
											: "全部状态"}
							</Button>
						</DropdownMenuTrigger>
						<DropdownMenuContent className="bg-[#1A1C1E] border-[#D4B485]/20">
							<DropdownMenuItem
								className="text-[#D4B485] hover:bg-[#D4B485]/10 cursor-pointer"
								onClick={() => setFilterStatus("")}
							>
								全部状态
							</DropdownMenuItem>
							<DropdownMenuItem
								className="text-[#D4B485] hover:bg-[#D4B485]/10 cursor-pointer"
								onClick={() =>
									setFilterStatus(MessageStatus.UNREAD)
								}
							>
								未读
							</DropdownMenuItem>
							<DropdownMenuItem
								className="text-[#D4B485] hover:bg-[#D4B485]/10 cursor-pointer"
								onClick={() =>
									setFilterStatus(MessageStatus.READ)
								}
							>
								已读
							</DropdownMenuItem>
							<DropdownMenuItem
								className="text-[#D4B485] hover:bg-[#D4B485]/10 cursor-pointer"
								onClick={() =>
									setFilterStatus(MessageStatus.ARCHIVED)
								}
							>
								已归档
							</DropdownMenuItem>
						</DropdownMenuContent>
					</DropdownMenu>

					<Button
						variant="outline"
						className={cn(
							"gap-2",
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"hover:bg-[#D4B485]/10",
						)}
						onClick={() => {
							setSearchTerm("");
							setFilterStatus("");
							setFilterType("");
							fetchMessages();
						}}
					>
						<Filter className="h-4 w-4" />
						<span>重置筛选</span>
					</Button>
				</div>
			</Card>

			{/* 批量操作栏 */}
			{selectedCount > 0 && (
				<div className="flex items-center justify-between p-4 bg-[#1A1C1E] border border-[#D4B485]/20 rounded-lg">
					<div className="flex items-center gap-2">
						<Checkbox
							checked={allSelected}
							onCheckedChange={handleSelectAll}
							className="border-[#D4B485]/40 data-[state=checked]:bg-[#D4B485] data-[state=checked]:text-[#1A1C1E]"
						/>
						<span className="text-[#D4B485]">
							已选择 {selectedCount} 条消息
						</span>
					</div>
					<div className="flex items-center gap-2">
						<Button
							variant="outline"
							size="sm"
							className="text-[#D4B485] hover:bg-[#D4B485]/10 border-[#D4B485]/20"
							onClick={() =>
								handleBatchUpdate(MessageStatus.READ)
							}
						>
							<CheckIcon className="h-4 w-4 mr-1" />
							标为已读
						</Button>
						<Button
							variant="outline"
							size="sm"
							className="text-[#D4B485] hover:bg-[#D4B485]/10 border-[#D4B485]/20"
							onClick={() =>
								handleBatchUpdate(MessageStatus.ARCHIVED)
							}
						>
							<ArchiveIcon className="h-4 w-4 mr-1" />
							归档
						</Button>
					</div>
				</div>
			)}

			{/* 消息列表 */}
			<div className="grid gap-4">
				{loading ? (
					// 加载中显示骨架屏
					<>
						<MessageSkeleton />
						<MessageSkeleton />
						<MessageSkeleton />
					</>
				) : filteredMessages.length > 0 ? (
					// 显示消息列表
					filteredMessages.map((message: Message) => (
						<MessageItem
							key={message.id}
							message={message}
							isSelected={selectedMessages.includes(message.id)}
							onSelect={handleSelectMessage}
							onStatusChange={handleStatusChange}
							router={router}
						/>
					))
				) : (
					// 无消息时显示空状态
					<div className="flex flex-col items-center justify-center py-12 text-[#D4B485]/40">
						<MessageCircle className="h-12 w-12 mb-4" />
						<p className="text-lg font-medium">暂无消息</p>
						<p className="mt-2">
							{searchTerm || filterStatus || filterType
								? "没有符合筛选条件的消息"
								: "您的消息中心目前为空"}
						</p>
					</div>
				)}
			</div>
		</div>
	);
}
