"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { ArrowLeft, Network, Users } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import type { AgentNode } from "../hooks/use-agent-hierarchy";
import type { AgentListItem } from "../hooks/use-agent-list";
import {
	type TeamMembersFilterParams,
	TeamMembersList,
} from "./TeamMembersList";

export interface HierarchyViewProps {
	className?: string;
	hierarchy?: AgentNode | null;
	teamMembers?: AgentListItem[];
	teamMembersLoading?: boolean;
	selectedAgent?: AgentListItem | null;
	hierarchyLoading?: boolean;
	onBackToList?: () => void;
	onNodeClick?: (node: AgentNode) => void;
	onViewMemberDetails?: (member: AgentListItem) => void;
	onEditMember?: (member: AgentListItem) => void;
	onDeleteMember?: (member: AgentListItem) => void;
	onAddMember?: () => void;
	onFilterTeamMembers?: (params: TeamMembersFilterParams) => void;
	onTeamMembersPageChange?: (page: number) => void;
	teamMembersTotal?: number;
	teamMembersCurrentPage?: number;
	teamMembersTotalPages?: number;
}

export function HierarchyView({
	className,
	hierarchy,
	teamMembers = [],
	teamMembersLoading = false,
	selectedAgent,
	hierarchyLoading = false,
	onBackToList,
	onNodeClick,
	onViewMemberDetails,
	onEditMember,
	onDeleteMember,
	onAddMember,
	onFilterTeamMembers,
	onTeamMembersPageChange,
	teamMembersTotal,
	teamMembersCurrentPage,
	teamMembersTotalPages,
}: HierarchyViewProps) {
	// 当前选中的节点（初始为层级根节点）
	const [selectedNode, setSelectedNode] = useState<AgentNode | null>(
		hierarchy || null,
	);

	// 当层级数据发生变化时，更新选中的节点
	useEffect(() => {
		if (hierarchy) {
			setSelectedNode(hierarchy);
		}
	}, [hierarchy]);

	// 处理节点点击
	const _handleNodeClick = useCallback(
		(node: AgentNode) => {
			setSelectedNode(node);
			onNodeClick?.(node);
		},
		[onNodeClick],
	);

	return (
		<div className={cn("space-y-8", className)}>
			{/* 标题区域 */}
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.1 }}
				className="relative mt-6 mb-8 space-y-1"
			>
				<div className="flex items-center gap-4">
					<div
						className={cn(
							"flex h-14 w-14 shrink-0 items-center justify-center",
							"rounded-xl bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
							"ring-1 ring-[#D4B485]/20",
							"shadow-[0_0_0_8px_rgba(212,180,133,0.03)]",
							"transform-gpu transition-transform duration-300",
							"hover:scale-105 hover:shadow-[0_0_0_10px_rgba(212,180,133,0.05)]",
						)}
					>
						<Users className="h-7 w-7 text-[#D4B485]" />
					</div>
					<div className="space-y-1">
						<h2
							className={cn(
								"font-semibold text-3xl",
								zywhFont.className,
								"leading-none",
								"tracking-[0.05em]",
								"relative",
								"after:absolute after:inset-0",
								"after:bg-[radial-gradient(circle_at_50%_50%,rgba(212,180,133,0.15),transparent_70%)]",
								"after:blur-xl after:-z-10",
							)}
							style={{
								background:
									"linear-gradient(135deg, rgba(229,201,165,0.95) 0%, rgba(212,180,133,0.9) 50%, rgba(176,137,104,0.85) 100%)",
								WebkitBackgroundClip: "text",
								WebkitTextFillColor: "transparent",
								backgroundClip: "text",
								textShadow:
									"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.1)",
								filter: "contrast(1.1) brightness(1.05)",
							}}
						>
							代理商管理
							<span className="ml-3 font-normal text-base text-[#D4B485]/80 tracking-wide">
								高效管理您的代理商团队
							</span>
						</h2>
						<p className="text-sm leading-relaxed text-white/40 group-hover:text-white/60 transition-colors duration-300">
							创建、查看和管理代理商账号，按角色设置权限，查看团队层级结构
						</p>
					</div>
				</div>
			</motion.div>

			{/* 层级结构标题区域 */}
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.2 }}
				className="relative mb-6 flex items-center justify-between"
			>
				<div className="flex items-center gap-3">
					<div
						className={cn(
							"flex h-10 w-10 shrink-0 items-center justify-center",
							"rounded-lg bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
							"ring-1 ring-[#D4B485]/20",
							"shadow-[0_0_0_4px_rgba(212,180,133,0.02)]",
						)}
					>
						<Network className="h-5 w-5 text-[#D4B485]" />
					</div>
					<h3
						className={cn(
							"font-medium text-xl",
							zywhFont.className,
							"leading-none",
							"tracking-wide",
						)}
						style={{
							background:
								"linear-gradient(135deg, rgba(229,201,165,0.95) 0%, rgba(212,180,133,0.9) 100%)",
							WebkitBackgroundClip: "text",
							WebkitTextFillColor: "transparent",
							backgroundClip: "text",
						}}
					>
						{selectedAgent?.name} 的团队层级
					</h3>
				</div>
				<div className="flex items-center gap-2">
					<Button
						variant="outline"
						size="sm"
						className="border-[#D4B485]/20 text-[#D4B485] hover:bg-[#D4B485]/10"
						onClick={onBackToList}
					>
						<ArrowLeft className="h-4 w-4 mr-1" />
						返回列表
					</Button>
				</div>
			</motion.div>

			{/* 层级树区域 - 替换为提示信息 */}
			<div className="relative">
				{hierarchyLoading ? (
					<div className="py-16 flex items-center justify-center">
						<div className="text-[#D4B485]/60">
							加载团队层级中...
						</div>
					</div>
				) : hierarchy ? (
					<motion.div
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ delay: 0.3 }}
						className="py-16 flex flex-col items-center justify-center gap-4 border border-dashed border-[#D4B485]/20 rounded-lg bg-[#D4B485]/5 p-8"
					>
						<div className="text-[#D4B485] text-center">
							<Network className="h-16 w-16 mx-auto mb-4 opacity-30" />
							<h4 className="text-xl font-semibold mb-2">
								层级树视图已移除
							</h4>
							<p className="text-[#D4B485]/60 max-w-md mx-auto">
								层级树组件已被移除。您仍然可以通过下方的团队成员列表查看和管理团队成员。
							</p>
						</div>
					</motion.div>
				) : (
					<div className="py-16 flex flex-col items-center justify-center gap-4">
						<div className="text-[#D4B485]/60">
							暂无团队层级数据
						</div>
					</div>
				)}
			</div>

			{/* 团队成员列表 */}
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.4 }}
			>
				<TeamMembersList
					members={teamMembers}
					isLoading={teamMembersLoading}
					selectedNode={selectedNode}
					onAddMember={onAddMember}
					onViewMemberDetails={onViewMemberDetails}
					onEditMember={onEditMember}
					onDeleteMember={onDeleteMember}
					onFilter={onFilterTeamMembers}
					onPageChange={onTeamMembersPageChange}
					total={teamMembersTotal}
					currentPage={teamMembersCurrentPage}
					totalPages={teamMembersTotalPages}
				/>
			</motion.div>
		</div>
	);
}
