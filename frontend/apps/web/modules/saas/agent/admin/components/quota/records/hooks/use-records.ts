import { logger } from "@repo/logs";
import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";

// 分配记录响应
export interface QuotaRecord {
	id: string;
	fromAgentId: string;
	fromAgentName: string;
	fromAgentRole: string;
	toAgentId: string;
	toAgentName: string;
	toAgentRole: string;
	quantity: number;
	amount?: number;
	unitPrice?: number;
	status: string;
	remark?: string;
	tradeRemark?: string;
	adminRemark?: string;
	createdAt: string;
	completedAt?: string;
	cancelledAt?: string;
}

// API响应类型
interface ApiResponse<T> {
	code: number;
	message?: string;
	data?: T;
	error?: string;
}

interface RecordsResponse {
	total: number;
	items: QuotaRecord[];
}

// 查询参数
export interface RecordsQuery {
	page: number;
	pageSize: number;
	status?: string;
	fromAgentId?: string;
	toAgentId?: string;
	startDate?: string;
	endDate?: string;
}

export function useRecords() {
	const [loading, setLoading] = useState(false);
	const [records, setRecords] = useState<QuotaRecord[]>([]);
	const [total, setTotal] = useState(0);

	const fetchRecords = useCallback(async (query: RecordsQuery) => {
		setLoading(true);
		try {
			const response = await apiClient.v1.agent.admin.quota.records.$get({
				query: query,
			});

			const result =
				(await response.json()) as ApiResponse<RecordsResponse>;

			if (result.code === 200 && result.data) {
				setRecords(result.data.items);
				setTotal(result.data.total);
			} else {
				toast.error("获取名额分配记录失败");
				logger.error("[Admin] 获取名额分配记录失败", {
					error: result.message || result.error,
				});
			}
		} catch (error) {
			toast.error("获取名额分配记录失败");
			logger.error("[Admin] 获取名额分配记录失败", {
				error: error instanceof Error ? error.message : "未知错误",
			});
		} finally {
			setLoading(false);
		}
	}, []);

	return {
		loading,
		records,
		total,
		fetchRecords,
	};
}
