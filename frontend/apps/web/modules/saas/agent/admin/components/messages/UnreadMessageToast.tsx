"use client";

import { But<PERSON> } from "@ui/components/button";
import { cn } from "@ui/lib";
import { AnimatePresence, motion } from "framer-motion";
import { Bell, ChevronRight, X } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

interface Message {
	id: string;
	title: string;
}

interface UnreadMessageToastProps {
	messages: Message[];
	totalCount: number;
	onClose: () => void;
}

export function UnreadMessageToast({
	messages,
	totalCount,
	onClose,
}: UnreadMessageToastProps) {
	const router = useRouter();
	const [visible, setVisible] = useState(true);

	// 自动关闭逻辑（10秒后自动关闭）
	useEffect(() => {
		const timer = setTimeout(() => {
			handleClose();
		}, 10000);

		return () => clearTimeout(timer);
	}, []);

	const handleClose = () => {
		setVisible(false);
		setTimeout(() => {
			onClose();
		}, 300); // 等动画结束后才移除组件
	};

	const handleViewMore = () => {
		router.push("/app/agent/admin/messages");
		handleClose();
	};

	const handleKeyDown = (event: React.KeyboardEvent) => {
		if (event.key === "Enter" || event.key === " ") {
			router.push("/app/agent/admin/messages");
		}
	};

	return (
		<AnimatePresence>
			{visible && (
				<motion.div
					initial={{ opacity: 0, y: 50 }}
					animate={{ opacity: 1, y: 0 }}
					exit={{ opacity: 0, y: 50 }}
					transition={{ duration: 0.3 }}
					className={cn(
						"fixed bottom-6 right-6 z-50",
						"w-80 rounded-xl",
						"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
						"border border-[#D4B485]/30",
						"shadow-lg",
						"overflow-hidden",
					)}
				>
					{/* 顶部标题栏 */}
					<div className="flex items-center justify-between p-3 bg-[#D4B485]/10 border-b border-[#D4B485]/20">
						<div className="flex items-center gap-2">
							<Bell className="h-5 w-5 text-[#D4B485]" />
							<span className="font-medium text-[#D4B485]">
								管理员未读消息
							</span>
							<span className="flex items-center justify-center h-5 min-w-5 rounded-full bg-[#D4B485] text-[#1A1C1E] text-xs font-medium px-1.5">
								{totalCount}
							</span>
						</div>
						<button
							type="button"
							onClick={handleClose}
							onKeyDown={(e) =>
								e.key === "Enter" && handleClose()
							}
							className="rounded-full p-1 hover:bg-[#D4B485]/10 transition-colors"
						>
							<X className="h-4 w-4 text-[#D4B485]/60" />
						</button>
					</div>

					{/* 消息列表 */}
					<div className="p-3 space-y-2">
						{messages.slice(0, 3).map((message) => (
							<button
								key={message.id}
								className="p-2 rounded-lg hover:bg-[#D4B485]/5 cursor-pointer transition-colors w-full text-left"
								onClick={() =>
									router.push(
										`/app/agent/admin/messages/${message.id}`,
									)
								}
								onKeyDown={handleKeyDown}
								type="button"
							>
								<p className="text-sm text-[#D4B485]/80 line-clamp-1">
									{message.title}
								</p>
							</button>
						))}
					</div>

					{/* 底部操作区 */}
					<div className="p-3 pt-0">
						<Button
							type="button"
							onClick={handleViewMore}
							variant="outline"
							className="w-full border-[#D4B485]/20 text-[#D4B485] hover:bg-[#D4B485] hover:text-[#1A1C1E] transition-colors"
						>
							<span>查看更多未读消息</span>
							<ChevronRight className="h-4 w-4 ml-1" />
						</Button>
					</div>
				</motion.div>
			)}
		</AnimatePresence>
	);
}
