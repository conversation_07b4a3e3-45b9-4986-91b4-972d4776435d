import { MessageStatus } from "@repo/api/src/routes/v1/agent/messages/types";
import { logger } from "@repo/logs";
import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";
import { useMessages } from "./useMessages";

// 批量更新消息状态请求参数
export interface BatchUpdateMessageStatusParams {
	messageIds: string[];
	status: MessageStatus;
}

// 批量更新消息状态响应数据
export interface BatchUpdateMessageStatusResponse {
	success: boolean;
	count: number;
}

/**
 * 管理员批量消息状态更新hook
 * 用于批量更新多个消息的状态（已读/未读/归档）
 */
export function useMessageBatchStatus() {
	const [loading, setLoading] = useState(false);
	const [updatedCount, setUpdatedCount] = useState(0);

	// 获取消息上下文
	const { removeMessagesFromUnread } = useMessages();

	/**
	 * 批量更新消息状态
	 */
	const batchUpdateStatus = useCallback(
		async (messageIds: string[], status: MessageStatus) => {
			if (!messageIds.length) {
				toast.error("请选择需要更新的消息");
				return { success: false, error: "未选择消息" };
			}

			setLoading(true);
			try {
				logger.info("开始批量更新管理员消息状态", {
					messageIds,
					status,
				});

				// 使用类型断言解决类型检查问题
				type PatchOptions = {
					json: { messageIds: string[]; status: MessageStatus };
				};

				// 使用管理员消息API路径
				const response =
					await apiClient.v1.agent.messages.batch.update.$patch({
						json: { messageIds, status },
					} as PatchOptions);

				const result =
					(await response.json()) as BatchUpdateMessageStatusResponse;

				logger.info("批量更新管理员消息状态响应:", result);

				if (response.ok && result.success) {
					setUpdatedCount(result.count);

					// 根据状态显示不同的成功提示
					const statusText =
						status === MessageStatus.READ
							? "已读"
							: status === MessageStatus.UNREAD
								? "未读"
								: "归档";

					toast.success(
						`已将${result.count}条消息标记为${statusText}`,
					);

					// 如果消息被标记为已读或归档，从未读消息列表中批量移除
					if (
						status === MessageStatus.READ ||
						status === MessageStatus.ARCHIVED
					) {
						removeMessagesFromUnread(messageIds);
					}

					return { success: true, count: result.count };
				}
				throw new Error("批量更新管理员消息状态失败");
			} catch (error) {
				logger.error("批量更新管理员消息状态失败", {
					error,
					messageIds,
					status,
				});
				toast.error("批量更新管理员消息状态失败", {
					description:
						error instanceof Error ? error.message : "请重试",
				});
				return { success: false, error };
			} finally {
				setLoading(false);
			}
		},
		[removeMessagesFromUnread],
	);

	return {
		loading,
		updatedCount,
		batchUpdateStatus,
	};
}
