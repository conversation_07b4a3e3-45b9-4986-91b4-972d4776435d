import { logger } from "@repo/logs";
import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";

// 激活记录列表项类型
export interface ActivationRecordItem {
	id: string;
	status: "SUCCESS" | "FAILED";
	activationMethod: "ADMIN" | "AGENT" | "AUTO";
	activatedAt: string;
	expiresAt?: string;
	remark?: string;

	// 消费者信息
	consumer: {
		id: string;
		name: string;
		phone: string;
		avatar?: string;
	};

	// 代理商信息
	agent: {
		id: string;
		name: string;
		phone: string;
		avatar?: string;
		role: string;
	};

	// 套餐信息
	plan: {
		id: string;
		name: string;
		level: "TRIAL" | "BASIC" | "ENHANCED" | "ENTERPRISE" | "PREMIUM";
		computingPower: number;
		validityDays: number;
		type: string;
	};

	// 激活相关信息
	activation?: {
		orderId?: string;
		originalExpiresAt?: string;
		activatedBy: string;
		activatedByType: "ADMIN" | "AGENT" | "SYSTEM";
	};
}

// 查询参数类型
export interface ActivationRecordsQuery {
	page?: number;
	pageSize?: number;
	searchTerm?: string;
	activationStatus?: "ALL" | "SUCCESS" | "FAILED";
	planLevel?:
		| "ALL"
		| "TRIAL"
		| "BASIC"
		| "ENHANCED"
		| "ENTERPRISE"
		| "PREMIUM";
	activationMethod?: "ALL" | "ADMIN" | "AGENT" | "AUTO";
	startDate?: string;
	endDate?: string;
	sortBy?: "activatedAt" | "expiresAt" | "planLevel";
	sortOrder?: "asc" | "desc";
	agentId?: string;
	consumerId?: string;
}

// API响应类型
interface ApiResponse {
	code: number;
	message?: string;
	data?: {
		items: ActivationRecordItem[];
		total: number;
		stats?: {
			totalActivations: number;
			successActivations: number;
			failedActivations: number;
			successRate: number;
			totalComputingPower: number;
		};
	};
	error?: string;
}

export function useActivationRecords() {
	const [loading, setLoading] = useState(false);
	const [records, setRecords] = useState<ActivationRecordItem[]>([]);
	const [total, setTotal] = useState(0);
	const [stats, setStats] = useState<{
		totalActivations: number;
		successActivations: number;
		failedActivations: number;
		successRate: number;
		totalComputingPower: number;
	} | null>(null);

	const fetchRecords = useCallback(async (query: ActivationRecordsQuery) => {
		setLoading(true);
		try {
			logger.info("[Admin] 开始获取激活记录列表", { query });

			const response = await apiClient.v1.agent.admin[
				"activation-records"
			].list.$get({
				query: {
					page: query.page || 1,
					pageSize: query.pageSize || 10,
					searchTerm: query.searchTerm,
					activationStatus: query.activationStatus,
					planLevel: query.planLevel,
					activationMethod: query.activationMethod,
					startDate: query.startDate,
					endDate: query.endDate,
					sortBy: query.sortBy || "activatedAt",
					sortOrder: query.sortOrder || "desc",
					agentId: query.agentId,
					consumerId: query.consumerId,
				},
			});

			const result = (await response.json()) as ApiResponse;

			logger.info("[Admin] 激活记录列表响应", { result });

			if (response.ok && result.code === 200 && result.data) {
				setRecords(result.data.items);
				setTotal(result.data.total);
				setStats(result.data.stats || null);
			} else {
				const errorMsg =
					result.message || result.error || "获取激活记录列表失败";
				toast.error(errorMsg);
				logger.error("[Admin] 获取激活记录列表失败", {
					error: errorMsg,
					code: result.code,
				});
			}
		} catch (error) {
			const errorMsg =
				error instanceof Error ? error.message : "获取激活记录列表失败";
			toast.error(errorMsg);
			logger.error("[Admin] 获取激活记录列表异常", {
				error: errorMsg,
			});
		} finally {
			setLoading(false);
		}
	}, []);

	return {
		loading,
		records,
		total,
		stats,
		fetchRecords,
	};
}
