"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { Ticket } from "lucide-react";
import { useState } from "react";
import { AllocationProgress } from "./components/AllocationProgress";
import { ConfirmAllocation } from "./components/ConfirmAllocation";
import { InputQuantity } from "./components/InputQuantity";
import { SelectAgent } from "./components/SelectAgent";
import { SuccessView } from "./components/SuccessView";

// 分配步骤
const STEPS = [
	{
		title: "选择代理商",
		description: "从代理商列表中选择账户",
	},
	{
		title: "输入数量",
		description: "输入要分配的名额数量",
	},
	{
		title: "确认分配",
		description: "确认分配信息和详情",
	},
];

// 表单数据类型
interface Agent {
	id: string;
	name: string;
	role: string;
	email: string;
	phone: string;
	teamQuota: number;
	usedTeamQuota: number;
	remainingQuota: number;
	teamSize: number;
	totalSales: number;
	monthSales: number;
	createdAt: string;
}

interface FormData {
	agent: Agent | null;
	quantity: number | null;
}

interface StepData {
	agent?: Agent;
	quantity?: number;
}

interface AllocationInfo {
	recordId: string;
	agent: Agent;
	quantity: number;
	amount?: number;
	unitPrice?: number;
	createdAt: string;
}

export function AllocationView() {
	const [currentStep, setCurrentStep] = useState(0);
	const [formData, setFormData] = useState<FormData>({
		agent: null,
		quantity: null,
	});

	// 处理下一步
	const handleNext = (data: StepData) => {
		setFormData((prev) => ({ ...prev, ...data }));
		setCurrentStep((prev) => prev + 1);
	};

	// 处理上一步
	const handlePrev = () => {
		setCurrentStep((prev) => prev - 1);
	};

	// 处理分配成功
	const handleAllocationSuccess = (allocationInfo: AllocationInfo) => {
		setFormData((prev) => ({
			...prev,
			allocationInfo,
		}));
	};

	// 渲染当前步骤内容
	const renderStepContent = () => {
		switch (currentStep) {
			case 0:
				return <SelectAgent onNext={handleNext} formData={formData} />;
			case 1:
				return (
					<InputQuantity
						onNext={handleNext}
						onPrev={handlePrev}
						formData={formData}
					/>
				);
			case 2:
				if (!formData.agent || !formData.quantity) {
					setCurrentStep(0);
					return null;
				}
				return (
					<motion.div
						key="confirm-allocation"
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						exit={{ opacity: 0, y: -20 }}
					>
						{"allocationInfo" in formData ? (
							<SuccessView
								selectedAgent={formData.agent}
								quantity={formData.quantity}
								onBackToList={() => {
									window.location.href =
										"/app/agent/admin/quota/records";
								}}
							/>
						) : (
							<ConfirmAllocation
								onPrev={handlePrev}
								formData={formData}
								onAllocationSuccess={handleAllocationSuccess}
							/>
						)}
					</motion.div>
				);
			default:
				return null;
		}
	};

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			className="w-full"
		>
			{/* 标题和进度条区域 */}
			<motion.div
				initial={{ opacity: 0, x: -20 }}
				animate={{ opacity: 1, x: 0 }}
				transition={{ delay: 0.2 }}
				className="relative mt-8 mb-6"
			>
				<div className="flex items-center justify-between">
					{/* 标题区域 */}
					<div className="flex items-center gap-4">
						<div
							className={cn(
								"flex h-14 w-14 shrink-0 items-center justify-center",
								"rounded-xl bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
								"ring-1 ring-[#D4B485]/20",
								"shadow-[0_0_0_8px_rgba(212,180,133,0.03)]",
								"transform-gpu transition-transform duration-300",
								"hover:scale-105 hover:shadow-[0_0_0_10px_rgba(212,180,133,0.05)]",
							)}
						>
							<Ticket className="h-7 w-7 text-[#D4B485]" />
						</div>
						<div className="space-y-1">
							<h2
								className={cn(
									"font-semibold text-3xl",
									zywhFont.className,
									"leading-none",
									"tracking-[0.05em]",
									"relative",
									"after:absolute after:inset-0",
									"after:bg-[radial-gradient(circle_at_50%_50%,rgba(212,180,133,0.15),transparent_70%)]",
									"after:blur-xl after:-z-10",
								)}
								style={{
									background:
										"linear-gradient(135deg, rgba(229,201,165,0.95) 0%, rgba(212,180,133,0.9) 50%, rgba(176,137,104,0.85) 100%)",
									WebkitBackgroundClip: "text",
									WebkitTextFillColor: "transparent",
									backgroundClip: "text",
									textShadow:
										"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.1)",
									filter: "contrast(1.1) brightness(1.05)",
								}}
							>
								名额分配
								<span className="ml-3 font-normal text-base text-[#D4B485]/80 tracking-wide">
									三步分配，即刻生效
								</span>
							</h2>
							<p className="text-sm leading-relaxed text-white/40 group-hover:text-white/60 transition-colors duration-300">
								为代理商分配团队名额，支持灵活配置
							</p>
						</div>
					</div>

					{/* 进度条 */}
					<div className="flex-1 max-w-[600px] px-8">
						<AllocationProgress
							steps={STEPS}
							currentStep={currentStep}
						/>
					</div>
				</div>
			</motion.div>

			{/* 主要内容区域 */}
			<motion.div
				initial={{ opacity: 0, y: 40 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.3 }}
				className="mt-6"
			>
				<div
					className={cn(
						"rounded-xl",
						"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
						"border border-[#D4B485]/20",
						"p-8",
						"shadow-[0_8px_32px_-4px_rgba(0,0,0,0.3)]",
						"backdrop-blur-xl",
					)}
				>
					{renderStepContent()}
				</div>
			</motion.div>
		</motion.div>
	);
}
