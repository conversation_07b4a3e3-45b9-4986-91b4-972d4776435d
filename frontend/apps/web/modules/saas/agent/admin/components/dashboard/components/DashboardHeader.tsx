"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";
import { Calendar, Download } from "lucide-react";

export interface DashboardHeaderProps {
	title: string;
	dateRange?: string;
}

export function DashboardHeader({
	title,
	dateRange = "2024-01-01 至 2024-12-31",
}: DashboardHeaderProps) {
	return (
		<div className="flex items-center justify-between">
			<h1
				className={cn("text-2xl font-bold", zywhFont.className)}
				style={{
					background:
						"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
					WebkitBackgroundClip: "text",
					WebkitTextFillColor: "transparent",
					backgroundClip: "text",
					textShadow:
						"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
				}}
			>
				{title}
			</h1>
			<div className="flex items-center gap-4">
				<div className="flex items-center gap-2">
					<Calendar className="h-4 w-4 text-[#D4B485]/60" />
					<span className="text-[#D4B485]/60">{dateRange}</span>
				</div>
				<Button
					variant="outline"
					className={cn(
						"gap-2",
						"bg-[#1A1C1E]",
						"border-[#D4B485]/20",
						"text-[#D4B485]",
						"hover:bg-[#D4B485]/10",
					)}
				>
					<Download className="h-4 w-4" />
					<span>导出数据</span>
				</Button>
			</div>
		</div>
	);
}
