import type { <PERSON><PERSON><PERSON> } from "@prisma/client";
import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import { cn } from "@ui/lib";
import { format } from "date-fns";
import { Calendar, ClipboardList, FileText, Users } from "lucide-react";
import {
	AgentRoleColors,
	AgentRoleLabels,
	OrderStatusColors,
	OrderStatusLabels,
} from "../config/records-config";
import type { QuotaRecord } from "../hooks/use-records";

interface RecordDetailDialogProps {
	record: QuotaRecord | null;
	open: boolean;
	onOpenChange: (open: boolean) => void;
}

export function RecordDetailDialog({
	record,
	open,
	onOpenChange,
}: RecordDetailDialogProps) {
	if (!record) {
		return null;
	}

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent
				className={cn(
					"max-w-2xl",
					"bg-[#1A1C1E]",
					"border-[#D4B485]/20",
				)}
			>
				<DialogHeader>
					<DialogTitle
						className={cn("text-[#D4B485]", zywhFont.className)}
					>
						名额分配详情
					</DialogTitle>
				</DialogHeader>

				<div className="space-y-6">
					{/* 记录ID和状态 */}
					<div className="flex items-center justify-between">
						<div className="flex items-center gap-2">
							<ClipboardList className="h-5 w-5 text-[#D4B485]/60" />
							<div className="text-sm text-[#D4B485]/60">
								记录ID:
							</div>
							<div className="font-mono text-sm text-[#D4B485]">
								{record.id}
							</div>
						</div>
						<div
							className={cn(
								"inline-flex items-center rounded-full px-3 py-1",
								"text-sm font-medium",
								"shadow-[0_2px_4px_rgba(0,0,0,0.1)]",
								"border border-current/20",
								OrderStatusColors[record.status],
							)}
						>
							{OrderStatusLabels[record.status]}
						</div>
					</div>

					{/* 代理商信息 */}
					<div className="rounded-lg bg-[#1E2023]/50 p-4">
						<div className="mb-3 flex items-center gap-2">
							<Users className="h-5 w-5 text-[#D4B485]/60" />
							<div className="text-base font-medium text-[#D4B485]">
								代理商信息
							</div>
						</div>
						<div className="grid grid-cols-2 gap-6">
							{/* 来源代理商 */}
							<div className="space-y-2">
								<div className="text-sm text-[#D4B485]/60">
									来源代理商
								</div>
								<div className="flex items-center justify-between rounded-lg bg-[#D4B485]/5 p-3">
									<div>
										<div className="font-medium text-[#D4B485]">
											{record.fromAgentName}
										</div>
										<div className="mt-1 text-sm text-[#D4B485]/60">
											{record.fromAgentId}
										</div>
									</div>
									<div
										className={cn(
											"rounded-full px-2.5 py-0.5 text-xs font-medium",
											"border border-current/20",
											AgentRoleColors[
												record.fromAgentRole as AgentRole
											],
										)}
									>
										{
											AgentRoleLabels[
												record.fromAgentRole as AgentRole
											]
										}
									</div>
								</div>
							</div>

							{/* 目标代理商 */}
							<div className="space-y-2">
								<div className="text-sm text-[#D4B485]/60">
									目标代理商
								</div>
								<div className="flex items-center justify-between rounded-lg bg-[#D4B485]/5 p-3">
									<div>
										<div className="font-medium text-[#D4B485]">
											{record.toAgentName}
										</div>
										<div className="mt-1 text-sm text-[#D4B485]/60">
											{record.toAgentId}
										</div>
									</div>
									<div
										className={cn(
											"rounded-full px-2.5 py-0.5 text-xs font-medium",
											"border border-current/20",
											AgentRoleColors[
												record.toAgentRole as AgentRole
											],
										)}
									>
										{
											AgentRoleLabels[
												record.toAgentRole as AgentRole
											]
										}
									</div>
								</div>
							</div>
						</div>
					</div>

					{/* 交易信息 */}
					<div className="rounded-lg bg-[#1E2023]/50 p-4">
						<div className="mb-3 flex items-center gap-2">
							<FileText className="h-5 w-5 text-[#D4B485]/60" />
							<div className="text-base font-medium text-[#D4B485]">
								交易信息
							</div>
						</div>
						<div className="grid grid-cols-3 gap-4">
							<div className="space-y-1">
								<div className="text-sm text-[#D4B485]/60">
									分配数量
								</div>
								<div className="flex items-baseline gap-1">
									<div className="text-lg font-medium text-[#D4B485]">
										{record.quantity.toLocaleString()}
									</div>
									<div className="text-sm text-[#D4B485]/60">
										个
									</div>
								</div>
							</div>
							{record.unitPrice && (
								<div className="space-y-1">
									<div className="text-sm text-[#D4B485]/60">
										单价
									</div>
									<div className="flex items-baseline gap-1">
										<div className="text-lg font-medium text-[#D4B485]">
											¥{record.unitPrice.toLocaleString()}
										</div>
										<div className="text-sm text-[#D4B485]/60">
											/个
										</div>
									</div>
								</div>
							)}
							{record.amount && (
								<div className="space-y-1">
									<div className="text-sm text-[#D4B485]/60">
										实付金额
									</div>
									<div className="text-lg font-medium text-[#D4B485]">
										¥{record.amount.toLocaleString()}
									</div>
								</div>
							)}
						</div>
					</div>

					{/* 备注信息 */}
					{(record.remark ||
						record.tradeRemark ||
						record.adminRemark) && (
						<div className="rounded-lg bg-[#1E2023]/50 p-4">
							<div className="mb-3 flex items-center gap-2">
								<FileText className="h-5 w-5 text-[#D4B485]/60" />
								<div className="text-base font-medium text-[#D4B485]">
									备注信息
								</div>
							</div>
							<div className="space-y-4">
								{record.remark && (
									<div className="rounded-lg bg-[#D4B485]/5 p-3">
										<div className="mb-1 text-sm text-[#D4B485]/60">
											主要备注
										</div>
										<div className="text-sm text-[#D4B485]">
											{record.remark}
										</div>
									</div>
								)}
								{record.tradeRemark && (
									<div className="rounded-lg bg-[#D4B485]/5 p-3">
										<div className="mb-1 text-sm text-[#D4B485]/60">
											交易备注
										</div>
										<div className="text-sm text-[#D4B485]">
											{record.tradeRemark}
										</div>
									</div>
								)}
								{record.adminRemark && (
									<div className="rounded-lg bg-[#D4B485]/5 p-3">
										<div className="mb-1 text-sm text-[#D4B485]/60">
											管理员备注
										</div>
										<div className="text-sm text-[#D4B485]">
											{record.adminRemark}
										</div>
									</div>
								)}
							</div>
						</div>
					)}

					{/* 时间信息 */}
					<div className="rounded-lg bg-[#1E2023]/50 p-4">
						<div className="mb-3 flex items-center gap-2">
							<Calendar className="h-5 w-5 text-[#D4B485]/60" />
							<div className="text-base font-medium text-[#D4B485]">
								时间信息
							</div>
						</div>
						<div className="grid grid-cols-2 gap-4">
							<div className="space-y-1">
								<div className="text-sm text-[#D4B485]/60">
									创建时间
								</div>
								<div className="text-sm text-[#D4B485]">
									{format(
										new Date(record.createdAt),
										"yyyy-MM-dd HH:mm:ss",
									)}
								</div>
							</div>
							{record.completedAt && (
								<div className="space-y-1">
									<div className="text-sm text-[#D4B485]/60">
										完成时间
									</div>
									<div className="text-sm text-[#D4B485]">
										{format(
											new Date(record.completedAt),
											"yyyy-MM-dd HH:mm:ss",
										)}
									</div>
								</div>
							)}
						</div>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
}
