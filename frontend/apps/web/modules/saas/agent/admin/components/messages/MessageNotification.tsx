"use client";

import { usePathname } from "next/navigation";
import { useEffect, useRef } from "react";
import { useMessages } from "./hooks/useMessages";
import { UnreadMessageToast } from "./UnreadMessageToast";

/**
 * 消息通知组件
 * 用于显示未读消息提示和管理未读消息状态
 * 应该被嵌入到应用布局中，以便在任何页面都能显示消息提示
 */
export function MessageNotification() {
	const {
		unreadMessages,
		unreadCount,
		showUnreadToast,
		closeUnreadToast,
		fetchUnreadMessages,
	} = useMessages();

	// 获取当前路径，用于检测路由变化
	const pathname = usePathname();

	// 引用上次未读消息数量和路径，用于判断是否有新消息或路由变化
	const prevUnreadCountRef = useRef(unreadCount);
	const prevPathnameRef = useRef(pathname);

	// 当路由变化时，检查未读消息
	useEffect(() => {
		// 如果路径发生变化，则获取最新未读消息
		if (pathname !== prevPathnameRef.current) {
			fetchUnreadMessages();
			prevPathnameRef.current = pathname;
		}
	}, [pathname, fetchUnreadMessages]);

	// 当未读消息数量变化时，检查是否需要显示提示
	useEffect(() => {
		// 如果之前没有未读消息，但现在有了，说明有新消息
		if (prevUnreadCountRef.current === 0 && unreadCount > 0) {
			// 可以在这里播放提示音效或其他通知效果
		}

		// 更新之前的未读消息数量
		prevUnreadCountRef.current = unreadCount;
	}, [unreadCount]);

	// 轮询获取未读消息（兜底机制，即使路由没变化也会定期检查）
	useEffect(() => {
		// 初始加载
		fetchUnreadMessages();

		// 设置轮询间隔，每3分钟检查一次
		const intervalId = setInterval(() => {
			fetchUnreadMessages();
		}, 180000);

		return () => clearInterval(intervalId);
	}, [fetchUnreadMessages]);

	// 只有在显示提示时才渲染Toast组件
	if (!showUnreadToast || unreadMessages.length === 0) {
		return null;
	}

	return (
		<UnreadMessageToast
			messages={unreadMessages}
			totalCount={unreadCount}
			onClose={closeUnreadToast}
		/>
	);
}
