"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { ClipboardListIcon } from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";
import { RecordsFilter } from "./components/RecordsFilter";
import { RecordsList } from "./components/RecordsList";
import type { RecordsQuery } from "./hooks/use-records";
import { useRecords } from "./hooks/use-records";

export function RecordsView() {
	const isFirstRender = useRef(true);
	const [query, setQuery] = useState<RecordsQuery>({
		page: 1,
		pageSize: 10,
	});

	const { loading, records, total, fetchRecords } = useRecords();

	// 处理过滤
	const handleFilter = useCallback(
		(newQuery: RecordsQuery) => {
			const updatedQuery = {
				...newQuery,
				page: 1, // 重置页码
			};
			setQuery(updatedQuery);
			fetchRecords(updatedQuery);
		},
		[fetchRecords],
	);

	// 处理分页
	const handlePageChange = useCallback(
		(page: number) => {
			const newQuery = { ...query, page };
			setQuery(newQuery);
			fetchRecords(newQuery);
		},
		[query, fetchRecords],
	);

	// 初始化加载
	useEffect(() => {
		if (isFirstRender.current) {
			isFirstRender.current = false;
			fetchRecords(query);
		}
	}, [fetchRecords, query]);

	return (
		<div className="space-y-6 pt-6">
			{/* 标题部分 */}
			<motion.div
				className="flex items-center gap-4"
				initial={{ opacity: 0, x: -20 }}
				animate={{ opacity: 1, x: 0 }}
				transition={{ duration: 0.3, ease: "easeOut" }}
			>
				<motion.div
					whileHover={{ scale: 1.05 }}
					whileTap={{ scale: 0.95 }}
					transition={{ type: "spring", stiffness: 400, damping: 17 }}
					className={cn(
						"flex h-14 w-14 shrink-0 items-center justify-center",
						"rounded-xl bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
						"ring-1 ring-[#D4B485]/20",
						"shadow-[0_4px_12px_-2px_rgba(212,180,133,0.2)]",
						"group-hover:from-[#D4B485]/15 group-hover:to-[#D4B485]/10",
						"group-hover:ring-[#D4B485]/30",
						"transition-all duration-200",
						"transform-gpu",
					)}
				>
					<ClipboardListIcon className="h-7 w-7 text-[#D4B485]" />
				</motion.div>
				<div className="space-y-1">
					<motion.h2
						initial={{ opacity: 0, y: -10 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ delay: 0.1, duration: 0.2 }}
						className={cn(
							"font-semibold text-3xl",
							zywhFont.className,
							"leading-none",
							"tracking-[0.05em]",
						)}
						style={{
							background:
								"linear-gradient(135deg, rgba(229,201,165,0.95) 0%, rgba(212,180,133,0.9) 50%, rgba(176,137,104,0.85) 100%)",
							WebkitBackgroundClip: "text",
							WebkitTextFillColor: "transparent",
							backgroundClip: "text",
							textShadow:
								"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
							filter: "contrast(1.1) brightness(1.05)",
						}}
					>
						名额分配记录
						<motion.span
							initial={{ opacity: 0, x: -10 }}
							animate={{ opacity: 1, x: 0 }}
							transition={{ delay: 0.2, duration: 0.2 }}
							className="ml-3 font-normal text-base text-[#D4B485]/80 tracking-wide"
						>
							追踪每一笔分配，掌控超级个体名额
						</motion.span>
					</motion.h2>
					<motion.p
						initial={{ opacity: 0, y: 10 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ delay: 0.3, duration: 0.2 }}
						className="text-sm leading-relaxed text-[#D4B485]/60"
					>
						查看和管理代理商的名额分配记录，包括分配数量、状态和时间等信息
					</motion.p>
				</div>
			</motion.div>

			<div className="space-y-4">
				{/* 过滤器 */}
				<RecordsFilter onFilter={handleFilter} defaultValues={query} />

				{/* 记录列表 */}
				<RecordsList
					loading={loading}
					records={records}
					total={total}
					page={query.page}
					pageSize={query.pageSize}
					onPageChange={handlePageChange}
				/>
			</div>
		</div>
	);
}
