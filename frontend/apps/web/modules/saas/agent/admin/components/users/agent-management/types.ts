import type { AgentDetail } from "./hooks/use-agent-detail";

/**
 * API错误响应
 */
export interface ApiErrorResponse {
	code: number;
	error: string;
	message: string;
}

/**
 * 成功响应 - 创建/更新代理商
 */
export interface ApiAgentResponse {
	agent: AgentDetail;
}

/**
 * 成功响应 - 删除代理商
 */
export interface ApiDeleteResponse {
	success: boolean;
}

/**
 * API响应类型联合
 */
export type ApiResponse =
	| ApiErrorResponse
	| ApiAgentResponse
	| ApiDeleteResponse;

/**
 * API调用结果 - 成功
 */
export interface ApiSuccessResult<_T> {
	success: true;
	agent?: AgentDetail;
	[key: string]: any;
}

/**
 * API调用结果 - 失败
 */
export interface ApiErrorResult {
	success: false;
	error: string;
	errorCode?: string;
}

/**
 * API调用结果联合类型
 */
export type ApiResult<T = any> = ApiSuccessResult<T> | ApiErrorResult;

/**
 * 用于辅助检查响应类型的类型守卫函数
 */
export function isErrorResponse(response: any): response is ApiErrorResponse {
	return (
		response &&
		typeof response === "object" &&
		"error" in response &&
		"code" in response &&
		"message" in response
	);
}

export function isAgentResponse(response: any): response is ApiAgentResponse {
	return (
		response &&
		typeof response === "object" &&
		"agent" in response &&
		response.agent !== undefined
	);
}

export function isDeleteResponse(response: any): response is ApiDeleteResponse {
	return (
		response &&
		typeof response === "object" &&
		"success" in response &&
		typeof response.success === "boolean"
	);
}
