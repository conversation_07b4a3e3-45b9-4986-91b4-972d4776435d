"use client";

import { But<PERSON> } from "@ui/components/button";
import { Card } from "@ui/components/card";
import { Input } from "@ui/components/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { cn } from "@ui/lib";
import { Calendar, Filter, Search } from "lucide-react";
import { useState } from "react";
import type { ActivationRecordsQuery } from "../hooks";

interface ActivationFiltersProps {
	onFiltersChange: (filters: Partial<ActivationRecordsQuery>) => void;
	loading?: boolean;
}

export function ActivationFilters({
	onFiltersChange,
	loading,
}: ActivationFiltersProps) {
	const [searchTerm, setSearchTerm] = useState("");
	const [activationStatus, setActivationStatus] = useState<string>("ALL");
	const [planLevel, setPlanLevel] = useState<string>("ALL");
	const [activationMethod, setActivationMethod] = useState<string>("ALL");

	const handleSearchChange = (value: string) => {
		setSearchTerm(value);
		onFiltersChange({ searchTerm: value });
	};

	const handleActivationStatusChange = (value: string) => {
		setActivationStatus(value);
		onFiltersChange({ activationStatus: value as any });
	};

	const handlePlanLevelChange = (value: string) => {
		setPlanLevel(value);
		onFiltersChange({ planLevel: value as any });
	};

	const handleActivationMethodChange = (value: string) => {
		setActivationMethod(value);
		onFiltersChange({ activationMethod: value as any });
	};

	const handleDateRangeChange = () => {
		// TODO: 实现日期范围选择
		// console.log("Date range change");
	};

	return (
		<Card
			className={cn(
				"p-4",
				"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
				"border border-[#D4B485]/20",
				"group",
				"hover:border-[#D4B485]/40",
				"transition-all duration-500",
			)}
		>
			<div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-5">
				{/* 搜索框 */}
				<div className="relative">
					<Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-[#D4B485]/40" />
					<Input
						placeholder="搜索消费者姓名或手机号"
						value={searchTerm}
						onChange={(e) => handleSearchChange(e.target.value)}
						disabled={loading}
						className={cn(
							"pl-10",
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"placeholder:text-[#D4B485]/40",
							"focus:border-[#D4B485]/60",
						)}
					/>
				</div>

				{/* 激活状态筛选 */}
				<Select
					value={activationStatus}
					onValueChange={handleActivationStatusChange}
					disabled={loading}
				>
					<SelectTrigger
						className={cn(
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"focus:border-[#D4B485]/60",
						)}
					>
						<SelectValue placeholder="激活状态" />
					</SelectTrigger>
					<SelectContent className="bg-[#1A1C1E] border-[#D4B485]/20">
						<SelectItem value="ALL">全部状态</SelectItem>
						<SelectItem value="SUCCESS">激活成功</SelectItem>
						<SelectItem value="FAILED">激活失败</SelectItem>
					</SelectContent>
				</Select>

				{/* 套餐等级筛选 */}
				<Select
					value={planLevel}
					onValueChange={handlePlanLevelChange}
					disabled={loading}
				>
					<SelectTrigger
						className={cn(
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"focus:border-[#D4B485]/60",
						)}
					>
						<SelectValue placeholder="套餐等级" />
					</SelectTrigger>
					<SelectContent className="bg-[#1A1C1E] border-[#D4B485]/20">
						<SelectItem value="ALL">全部等级</SelectItem>
						<SelectItem value="TRIAL">体验版</SelectItem>
						<SelectItem value="BASIC">基础版</SelectItem>
						<SelectItem value="ENHANCED">增强版</SelectItem>
						<SelectItem value="ENTERPRISE">企业版</SelectItem>
						<SelectItem value="PREMIUM">招财进宝版</SelectItem>
					</SelectContent>
				</Select>

				{/* 激活方式筛选 */}
				<Select
					value={activationMethod}
					onValueChange={handleActivationMethodChange}
					disabled={loading}
				>
					<SelectTrigger
						className={cn(
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"focus:border-[#D4B485]/60",
						)}
					>
						<SelectValue placeholder="激活方式" />
					</SelectTrigger>
					<SelectContent className="bg-[#1A1C1E] border-[#D4B485]/20">
						<SelectItem value="ALL">全部方式</SelectItem>
						<SelectItem value="ADMIN">管理员激活</SelectItem>
						<SelectItem value="AGENT">代理商激活</SelectItem>
						<SelectItem value="AUTO">系统自动激活</SelectItem>
					</SelectContent>
				</Select>

				{/* 日期范围和高级筛选 */}
				<div className="flex gap-2">
					<Button
						variant="outline"
						size="sm"
						onClick={handleDateRangeChange}
						disabled={loading}
						className={cn(
							"flex-1 gap-2",
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"hover:bg-[#D4B485]/10",
						)}
					>
						<Calendar className="h-4 w-4" />
						<span className="hidden sm:inline">日期</span>
					</Button>
					<Button
						variant="outline"
						size="sm"
						disabled={loading}
						className={cn(
							"gap-2",
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"hover:bg-[#D4B485]/10",
						)}
					>
						<Filter className="h-4 w-4" />
						<span className="hidden sm:inline">高级</span>
					</Button>
				</div>
			</div>
		</Card>
	);
}
