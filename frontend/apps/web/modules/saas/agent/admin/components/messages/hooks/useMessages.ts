// Import MessageType and MessageStatus from the canonical source
import {
	MessageStatus,
	type MessageType,
} from "@repo/api/src/routes/v1/agent/messages/types";
import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";

// 获取消息列表参数
export interface GetMessagesParams {
	page?: number;
	limit?: number;
	type?: MessageType;
	status?: MessageStatus;
}

// 消息列表响应
export interface MessagesResponse {
	messages: Array<{
		id: string;
		userId: string;
		title: string;
		content: string;
		type: MessageType;
		routeLink?: string; // 跳转路由链接(可选)
		status: MessageStatus;
		readAt: string | null;
		createdAt: string;
		updatedAt: string;
	}>;
	total: number;
	page: number;
	limit: number;
}

// 未读消息类型
export interface UnreadMessage {
	id: string;
	title: string;
}

/**
 * 管理员消息列表hook
 * 用于获取管理员消息中心的消息列表和未读消息提示
 */
export function useMessages() {
	const [loading, setLoading] = useState(false);
	const [messages, setMessages] = useState<MessagesResponse["messages"]>([]);
	const [unreadMessages, setUnreadMessages] = useState<UnreadMessage[]>([]);
	const [showUnreadToast, setShowUnreadToast] = useState(false);
	const [total, setTotal] = useState(0);
	const [unreadCount, setUnreadCount] = useState(0);
	const [page, setPage] = useState(1);
	const [limit, setLimit] = useState(10);

	// 上次检查时间
	const [lastCheckTime, setLastCheckTime] = useState(Date.now());

	/**
	 * 获取消息列表
	 */
	const fetchMessages = useCallback(
		async (params: GetMessagesParams = {}) => {
			setLoading(true);
			try {
				// logger.info("开始获取管理员消息列表", { params });

				// 注意这里使用管理员的API路径
				const response = await apiClient.v1.agent.messages.list.$get({
					query: params,
				});

				const result = (await response.json()) as MessagesResponse;

				// logger.info("管理员消息列表响应:", result);

				if (response.ok) {
					setMessages(result.messages);
					setTotal(result.total);
					setPage(result.page);
					setLimit(result.limit);
					return result;
				}
				throw new Error("获取管理员消息列表失败");
			} catch (error) {
				// logger.error("获取管理员消息列表失败", { error });
				toast.error("获取管理员消息列表失败", {
					description:
						error instanceof Error ? error.message : "请重试",
				});
				return {
					messages: [],
					total: 0,
					page: 1,
					limit: 10,
				};
			} finally {
				setLoading(false);
			}
		},
		[],
	);

	/**
	 * 获取未读消息列表
	 * 当页面变化或定时轮询时会触发此函数
	 */
	const fetchUnreadMessages = useCallback(async () => {
		try {
			// 记录本次检查时间
			const currentTime = Date.now();
			const timeSinceLastCheck = currentTime - lastCheckTime;

			// 限制检查频率，至少间隔5秒
			if (timeSinceLastCheck < 5000) {
				// logger.info("检查频率过高，跳过此次检查", {
				// 	timeSinceLastCheck,
				// 	lastCheckTime: new Date().toLocaleString(),
				// });
				return [];
			}

			// logger.info("开始获取管理员未读消息列表");
			setLastCheckTime(currentTime);

			// 使用status参数获取未读消息
			const response = await apiClient.v1.agent.messages.list.$get({
				query: { status: MessageStatus.UNREAD, limit: 10 },
			});

			const result = (await response.json()) as MessagesResponse;

			// logger.info("管理员未读消息列表响应:", result);

			if (response.ok) {
				const unreadList = result.messages.map((msg) => ({
					id: msg.id,
					title: msg.title,
				}));
				setUnreadMessages(unreadList);
				setUnreadCount(result.total);

				// 如果有未读消息且数量变化，显示提示
				if (result.total > 0) {
					setShowUnreadToast(true);
				}

				return unreadList;
			}
			throw new Error("获取管理员未读消息列表失败");
		} catch (_error) {
			// logger.error("获取管理员未读消息列表失败", { error });
			return [];
		}
	}, [lastCheckTime]);

	// 关闭未读消息提示
	const closeUnreadToast = useCallback(() => {
		setShowUnreadToast(false);
	}, []);

	// 从未读消息列表中移除单个消息
	const removeMessageFromUnread = useCallback((messageId: string) => {
		setUnreadMessages((prev) => prev.filter((msg) => msg.id !== messageId));
		setUnreadCount((prev) => Math.max(0, prev - 1));
		// logger.info("从未读消息列表中移除消息", { messageId });
	}, []);

	// 从未读消息列表中移除多个消息
	const removeMessagesFromUnread = useCallback((messageIds: string[]) => {
		setUnreadMessages((prev) =>
			prev.filter((msg) => !messageIds.includes(msg.id)),
		);
		setUnreadCount((prev) => Math.max(0, prev - messageIds.length));
		// logger.info("从未读消息列表中批量移除消息", { messageIds });
	}, []);

	return {
		// 消息列表数据
		loading,
		messages,
		total,
		page,
		limit,
		fetchMessages,

		// 未读消息数据
		unreadMessages,
		unreadCount,
		showUnreadToast,
		closeUnreadToast,
		fetchUnreadMessages,
		removeMessageFromUnread,
		removeMessagesFromUnread,
	};
}
