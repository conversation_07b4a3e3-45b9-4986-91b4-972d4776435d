// IP地理位置信息接口
export interface IPLocationInfo {
	/** 地理位置描述 */
	location: string;
	/** 是否为内网IP */
	isPrivate: boolean;
	/** 位置类型 */
	locationType: "city" | "province" | "country" | "private" | "unknown";
	/** 可信度（0-1） */
	confidence: number;
	/** 数据源 */
	dataSource: string;
	/** 国家代码（可选） */
	countryCode?: string;
	/** 省份/州代码（可选） */
	regionCode?: string;
	/** 纬度（可选） */
	latitude?: number;
	/** 经度（可选） */
	longitude?: number;
	/** 时区（可选） */
	timezone?: string;
	/** ISP信息（可选） */
	isp?: string;
}

// 用户资料接口
export interface UserProfile {
	id: string;
	name: string;
	avatar: string | null;
	phone: string;
	computingPower: number;
	createdAt: string;
	lastLoginIp: string | null;
	/** IP地理位置信息 */
	lastLoginLocation: IPLocationInfo | null;
	currentPlanName: string | null;
	currentPlanLevel: string | null;
	planStartAt: string | null;
	planExpireAt: string | null;
	planStatus: "active" | "expired" | "none";
}

// 套餐接口
export interface Plan {
	id: string;
	name: string;
	level: string;
	computingPower: number;
	validityDays: number;
	price: number;
	discountPrice: number;
	description?: string;
	features?: Array<{
		type: string;
		enabled: boolean;
		description?: string;
	}>;
}

// 激活结果接口
export interface ActivationResult {
	activationId: string;
	user: {
		id: string;
		name: string;
		phone: string;
	};
	plan: {
		id: string;
		name: string;
		computingPower: number;
		validityDays: number;
	};
	quantity: number;
	addedComputingPower: number;
	activatedAt: string;
	expireAt: string;
}

// API响应接口
export interface ApiResponse<T = unknown> {
	code: number;
	message?: string;
	error?: string;
	data?: T;
}

// 错误类型
export interface ApiError {
	code: number;
	message: string;
	error?: string;
	details?: unknown;
}
