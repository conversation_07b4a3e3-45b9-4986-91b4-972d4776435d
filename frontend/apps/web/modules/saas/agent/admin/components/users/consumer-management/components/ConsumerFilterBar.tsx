"use client";

import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { cn } from "@ui/lib";
import { SearchIcon, XCircleIcon } from "lucide-react";
import { useEffect, useId, useState } from "react";

export interface ConsumerFilterOptions {
	searchTerm: string;
	role: string;
	status: string;
}

export interface ConsumerFilterBarProps {
	onChange?: (filters: ConsumerFilterOptions) => void;
}

const defaultFilters: ConsumerFilterOptions = {
	searchTerm: "",
	role: "ALL",
	status: "ALL",
};

export function ConsumerFilterBar({ onChange }: ConsumerFilterBarProps) {
	const [filters, setFilters] =
		useState<ConsumerFilterOptions>(defaultFilters);
	const id = useId();

	// 当筛选条件变化时触发onChange回调
	useEffect(() => {
		onChange?.(filters);
	}, [filters, onChange]);

	// 更新搜索词
	const updateSearchTerm = (value: string) => {
		setFilters((prev) => ({ ...prev, searchTerm: value }));
	};

	// 更新角色筛选
	const updateRole = (value: string) => {
		setFilters((prev) => ({ ...prev, role: value }));
	};

	// 更新状态筛选
	const updateStatus = (value: string) => {
		setFilters((prev) => ({ ...prev, status: value }));
	};

	// 重置所有筛选
	const resetFilters = () => {
		setFilters(defaultFilters);
	};

	return (
		<div className="space-y-4">
			<div className="flex flex-wrap gap-4">
				<div className="flex-1 min-w-[240px]">
					<Label
						htmlFor={`${id}-search`}
						className="mb-2 block text-[#D4B485]/60 text-sm"
					>
						搜索
					</Label>
					<div className="relative">
						<SearchIcon className="absolute left-3 top-2.5 h-4 w-4 text-[#D4B485]/40" />
						<Input
							id={`${id}-search`}
							placeholder="搜索名称、邮箱或电话"
							value={filters.searchTerm}
							onChange={(e) => updateSearchTerm(e.target.value)}
							className={cn(
								"pl-9",
								"bg-[#1A1C1E]",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
								"placeholder:text-[#D4B485]/40",
								"focus-visible:ring-[#D4B485]/40",
							)}
						/>
						{filters.searchTerm && (
							<button
								type="button"
								onClick={() => updateSearchTerm("")}
								className="absolute right-3 top-2.5"
							>
								<XCircleIcon className="h-4 w-4 text-[#D4B485]/40" />
							</button>
						)}
					</div>
				</div>

				<div className="w-[180px]">
					<Label
						htmlFor={`${id}-role`}
						className="mb-2 block text-[#D4B485]/60 text-sm"
					>
						角色
					</Label>
					<Select value={filters.role} onValueChange={updateRole}>
						<SelectTrigger
							id={`${id}-role`}
							className={cn(
								"bg-[#1A1C1E]",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
								"focus:ring-[#D4B485]/40",
							)}
						>
							<SelectValue placeholder="选择角色" />
						</SelectTrigger>
						<SelectContent
							className={cn(
								"bg-[#1A1C1E]",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
							)}
						>
							<SelectItem value="ALL">全部角色</SelectItem>
							<SelectItem value="INTERNAL">内部测试</SelectItem>
							<SelectItem value="REGULAR">普通用户</SelectItem>
						</SelectContent>
					</Select>
				</div>

				<div className="w-[180px]">
					<Label
						htmlFor={`${id}-status`}
						className="mb-2 block text-[#D4B485]/60 text-sm"
					>
						状态
					</Label>
					<Select value={filters.status} onValueChange={updateStatus}>
						<SelectTrigger
							id={`${id}-status`}
							className={cn(
								"bg-[#1A1C1E]",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
								"focus:ring-[#D4B485]/40",
							)}
						>
							<SelectValue placeholder="选择状态" />
						</SelectTrigger>
						<SelectContent
							className={cn(
								"bg-[#1A1C1E]",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
							)}
						>
							<SelectItem value="ALL">全部状态</SelectItem>
							<SelectItem value="ACTIVE">正常</SelectItem>
							<SelectItem value="DISABLED">禁用</SelectItem>
							<SelectItem value="PENDING">待审核</SelectItem>
						</SelectContent>
					</Select>
				</div>

				<div className="flex items-end">
					<Button
						variant="outline"
						className={cn(
							"h-10",
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"hover:bg-[#D4B485]/10",
						)}
						onClick={resetFilters}
					>
						重置筛选
					</Button>
				</div>
			</div>
		</div>
	);
}
