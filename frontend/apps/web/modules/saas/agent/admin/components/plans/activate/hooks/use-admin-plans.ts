"use client";

import { useQuery } from "@tanstack/react-query";
import type { ApiError, ApiResponse, Plan } from "../types";

export function useAdminPlans() {
	const {
		data: plans = [],
		isLoading,
		error,
	} = useQuery<Plan[], ApiError>({
		queryKey: ["admin-plans"],
		queryFn: async () => {
			const response = await fetch("/api/v1/agent/admin/plans/list");
			const data: ApiResponse<{ items: Plan[]; total: number }> =
				await response.json();

			// 检查业务错误
			if (data.code !== 200) {
				const error: ApiError = {
					code: data.code,
					message: data.message || "获取套餐列表失败",
					error: data.error,
					details: data.data,
				};
				throw error;
			}

			return data.data?.items || [];
		},
		staleTime: 5 * 60 * 1000, // 5分钟缓存
		retry: 2,
	});

	return {
		plans,
		isLoading,
		error: error as ApiError | null,
	};
}
