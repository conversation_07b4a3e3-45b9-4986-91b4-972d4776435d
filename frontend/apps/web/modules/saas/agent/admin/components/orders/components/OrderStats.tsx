"use client";

import { Card } from "@ui/components/card";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { DollarSign, Package, TrendingUp } from "lucide-react";

interface OrderStatsProps {
	stats: {
		totalAmount: number;
		totalOrders: number;
		successRate: number;
	} | null;
	loading?: boolean;
}

export function OrderStats({ stats, loading }: OrderStatsProps) {
	if (loading) {
		return (
			<div className="grid grid-cols-1 gap-4 md:grid-cols-3">
				{[1, 2, 3].map((i) => (
					<Card
						key={i}
						className={cn(
							"p-6",
							"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
							"border border-[#D4B485]/20",
						)}
					>
						<div className="animate-pulse">
							<div className="h-4 w-16 bg-[#D4B485]/20 rounded mb-2" />
							<div className="h-8 w-24 bg-[#D4B485]/20 rounded" />
						</div>
					</Card>
				))}
			</div>
		);
	}

	if (!stats) {
		return null;
	}

	const statsData = [
		{
			title: "总订单数",
			value: stats.totalOrders.toLocaleString(),
			icon: Package,
			color: "text-blue-500",
			bgColor: "bg-blue-500/10",
		},
		{
			title: "总金额",
			value: `¥${(stats.totalAmount / 100).toLocaleString()}`,
			icon: DollarSign,
			color: "text-emerald-500",
			bgColor: "bg-emerald-500/10",
		},
		{
			title: "成功率",
			value: `${stats.successRate.toFixed(1)}%`,
			icon: TrendingUp,
			color: "text-yellow-500",
			bgColor: "bg-yellow-500/10",
		},
	];

	return (
		<div className="grid grid-cols-1 gap-4 md:grid-cols-3">
			{statsData.map((stat, index) => (
				<motion.div
					key={stat.title}
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ delay: index * 0.1 }}
				>
					<Card
						className={cn(
							"p-6",
							"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
							"border border-[#D4B485]/20",
							"group",
							"hover:border-[#D4B485]/40",
							"transition-all duration-300",
							"relative overflow-hidden",
						)}
					>
						{/* 装饰性渐变 */}
						<div className="absolute inset-0 bg-gradient-to-r from-[#D4B485]/5 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

						<div className="relative flex items-center gap-4">
							<div
								className={cn(
									"flex h-12 w-12 items-center justify-center rounded-lg",
									stat.bgColor,
								)}
							>
								<stat.icon
									className={cn("h-6 w-6", stat.color)}
								/>
							</div>
							<div>
								<p className="text-sm text-[#D4B485]/60">
									{stat.title}
								</p>
								<p className="text-2xl font-bold text-[#D4B485]">
									{stat.value}
								</p>
							</div>
						</div>
					</Card>
				</motion.div>
			))}
		</div>
	);
}
