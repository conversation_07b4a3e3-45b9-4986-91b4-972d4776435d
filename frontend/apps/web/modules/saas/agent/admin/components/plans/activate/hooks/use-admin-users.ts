"use client";

import { useMutation } from "@tanstack/react-query";
import { useState } from "react";
import type { ApiError, ApiResponse, UserProfile } from "../types";

export function useAdminUsers() {
	const [users, setUsers] = useState<UserProfile[]>([]);

	const {
		mutate: searchUsers,
		isPending: isLoading,
		error,
	} = useMutation<
		ApiResponse<{ users: UserProfile[]; total: number; hasMore: boolean }>,
		ApiError,
		string
	>({
		mutationFn: async (phone: string) => {
			const response = await fetch(
				`/api/v1/agent/admin/plans/get-consumer?phone=${encodeURIComponent(phone)}`,
			);

			const data = await response.json();

			// 检查业务错误
			if (data.code !== 200) {
				const error: ApiError = {
					code: data.code,
					message: data.message || "搜索用户失败",
					error: data.error,
					details: data.data?.details,
				};
				throw error;
			}

			return data;
		},
		onSuccess: (data) => {
			setUsers(data.data?.users || []);
		},
		onError: (err: ApiError) => {
			console.error("搜索用户失败:", err);
			setUsers([]);
		},
	});

	return {
		users,
		isLoading,
		error: error as ApiError | null,
		searchUsers,
	};
}
