"use client";

import { useMutation } from "@tanstack/react-query";
import type { ActivationResult, ApiError, ApiResponse } from "../types";

interface ActivationPayload {
	userId: string;
	planId: string;
	quantity: number;
}

export function useAdminActivate() {
	const {
		mutate: activatePlan,
		isPending,
		error,
		reset,
	} = useMutation<ActivationResult, ApiError, ActivationPayload>({
		mutationFn: async (payload: ActivationPayload) => {
			const response = await fetch("/api/v1/agent/admin/plans/activate", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(payload),
			});

			const data: ApiResponse<ActivationResult> = await response.json();

			// 检查业务错误
			if (data.code !== 200) {
				const error: ApiError = {
					code: data.code,
					message: data.message || "套餐激活失败",
					error: data.error,
					details: data.data,
				};
				throw error;
			}

			if (!data.data) {
				throw {
					code: 500,
					message: "服务器返回数据格式错误",
				} as ApiError;
			}

			return data.data;
		},
		retry: 1, // 只重试一次
	});

	return {
		activatePlan,
		isPending,
		error: error as ApiError | null,
		reset,
	};
}
