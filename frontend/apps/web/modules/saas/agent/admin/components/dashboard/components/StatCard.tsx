"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import type { LucideIcon } from "lucide-react";

// 统计卡片组件
export interface StatCardProps {
	title: string;
	value: string | number;
	description: string;
	icon: LucideIcon;
	delay?: number;
}

export function StatCard({
	title,
	value,
	description,
	icon: Icon,
	delay = 0,
}: StatCardProps) {
	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ delay }}
			className={cn(
				"rounded-xl p-6",
				"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
				"border border-[#D4B485]/20",
				"group",
				"hover:border-[#D4B485]/40",
				"transition-all duration-500",
				"relative overflow-hidden",
			)}
		>
			{/* 背景装饰 */}
			<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
			<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />

			<div className="relative space-y-3">
				<div className="flex items-center justify-between">
					<h3 className="text-sm font-medium text-[#D4B485]/60">
						{title}
					</h3>
					<Icon className="h-4 w-4 text-[#D4B485]/40" />
				</div>

				<div>
					<div
						className={cn(
							"text-2xl font-semibold",
							zywhFont.className,
						)}
						style={{
							background:
								"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
							WebkitBackgroundClip: "text",
							WebkitTextFillColor: "transparent",
							backgroundClip: "text",
							textShadow:
								"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
						}}
					>
						{value}
					</div>
					<div className="mt-1 text-sm text-[#D4B485]/40">
						{description}
					</div>
				</div>
			</div>
		</motion.div>
	);
}
