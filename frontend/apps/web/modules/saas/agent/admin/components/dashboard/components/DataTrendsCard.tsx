"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Button } from "@ui/components/button";
import { Card } from "@ui/components/card";
import { cn } from "@ui/lib";
import { BarChart3 } from "lucide-react";

export interface DataTrendsCardProps {
	title?: string;
}

export function DataTrendsCard({ title = "数据趋势" }: DataTrendsCardProps) {
	return (
		<Card
			className={cn(
				"p-6",
				"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
				"border border-[#D4B485]/20",
				"group",
				"hover:border-[#D4B485]/40",
				"transition-all duration-500",
				"relative overflow-hidden",
			)}
		>
			{/* 背景装饰 */}
			<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
			<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />

			<div className="relative">
				<h2
					className={cn("text-lg font-semibold", zywhFont.className)}
					style={{
						background:
							"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
						WebkitBackgroundClip: "text",
						WebkitTextFillColor: "transparent",
						backgroundClip: "text",
						textShadow:
							"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
					}}
				>
					{title}
				</h2>
				<div className="mt-4 flex items-center gap-4">
					<Button
						variant="outline"
						size="sm"
						className={cn(
							"h-8 px-3",
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"hover:bg-[#D4B485]/10",
						)}
					>
						代理商增长
					</Button>
					<Button
						variant="outline"
						size="sm"
						className={cn(
							"h-8 px-3",
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"hover:bg-[#D4B485]/10",
						)}
					>
						订单趋势
					</Button>
					<Button
						variant="outline"
						size="sm"
						className={cn(
							"h-8 px-3",
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"hover:bg-[#D4B485]/10",
						)}
					>
						营收分析
					</Button>
				</div>
				<div className="mt-6 h-[400px]">
					{/* 这里可以添加趋势图表组件 */}
					<div className="flex h-full items-center justify-center text-[#D4B485]/40">
						<BarChart3 className="mr-2 h-5 w-5" />
						<span>图表加载中...</span>
					</div>
				</div>
			</div>
		</Card>
	);
}
