"use client";

import { But<PERSON> } from "@ui/components/button";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { cn } from "@ui/lib";
import { ChevronLeft, ChevronRight } from "lucide-react";

interface OrderPaginationProps {
	currentPage: number;
	pageSize: number;
	total: number;
	onPageChange: (page: number) => void;
	onPageSizeChange: (pageSize: number) => void;
	loading?: boolean;
}

export function OrderPagination({
	currentPage,
	pageSize,
	total,
	onPageChange,
	onPageSizeChange,
	loading,
}: OrderPaginationProps) {
	const totalPages = Math.ceil(total / pageSize);
	const startItem = (currentPage - 1) * pageSize + 1;
	const endItem = Math.min(currentPage * pageSize, total);

	const handlePrevious = () => {
		if (currentPage > 1) {
			onPageChange(currentPage - 1);
		}
	};

	const handleNext = () => {
		if (currentPage < totalPages) {
			onPageChange(currentPage + 1);
		}
	};

	const handlePageSizeChange = (value: string) => {
		onPageSizeChange(Number(value));
		onPageChange(1); // 重置到第一页
	};

	return (
		<div className="flex items-center justify-between">
			<div className="flex items-center gap-4">
				<p className="text-sm text-[#D4B485]/60">
					显示 {startItem}-{endItem} 条，共 {total} 条记录
				</p>
				<div className="flex items-center gap-2">
					<span className="text-sm text-[#D4B485]/60">每页显示</span>
					<Select
						value={pageSize.toString()}
						onValueChange={handlePageSizeChange}
						disabled={loading}
					>
						<SelectTrigger
							className={cn(
								"w-20",
								"bg-[#1A1C1E]",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
								"focus:border-[#D4B485]/60",
							)}
						>
							<SelectValue />
						</SelectTrigger>
						<SelectContent className="bg-[#1A1C1E] border-[#D4B485]/20">
							<SelectItem value="10">10</SelectItem>
							<SelectItem value="20">20</SelectItem>
							<SelectItem value="50">50</SelectItem>
							<SelectItem value="100">100</SelectItem>
						</SelectContent>
					</Select>
					<span className="text-sm text-[#D4B485]/60">条</span>
				</div>
			</div>

			<div className="flex items-center gap-2">
				<Button
					variant="outline"
					size="sm"
					onClick={handlePrevious}
					disabled={currentPage <= 1 || loading}
					className={cn(
						"gap-1",
						"bg-[#1A1C1E]",
						"border-[#D4B485]/20",
						"text-[#D4B485]",
						"hover:bg-[#D4B485]/10",
						"disabled:opacity-50",
					)}
				>
					<ChevronLeft className="h-4 w-4" />
					上一页
				</Button>

				<div className="flex items-center gap-1">
					{/* 页码显示逻辑 */}
					{totalPages <= 7 ? (
						// 总页数少于等于7页，显示所有页码
						Array.from({ length: totalPages }, (_, i) => i + 1).map(
							(page) => (
								<Button
									key={page}
									variant={
										page === currentPage
											? "primary"
											: "outline"
									}
									size="sm"
									onClick={() => onPageChange(page)}
									disabled={loading}
									className={cn(
										"w-8 h-8 p-0",
										page === currentPage
											? "bg-[#D4B485] text-black hover:bg-[#D4B485]/80"
											: "bg-[#1A1C1E] border-[#D4B485]/20 text-[#D4B485] hover:bg-[#D4B485]/10",
									)}
								>
									{page}
								</Button>
							),
						)
					) : // 总页数大于7页，显示省略号
					currentPage <= 4 ? (
						<>
							{Array.from({ length: 5 }, (_, i) => i + 1).map(
								(page) => (
									<Button
										key={page}
										variant={
											page === currentPage
												? "primary"
												: "outline"
										}
										size="sm"
										onClick={() => onPageChange(page)}
										disabled={loading}
										className={cn(
											"w-8 h-8 p-0",
											page === currentPage
												? "bg-[#D4B485] text-black hover:bg-[#D4B485]/80"
												: "bg-[#1A1C1E] border-[#D4B485]/20 text-[#D4B485] hover:bg-[#D4B485]/10",
										)}
									>
										{page}
									</Button>
								),
							)}
							<span className="text-[#D4B485]/60">...</span>
							<Button
								variant="outline"
								size="sm"
								onClick={() => onPageChange(totalPages)}
								disabled={loading}
								className={cn(
									"w-8 h-8 p-0",
									"bg-[#1A1C1E] border-[#D4B485]/20 text-[#D4B485] hover:bg-[#D4B485]/10",
								)}
							>
								{totalPages}
							</Button>
						</>
					) : currentPage >= totalPages - 3 ? (
						<>
							<Button
								variant="outline"
								size="sm"
								onClick={() => onPageChange(1)}
								disabled={loading}
								className={cn(
									"w-8 h-8 p-0",
									"bg-[#1A1C1E] border-[#D4B485]/20 text-[#D4B485] hover:bg-[#D4B485]/10",
								)}
							>
								1
							</Button>
							<span className="text-[#D4B485]/60">...</span>
							{Array.from(
								{ length: 5 },
								(_, i) => totalPages - 4 + i,
							).map((page) => (
								<Button
									key={page}
									variant={
										page === currentPage
											? "primary"
											: "outline"
									}
									size="sm"
									onClick={() => onPageChange(page)}
									disabled={loading}
									className={cn(
										"w-8 h-8 p-0",
										page === currentPage
											? "bg-[#D4B485] text-black hover:bg-[#D4B485]/80"
											: "bg-[#1A1C1E] border-[#D4B485]/20 text-[#D4B485] hover:bg-[#D4B485]/10",
									)}
								>
									{page}
								</Button>
							))}
						</>
					) : (
						<>
							<Button
								variant="outline"
								size="sm"
								onClick={() => onPageChange(1)}
								disabled={loading}
								className={cn(
									"w-8 h-8 p-0",
									"bg-[#1A1C1E] border-[#D4B485]/20 text-[#D4B485] hover:bg-[#D4B485]/10",
								)}
							>
								1
							</Button>
							<span className="text-[#D4B485]/60">...</span>
							{Array.from(
								{ length: 3 },
								(_, i) => currentPage - 1 + i,
							).map((page) => (
								<Button
									key={page}
									variant={
										page === currentPage
											? "primary"
											: "outline"
									}
									size="sm"
									onClick={() => onPageChange(page)}
									disabled={loading}
									className={cn(
										"w-8 h-8 p-0",
										page === currentPage
											? "bg-[#D4B485] text-black hover:bg-[#D4B485]/80"
											: "bg-[#1A1C1E] border-[#D4B485]/20 text-[#D4B485] hover:bg-[#D4B485]/10",
									)}
								>
									{page}
								</Button>
							))}
							<span className="text-[#D4B485]/60">...</span>
							<Button
								variant="outline"
								size="sm"
								onClick={() => onPageChange(totalPages)}
								disabled={loading}
								className={cn(
									"w-8 h-8 p-0",
									"bg-[#1A1C1E] border-[#D4B485]/20 text-[#D4B485] hover:bg-[#D4B485]/10",
								)}
							>
								{totalPages}
							</Button>
						</>
					)}
				</div>

				<Button
					variant="outline"
					size="sm"
					onClick={handleNext}
					disabled={currentPage >= totalPages || loading}
					className={cn(
						"gap-1",
						"bg-[#1A1C1E]",
						"border-[#D4B485]/20",
						"text-[#D4B485]",
						"hover:bg-[#D4B485]/10",
						"disabled:opacity-50",
					)}
				>
					下一页
					<ChevronRight className="h-4 w-4" />
				</Button>
			</div>
		</div>
	);
}
