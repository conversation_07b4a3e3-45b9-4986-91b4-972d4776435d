"use client";

import { But<PERSON> } from "@ui/components/button";
import { useToast } from "@ui/hooks/use-toast";
import { cn } from "@ui/lib";
import { format } from "date-fns";
import { motion } from "framer-motion";
import {
	CheckCircle2,
	Clock,
	Copy,
	Crown,
	RotateCcw,
	User,
	Zap,
} from "lucide-react";
import type { ActivationResult } from "../types";

interface SuccessViewProps {
	result: ActivationResult;
	onReset: () => void;
}

export function SuccessView({ result, onReset }: SuccessViewProps) {
	const {
		user,
		plan,
		quantity,
		addedComputingPower,
		activatedAt,
		expireAt,
		activationId,
	} = result;
	const { toast } = useToast();

	const copyActivationId = () => {
		navigator.clipboard.writeText(activationId);
		toast({
			title: "已复制",
			description: "激活ID已复制到剪贴板",
			variant: "success",
		});
	};

	return (
		<motion.div
			initial={{ opacity: 0, scale: 0.95 }}
			animate={{ opacity: 1, scale: 1 }}
			className="space-y-8"
		>
			{/* 成功图标和标题 */}
			<div className="text-center space-y-4">
				<motion.div
					initial={{ scale: 0 }}
					animate={{ scale: 1 }}
					transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
					className="flex justify-center"
				>
					<div className="relative">
						<div className="w-20 h-20 bg-gradient-to-br from-green-500/20 to-green-600/20 rounded-full flex items-center justify-center">
							<CheckCircle2 className="h-12 w-12 text-green-500" />
						</div>
						{/* 成功动画环 */}
						<div className="absolute inset-0 w-20 h-20 border-4 border-green-500/20 rounded-full animate-ping" />
					</div>
				</motion.div>

				<div className="space-y-2">
					<h2 className="text-3xl font-bold text-white">
						套餐激活成功！
					</h2>
					<p className="text-[#D4B485]/80 text-lg">
						已成功为用户开通套餐，算力已充值到账
					</p>
				</div>
			</div>

			{/* 激活详情卡片 */}
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.3 }}
				className={cn(
					"p-6 rounded-xl space-y-6",
					"bg-gradient-to-br from-[#1E2023] via-[#1C1F22] to-[#1A1C1E]",
					"border border-[#D4B485]/20",
				)}
			>
				<h3 className="text-xl font-semibold text-white flex items-center gap-2">
					<CheckCircle2 className="h-5 w-5 text-green-500" />
					激活详情
				</h3>

				<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
					{/* 用户信息 */}
					<div className="space-y-4">
						<h4 className="text-[#D4B485] font-medium flex items-center gap-2">
							<User className="h-4 w-4" />
							用户信息
						</h4>
						<div className="space-y-3 pl-6">
							<div className="flex items-center justify-between">
								<span className="text-white/60">姓名</span>
								<span className="text-white font-medium">
									{user.name}
								</span>
							</div>
							<div className="flex items-center justify-between">
								<span className="text-white/60">手机号</span>
								<span className="text-white font-mono">
									{user.phone}
								</span>
							</div>
						</div>
					</div>

					{/* 套餐信息 */}
					<div className="space-y-4">
						<h4 className="text-[#D4B485] font-medium flex items-center gap-2">
							<Crown className="h-4 w-4" />
							套餐信息
						</h4>
						<div className="space-y-3 pl-6">
							<div className="flex items-center justify-between">
								<span className="text-white/60">套餐名称</span>
								<span className="text-white font-medium">
									{plan.name}
								</span>
							</div>
							<div className="flex items-center justify-between">
								<span className="text-white/60">开通数量</span>
								<span className="text-white font-medium">
									{quantity} 个
								</span>
							</div>
							<div className="flex items-center justify-between">
								<span className="text-white/60">有效期</span>
								<span className="text-white font-medium">
									{plan.validityDays} 天
								</span>
							</div>
						</div>
					</div>

					{/* 算力信息 */}
					<div className="space-y-4">
						<h4 className="text-[#D4B485] font-medium flex items-center gap-2">
							<Zap className="h-4 w-4" />
							算力信息
						</h4>
						<div className="space-y-3 pl-6">
							<div className="flex items-center justify-between">
								<span className="text-white/60">单次算力</span>
								<span className="text-white font-medium">
									{plan.computingPower.toLocaleString()}
								</span>
							</div>
							<div className="flex items-center justify-between">
								<span className="text-white/60">
									总增加算力
								</span>
								<span className="text-[#D4B485] font-bold text-lg">
									{addedComputingPower.toLocaleString()}
								</span>
							</div>
						</div>
					</div>

					{/* 时间信息 */}
					<div className="space-y-4">
						<h4 className="text-[#D4B485] font-medium flex items-center gap-2">
							<Clock className="h-4 w-4" />
							时间信息
						</h4>
						<div className="space-y-3 pl-6">
							<div className="flex items-center justify-between">
								<span className="text-white/60">激活时间</span>
								<span className="text-white font-mono text-sm">
									{format(
										new Date(activatedAt),
										"yyyy-MM-dd HH:mm:ss",
									)}
								</span>
							</div>
							<div className="flex items-center justify-between">
								<span className="text-white/60">到期时间</span>
								<span className="text-white font-mono text-sm">
									{format(
										new Date(expireAt),
										"yyyy-MM-dd HH:mm:ss",
									)}
								</span>
							</div>
						</div>
					</div>
				</div>

				{/* 激活ID */}
				<div className="pt-4 border-t border-white/10">
					<div className="flex items-center justify-between">
						<span className="text-white/60">激活ID</span>
						<div className="flex items-center gap-2">
							<span className="text-white font-mono text-sm">
								{activationId}
							</span>
							<Button
								variant="ghost"
								size="sm"
								onClick={copyActivationId}
								className="h-8 w-8 p-0 text-[#D4B485] hover:bg-[#D4B485]/10"
							>
								<Copy className="h-3 w-3" />
							</Button>
						</div>
					</div>
				</div>
			</motion.div>

			{/* 操作按钮 */}
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.4 }}
				className="flex items-center justify-center gap-4"
			>
				<Button
					onClick={onReset}
					className={cn(
						"h-12 px-8 font-medium",
						"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
						"hover:from-[#E5C9A5] hover:to-[#D4B485]",
						"text-black",
						"transition-all duration-200",
						"shadow-lg shadow-[#D4B485]/25",
					)}
				>
					<RotateCcw className="mr-2 h-4 w-4" />
					继续为其他用户开通
				</Button>
			</motion.div>
		</motion.div>
	);
}
