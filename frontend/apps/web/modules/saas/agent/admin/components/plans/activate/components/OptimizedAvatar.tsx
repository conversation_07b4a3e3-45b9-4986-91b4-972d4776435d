"use client";

import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { cn } from "@ui/lib";
import { User } from "lucide-react";
import { useMemo } from "react";

interface OptimizedAvatarProps {
	src?: string | null;
	name: string;
	size?: "sm" | "md" | "lg" | "xl";
	className?: string;
	showOnlineStatus?: boolean;
}

const sizeClasses = {
	sm: "h-8 w-8",
	md: "h-12 w-12",
	lg: "h-16 w-16",
	xl: "h-20 w-20",
};

const iconSizes = {
	sm: "h-4 w-4",
	md: "h-6 w-6",
	lg: "h-8 w-8",
	xl: "h-10 w-10",
};

const textSizes = {
	sm: "text-xs",
	md: "text-sm",
	lg: "text-lg",
	xl: "text-xl",
};

export function OptimizedAvatar({
	src,
	name,
	size = "md",
	className,
	showOnlineStatus = false,
}: OptimizedAvatarProps) {
	// 生成用户名首字母
	const initials = useMemo(() => {
		if (!name) {
			return "U";
		}

		const words = name.trim().split(/\s+/);
		if (words.length === 1) {
			// 单个词，取前两个字符
			return words[0].slice(0, 2).toUpperCase();
		}
		// 多个词，取每个词的首字母
		return words
			.slice(0, 2)
			.map((word) => word[0])
			.join("")
			.toUpperCase();
	}, [name]);

	// 生成基于名字的颜色
	const avatarColor = useMemo(() => {
		if (!name) {
			return "from-gray-500 to-gray-600";
		}

		const colors = [
			"from-blue-500 to-blue-600",
			"from-green-500 to-green-600",
			"from-purple-500 to-purple-600",
			"from-pink-500 to-pink-600",
			"from-indigo-500 to-indigo-600",
			"from-yellow-500 to-yellow-600",
			"from-red-500 to-red-600",
			"from-teal-500 to-teal-600",
		];

		const hash = name.split("").reduce((acc, char) => {
			return char.charCodeAt(0) + ((acc << 5) - acc);
		}, 0);

		return colors[Math.abs(hash) % colors.length];
	}, [name]);

	return (
		<div className="relative">
			<Avatar
				className={cn(
					sizeClasses[size],
					"ring-2 ring-[#D4B485]/20 transition-all duration-200",
					"hover:ring-[#D4B485]/50 hover:scale-105",
					className,
				)}
			>
				{src && (
					<AvatarImage
						src={src}
						alt={name}
						className="object-cover"
						onError={(e) => {
							// 如果图片加载失败，隐藏图片元素
							e.currentTarget.style.display = "none";
						}}
					/>
				)}
				<AvatarFallback
					className={cn(
						"bg-gradient-to-br text-white font-semibold",
						"border border-white/10",
						avatarColor,
						textSizes[size],
					)}
				>
					{src ? (
						// 如果有头像URL但加载失败，显示首字母
						initials
					) : // 如果没有头像URL，显示图标或首字母
					size === "sm" ? (
						<User className={iconSizes[size]} />
					) : (
						initials
					)}
				</AvatarFallback>
			</Avatar>

			{/* 在线状态指示器 */}
			{showOnlineStatus && (
				<div
					className={cn(
						"absolute -bottom-0.5 -right-0.5 rounded-full border-2 border-[#1E2023]",
						"bg-green-500",
						size === "sm"
							? "w-2.5 h-2.5"
							: size === "md"
								? "w-3 h-3"
								: size === "lg"
									? "w-4 h-4"
									: "w-5 h-5",
					)}
				/>
			)}
		</div>
	);
}
