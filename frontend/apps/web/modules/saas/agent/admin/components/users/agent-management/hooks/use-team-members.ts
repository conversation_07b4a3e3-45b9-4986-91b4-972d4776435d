"use client";

import type { Agent<PERSON><PERSON> } from "@prisma/client";
import { logger } from "@repo/logs";
import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";
import type { AgentListItem } from "./use-agent-list";

// 团队成员筛选参数接口
export interface TeamMembersFilterParams {
	page: number;
	pageSize: number;
	searchTerm?: string;
	role?: AgentRole | "ALL";
	status?: string;
	includeDescendants?: boolean; // 是否包含所有后代节点
}

interface ApiResponse<T> {
	code?: number;
	message?: string;
	data?: T;
	error?: string;
	items?: AgentListItem[];
	total?: number;
	page?: number;
	pageSize?: number;
	totalPages?: number;
}

export interface UseTeamMembersReturn {
	loading: boolean;
	members: AgentListItem[];
	total: number;
	currentPage: number;
	totalPages: number;
	fetchTeamMembers: (
		nodeId: string,
		includeDescendants?: boolean,
	) => Promise<void>;
	filterTeamMembers: (params: TeamMembersFilterParams) => Promise<void>;
}

/**
 * 获取团队成员的Hook
 * @returns {UseTeamMembersReturn} 团队成员加载状态和数据
 */
export function useTeamMembers(): UseTeamMembersReturn {
	const [loading, setLoading] = useState(false);
	const [members, setMembers] = useState<AgentListItem[]>([]);
	const [total, setTotal] = useState(0);
	const [currentPage, setCurrentPage] = useState(1);
	const [totalPages, setTotalPages] = useState(1);
	const [currentNodeId, setCurrentNodeId] = useState<string | null>(null);
	const [allDescendants, setAllDescendants] = useState<AgentListItem[]>([]);

	// 递归获取团队成员及其子成员
	const fetchAllTeamMembers = useCallback(
		async (nodeId: string): Promise<AgentListItem[]> => {
			try {
				// 获取直接下属
				const response =
					await apiClient.v1.agent.admin.user.agent.list.$get({
						query: {
							pageSize: 100, // 获取较多记录以避免分页问题
							parentId: nodeId,
						},
					});

				const result = (await response.json()) as ApiResponse<unknown>;

				if (!response.ok || !result.items) {
					return [];
				}

				const directMembers = result.items;
				let allMembers = [...directMembers];

				// 对每个直接下属递归获取其成员
				for (const member of directMembers) {
					if (member.hasChildren) {
						const childMembers = await fetchAllTeamMembers(
							member.id,
						);
						allMembers = [...allMembers, ...childMembers];
					}
				}

				return allMembers;
			} catch (error) {
				logger.error("[Admin] 递归获取团队成员失败", {
					error: error instanceof Error ? error.message : "未知错误",
					nodeId,
				});
				return [];
			}
		},
		[],
	);

	// 加载团队成员函数
	const fetchTeamMembers = useCallback(
		async (nodeId: string, includeDescendants = true) => {
			if (!nodeId) {
				return;
			}

			setLoading(true);
			setCurrentNodeId(nodeId);

			try {
				if (includeDescendants) {
					// 递归获取所有成员
					const allMembers = await fetchAllTeamMembers(nodeId);
					setMembers(allMembers);
					setTotal(allMembers.length);
					setCurrentPage(1);
					setTotalPages(1);
					setAllDescendants(allMembers);
				} else {
					// 只获取直接下属
					const response =
						await apiClient.v1.agent.admin.user.agent.list.$get({
							query: {
								page: 1,
								pageSize: 5,
								parentId: nodeId,
							},
						});

					const result =
						(await response.json()) as ApiResponse<unknown>;

					if (response.ok && result.items) {
						setMembers(result.items);
						setTotal(result.total || 0);
						setCurrentPage(result.page || 1);
						setTotalPages(result.totalPages || 1);
					} else {
						toast.error("获取团队成员失败");
						logger.error("[Admin] 获取团队成员失败", {
							error: result.message || result.error,
							nodeId,
						});
					}
				}
			} catch (error) {
				toast.error("获取团队成员失败");
				logger.error("[Admin] 获取团队成员失败", {
					error: error instanceof Error ? error.message : "未知错误",
					nodeId,
				});
			} finally {
				setLoading(false);
			}
		},
		[fetchAllTeamMembers],
	);

	// 筛选团队成员函数
	const filterTeamMembers = useCallback(
		async (params: TeamMembersFilterParams) => {
			if (!currentNodeId) {
				return;
			}

			setLoading(true);

			try {
				if (params.includeDescendants) {
					// 如果需要包含所有后代，使用本地筛选
					let filteredMembers = [...allDescendants];

					// 应用筛选条件
					if (params.searchTerm) {
						const searchLower = params.searchTerm.toLowerCase();
						filteredMembers = filteredMembers.filter(
							(member) =>
								member.name
									.toLowerCase()
									.includes(searchLower) ||
								member.email
									.toLowerCase()
									.includes(searchLower) ||
								member.phone
									.toLowerCase()
									.includes(searchLower),
						);
					}

					if (params.role && params.role !== "ALL") {
						filteredMembers = filteredMembers.filter(
							(member) => member.role === params.role,
						);
					}

					if (params.status && params.status !== "ALL") {
						filteredMembers = filteredMembers.filter(
							(member) => member.status === params.status,
						);
					}

					// 计算分页
					const startIdx = (params.page - 1) * params.pageSize;
					const endIdx = startIdx + params.pageSize;
					const pagedMembers = filteredMembers.slice(
						startIdx,
						endIdx,
					);

					setMembers(pagedMembers);
					setTotal(filteredMembers.length);
					setCurrentPage(params.page);
					setTotalPages(
						Math.ceil(filteredMembers.length / params.pageSize),
					);
				} else {
					// 使用API筛选
					const queryParams: Record<
						string,
						string | number | boolean | undefined
					> = {
						...params,
						parentId: currentNodeId,
					};

					// 移除undefined和null值
					for (const key of Object.keys(queryParams)) {
						if (
							queryParams[key] === undefined ||
							queryParams[key] === null
						) {
							delete queryParams[key];
						}
					}

					const response =
						await apiClient.v1.agent.admin.user.agent.list.$get({
							query: queryParams,
						});

					const result =
						(await response.json()) as ApiResponse<unknown>;

					if (response.ok && result.items) {
						setMembers(result.items);
						setTotal(result.total || 0);
						setCurrentPage(result.page || 1);
						setTotalPages(result.totalPages || 1);
					} else {
						toast.error("筛选团队成员失败");
						logger.error("[Admin] 筛选团队成员失败", {
							error: result.message || result.error,
							nodeId: currentNodeId,
							params,
						});
					}
				}
			} catch (error) {
				toast.error("筛选团队成员失败");
				logger.error("[Admin] 筛选团队成员失败", {
					error: error instanceof Error ? error.message : "未知错误",
					nodeId: currentNodeId,
					params,
				});
			} finally {
				setLoading(false);
			}
		},
		[currentNodeId, allDescendants],
	);

	// 处理团队成员分页
	const _handleTeamMembersPageChange = useCallback(
		(page: number) => {
			filterTeamMembers({
				page,
				pageSize: 5,
				includeDescendants: true,
			});
		},
		[filterTeamMembers],
	);

	return {
		loading,
		members,
		total,
		currentPage,
		totalPages,
		fetchTeamMembers,
		filterTeamMembers,
	};
}
