"use client";

import { logger } from "@repo/logs";
import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";
import type { ConsumerListItem } from "./use-consumer-list";

// 消费者详情
export interface ConsumerDetail extends ConsumerListItem {
	userId: string;
	agentId: string | null;
	currentPlanId: string | null;
	planStartAt: string | null;
	updatedAt: string;
	// 可以根据需要扩展更多字段
}

// API响应类型
interface ApiResponse<T> {
	code?: number;
	message?: string;
	data?: T;
	error?: string;
	consumer?: ConsumerDetail;
}

export function useConsumerDetail() {
	const [loading, setLoading] = useState(false);
	const [consumer, setConsumer] = useState<ConsumerDetail | null>(null);

	const fetchConsumerDetail = useCallback(async (id: string) => {
		setLoading(true);
		try {
			const response =
				await apiClient.v1.agent.admin.user.consumers.detail[
					":id"
				].$get({
					param: { id },
				});
			const result = (await response.json()) as ApiResponse<unknown>;

			if (response.ok && result.consumer) {
				setConsumer(result.consumer);
			} else {
				toast.error("获取消费者详情失败");
				logger.error("[Admin] 获取消费者详情失败", {
					error: result.message || result.error,
					id,
				});
			}
		} catch (error) {
			toast.error("获取消费者详情失败");
			logger.error("[Admin] 获取消费者详情失败", {
				error: error instanceof Error ? error.message : "未知错误",
				id,
			});
		} finally {
			setLoading(false);
		}
	}, []);

	return {
		loading,
		consumer,
		fetchConsumerDetail,
	};
}
