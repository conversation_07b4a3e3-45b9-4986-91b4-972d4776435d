import type { Agent<PERSON><PERSON> } from "@prisma/client";
import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { useState } from "react";
import {
	calculatePurchaseAmount,
	getQuotaRule,
	validatePurchaseQuantity,
} from "../config/quota-rules";

interface Agent {
	id: string;
	name: string;
	role: string;
	email: string;
	phone: string;
	teamQuota: number;
	usedTeamQuota: number;
	remainingQuota: number;
	teamSize: number;
	totalSales: number;
	monthSales: number;
	createdAt: string;
}

interface FormData {
	agent: Agent | null;
	quantity: number | null;
}

interface StepData {
	agent?: Agent;
	quantity?: number;
}

interface InputQuantityProps {
	onNext: (data: StepData) => void;
	onPrev: () => void;
	formData: FormData;
}

export function InputQuantity({
	onNext,
	onPrev,
	formData,
}: InputQuantityProps) {
	// 获取最小购买数量
	const minPurchase = formData.agent
		? getQuotaRule(formData.agent.role as AgentRole).minPurchase
		: 0;

	const [quantity, setQuantity] = useState<number | null>(minPurchase);
	const [error, setError] = useState<string | null>(null);

	const handleQuantityChange = (value: string) => {
		// 允许清空输入
		if (!value) {
			setQuantity(null);
			setError(null);
			return;
		}

		const num = Number.parseInt(value);

		// 检查是否为有效数字
		if (Number.isNaN(num)) {
			setError("请输入有效的数字");
			return;
		}

		// 更新数量
		setQuantity(num);

		// 验证数量
		if (num <= 0) {
			setError("分配数量必须大于0");
			return;
		}

		if (formData.agent) {
			const validation = validatePurchaseQuantity(
				formData.agent.role as AgentRole,
				num,
			);
			if (!validation.isValid) {
				setError(validation.message || "无效的购买数量");
				return;
			}
		}

		setError(null);
	};

	// 计算购买金额
	const purchaseAmount =
		formData.agent && quantity
			? calculatePurchaseAmount(
					formData.agent.role as AgentRole,
					quantity,
				)
			: null;

	return (
		<div className="space-y-8">
			{/* 大标题 */}
			<div className="space-y-2 text-center">
				<h3
					className={cn("text-2xl font-semibold", zywhFont.className)}
					style={{
						background:
							"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
						WebkitBackgroundClip: "text",
						WebkitTextFillColor: "transparent",
						backgroundClip: "text",
						textShadow:
							"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
					}}
				>
					输入分配数量
				</h3>
				<p className="text-[#D4B485]/60">
					请输入要分配给代理商的名额数量
				</p>
			</div>

			{/* 主要内容区域 - 左右两栏布局 */}
			<div className="grid grid-cols-2 gap-6">
				{/* 左侧：代理商信息 */}
				{formData.agent && (
					<motion.div
						initial={{ opacity: 0, x: -20 }}
						animate={{ opacity: 1, x: 0 }}
						className={cn(
							"rounded-lg p-6",
							"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
							"border border-[#D4B485]/20",
							"group",
							"hover:border-[#D4B485]/40",
							"transition-all duration-200",
							"relative overflow-hidden",
							"h-[calc(100%)]", // 减去间距
						)}
					>
						{/* 背景装饰 */}
						<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
						<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />

						<div className="relative space-y-4">
							<div className="flex items-center justify-between">
								<div className="space-y-1">
									<div className="flex items-center gap-2">
										<span className="font-medium text-[#D4B485]">
											{formData.agent.name}
										</span>
										<span
											className={cn(
												"rounded-full px-2 py-0.5 text-xs",
												"bg-[#D4B485]/10",
												"text-[#D4B485]/60",
											)}
										>
											{formData.agent.role}
										</span>
									</div>
									<div className="text-sm text-[#D4B485]/40">
										{formData.agent.email} ·{" "}
										{formData.agent.phone}
									</div>
								</div>
							</div>

							<div className="grid grid-cols-3 gap-4">
								<div className="text-center">
									<div className="text-sm text-[#D4B485]/60">
										总名额
									</div>
									<div className="font-medium text-[#D4B485]">
										{formData.agent.teamQuota}个
									</div>
								</div>
								<div className="text-center">
									<div className="text-sm text-[#D4B485]/60">
										已使用
									</div>
									<div className="font-medium text-[#D4B485]">
										{formData.agent.usedTeamQuota}个
									</div>
								</div>
								<div className="text-center">
									<div className="text-sm text-[#D4B485]/60">
										剩余名额
									</div>
									<div className="font-medium text-[#D4B485]">
										{formData.agent.remainingQuota}个
									</div>
								</div>
							</div>

							{/* 加购规则信息 */}
							{getQuotaRule(formData.agent.role as AgentRole)
								.canPurchaseQuota && (
								<div className="mt-4 rounded-lg bg-[#D4B485]/5 p-4">
									<div className="text-sm font-medium text-[#D4B485]">
										加购规则
									</div>
									<div className="mt-2 space-y-2 text-sm text-[#D4B485]/60">
										<div>
											单价：¥
											{
												getQuotaRule(
													formData.agent
														.role as AgentRole,
												).discountedPrice
											}
										</div>
										<div>
											最少购买：
											{
												getQuotaRule(
													formData.agent
														.role as AgentRole,
												).minPurchase
											}
											个
										</div>
										<div>
											折扣：
											{getQuotaRule(
												formData.agent
													.role as AgentRole,
											).discount * 100}
											%
										</div>
									</div>
								</div>
							)}
						</div>
					</motion.div>
				)}

				{/* 右侧：输入和金额计算 */}
				<div className="flex flex-col gap-4">
					{/* 输入数量 */}
					<motion.div
						initial={{ opacity: 0, x: 20 }}
						animate={{ opacity: 1, x: 0 }}
						className={cn(
							"rounded-lg p-6",
							"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
							"border border-[#D4B485]/20",
							"group",
							"hover:border-[#D4B485]/40",
							"transition-all duration-200",
							"relative overflow-hidden",
						)}
					>
						{/* 背景装饰 */}
						<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
						<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />

						<div className="relative space-y-4">
							<div className="text-base font-medium text-[#D4B485]">
								分配数量
							</div>
							<div className="flex items-center gap-4">
								<Input
									type="number"
									placeholder="请输入分配数量"
									className={cn(
										"flex-1",
										"bg-[#1A1C1E]",
										"border-[#D4B485]/20",
										"text-[#D4B485]",
										"placeholder:text-[#D4B485]/40",
										error && "border-rose-500",
									)}
									value={quantity || ""}
									onChange={(e) =>
										handleQuantityChange(e.target.value)
									}
								/>
								<span className="text-[#D4B485]/60">个</span>
							</div>
							{error && (
								<div className="text-sm text-rose-500">
									{error}
								</div>
							)}
						</div>
					</motion.div>

					{/* 金额计算 */}
					{purchaseAmount && !error && (
						<motion.div
							initial={{ opacity: 0, x: 20 }}
							animate={{ opacity: 1, x: 0 }}
							transition={{ delay: 0.1 }}
							className={cn(
								"rounded-lg p-6",
								"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
								"border border-[#D4B485]/20",
								"group",
								"hover:border-[#D4B485]/40",
								"transition-all duration-200",
								"relative overflow-hidden",
								"flex-1", // 填充剩余空间
							)}
						>
							{/* 背景装饰 */}
							<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
							<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />

							<div className="relative space-y-4">
								<div className="flex items-center justify-between">
									<span className="text-sm text-[#D4B485]/60">
										原价金额
									</span>
									<span className="text-sm text-[#D4B485]">
										¥
										{purchaseAmount.baseAmount.toLocaleString()}
									</span>
								</div>
								<div className="flex items-center justify-between">
									<span className="text-sm text-[#D4B485]/60">
										优惠金额
									</span>
									<span className="text-sm text-emerald-500">
										-¥
										{purchaseAmount.discountAmount.toLocaleString()}
									</span>
								</div>
								<div className="flex items-center justify-between border-t border-[#D4B485]/10 pt-2">
									<span className="text-base font-medium text-[#D4B485]">
										最终金额
									</span>
									<span className="text-lg font-medium text-[#D4B485]">
										¥
										{purchaseAmount.finalAmount.toLocaleString()}
									</span>
								</div>
							</div>
						</motion.div>
					)}
				</div>
			</div>

			{/* 底部按钮 */}
			<div className="flex justify-between pt-4">
				<Button
					variant="outline"
					className={cn(
						"px-8",
						"bg-[#1E2023]/50 border-[#D4B485]/20",
						"text-[#D4B485] hover:text-[#E5C9A5]",
						"hover:bg-[#D4B485]/10",
						"transition duration-200",
					)}
					onClick={onPrev}
				>
					上一步
				</Button>
				<Button
					className={cn(
						"px-8",
						"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
						"text-white",
						"border-none",
						"shadow-[0_8px_16px_-4px_rgba(212,180,133,0.3)]",
						"hover:shadow-[0_12px_20px_-4px_rgba(212,180,133,0.4)]",
						"transition-all duration-200",
						"disabled:opacity-50 disabled:cursor-not-allowed",
					)}
					onClick={() => quantity && onNext({ quantity })}
					disabled={!quantity}
				>
					下一步
				</Button>
			</div>
		</div>
	);
}
