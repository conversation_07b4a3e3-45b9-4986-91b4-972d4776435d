import { logger } from "@repo/logs";
import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";
import type { OrderListItem } from "./use-order-list";

// 订单详情类型
export interface OrderDetail extends OrderListItem {
	// 详细的支付记录
	paymentRecords?: Array<{
		id: string;
		amount: number;
		status: string;
		method: string;
		transactionId?: string;
		createdAt: string;
		completedAt?: string;
		failureReason?: string;
	}>;

	// 操作日志
	operationLogs?: Array<{
		id: string;
		action: string;
		operator: string;
		operatorType: "ADMIN" | "AGENT" | "SYSTEM";
		description: string;
		createdAt: string;
	}>;
}

// API响应类型
interface ApiResponse {
	code: number;
	message?: string;
	data?: OrderDetail;
	error?: string;
}

export function useOrderDetail() {
	const [loading, setLoading] = useState(false);
	const [orderDetail, setOrderDetail] = useState<OrderDetail | null>(null);

	const fetchOrderDetail = useCallback(async (orderId: string) => {
		setLoading(true);
		try {
			logger.info("[Admin] 开始获取订单详情", { orderId });

			const response = await apiClient.v1.agent.admin.orders.detail[
				":id"
			].$get({
				param: { id: orderId },
			});

			const result = (await response.json()) as ApiResponse;

			logger.info("[Admin] 订单详情响应", { result });

			if (response.ok && result.code === 200 && result.data) {
				setOrderDetail(result.data);
			} else if (result.code === 404) {
				toast.error("订单不存在");
				setOrderDetail(null);
			} else {
				const errorMsg =
					result.message || result.error || "获取订单详情失败";
				toast.error(errorMsg);
				logger.error("[Admin] 获取订单详情失败", {
					error: errorMsg,
					code: result.code,
					orderId,
				});
			}
		} catch (error) {
			const errorMsg =
				error instanceof Error ? error.message : "获取订单详情失败";
			toast.error(errorMsg);
			logger.error("[Admin] 获取订单详情异常", {
				error: errorMsg,
				orderId,
			});
		} finally {
			setLoading(false);
		}
	}, []);

	const clearOrderDetail = useCallback(() => {
		setOrderDetail(null);
	}, []);

	return {
		loading,
		orderDetail,
		fetchOrderDetail,
		clearOrderDetail,
	};
}
