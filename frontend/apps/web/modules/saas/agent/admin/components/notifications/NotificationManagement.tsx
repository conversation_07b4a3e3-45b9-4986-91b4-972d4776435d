"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Button } from "@ui/components/button";
import { Card } from "@ui/components/card";
import { Input } from "@ui/components/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import {
	Bell,
	Calendar,
	Download,
	Filter,
	Plus,
	Search,
	Settings,
	Users,
} from "lucide-react";

// Mock数据
type NotificationType = "system" | "feature" | "promotion";
type NotificationStatus = "draft" | "scheduled" | "sent";

const NOTIFICATION_TYPE_LABELS: Record<NotificationType, string> = {
	system: "系统通知",
	feature: "功能更新",
	promotion: "活动通知",
};

const NOTIFICATION_STATUS_LABELS: Record<NotificationStatus, string> = {
	draft: "草稿",
	scheduled: "待发送",
	sent: "已发送",
};

const mockNotifications = [
	{
		id: "1",
		title: "系统维护通知",
		content: "系统将于今晚22:00进行例行维护，预计持续2小时",
		type: "system" as NotificationType,
		status: "scheduled" as NotificationStatus,
		targetRole: "all",
		targetUsers: [],
		scheduledAt: "2024-02-24 22:00:00",
		createdAt: "2024-02-24 10:30:00",
	},
	{
		id: "2",
		title: "新功能发布",
		content: "数字人平台新增表情编辑功能，欢迎体验",
		type: "feature" as NotificationType,
		status: "sent" as NotificationStatus,
		targetRole: "DIRECTOR",
		targetUsers: ["user1", "user2"],
		sentAt: "2024-02-23 15:45:00",
		createdAt: "2024-02-23 15:45:00",
	},
	{
		id: "3",
		title: "活动提醒",
		content: "春节促销活动即将开始，请做好准备",
		type: "promotion" as NotificationType,
		status: "draft" as NotificationStatus,
		targetRole: "SALES",
		targetUsers: [],
		createdAt: "2024-02-23 09:15:00",
	},
];

export function NotificationManagement() {
	return (
		<div className="w-full space-y-6 p-6">
			{/* 标题和操作栏 */}
			<div className="flex items-center justify-between">
				<h1
					className={cn("text-2xl font-bold", zywhFont.className)}
					style={{
						background:
							"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
						WebkitBackgroundClip: "text",
						WebkitTextFillColor: "transparent",
						backgroundClip: "text",
						textShadow:
							"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
					}}
				>
					通知管理
				</h1>
				<div className="flex items-center gap-4">
					<div className="flex items-center gap-2">
						<Calendar className="h-4 w-4 text-[#D4B485]/60" />
						<span className="text-[#D4B485]/60">
							2024-01-01 至 2024-12-31
						</span>
					</div>
					<Button
						variant="outline"
						className={cn(
							"gap-2",
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"hover:bg-[#D4B485]/10",
						)}
					>
						<Download className="h-4 w-4" />
						<span>导出记录</span>
					</Button>
					<Button
						className={cn(
							"gap-2",
							"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
							"text-white hover:text-white/90",
							"border-none",
							"shadow-[0_8px_16px_-4px_rgba(212,180,133,0.3)]",
							"transition-all duration-300",
							"hover:shadow-[0_12px_20px_-4px_rgba(212,180,133,0.4)]",
							"hover:scale-105",
						)}
					>
						<Plus className="h-4 w-4" />
						<span>新建通知</span>
					</Button>
				</div>
			</div>

			{/* 搜索和筛选 */}
			<Card
				className={cn(
					"p-4",
					"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
					"border border-[#D4B485]/20",
					"group",
					"hover:border-[#D4B485]/40",
					"transition-all duration-500",
				)}
			>
				<div className="flex items-center gap-4">
					<div className="relative flex-1">
						<Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-[#D4B485]/40" />
						<Input
							placeholder="搜索通知..."
							className={cn(
								"pl-10",
								"bg-[#1A1C1E]",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
								"placeholder:text-[#D4B485]/40",
							)}
						/>
					</div>
					<Select defaultValue="all">
						<SelectTrigger
							className={cn(
								"w-[160px]",
								"bg-[#1A1C1E]",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
							)}
						>
							<SelectValue placeholder="通知类型" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="all">全部类型</SelectItem>
							<SelectItem value="system">系统通知</SelectItem>
							<SelectItem value="feature">功能更新</SelectItem>
							<SelectItem value="promotion">活动通知</SelectItem>
						</SelectContent>
					</Select>
					<Select defaultValue="all">
						<SelectTrigger
							className={cn(
								"w-[160px]",
								"bg-[#1A1C1E]",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
							)}
						>
							<SelectValue placeholder="通知状态" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="all">全部状态</SelectItem>
							<SelectItem value="draft">草稿</SelectItem>
							<SelectItem value="scheduled">待发送</SelectItem>
							<SelectItem value="sent">已发送</SelectItem>
						</SelectContent>
					</Select>
					<Button
						variant="outline"
						className={cn(
							"gap-2",
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"hover:bg-[#D4B485]/10",
						)}
					>
						<Filter className="h-4 w-4" />
						<span>筛选</span>
					</Button>
				</div>
			</Card>

			{/* 通知列表 */}
			<div className="grid gap-4">
				{mockNotifications.map((notification, index) => (
					<motion.div
						key={notification.id}
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ delay: index * 0.1 }}
						className={cn(
							"rounded-lg p-6",
							"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
							"border border-[#D4B485]/20",
							"group",
							"hover:border-[#D4B485]/40",
							"transition-all duration-200",
							"relative overflow-hidden",
						)}
					>
						{/* 背景装饰 */}
						<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
						<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />

						<div className="relative flex items-start gap-4">
							<div
								className={cn(
									"flex h-12 w-12 shrink-0 items-center justify-center rounded-lg",
									"bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
									"ring-1 ring-[#D4B485]/20",
								)}
							>
								{notification.type === "system" ? (
									<Settings className="h-6 w-6 text-[#D4B485]" />
								) : notification.type === "feature" ? (
									<Bell className="h-6 w-6 text-[#D4B485]" />
								) : (
									<Users className="h-6 w-6 text-[#D4B485]" />
								)}
							</div>

							<div className="flex-1 min-w-0">
								<div className="flex items-center gap-3">
									<h3 className="font-medium text-[#D4B485]">
										{notification.title}
									</h3>
									<span
										className={cn(
											"rounded-full px-2 py-0.5 text-xs",
											"bg-[#D4B485]/10",
											"text-[#D4B485]/60",
										)}
									>
										{
											NOTIFICATION_TYPE_LABELS[
												notification.type
											]
										}
									</span>
									<span
										className={cn(
											"rounded-full px-2 py-0.5 text-xs",
											notification.status === "sent"
												? "bg-emerald-500/10 text-emerald-500"
												: notification.status ===
														"scheduled"
													? "bg-amber-500/10 text-amber-500"
													: "bg-blue-500/10 text-blue-500",
										)}
									>
										{
											NOTIFICATION_STATUS_LABELS[
												notification.status
											]
										}
									</span>
								</div>
								<p className="mt-2 text-sm text-[#D4B485]/40">
									{notification.content}
								</p>
								<div className="mt-3 flex items-center gap-6 text-sm text-[#D4B485]/40">
									<div>
										目标角色：
										<span className="font-medium text-[#D4B485]">
											{notification.targetRole === "all"
												? "全部用户"
												: notification.targetRole}
										</span>
									</div>
									{notification.targetUsers.length > 0 && (
										<div>
											指定用户：
											<span className="font-medium text-[#D4B485]">
												{
													notification.targetUsers
														.length
												}
												人
											</span>
										</div>
									)}
									{notification.scheduledAt && (
										<div>
											计划发送：
											<span className="font-medium text-[#D4B485]">
												{notification.scheduledAt}
											</span>
										</div>
									)}
									{notification.sentAt && (
										<div>
											发送时间：
											<span className="font-medium text-[#D4B485]">
												{notification.sentAt}
											</span>
										</div>
									)}
									<div>
										创建时间：
										<span className="font-medium text-[#D4B485]">
											{notification.createdAt}
										</span>
									</div>
								</div>
							</div>

							<div className="flex gap-2">
								<Button
									variant="outline"
									className={cn(
										"h-8 px-3",
										"bg-[#1A1C1E]",
										"border-[#D4B485]/20",
										"text-[#D4B485]",
										"hover:bg-[#D4B485]/10",
									)}
								>
									编辑
								</Button>
								{notification.status !== "sent" && (
									<Button
										className={cn(
											"h-8 px-3",
											"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
											"text-white hover:text-white/90",
											"border-none",
											"shadow-[0_8px_16px_-4px_rgba(212,180,133,0.3)]",
											"transition-all duration-300",
											"hover:shadow-[0_12px_20px_-4px_rgba(212,180,133,0.4)]",
											"hover:scale-105",
										)}
									>
										发送
									</Button>
								)}
							</div>
						</div>
					</motion.div>
				))}
			</div>
		</div>
	);
}
