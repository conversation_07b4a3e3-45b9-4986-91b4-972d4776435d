"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { ZapIcon } from "lucide-react";
import { useState } from "react";
import { PlanSelection } from "./components/PlanSelection";
import { SuccessView } from "./components/SuccessView";
import { UserSelection } from "./components/UserSelection";
import type { ActivationResult, UserProfile } from "./types";

export function AdminActivationView() {
	const [selectedUser, setSelectedUser] = useState<UserProfile | null>(null);
	const [isSuccess, setIsSuccess] = useState(false);
	const [activationResult, setActivationResult] =
		useState<ActivationResult | null>(null);

	const handleUserSelect = (user: UserProfile) => {
		setSelectedUser(user);
	};

	const handleBackToSelectUser = () => {
		setSelectedUser(null);
	};

	const handleActivationSuccess = (result: ActivationResult) => {
		setActivationResult(result);
		setIsSuccess(true);
	};

	const handleReset = () => {
		setSelectedUser(null);
		setIsSuccess(false);
		setActivationResult(null);
	};

	const getTitle = () => {
		if (isSuccess) {
			return "开通成功";
		}
		if (selectedUser) {
			return `为 ${selectedUser.name} 开通套餐`;
		}
		return "选择用户";
	};

	const getDescription = () => {
		if (isSuccess) {
			return "套餐已成功激活并充值到用户账户";
		}
		if (selectedUser) {
			return "请为该用户选择一个需要开通的套餐";
		}
		return "通过手机号搜索，为指定用户开通套餐";
	};

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			className="w-full"
		>
			<motion.div
				initial={{ opacity: 0, x: -20 }}
				animate={{ opacity: 1, x: 0 }}
				transition={{ delay: 0.2 }}
				className="relative mt-8 mb-6"
			>
				<div className="flex items-center gap-4">
					<div
						className={cn(
							"flex h-14 w-14 shrink-0 items-center justify-center",
							"rounded-xl bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
							"ring-1 ring-[#D4B485]/20",
						)}
					>
						<ZapIcon className="h-7 w-7 text-[#D4B485]" />
					</div>
					<div className="space-y-1">
						<h2
							className={cn(
								"font-semibold text-3xl",
								zywhFont.className,
							)}
							style={{
								background:
									"linear-gradient(135deg, rgba(229,201,165,0.95) 0%, rgba(212,180,133,0.9) 50%, rgba(176,137,104,0.85) 100%)",
								WebkitBackgroundClip: "text",
								WebkitTextFillColor: "transparent",
								backgroundClip: "text",
							}}
						>
							{getTitle()}
						</h2>
						<p className="text-sm text-[#D4B485]/80">
							{getDescription()}
						</p>
					</div>
				</div>
			</motion.div>

			<motion.div
				initial={{ opacity: 0, y: 40 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.3 }}
				className="mt-6"
			>
				<div
					className={cn(
						"rounded-xl",
						"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
						"border border-white/10",
						"p-8",
					)}
				>
					{isSuccess && activationResult ? (
						<SuccessView
							result={activationResult}
							onReset={handleReset}
						/>
					) : !selectedUser ? (
						<UserSelection onUserSelect={handleUserSelect} />
					) : (
						<PlanSelection
							user={selectedUser}
							onActivationSuccess={handleActivationSuccess}
							onCancel={handleBackToSelectUser}
						/>
					)}
				</div>
			</motion.div>
		</motion.div>
	);
}
