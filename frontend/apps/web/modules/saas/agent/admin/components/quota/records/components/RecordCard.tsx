import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { cn } from "@ui/lib";
import { format } from "date-fns";
import {
	AgentRoleColors,
	AgentRoleLabels,
	OrderStatusColors,
	OrderStatusLabels,
} from "../config/records-config";
import type { QuotaRecord } from "../hooks/use-records";

interface RecordCardProps {
	record: QuotaRecord;
}

export function RecordCard({ record }: RecordCardProps) {
	return (
		<div
			className={cn(
				"rounded-lg p-4",
				"bg-[#1E2023]/50",
				"border border-[#D4B485]/20",
				"shadow-[inset_0_0_0_1px_rgba(212,180,133,0.05)]",
			)}
		>
			<div className="space-y-4">
				{/* 记录信息 */}
				<div className="flex items-center justify-between">
					<div
						className={cn(
							"text-lg font-medium",
							zywhFont.className,
						)}
						style={{
							background:
								"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
							WebkitBackgroundClip: "text",
							WebkitTextFillColor: "transparent",
							backgroundClip: "text",
							textShadow:
								"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
						}}
					>
						{record.id}
					</div>
					<div
						className={cn(
							"text-sm",
							OrderStatusColors[record.status],
						)}
					>
						{OrderStatusLabels[record.status]}
					</div>
				</div>

				{/* 代理商信息 */}
				<div className="grid grid-cols-2 gap-4">
					<div className="space-y-2">
						<div className="text-sm text-[#D4B485]/60">
							来源代理商
						</div>
						<div className="flex items-center justify-between">
							<div className="text-base font-medium text-[#D4B485]">
								{record.fromAgentName}
							</div>
							<div
								className={cn(
									"text-sm",
									AgentRoleColors[
										record.fromAgentRole as keyof typeof AgentRoleColors
									],
								)}
							>
								{
									AgentRoleLabels[
										record.fromAgentRole as keyof typeof AgentRoleLabels
									]
								}
							</div>
						</div>
						<div className="text-sm text-[#D4B485]/60">
							{record.fromAgentId}
						</div>
					</div>

					<div className="space-y-2">
						<div className="text-sm text-[#D4B485]/60">
							目标代理商
						</div>
						<div className="flex items-center justify-between">
							<div className="text-base font-medium text-[#D4B485]">
								{record.toAgentName}
							</div>
							<div
								className={cn(
									"text-sm",
									AgentRoleColors[
										record.toAgentRole as keyof typeof AgentRoleColors
									],
								)}
							>
								{
									AgentRoleLabels[
										record.toAgentRole as keyof typeof AgentRoleLabels
									]
								}
							</div>
						</div>
						<div className="text-sm text-[#D4B485]/60">
							{record.toAgentId}
						</div>
					</div>
				</div>

				{/* 分配信息 */}
				<div className="space-y-4">
					<div className="grid grid-cols-3 gap-4">
						<div>
							<div className="text-sm text-[#D4B485]/60 mb-1">
								分配数量
							</div>
							<div className="text-base font-medium text-[#D4B485]">
								{record.quantity.toLocaleString()}个
							</div>
						</div>
						<div>
							<div className="text-sm text-[#D4B485]/60 mb-1">
								实付金额
							</div>
							<div className="text-base font-medium text-[#D4B485]">
								{record.amount
									? `¥${record.amount.toLocaleString()}`
									: "-"}
							</div>
						</div>
						<div>
							<div className="text-sm text-[#D4B485]/60 mb-1">
								单价
							</div>
							<div className="text-base font-medium text-[#D4B485]">
								{record.unitPrice
									? `¥${record.unitPrice.toLocaleString()}/个`
									: "-"}
							</div>
						</div>
					</div>

					{/* 备注信息 */}
					{(record.remark ||
						record.tradeRemark ||
						record.adminRemark) && (
						<div className="space-y-2 rounded-lg bg-[#D4B485]/5 p-4">
							{record.remark && (
								<div>
									<div className="text-sm text-[#D4B485]/60 mb-1">
										主要备注
									</div>
									<div className="text-sm text-[#D4B485]">
										{record.remark}
									</div>
								</div>
							)}
							{record.tradeRemark && (
								<div className="mt-2">
									<div className="text-sm text-[#D4B485]/60 mb-1">
										交易备注
									</div>
									<div className="text-sm text-[#D4B485]">
										{record.tradeRemark}
									</div>
								</div>
							)}
							{record.adminRemark && (
								<div className="mt-2">
									<div className="text-sm text-[#D4B485]/60 mb-1">
										管理员备注
									</div>
									<div className="text-sm text-[#D4B485]">
										{record.adminRemark}
									</div>
								</div>
							)}
						</div>
					)}
				</div>

				{/* 时间信息 */}
				<div className="grid grid-cols-2 gap-4 border-t border-[#D4B485]/10 pt-4">
					<div>
						<div className="text-sm text-[#D4B485]/60 mb-1">
							创建时间
						</div>
						<div className="text-sm text-[#D4B485]">
							{format(
								new Date(record.createdAt),
								"yyyy-MM-dd HH:mm:ss",
							)}
						</div>
					</div>
					{record.completedAt && (
						<div>
							<div className="text-sm text-[#D4B485]/60 mb-1">
								完成时间
							</div>
							<div className="text-sm text-[#D4B485]">
								{format(
									new Date(record.completedAt),
									"yyyy-MM-dd HH:mm:ss",
								)}
							</div>
						</div>
					)}
					{record.cancelledAt && (
						<div>
							<div className="text-sm text-[#D4B485]/60 mb-1">
								取消时间
							</div>
							<div className="text-sm text-[#D4B485]">
								{format(
									new Date(record.cancelledAt),
									"yyyy-MM-dd HH:mm:ss",
								)}
							</div>
						</div>
					)}
				</div>
			</div>
		</div>
	);
}
