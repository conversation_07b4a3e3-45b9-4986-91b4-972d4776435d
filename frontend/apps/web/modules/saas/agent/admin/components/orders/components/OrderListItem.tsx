"use client";

import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { Calendar, CreditCard, Package, User, Zap } from "lucide-react";
import type { OrderListItem as OrderData } from "../hooks";

interface OrderListItemProps {
	order: OrderData;
	index: number;
	onOrderClick?: (orderId: string) => void;
}

const ORDER_TYPE_LABELS = {
	AGENT_PLAN: "代理商套餐",
	DIGITAL_HUMAN: "数字人",
} as const;

const ORDER_STATUS_LABELS = {
	PENDING: "待支付",
	PAID: "已支付",
	COMPLETED: "已完成",
	FAILED: "失败",
	CANCELLED: "已取消",
	REFUNDED: "已退款",
} as const;

const PAYMENT_STATUS_LABELS = {
	PENDING: "待支付",
	SUCCESS: "支付成功",
	FAILED: "支付失败",
	REFUNDED: "已退款",
} as const;

const PAYMENT_METHOD_LABELS = {
	WECHAT_NATIVE: "微信扫码",
	WECHAT_JSAPI: "微信支付",
	WECHAT_H5: "微信H5",
	WECHAT_APP: "微信APP",
	ALIPAY_PAGE: "支付宝",
	ALIPAY_WAP: "支付宝手机",
} as const;

export function OrderListItem({
	order,
	index,
	onOrderClick,
}: OrderListItemProps) {
	const handleClick = () => {
		onOrderClick?.(order.id);
	};

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ delay: index * 0.1 }}
			onClick={handleClick}
			className={cn(
				"rounded-lg p-6",
				"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
				"border border-[#D4B485]/20",
				"group",
				"hover:border-[#D4B485]/40",
				"transition-all duration-200",
				"relative overflow-hidden",
				"cursor-pointer",
			)}
		>
			{/* 装饰性渐变 */}
			<div className="absolute inset-0 bg-gradient-to-r from-[#D4B485]/5 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

			<div className="relative grid grid-cols-1 gap-4 lg:grid-cols-4">
				{/* 基本信息 */}
				<div className="space-y-3">
					<div className="flex items-center gap-3">
						<div className="flex h-10 w-10 items-center justify-center rounded-lg bg-[#D4B485]/10">
							<Package className="h-5 w-5 text-[#D4B485]" />
						</div>
						<div>
							<p className="font-medium text-[#D4B485]">
								{order.id}
							</p>
							<p className="text-sm text-[#D4B485]/60">
								{ORDER_TYPE_LABELS[order.type]}
							</p>
						</div>
					</div>
					<div className="flex gap-2">
						<div
							className={cn(
								"inline-flex items-center rounded-full px-2 py-1 text-xs font-medium",
								order.status === "COMPLETED" &&
									"bg-emerald-500/10 text-emerald-500",
								order.status === "PAID" &&
									"bg-blue-500/10 text-blue-500",
								order.status === "PENDING" &&
									"bg-yellow-500/10 text-yellow-500",
								(order.status === "FAILED" ||
									order.status === "CANCELLED") &&
									"bg-red-500/10 text-red-500",
								order.status === "REFUNDED" &&
									"bg-gray-500/10 text-gray-500",
							)}
						>
							{ORDER_STATUS_LABELS[order.status]}
						</div>
						<div
							className={cn(
								"inline-flex items-center rounded-full px-2 py-1 text-xs font-medium",
								order.paymentStatus === "SUCCESS" &&
									"bg-emerald-500/10 text-emerald-500",
								order.paymentStatus === "PENDING" &&
									"bg-yellow-500/10 text-yellow-500",
								order.paymentStatus === "FAILED" &&
									"bg-red-500/10 text-red-500",
								order.paymentStatus === "REFUNDED" &&
									"bg-gray-500/10 text-gray-500",
							)}
						>
							{PAYMENT_STATUS_LABELS[order.paymentStatus]}
						</div>
					</div>
				</div>

				{/* 用户信息 */}
				<div className="space-y-2">
					<div className="flex items-center gap-2">
						<User className="h-4 w-4 text-[#D4B485]/60" />
						<span className="text-sm font-medium text-[#D4B485]">
							用户信息
						</span>
					</div>
					{order.consumer && (
						<div className="space-y-1">
							<p className="text-sm text-[#D4B485]/80">
								消费者：{order.consumer.name}
							</p>
							<p className="text-xs text-[#D4B485]/60">
								{order.consumer.phone}
							</p>
						</div>
					)}
					{order.agent && (
						<div className="space-y-1">
							<p className="text-xs text-[#D4B485]/60">
								代理商：{order.agent.name}
							</p>
							<p className="text-xs text-[#D4B485]/40">
								{order.agent.phone}
							</p>
						</div>
					)}
				</div>

				{/* 套餐/商品信息 */}
				<div className="space-y-2">
					<div className="flex items-center gap-2">
						<Zap className="h-4 w-4 text-[#D4B485]/60" />
						<span className="text-sm font-medium text-[#D4B485]">
							商品信息
						</span>
					</div>
					{order.plan && (
						<div className="space-y-1">
							<p className="text-sm text-[#D4B485]/80">
								{order.plan.name}
							</p>
							<p className="text-xs text-[#D4B485]/60">
								算力：
								{order.plan.computingPower.toLocaleString()}
							</p>
							<p className="text-xs text-[#D4B485]/60">
								有效期：{order.plan.validityDays}天
							</p>
						</div>
					)}
					<div className="space-y-1">
						<p className="text-xs text-[#D4B485]/60">
							数量：{order.quantity}
						</p>
						<p className="text-sm font-medium text-[#D4B485]">
							金额：¥{(order.amount / 100).toFixed(2)}
						</p>
					</div>
				</div>

				{/* 时间和支付信息 */}
				<div className="space-y-2">
					<div className="flex items-center gap-2">
						<Calendar className="h-4 w-4 text-[#D4B485]/60" />
						<span className="text-sm font-medium text-[#D4B485]">
							订单信息
						</span>
					</div>
					<div className="space-y-1">
						<p className="text-xs text-[#D4B485]/60">
							创建时间：
							{new Date(order.createdAt).toLocaleString()}
						</p>
						{order.paidAt && (
							<p className="text-xs text-[#D4B485]/60">
								支付时间：
								{new Date(order.paidAt).toLocaleString()}
							</p>
						)}
						{order.completedAt && (
							<p className="text-xs text-[#D4B485]/60">
								完成时间：
								{new Date(order.completedAt).toLocaleString()}
							</p>
						)}
					</div>
					{order.payment && (
						<div className="space-y-1">
							<div className="flex items-center gap-1">
								<CreditCard className="h-3 w-3 text-[#D4B485]/40" />
								<p className="text-xs text-[#D4B485]/60">
									{PAYMENT_METHOD_LABELS[
										order.payment
											.method as keyof typeof PAYMENT_METHOD_LABELS
									] || order.payment.method}
								</p>
							</div>
							{order.payment.transactionId && (
								<p className="text-xs text-[#D4B485]/40">
									交易号：{order.payment.transactionId}
								</p>
							)}
						</div>
					)}
					{order.remark && (
						<p className="text-xs text-[#D4B485]/40">
							备注：{order.remark}
						</p>
					)}
				</div>
			</div>
		</motion.div>
	);
}
