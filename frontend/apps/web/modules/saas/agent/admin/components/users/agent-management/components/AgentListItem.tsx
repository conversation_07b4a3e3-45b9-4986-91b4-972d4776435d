"use client";
import type { <PERSON><PERSON><PERSON> } from "@prisma/client";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";

// 角色名称映射
const roleNameMap: Record<AgentRole, string> = {
	ADMIN: "管理员",
	BRANCH: "分公司",
	DIRECTOR: "联席董事",
	PARTNER: "合伙人",
	SALES: "超级个体",
};

// 为了兼容性保留旧的类型
export interface AgentItemData {
	id: string;
	name: string;
	role: AgentRole;
	email: string;
	phone: string;
	status: string;
	teamSize: number;
	performance: number;
	avatar: string | null;
	hasChildren?: boolean;
}

export interface AgentListItemProps {
	agent: AgentItemData;
	index: number;
	onEdit?: (agent: AgentItemData) => void;
	onViewDetails?: (agent: AgentItemData) => void;
	onViewStructure?: (agent: AgentItemData) => void;
	onDelete?: (agent: AgentItemData) => void;
}

export function AgentListItem({
	agent,
	index,
	onEdit,
	onViewDetails,
	onViewStructure,
	onDelete,
}: AgentListItemProps) {
	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ delay: index * 0.1 }}
			className={cn(
				"rounded-lg p-6",
				"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
				"border border-[#D4B485]/20",
				"group",
				"hover:border-[#D4B485]/40",
				"transition-all duration-200",
				"relative overflow-hidden",
			)}
		>
			{/* 背景装饰 */}
			<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
			<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />

			<div className="relative flex items-center gap-4">
				<Avatar
					className={cn("h-12 w-12", "border border-[#D4B485]/20")}
				>
					<AvatarImage
						src={agent.avatar || undefined}
						alt={agent.name}
					/>
					<AvatarFallback
						className={cn(
							"text-[#1E2023] font-semibold",
							"bg-gradient-to-br from-[#D4B485] to-[#B08968]",
						)}
					>
						{agent.name.slice(0, 1)}
					</AvatarFallback>
				</Avatar>
				<div className="flex-1">
					<div className="flex items-center gap-2">
						<span className="font-medium text-[#D4B485]">
							{agent.name}
						</span>
						<span
							className={cn(
								"rounded-full px-2 py-0.5 text-xs",
								"bg-[#D4B485]/10",
								"text-[#D4B485]/60",
							)}
						>
							{roleNameMap[agent.role] || agent.role}
						</span>
					</div>
					<div className="mt-1 text-sm text-[#D4B485]/40">
						{agent.email} · {agent.phone}
					</div>
				</div>
				<div className="flex items-center gap-8">
					<div className="text-right">
						<div className="text-sm text-[#D4B485]/60">
							团队规模
						</div>
						<div className="font-medium text-[#D4B485]">
							{agent.teamSize}人
						</div>
					</div>
					<div className="text-right">
						<div className="text-sm text-[#D4B485]/60">
							本月业绩
						</div>
						<div className="font-medium text-[#D4B485]">
							¥{(agent.performance || 0).toLocaleString()}
						</div>
					</div>
					<div className="flex gap-2">
						<Button
							variant="outline"
							className={cn(
								"h-8 px-3",
								"bg-[#1A1C1E]",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
								"hover:bg-[#D4B485]/10",
							)}
							onClick={() => onEdit?.(agent)}
						>
							编辑
						</Button>
						<Button
							variant="outline"
							className={cn(
								"h-8 px-3",
								"bg-[#1A1C1E]",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
								"hover:bg-[#D4B485]/10",
							)}
							onClick={() => onViewDetails?.(agent)}
						>
							查看详情
						</Button>
						{agent.hasChildren !== false && (
							<Button
								variant="outline"
								className={cn(
									"h-8 px-3",
									"bg-[#1A1C1E]",
									"border-[#D4B485]/20",
									"text-[#D4B485]",
									"hover:bg-[#D4B485]/10",
								)}
								onClick={() => onViewStructure?.(agent)}
							>
								查看结构
							</Button>
						)}
						<Button
							variant="outline"
							className={cn(
								"h-8 px-3",
								"bg-[#1A1C1E]",
								"border-[#D4B485]/20",
								"text-red-500",
								"hover:bg-red-500/10",
							)}
							onClick={() => onDelete?.(agent)}
						>
							删除
						</Button>
					</div>
				</div>
			</div>
		</motion.div>
	);
}
