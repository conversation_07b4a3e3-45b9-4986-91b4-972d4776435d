import { MessageStatus } from "@repo/api/src/routes/v1/agent/messages/types";
import { logger } from "@repo/logs";
import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";
import { useMessages } from "./useMessages";

// 更新消息状态请求参数
export interface UpdateMessageStatusParams {
	messageId: string;
	status: MessageStatus;
}

// 更新消息状态响应数据
export interface UpdateMessageStatusResponse {
	success: boolean;
	message: {
		id: string;
		userId: string;
		title: string;
		content: string;
		type: string;
		status: MessageStatus;
		readAt: string | null;
		createdAt: string;
		updatedAt: string;
	};
}

/**
 * 管理员消息状态更新hook
 * 用于更新单个消息的状态（已读/未读/归档）
 */
export function useMessageStatus() {
	const [loading, setLoading] = useState(false);
	const [updatedMessage, setUpdatedMessage] = useState<
		UpdateMessageStatusResponse["message"] | null
	>(null);

	// 获取消息上下文
	const { removeMessageFromUnread } = useMessages();

	/**
	 * 更新消息状态
	 */
	const updateStatus = useCallback(
		async (messageId: string, status: MessageStatus) => {
			setLoading(true);
			try {
				logger.info("开始更新管理员消息状态", { messageId, status });

				// 使用类型断言解决类型检查问题
				type PatchOptions = {
					json: { messageId: string; status: MessageStatus };
				};

				// 使用管理员消息API路径
				const response =
					await apiClient.v1.agent.messages.update.$patch({
						json: { messageId, status },
					} as PatchOptions);

				const result =
					(await response.json()) as UpdateMessageStatusResponse;

				logger.info("更新管理员消息状态响应:", result);

				if (response.ok && result.success) {
					setUpdatedMessage(result.message);

					// 根据状态显示不同的成功提示
					const statusText =
						status === MessageStatus.READ
							? "已标记为已读"
							: status === MessageStatus.UNREAD
								? "已标记为未读"
								: "已归档";

					toast.success(`消息${statusText}`);

					// 如果消息被标记为已读或归档，从未读消息列表中移除
					if (
						status === MessageStatus.READ ||
						status === MessageStatus.ARCHIVED
					) {
						removeMessageFromUnread(messageId);
					}

					return { success: true, message: result.message };
				}
				throw new Error("更新管理员消息状态失败");
			} catch (error) {
				logger.error("更新管理员消息状态失败", {
					error,
					messageId,
					status,
				});
				toast.error("更新管理员消息状态失败", {
					description:
						error instanceof Error ? error.message : "请重试",
				});
				return { success: false, error };
			} finally {
				setLoading(false);
			}
		},
		[removeMessageFromUnread],
	);

	return {
		loading,
		updatedMessage,
		updateStatus,
	};
}
