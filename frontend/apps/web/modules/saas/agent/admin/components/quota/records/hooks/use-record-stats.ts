import { logger } from "@repo/logs";
import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";

// 代理商名额统计响应
export interface QuotaStats {
	agentId: string; // 代理商ID
	agentName: string; // 代理商名称
	agentRole: string; // 代理商角色
	teamQuota: number; // 总名额
	usedTeamQuota: number; // 已使用名额
	remainingQuota: number; // 剩余名额
	totalAllocated: number; // 累计分配名额
	monthAllocated: number; // 本月分配名额
}

// API响应类型
interface ApiResponse<T> {
	code: number;
	message?: string;
	data?: T;
	error?: string;
}

// 查询参数
export interface StatsQuery {
	agentId?: string;
	role?: string;
}

export function useRecordStats() {
	const [loading, setLoading] = useState(false);
	const [stats, setStats] = useState<QuotaStats | null>(null);

	const fetchStats = useCallback(async (query: StatsQuery) => {
		setLoading(true);
		try {
			const response = await apiClient.v1.agent.admin.quota.stats.$get({
				query: query,
			});

			const result = (await response.json()) as ApiResponse<QuotaStats>;

			if (result.code === 200 && result.data) {
				setStats(result.data);
			} else {
				toast.error("获取名额统计失败");
				logger.error("[Admin] 获取名额统计失败", {
					error: result.message || result.error,
				});
			}
		} catch (error) {
			toast.error("获取名额统计失败");
			logger.error("[Admin] 获取名额统计失败", {
				error: error instanceof Error ? error.message : "未知错误",
			});
		} finally {
			setLoading(false);
		}
	}, []);

	return {
		loading,
		stats,
		fetchStats,
	};
}
