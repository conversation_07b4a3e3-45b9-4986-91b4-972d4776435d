"use client";

import { <PERSON><PERSON> } from "@ui/components/button";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import { useState } from "react";
import { DashboardHeader } from "./components/DashboardHeader";
import { StatsCardGrid } from "./components/StatsCardGrid";

// 本地定义AgentNode接口以替代删除的导入
export type AgentRole = "ADMIN" | "BRANCH" | "DIRECTOR" | "PARTNER" | "SALES";

export interface AgentNode {
	id: string;
	name: string;
	role: AgentRole;
	teamSize: number;
	children?: AgentNode[];
}

export function AdminDashboard() {
	// 保留状态但默认为null，由于删除了层级树组件，该状态实际上不会被设置
	const [selectedAgent, setSelectedAgent] = useState<AgentNode | null>(null);

	return (
		<div className="w-full space-y-6 p-6">
			{/* 标题和操作栏 */}
			<DashboardHeader title="管理控制台" />

			{/* 统计卡片 */}
			<StatsCardGrid />

			{/* 代理商详情弹窗 - 保留但默认不显示 */}
			<Dialog
				open={!!selectedAgent}
				onOpenChange={() => setSelectedAgent(null)}
			>
				<DialogContent className="sm:max-w-md">
					<DialogHeader>
						<DialogTitle>代理商详情</DialogTitle>
					</DialogHeader>
					{selectedAgent && (
						<div className="grid gap-4 py-4">
							<div className="grid grid-cols-4 items-center gap-4">
								<span className="col-span-1 text-right font-medium">
									ID:
								</span>
								<span className="col-span-3">
									{selectedAgent.id}
								</span>
							</div>
							<div className="grid grid-cols-4 items-center gap-4">
								<span className="col-span-1 text-right font-medium">
									名称:
								</span>
								<span className="col-span-3">
									{selectedAgent.name}
								</span>
							</div>
							<div className="grid grid-cols-4 items-center gap-4">
								<span className="col-span-1 text-right font-medium">
									角色:
								</span>
								<span className="col-span-3">
									{selectedAgent.role}
								</span>
							</div>
							<div className="grid grid-cols-4 items-center gap-4">
								<span className="col-span-1 text-right font-medium">
									团队规模:
								</span>
								<span className="col-span-3">
									{selectedAgent.teamSize} 人
								</span>
							</div>
							<div className="grid grid-cols-4 items-center gap-4">
								<span className="col-span-1 text-right font-medium">
									下级数量:
								</span>
								<span className="col-span-3">
									{selectedAgent.children?.length || 0} 个
								</span>
							</div>
							<div className="mt-4 flex justify-end">
								<Button
									variant="outline"
									onClick={() => setSelectedAgent(null)}
								>
									关闭
								</Button>
							</div>
						</div>
					)}
				</DialogContent>
			</Dialog>
		</div>
	);
}
