"use client";

import type { <PERSON><PERSON><PERSON> } from "@prisma/client";
import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Skeleton } from "@ui/components/skeleton";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import {
	Award,
	BarChart2,
	Building2,
	ChevronLeftIcon,
	ChevronRightIcon,
	Handshake,
	ListIcon,
	RefreshCcwIcon,
	SearchIcon,
	Settings,
	UserPlus,
	UsersIcon,
} from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import type { AgentNode } from "../hooks/use-agent-hierarchy";
import type { AgentListItem } from "../hooks/use-agent-list";

export interface TeamMembersListProps {
	members: AgentListItem[];
	isLoading?: boolean;
	selectedNode?: AgentNode | null;
	onAddMember?: () => void;
	onViewMemberDetails?: (member: AgentListItem) => void;
	onEditMember?: (member: AgentListItem) => void;
	onDeleteMember?: (member: AgentListItem) => void;
	onFilter?: (params: TeamMembersFilterParams) => void;
	onPageChange?: (page: number) => void;
	total?: number;
	currentPage?: number;
	totalPages?: number;
	className?: string;
}

// 团队成员筛选参数接口
export interface TeamMembersFilterParams {
	page: number;
	pageSize: number;
	searchTerm?: string;
	role?: AgentRole | "ALL";
	status?: string;
	includeDescendants?: boolean;
}

// 角色名称映射
const roleNameMap: Record<string, string> = {
	ADMIN: "管理员",
	BRANCH: "分公司",
	DIRECTOR: "联席董事",
	PARTNER: "合伙人",
	SALES: "超级个体",
};

// 角色图标映射
const RoleIcon: Record<
	string,
	React.ComponentType<React.SVGProps<SVGSVGElement>>
> = {
	ADMIN: Settings,
	BRANCH: Building2,
	DIRECTOR: Award,
	PARTNER: Handshake,
	SALES: BarChart2,
};

// 状态映射
const statusOptions = [
	{ value: "ALL", label: "全部状态" },
	{ value: "ACTIVE", label: "已激活" },
	{ value: "DISABLED", label: "已禁用" },
	{ value: "PENDING", label: "待审核" },
];

// 成员列表项组件
const TeamMemberItem = ({
	member,
	index,
	onViewDetails,
	onEdit,
	onDelete,
}: {
	member: AgentListItem;
	index: number;
	onViewDetails?: (member: AgentListItem) => void;
	onEdit?: (member: AgentListItem) => void;
	onDelete?: (member: AgentListItem) => void;
}) => {
	const IconComponent = RoleIcon[member.role] || Settings;

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ delay: index * 0.1 }}
			className={cn(
				"rounded-lg p-6",
				"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
				"border border-[#D4B485]/20",
				"group",
				"hover:border-[#D4B485]/40",
				"transition-all duration-200",
				"relative overflow-hidden",
			)}
		>
			{/* 背景装饰 */}
			<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
			<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />

			<div className="relative flex items-center gap-4">
				<Avatar
					className={cn("h-12 w-12", "border border-[#D4B485]/20")}
				>
					<AvatarImage
						src={member.avatar || undefined}
						alt={member.name}
					/>
					<AvatarFallback
						className={cn(
							"text-[#1E2023] font-semibold",
							"bg-gradient-to-br from-[#D4B485] to-[#B08968]",
						)}
					>
						{member.name.slice(0, 1)}
					</AvatarFallback>
				</Avatar>
				<div className="flex-1">
					<div className="flex items-center gap-2">
						<span className="font-medium text-[#D4B485]">
							{member.name}
						</span>
						<span
							className={cn(
								"inline-flex items-center gap-1 rounded-full px-2 py-0.5 text-xs",
								"bg-[#D4B485]/10",
								"text-[#D4B485]/60",
							)}
						>
							<IconComponent className="h-3 w-3" />
							{roleNameMap[member.role] || member.role}
						</span>
					</div>
					<div className="mt-1 text-sm text-[#D4B485]/40">
						{member.email} · {member.phone}
					</div>
				</div>
				<div className="flex items-center gap-8">
					<div className="text-right">
						<div className="text-sm text-[#D4B485]/60">
							团队规模
						</div>
						<div className="font-medium text-[#D4B485]">
							{member.teamSize}人
						</div>
					</div>
					<div className="text-right">
						<div className="text-sm text-[#D4B485]/60">
							本月业绩
						</div>
						<div className="font-medium text-[#D4B485]">
							¥{(member.performance || 0).toLocaleString()}
						</div>
					</div>
					<div className="flex gap-2">
						{onEdit && (
							<Button
								variant="outline"
								className={cn(
									"h-8 px-3",
									"bg-[#1A1C1E]",
									"border-[#D4B485]/20",
									"text-[#D4B485]",
									"hover:bg-[#D4B485]/10",
								)}
								onClick={() => onEdit(member)}
							>
								编辑
							</Button>
						)}
						{onViewDetails && (
							<Button
								variant="outline"
								className={cn(
									"h-8 px-3",
									"bg-[#1A1C1E]",
									"border-[#D4B485]/20",
									"text-[#D4B485]",
									"hover:bg-[#D4B485]/10",
								)}
								onClick={() => onViewDetails(member)}
							>
								查看详情
							</Button>
						)}
						{member.hasChildren && (
							<Button
								variant="outline"
								className={cn(
									"h-8 px-3",
									"bg-[#1A1C1E]",
									"border-[#D4B485]/20",
									"text-[#D4B485]",
									"hover:bg-[#D4B485]/10",
								)}
								onClick={() => onViewDetails?.(member)}
							>
								查看结构
							</Button>
						)}
						{onDelete && (
							<Button
								variant="outline"
								className={cn(
									"h-8 px-3",
									"bg-[#1A1C1E]",
									"border-[#D4B485]/20",
									"text-red-500",
									"hover:bg-red-500/10",
								)}
								onClick={() => onDelete(member)}
							>
								删除
							</Button>
						)}
					</div>
				</div>
			</div>
		</motion.div>
	);
};

export function TeamMembersList({
	members,
	isLoading = false,
	selectedNode,
	onAddMember,
	onViewMemberDetails,
	onEditMember,
	onDeleteMember,
	onFilter,
	onPageChange,
	total = 0,
	currentPage = 1,
	totalPages = 1,
	className,
}: TeamMembersListProps) {
	const [searchTerm, setSearchTerm] = useState("");
	const [role, setRole] = useState<string>("ALL");
	const [status, setStatus] = useState<string>("ALL");
	const [debouncedSearch, setDebouncedSearch] = useState("");

	// 处理搜索防抖
	useEffect(() => {
		const timer = setTimeout(() => {
			setDebouncedSearch(searchTerm);
		}, 500);

		return () => clearTimeout(timer);
	}, [searchTerm]);

	// 处理筛选条件变化
	const handleFilterChange = useCallback(() => {
		onFilter?.({
			page: 1,
			pageSize: 5,
			searchTerm: debouncedSearch,
			role: role as AgentRole | "ALL",
			status: status,
			includeDescendants: true,
		});
	}, [debouncedSearch, onFilter, role, status]);

	// 当防抖搜索词变化时触发筛选
	useEffect(() => {
		handleFilterChange();
	}, [handleFilterChange]);

	// 处理页码变化
	const handlePageChange = (page: number) => {
		onPageChange?.(page);
	};

	// 重置所有筛选
	const handleReset = () => {
		setSearchTerm("");
		setRole("ALL");
		setStatus("ALL");
		onFilter?.({
			page: 1,
			pageSize: 5,
			includeDescendants: true,
		});
	};

	// 生成页码数组
	const getPageNumbers = () => {
		const pageNumbers = [];
		const maxPagesToShow = 5; // 最多显示的页码数

		if (totalPages <= maxPagesToShow) {
			// 如果总页数小于等于最大显示页码数，显示所有页码
			for (let i = 1; i <= totalPages; i++) {
				pageNumbers.push(i);
			}
		} else {
			// 否则，显示当前页附近的页码
			let startPage = Math.max(
				1,
				currentPage - Math.floor(maxPagesToShow / 2),
			);
			let endPage = startPage + maxPagesToShow - 1;

			if (endPage > totalPages) {
				endPage = totalPages;
				startPage = Math.max(1, endPage - maxPagesToShow + 1);
			}

			for (let i = startPage; i <= endPage; i++) {
				pageNumbers.push(i);
			}
		}

		return pageNumbers;
	};

	return (
		<div className={cn("space-y-6 mt-8", className)}>
			{/* 标题区域 */}
			<div className="flex items-center justify-between">
				<div className="flex items-center gap-3">
					<div
						className={cn(
							"flex h-10 w-10 shrink-0 items-center justify-center",
							"rounded-lg bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
							"ring-1 ring-[#D4B485]/20",
							"shadow-[0_0_0_4px_rgba(212,180,133,0.02)]",
						)}
					>
						<UsersIcon className="h-5 w-5 text-[#D4B485]" />
					</div>
					<h3
						className={cn(
							"font-medium text-xl",
							zywhFont.className,
							"leading-none",
							"tracking-wide",
						)}
						style={{
							background:
								"linear-gradient(135deg, rgba(229,201,165,0.95) 0%, rgba(212,180,133,0.9) 100%)",
							WebkitBackgroundClip: "text",
							WebkitTextFillColor: "transparent",
							backgroundClip: "text",
						}}
					>
						团队成员列表
						{selectedNode && (
							<span className="ml-2 text-base font-normal text-[#D4B485]/80">
								({selectedNode.name}的团队)
							</span>
						)}
					</h3>
				</div>
			</div>

			{/* 搜索和筛选区域 */}
			<div className="rounded-lg bg-[#1E2023]/50 p-4">
				<div className="flex flex-wrap items-center gap-4">
					{/* 搜索框 */}
					<div className="relative flex-1">
						<SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-[#D4B485]/40" />
						<Input
							placeholder="搜索成员名称、邮箱或手机号"
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className={cn(
								"pl-9",
								"bg-[#1E2023]/50",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
								"placeholder:text-[#D4B485]/40",
								"focus:border-[#D4B485]/40",
								"focus:ring-[#D4B485]/20",
							)}
							onKeyDown={(e) => {
								if (e.key === "Enter") {
									handleFilterChange();
								}
							}}
						/>
					</div>

					{/* 角色筛选 */}
					<Select
						value={role}
						onValueChange={(value) => setRole(value)}
					>
						<SelectTrigger
							className={cn(
								"w-[160px]",
								"bg-[#1E2023]/50",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
								"focus:border-[#D4B485]/40",
								"focus:ring-[#D4B485]/20",
							)}
						>
							<SelectValue placeholder="选择角色" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem
								value="ALL"
								className="text-[#D4B485] focus:bg-[#D4B485]/10"
							>
								全部角色
							</SelectItem>
							{Object.entries(roleNameMap).map(
								([value, label]) => (
									<SelectItem
										key={value}
										value={value}
										className="text-[#D4B485] focus:bg-[#D4B485]/10"
									>
										{label}
									</SelectItem>
								),
							)}
						</SelectContent>
					</Select>

					{/* 状态筛选 */}
					<Select
						value={status}
						onValueChange={(value) => setStatus(value)}
					>
						<SelectTrigger
							className={cn(
								"w-[160px]",
								"bg-[#1E2023]/50",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
								"focus:border-[#D4B485]/40",
								"focus:ring-[#D4B485]/20",
							)}
						>
							<SelectValue placeholder="选择状态" />
						</SelectTrigger>
						<SelectContent>
							{statusOptions.map((option) => (
								<SelectItem
									key={option.value}
									value={option.value}
									className="text-[#D4B485] focus:bg-[#D4B485]/10"
								>
									{option.label}
								</SelectItem>
							))}
						</SelectContent>
					</Select>

					{/* 搜索按钮 */}
					<Button
						type="button"
						className={cn(
							"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
							"text-white",
							"border-none",
							"shadow-[0_8px_16px_-4px_rgba(212,180,133,0.3)]",
							"hover:shadow-[0_12px_20px_-4px_rgba(212,180,133,0.4)]",
							"transition-all duration-200",
						)}
						onClick={handleFilterChange}
					>
						搜索
					</Button>

					{/* 刷新按钮 */}
					<Button
						variant="outline"
						size="icon"
						className={cn(
							"bg-[#1E2023]/50 border-[#D4B485]/20",
							"text-[#D4B485] hover:text-[#E5C9A5]",
							"hover:bg-[#D4B485]/10",
							"transition duration-200",
							"w-10 h-10",
							"flex items-center justify-center",
						)}
						onClick={handleReset}
					>
						<RefreshCcwIcon className="h-4 w-4" />
					</Button>

					{/* 添加成员按钮 */}
					{onAddMember && (
						<Button
							size="sm"
							className={cn(
								"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
								"text-[#1A1C1E]",
								"hover:from-[#B08968] hover:to-[#D4B485]",
							)}
							onClick={onAddMember}
						>
							<UserPlus className="h-4 w-4 mr-2" />
							添加成员
						</Button>
					)}
				</div>
			</div>

			{/* 成员列表区域 */}
			<div className="space-y-4">
				{isLoading ? (
					// 加载状态
					["skeleton-1", "skeleton-2", "skeleton-3"].map((id) => (
						<Skeleton
							key={id}
							className="h-[92px] w-full rounded-lg bg-[#1E2023]/60"
						/>
					))
				) : members.length > 0 ? (
					<>
						{/* 显示成员列表 */}
						{members.map((member, index) => (
							<TeamMemberItem
								key={member.id}
								member={member}
								index={index}
								onViewDetails={onViewMemberDetails}
								onEdit={onEditMember}
								onDelete={onDeleteMember}
							/>
						))}

						{/* 分页控件 */}
						{totalPages > 1 && (
							<div className="flex items-center justify-between pt-4 border-t border-[#D4B485]/10">
								<div className="text-sm text-[#D4B485]/60">
									显示{" "}
									{Math.min(total, (currentPage - 1) * 5 + 1)}
									-{Math.min(total, currentPage * 5)} 条，共{" "}
									{total} 条
								</div>
								<div className="flex items-center space-x-2">
									<Button
										variant="outline"
										size="sm"
										className={cn(
											"bg-[#1A1C1E]",
											"border-[#D4B485]/20",
											"text-[#D4B485]",
											"hover:bg-[#D4B485]/10",
											"h-8 w-8 p-0",
										)}
										disabled={currentPage === 1}
										onClick={() =>
											handlePageChange(currentPage - 1)
										}
									>
										<ChevronLeftIcon className="h-4 w-4" />
									</Button>

									{getPageNumbers().map((page) => (
										<Button
											key={page}
											variant={
												page === currentPage
													? "primary"
													: "outline"
											}
											size="sm"
											className={cn(
												page === currentPage
													? "bg-[#D4B485] text-[#1A1C1E]"
													: "bg-[#1A1C1E] border-[#D4B485]/20 text-[#D4B485] hover:bg-[#D4B485]/10",
												"h-8 w-8 p-0",
											)}
											onClick={() =>
												handlePageChange(page)
											}
										>
											{page}
										</Button>
									))}

									<Button
										variant="outline"
										size="sm"
										className={cn(
											"bg-[#1A1C1E]",
											"border-[#D4B485]/20",
											"text-[#D4B485]",
											"hover:bg-[#D4B485]/10",
											"h-8 w-8 p-0",
										)}
										disabled={currentPage === totalPages}
										onClick={() =>
											handlePageChange(currentPage + 1)
										}
									>
										<ChevronRightIcon className="h-4 w-4" />
									</Button>
								</div>
							</div>
						)}
					</>
				) : (
					// 无数据状态
					<div className="py-12 flex flex-col items-center justify-center text-center">
						<div className="rounded-full bg-[#D4B485]/10 p-4 mb-4">
							<ListIcon className="h-8 w-8 text-[#D4B485]/40" />
						</div>
						<h3 className="text-lg font-medium text-[#D4B485] mb-1">
							暂无团队成员
						</h3>
						<p className="text-[#D4B485]/60 max-w-sm">
							该团队下暂无成员或全部成员已被筛选。
							{onAddMember && (
								<span className="ml-1">
									您可以点击"添加成员"按钮添加新的成员。
								</span>
							)}
						</p>
					</div>
				)}
			</div>
		</div>
	);
}
