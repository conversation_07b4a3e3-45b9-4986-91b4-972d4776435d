"use client";

import { <PERSON><PERSON>, AlertDescription } from "@ui/components/alert";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import {
	<PERSON>,
	Card<PERSON>ontent,
	<PERSON><PERSON><PERSON>er,
	<PERSON><PERSON>eader,
	CardTitle,
} from "@ui/components/card";
import { Input } from "@ui/components/input";
import { useToast } from "@ui/hooks/use-toast";
import { cn } from "@ui/lib";
import { format } from "date-fns";
import { AnimatePresence, motion } from "framer-motion";
import {
	AlertCircle,
	Calendar,
	CheckCircle2,
	Clock,
	Crown,
	Loader2,
	MapPin,
	Phone,
	Search,
	User as UserIcon,
	XCircle,
	Zap,
} from "lucide-react";
import { useState } from "react";
import { useAdminUsers } from "../hooks/use-admin-users";
import type { UserProfile } from "../types";
import { OptimizedAvatar } from "./OptimizedAvatar";

interface UserSelectionProps {
	onUserSelect: (user: UserProfile) => void;
}

const UserInfoRow = ({
	icon,
	label,
	value,
	highlight = false,
}: {
	icon: React.ReactNode;
	label: string;
	value: string | React.ReactNode;
	highlight?: boolean;
}) => (
	<div
		className={cn(
			"flex items-center text-xs",
			highlight ? "text-[#D4B485]" : "text-muted-foreground",
		)}
	>
		<div className="flex items-center justify-center w-4 h-4 mr-2">
			{icon}
		</div>
		<span className="w-16 shrink-0 text-[#D4B485]/60">{label}:</span>
		<span
			className={cn(
				"truncate font-mono",
				highlight ? "text-[#D4B485] font-medium" : "text-neutral-300",
			)}
		>
			{value}
		</span>
	</div>
);

export function UserSelection({ onUserSelect }: UserSelectionProps) {
	const [searchTerm, setSearchTerm] = useState("");
	const { users, isLoading, error, searchUsers } = useAdminUsers();
	const { toast } = useToast();

	const handleSearch = (e: React.FormEvent) => {
		e.preventDefault();
		if (!searchTerm.trim()) {
			toast({
				title: "请输入手机号",
				description: "请输入要搜索的用户手机号",
				variant: "error",
			});
			return;
		}
		searchUsers(searchTerm.trim());
	};

	// 获取套餐状态的显示信息
	const getPlanStatusInfo = (status: UserProfile["planStatus"]) => {
		switch (status) {
			case "active":
				return {
					icon: <CheckCircle2 className="h-3 w-3 text-green-500" />,
					text: "有效",
					className:
						"bg-green-500/10 text-green-500 border-green-500/20",
				};
			case "expired":
				return {
					icon: <XCircle className="h-3 w-3 text-red-500" />,
					text: "已过期",
					className: "bg-red-500/10 text-red-500 border-red-500/20",
				};
			default:
				return {
					icon: <AlertCircle className="h-3 w-3 text-gray-500" />,
					text: "无套餐",
					className:
						"bg-gray-500/10 text-gray-500 border-gray-500/20",
				};
		}
	};

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			exit={{ opacity: 0, y: -20 }}
		>
			{/* 搜索表单 */}
			<form onSubmit={handleSearch} className="mb-8">
				<div className="flex items-center gap-3">
					<div className="relative flex-1">
						<Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[#D4B485]/60" />
						<Input
							type="text"
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							placeholder="输入用户手机号进行搜索（支持模糊搜索）"
							className={cn(
								"pl-10 h-12 text-base",
								"bg-gradient-to-r from-[#1E2023] to-[#1A1C1E]",
								"border border-white/10 focus:border-[#D4B485]/50",
								"text-white placeholder:text-white/40",
								"focus:ring-2 focus:ring-[#D4B485]/20",
							)}
						/>
					</div>
					<Button
						type="submit"
						disabled={isLoading || !searchTerm.trim()}
						className={cn(
							"h-12 px-6",
							"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
							"hover:from-[#E5C9A5] hover:to-[#D4B485]",
							"text-black font-medium",
							"disabled:opacity-50 disabled:cursor-not-allowed",
							"transition-all duration-200",
						)}
					>
						{isLoading ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								搜索中...
							</>
						) : (
							<>
								<Search className="mr-2 h-4 w-4" />
								搜索用户
							</>
						)}
					</Button>
				</div>
			</form>

			{/* 错误提示 */}
			{error && (
				<Alert className="mb-6 border-red-500/20 bg-red-500/10">
					<AlertCircle className="h-4 w-4 text-red-500" />
					<AlertDescription className="text-red-400">
						{error.message || "搜索用户时发生错误，请稍后重试"}
					</AlertDescription>
				</Alert>
			)}

			{/* 加载状态 */}
			{isLoading && (
				<motion.div
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					className="flex flex-col justify-center items-center h-40 space-y-4"
				>
					<div className="relative">
						<div className="w-12 h-12 border-4 border-[#D4B485]/20 border-t-[#D4B485] rounded-full animate-spin" />
						<div className="absolute inset-0 w-12 h-12 border-4 border-transparent border-r-[#D4B485]/40 rounded-full animate-spin animation-delay-150" />
					</div>
					<p className="text-[#D4B485]/80 text-sm">正在搜索用户...</p>
				</motion.div>
			)}

			{/* 用户列表 */}
			{!isLoading && users.length > 0 && (
				<AnimatePresence>
					<motion.div
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6"
					>
						{users.map((user, index) => {
							const planStatus = getPlanStatusInfo(
								user.planStatus,
							);
							return (
								<motion.div
									key={user.id}
									initial={{ opacity: 0, y: 20 }}
									animate={{ opacity: 1, y: 0 }}
									transition={{ delay: index * 0.1 }}
								>
									<Card
										className={cn(
											"group overflow-hidden transition-all duration-300",
											"bg-gradient-to-br from-[#1E2023] via-[#1C1F22] to-[#1A1C1E]",
											"border border-white/10 hover:border-[#D4B485]/50",
											"hover:shadow-2xl hover:shadow-[#D4B485]/10",
											"hover:-translate-y-2 hover:scale-[1.02]",
											"cursor-pointer",
										)}
									>
										<CardHeader className="p-5">
											<div className="flex items-center gap-4">
												<OptimizedAvatar
													src={user.avatar}
													name={user.name}
													size="lg"
													showOnlineStatus={true}
													className="group-hover:scale-110 transition-transform duration-200"
												/>
												<div className="flex-1 min-w-0">
													<CardTitle className="text-white text-lg font-semibold truncate group-hover:text-[#D4B485] transition-colors">
														{user.name}
													</CardTitle>
													<p className="text-[#D4B485]/60 font-mono text-sm">
														{user.phone}
													</p>
													{/* 套餐状态徽章 */}
													<Badge
														className={cn(
															"mt-2 text-xs border",
															planStatus.className,
														)}
													>
														{planStatus.icon}
														<span className="ml-1">
															{planStatus.text}
														</span>
													</Badge>
												</div>
											</div>
										</CardHeader>

										<CardContent className="p-5 pt-0 space-y-3">
											<UserInfoRow
												icon={
													<Zap className="h-3 w-3 text-[#D4B485]" />
												}
												label="算力"
												value={`${user.computingPower.toLocaleString()}`}
												highlight={
													user.computingPower > 0
												}
											/>
											<UserInfoRow
												icon={
													<Crown className="h-3 w-3 text-yellow-500" />
												}
												label="套餐"
												value={
													user.currentPlanName ||
													"无套餐"
												}
											/>
											<UserInfoRow
												icon={
													<Clock className="h-3 w-3 text-blue-400" />
												}
												label="到期"
												value={
													user.planExpireAt
														? format(
																new Date(
																	user.planExpireAt,
																),
																"MM-dd HH:mm",
															)
														: "N/A"
												}
											/>
											<UserInfoRow
												icon={
													<Calendar className="h-3 w-3 text-purple-400" />
												}
												label="注册"
												value={format(
													new Date(user.createdAt),
													"yyyy-MM-dd",
												)}
											/>
											{(() => {
												// 使用后端返回的地理位置信息
												const locationInfo =
													user.lastLoginLocation;
												const hasLocation =
													locationInfo &&
													locationInfo.location !==
														"未知位置";

												// 根据位置类型和是否为内网IP确定颜色
												const getLocationColor = () => {
													if (!locationInfo) {
														return "text-gray-500";
													}

													if (
														locationInfo.isPrivate
													) {
														return "text-blue-400"; // 内网IP用蓝色
													}

													switch (
														locationInfo.locationType
													) {
														case "city":
															// 一线城市用绿色
															if (
																locationInfo.location.includes(
																	"北京",
																) ||
																locationInfo.location.includes(
																	"上海",
																) ||
																locationInfo.location.includes(
																	"广州",
																) ||
																locationInfo.location.includes(
																	"深圳",
																)
															) {
																return "text-green-400";
															}
															return "text-yellow-400"; // 其他城市用黄色
														case "province":
															return "text-yellow-400"; // 省份用黄色
														case "country":
															if (
																locationInfo.location.includes(
																	"中国",
																)
															) {
																return "text-yellow-400"; // 中国其他地区用黄色
															}
															return "text-purple-400"; // 海外用紫色
														default:
															return "text-gray-500"; // 未知用灰色
													}
												};

												const locationColor =
													getLocationColor();

												return (
													<UserInfoRow
														icon={
															<MapPin
																className={cn(
																	"h-3 w-3",
																	locationColor,
																)}
															/>
														}
														label="位置"
														value={
															<div className="flex flex-col">
																<span
																	className={cn(
																		"text-xs",
																		locationColor,
																	)}
																>
																	{hasLocation
																		? locationInfo.location
																		: "未知位置"}
																</span>
																{user.lastLoginIp && (
																	<span className="text-xs text-white/40 font-mono">
																		{
																			user.lastLoginIp
																		}
																	</span>
																)}
																{/* 显示数据源和可信度（仅在调试模式下） */}
																{process.env
																	.NODE_ENV ===
																	"development" &&
																	locationInfo && (
																		<span className="text-xs text-white/30">
																			{
																				locationInfo.dataSource
																			}{" "}
																			(
																			{Math.round(
																				locationInfo.confidence *
																					100,
																			)}
																			%)
																		</span>
																	)}
															</div>
														}
													/>
												);
											})()}
										</CardContent>

										<CardFooter className="p-5 pt-0">
											<Button
												onClick={() =>
													onUserSelect(user)
												}
												className={cn(
													"w-full h-11 font-medium transition-all duration-200",
													"bg-gradient-to-r from-transparent to-transparent",
													"border-2 border-[#D4B485]/50 text-[#D4B485]",
													"hover:from-[#D4B485] hover:to-[#B08968]",
													"hover:text-black hover:border-[#D4B485]",
													"hover:shadow-lg hover:shadow-[#D4B485]/25",
													"group-hover:scale-105",
												)}
											>
												<UserIcon className="mr-2 h-4 w-4" />
												选择该用户
											</Button>
										</CardFooter>
									</Card>
								</motion.div>
							);
						})}
					</motion.div>
				</AnimatePresence>
			)}

			{/* 空状态 */}
			{!isLoading && users.length === 0 && searchTerm && (
				<motion.div
					initial={{ opacity: 0, scale: 0.95 }}
					animate={{ opacity: 1, scale: 1 }}
					className="text-center py-16"
				>
					<div className="flex flex-col items-center space-y-4">
						<div className="w-20 h-20 rounded-full bg-gradient-to-br from-[#D4B485]/10 to-[#B08968]/10 flex items-center justify-center">
							<Search className="h-8 w-8 text-[#D4B485]/60" />
						</div>
						<div className="space-y-2">
							<h3 className="text-lg font-medium text-white">
								未找到匹配的用户
							</h3>
							<p className="text-[#D4B485]/60 max-w-md">
								没有找到手机号包含 "{searchTerm}"
								的用户，请检查输入是否正确或尝试其他关键词
							</p>
						</div>
						<Button
							variant="ghost"
							onClick={() => setSearchTerm("")}
							className="text-[#D4B485] hover:bg-[#D4B485]/10"
						>
							清空搜索
						</Button>
					</div>
				</motion.div>
			)}

			{/* 初始状态提示 */}
			{!isLoading && users.length === 0 && !searchTerm && (
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					className="text-center py-16"
				>
					<div className="flex flex-col items-center space-y-4">
						<div className="w-20 h-20 rounded-full bg-gradient-to-br from-[#D4B485]/10 to-[#B08968]/10 flex items-center justify-center">
							<Phone className="h-8 w-8 text-[#D4B485]/60" />
						</div>
						<div className="space-y-2">
							<h3 className="text-lg font-medium text-white">
								开始搜索用户
							</h3>
							<p className="text-[#D4B485]/60 max-w-md">
								请在上方输入用户手机号进行搜索，支持模糊匹配
							</p>
						</div>
					</div>
				</motion.div>
			)}
		</motion.div>
	);
}
