"use client";

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import type { ConsumerListItem as ConsumerListItemType } from "../hooks/use-consumer-list";

export interface ConsumerListItemProps {
	consumer: ConsumerListItemType;
	index: number;
	onViewDetails?: (consumer: ConsumerListItemType) => void;
}

export function ConsumerListItem({
	consumer,
	index,
	onViewDetails,
}: ConsumerListItemProps) {
	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ delay: index * 0.1 }}
			className={cn(
				"rounded-lg p-6",
				"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
				"border border-[#D4B485]/20",
				"group",
				"hover:border-[#D4B485]/40",
				"transition-all duration-200",
				"relative overflow-hidden",
			)}
		>
			{/* 背景装饰 */}
			<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
			<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />

			<div className="relative flex items-center gap-4">
				<Avatar
					className={cn("h-12 w-12", "border border-[#D4B485]/20")}
				>
					<AvatarImage
						src={consumer.avatar || undefined}
						alt={consumer.name}
					/>
					<AvatarFallback
						className={cn(
							"text-[#1E2023] font-semibold",
							"bg-gradient-to-br from-[#D4B485] to-[#B08968]",
						)}
					>
						{consumer.name.slice(0, 1)}
					</AvatarFallback>
				</Avatar>
				<div className="flex-1">
					<div className="flex items-center gap-2">
						<span className="font-medium text-[#D4B485]">
							{consumer.name}
						</span>
						<span
							className={cn(
								"rounded-full px-2 py-0.5 text-xs",
								"bg-[#D4B485]/10",
								"text-[#D4B485]/60",
							)}
						>
							{consumer.role === "INTERNAL"
								? "内部测试"
								: "普通用户"}
						</span>
					</div>
					<div className="mt-1 text-sm text-[#D4B485]/40">
						{consumer.email} · {consumer.phone}
					</div>
				</div>
				<div className="flex items-center gap-8">
					<div className="text-right">
						<div className="text-sm text-[#D4B485]/60">
							剩余算力
						</div>
						<div className="font-medium text-[#D4B485]">
							{consumer.computingPower}
						</div>
					</div>
					<div className="text-right">
						<div className="text-sm text-[#D4B485]/60">
							归属代理
						</div>
						<div className="font-medium text-[#D4B485]">
							{consumer.agentName || "未分配"}
						</div>
					</div>
					<div className="flex items-center">
						<Button
							variant="outline"
							className="bg-[#1A1C1E] border-[#D4B485]/20 text-[#D4B485] hover:bg-[#D4B485]/10"
							onClick={() => onViewDetails?.(consumer)}
						>
							查看详情
						</Button>
					</div>
				</div>
			</div>
		</motion.div>
	);
}
