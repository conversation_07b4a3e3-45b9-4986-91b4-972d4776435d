import type { <PERSON><PERSON><PERSON> } from "@prisma/client";
import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Button } from "@ui/components/button";
import { Card } from "@ui/components/card";
import { Input } from "@ui/components/input";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { Search } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { getQuotaRule, QUOTA_RULES } from "../config/quota-rules";
import { useDebounce, useSearchAgents } from "../hooks";
import { AgentPagination } from "./AgentPagination";

interface Agent {
	id: string;
	name: string;
	role: string;
	email: string;
	phone: string;
	teamQuota: number;
	usedTeamQuota: number;
	remainingQuota: number;
	teamSize: number;
	totalSales: number;
	monthSales: number;
	createdAt: string;
}

interface FormData {
	agent: Agent | null;
	quantity: number | null;
}

interface StepData {
	agent?: Agent;
	quantity?: number;
}

interface SelectAgentProps {
	onNext: (data: StepData) => void;
	formData: FormData;
}

export function SelectAgent({ onNext, formData }: SelectAgentProps) {
	const {
		agents,
		total,
		loading,
		currentPage,
		pageSize,
		searchAgentsByPhone,
		setCurrentPage,
	} = useSearchAgents();
	const [selectedAgent, setSelectedAgent] = useState<Agent | null>(
		formData.agent,
	);
	const [phoneKeyword, setPhoneKeyword] = useState("");

	// 使用防抖Hook - 手机号搜索800ms
	const debouncedPhoneKeyword = useDebounce(phoneKeyword, 800);

	// 处理手机号搜索逻辑 - 使用防抖后的关键词
	useEffect(() => {
		if (debouncedPhoneKeyword.trim()) {
			// 有输入内容时才搜索，重置到第一页
			setCurrentPage(1);
			searchAgentsByPhone({
				phone: debouncedPhoneKeyword.trim(),
				page: 1,
				pageSize: pageSize,
			});
		}
		// 注意：没有输入内容时，不执行任何搜索
	}, [debouncedPhoneKeyword, searchAgentsByPhone, setCurrentPage, pageSize]);

	// 计算分页信息
	const totalPages = Math.ceil(total / pageSize);

	// 分页处理函数
	const handlePageChange = useCallback(
		(page: number) => {
			setCurrentPage(page);
			if (debouncedPhoneKeyword.trim()) {
				searchAgentsByPhone({
					phone: debouncedPhoneKeyword.trim(),
					page,
					pageSize: pageSize,
				});
			}
		},
		[setCurrentPage, debouncedPhoneKeyword, searchAgentsByPhone, pageSize],
	);

	return (
		<div className="space-y-8">
			{/* 大标题 */}
			<div className="space-y-2 text-center">
				<h3
					className={cn("text-2xl font-semibold", zywhFont.className)}
					style={{
						background:
							"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
						WebkitBackgroundClip: "text",
						WebkitTextFillColor: "transparent",
						backgroundClip: "text",
						textShadow:
							"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
					}}
				>
					选择代理商
				</h3>
				<p className="text-[#D4B485]/60">
					从代理商列表中选择要分配名额的账户
				</p>
			</div>

			{/* 搜索和筛选 */}
			<Card
				className={cn(
					"p-4",
					"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
					"border border-[#D4B485]/20",
					"group",
					"hover:border-[#D4B485]/40",
					"transition-all duration-500",
				)}
			>
				<div className="space-y-4">
					{/* 手机号搜索标题 */}
					<div className="flex items-center gap-2">
						<div className="text-lg font-medium text-[#D4B485]">
							📱 手机号搜索代理商
						</div>
					</div>

					{/* 手机号搜索输入区域 */}
					<div className="flex items-center gap-4">
						<div className="relative flex-1">
							<Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-[#D4B485]/40" />
							<Input
								placeholder="请输入代理商手机号进行精准搜索"
								className={cn(
									"pl-10",
									"bg-[#1A1C1E]",
									"border-[#D4B485]/20",
									"text-[#D4B485]",
									"placeholder:text-[#D4B485]/40",
								)}
								value={phoneKeyword}
								onChange={(e) =>
									setPhoneKeyword(e.target.value)
								}
							/>
						</div>
						<div className="flex items-center gap-2 text-sm">
							{phoneKeyword !== debouncedPhoneKeyword &&
								phoneKeyword.trim() && (
									<div className="text-[#D4B485]/60">
										等待输入完成...
									</div>
								)}
							{loading && (
								<div className="text-[#D4B485]/60">
									搜索中...
								</div>
							)}
							{!loading &&
								debouncedPhoneKeyword.trim() &&
								agents.length === 0 && (
									<div className="text-[#D4B485]/60">
										未找到匹配结果
									</div>
								)}
						</div>
					</div>
				</div>
			</Card>

			{/* 代理商列表 */}
			<div className="grid gap-4">
				{loading && (
					<div className="text-center py-8">
						<div className="text-[#D4B485]/60">
							正在搜索代理商...
						</div>
					</div>
				)}
				{!loading && agents.length === 0 && (
					<div className="text-center py-12">
						<div className="text-[#D4B485]/60 space-y-2">
							{phoneKeyword.trim() ? (
								<>
									<div>
										📱 未找到手机号包含 "{phoneKeyword}"
										的代理商
									</div>
									<div className="text-sm">
										请检查手机号是否正确，或尝试输入部分号码
									</div>
								</>
							) : (
								<>
									<div>📱 请输入代理商手机号进行搜索</div>
									<div className="text-sm">
										支持模糊匹配，输入部分号码即可
									</div>
								</>
							)}
						</div>
					</div>
				)}
				{!loading &&
					agents.map((agent, index) => (
						<motion.div
							key={agent.id}
							initial={{ opacity: 0, y: 20 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ delay: index * 0.1 }}
							className={cn(
								"rounded-lg p-6",
								"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
								"border border-[#D4B485]/20",
								"group",
								"hover:border-[#D4B485]/40",
								"transition-all duration-200",
								"relative overflow-hidden",
								"cursor-pointer",
								selectedAgent?.id === agent.id &&
									"border-[#D4B485] bg-[#D4B485]/5",
							)}
							onClick={() => setSelectedAgent(agent)}
						>
							{/* 背景装饰 */}
							<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
							<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />

							<div className="relative flex items-center gap-4">
								<Avatar
									className={cn(
										"h-12 w-12",
										"border border-[#D4B485]/20",
									)}
								>
									<AvatarImage
										src={undefined}
										alt={agent.name}
									/>
									<AvatarFallback
										className={cn(
											"text-[#1E2023] font-semibold",
											"bg-gradient-to-br from-[#D4B485] to-[#B08968]",
										)}
									>
										{agent.name.slice(0, 1)}
									</AvatarFallback>
								</Avatar>
								<div className="flex-1">
									<div className="flex items-center gap-2">
										<span className="font-medium text-[#D4B485]">
											{agent.name}
										</span>
										<span
											className={cn(
												"rounded-full px-2 py-0.5 text-xs",
												"bg-[#D4B485]/10",
												"text-[#D4B485]/60",
											)}
										>
											{
												QUOTA_RULES[
													agent.role as AgentRole
												].roleName
											}
										</span>
									</div>
									<div className="mt-1 text-sm text-[#D4B485]/40">
										{agent.email} · {agent.phone}
									</div>
									<div className="mt-2 flex flex-wrap gap-2">
										{getQuotaRule(agent.role as AgentRole)
											.canPurchaseQuota ? (
											<span
												className={cn(
													"rounded-full px-2 py-0.5 text-xs",
													"bg-[#D4B485]/5",
													"text-[#D4B485]/60",
													"ring-1 ring-[#D4B485]/10",
												)}
											>
												{`可加购：${getQuotaRule(agent.role as AgentRole).minPurchase}个起，¥${getQuotaRule(agent.role as AgentRole).discountedPrice}/个`}
											</span>
										) : (
											<span
												className={cn(
													"rounded-full px-2 py-0.5 text-xs",
													"bg-rose-500/5",
													"text-rose-500/60",
													"ring-1 ring-rose-500/10",
												)}
											>
												不可加购名额
											</span>
										)}
									</div>
								</div>
								<div className="flex items-center gap-8">
									<div className="text-right">
										<div className="text-sm text-[#D4B485]/60">
											总名额
										</div>
										<div className="font-medium text-[#D4B485]">
											{agent.teamQuota}个
										</div>
									</div>
									<div className="text-right">
										<div className="text-sm text-[#D4B485]/60">
											已使用
										</div>
										<div className="font-medium text-[#D4B485]">
											{agent.usedTeamQuota}个
										</div>
									</div>
									<div className="text-right">
										<div className="text-sm text-[#D4B485]/60">
											剩余名额
										</div>
										<div className="font-medium text-[#D4B485]">
											{agent.remainingQuota}个
										</div>
									</div>
								</div>
							</div>
						</motion.div>
					))}
			</div>

			{/* 分页组件 */}
			{total > 0 && (
				<AgentPagination
					currentPage={currentPage}
					totalPages={totalPages}
					total={total}
					pageSize={pageSize}
					onPageChange={handlePageChange}
					loading={loading}
					className="mt-6"
				/>
			)}

			{/* 底部按钮 */}
			<div className="flex justify-end pt-6">
				<Button
					className={cn(
						"px-8",
						"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
						"text-white",
						"border-none",
						"shadow-[0_8px_16px_-4px_rgba(212,180,133,0.3)]",
						"hover:shadow-[0_12px_20px_-4px_rgba(212,180,133,0.4)]",
						"transition-all duration-200",
						"disabled:opacity-50 disabled:cursor-not-allowed",
					)}
					onClick={() =>
						selectedAgent && onNext({ agent: selectedAgent })
					}
					disabled={!selectedAgent}
				>
					下一步
				</Button>
			</div>
		</div>
	);
}
