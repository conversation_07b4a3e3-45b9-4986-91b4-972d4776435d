import { logger } from "@repo/logs";
import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";

interface AllocationResponse {
	id: string;
	orderNo: string;
	fromAgentId: string;
	fromAgentName: string;
	fromAgentRole: string;
	toAgentId: string;
	toAgentName: string;
	toAgentRole: string;
	quantity: number;
	amount?: number;
	unitPrice?: number;
	status: string;
	remark?: string;
	tradeRemark?: string;
	adminRemark?: string;
	createdAt: string;
	completedAt?: string;
	cancelledAt?: string;
}

interface ApiSuccessResponse<T> {
	code: number;
	message?: string;
	data: T;
}

interface ApiErrorResponse {
	code: number;
	error: string;
	message: string;
}

type ApiResponse<T> = ApiSuccessResponse<T> | ApiErrorResponse;

export function useAllocation() {
	const [loading, setLoading] = useState(false);
	const [allocationRecord, setAllocationRecord] =
		useState<AllocationResponse | null>(null);

	const allocateQuota = useCallback(
		async (params: {
			agentId: string;
			quantity: number;
			amount?: number;
			unitPrice?: number;
			remark?: string;
			tradeRemark?: string;
		}) => {
			try {
				setLoading(true);
				logger.info("开始分配名额", { params });

				const res = await apiClient.v1.agent.admin.quota.allocate.$post(
					{
						json: {
							toAgentId: params.agentId,
							quantity: params.quantity,
							amount: params.amount,
							unitPrice: params.unitPrice,
							remark: params.remark,
							tradeRemark: params.tradeRemark,
						},
					},
				);
				const result =
					(await res.json()) as ApiResponse<AllocationResponse>;

				logger.info("名额分配响应:", result);

				if ("error" in result || !result.data) {
					throw new Error(result.message || "名额分配失败");
				}

				setAllocationRecord(result.data);
				toast.success("名额分配成功", {
					description: `已为 ${result.data.toAgentName} 分配 ${result.data.quantity} 个名额`,
				});

				return result.data;
			} catch (error) {
				logger.error("名额分配失败", { error });
				toast.error("名额分配失败", {
					description:
						error instanceof Error ? error.message : "请重试",
				});
				return null;
			} finally {
				setLoading(false);
			}
		},
		[],
	);

	return {
		loading,
		allocationRecord,
		allocateQuota,
	};
}
