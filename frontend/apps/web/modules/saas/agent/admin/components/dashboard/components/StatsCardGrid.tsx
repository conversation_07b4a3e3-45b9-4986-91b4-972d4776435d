"use client";

import { FileText, Settings, Users, Wallet } from "lucide-react";
import { StatCard, type StatCardProps } from "./StatCard";

export interface StatsCardGridProps {
	stats?: Array<Omit<StatCardProps, "delay">>;
}

export function StatsCardGrid({ stats }: StatsCardGridProps) {
	// 如果未提供统计数据，则使用默认数据
	const defaultStats = [
		{
			title: "代理商总数",
			value: "1,234",
			description: "较上月增长 15.2%",
			icon: Users,
		},
		{
			title: "订单总量",
			value: "5,678",
			description: "较上月增长 8.5%",
			icon: FileText,
		},
		{
			title: "营业额",
			value: "¥1,234,567",
			description: "较上月增长 12.3%",
			icon: Wallet,
		},
		{
			title: "系统负载",
			value: "65%",
			description: "运行状态良好",
			icon: Settings,
		},
	];

	const statsToRender = stats || defaultStats;

	return (
		<div className="grid grid-cols-4 gap-6">
			{statsToRender.map((stat, index) => (
				<StatCard
					key={`stat-card-${stat.title}-${index}`}
					{...stat}
					delay={0.1 * (index + 1)}
				/>
			))}
		</div>
	);
}
