"use client";

import { Card, CardContent } from "@ui/components/card";
import type { ConsumerDetail } from "../hooks/use-consumer-detail";

export interface ConsumerDetailViewProps {
	consumer: ConsumerDetail;
	isLoading?: boolean;
}

export function ConsumerDetailView({
	consumer,
	isLoading = false,
}: ConsumerDetailViewProps) {
	// 格式化日期
	const formatDate = (dateString: string | null) => {
		if (!dateString) {
			return "未设置";
		}
		const date = new Date(dateString);
		return date.toLocaleDateString("zh-CN", {
			year: "numeric",
			month: "long",
			day: "numeric",
		});
	};

	if (isLoading) {
		return (
			<div className="py-8 flex items-center justify-center">
				<div className="text-[#D4B485]/60">加载中...</div>
			</div>
		);
	}

	if (!consumer) {
		return (
			<div className="py-8 flex items-center justify-center">
				<div className="text-[#D4B485]/60">暂无数据</div>
			</div>
		);
	}

	return (
		<div className="space-y-4">
			<div className="flex items-center gap-4">
				<div className="h-16 w-16 rounded-full bg-gradient-to-br from-[#D4B485] to-[#B08968] flex items-center justify-center text-[#1E2023] text-xl font-bold">
					{consumer.name.slice(0, 1)}
				</div>
				<div>
					<h3 className="text-lg font-medium text-[#D4B485]">
						{consumer.name}
					</h3>
					<p className="text-sm text-[#D4B485]/60">
						{consumer.email} · {consumer.phone}
					</p>
				</div>
			</div>

			<div className="grid grid-cols-2 gap-4">
				<Card className="bg-[#1A1C1E] border-[#D4B485]/20">
					<CardContent className="p-4">
						<div className="text-sm text-[#D4B485]/60">角色</div>
						<div className="text-lg text-[#D4B485]">
							{consumer.role === "INTERNAL"
								? "内部测试"
								: "普通用户"}
						</div>
					</CardContent>
				</Card>
				<Card className="bg-[#1A1C1E] border-[#D4B485]/20">
					<CardContent className="p-4">
						<div className="text-sm text-[#D4B485]/60">状态</div>
						<div className="text-lg text-[#D4B485]">
							{consumer.status === "ACTIVE"
								? "正常"
								: consumer.status === "DISABLED"
									? "禁用"
									: consumer.status === "PENDING"
										? "待审核"
										: consumer.status}
						</div>
					</CardContent>
				</Card>
				<Card className="bg-[#1A1C1E] border-[#D4B485]/20">
					<CardContent className="p-4">
						<div className="text-sm text-[#D4B485]/60">
							剩余算力
						</div>
						<div className="text-lg text-[#D4B485]">
							{consumer.computingPower}
						</div>
					</CardContent>
				</Card>
				<Card className="bg-[#1A1C1E] border-[#D4B485]/20">
					<CardContent className="p-4">
						<div className="text-sm text-[#D4B485]/60">
							历史总算力
						</div>
						<div className="text-lg text-[#D4B485]">
							{consumer.totalComputingPower}
						</div>
					</CardContent>
				</Card>
			</div>

			<div className="grid grid-cols-2 gap-4 mt-4">
				<Card className="bg-[#1A1C1E] border-[#D4B485]/20">
					<CardContent className="p-4">
						<div className="text-sm text-[#D4B485]/60">
							归属代理商
						</div>
						<div className="text-lg text-[#D4B485]">
							{consumer.agentName || "未分配"}
						</div>
					</CardContent>
				</Card>
				<Card className="bg-[#1A1C1E] border-[#D4B485]/20">
					<CardContent className="p-4">
						<div className="text-sm text-[#D4B485]/60">
							当前套餐
						</div>
						<div className="text-lg text-[#D4B485]">
							{consumer.planName || "未开通套餐"}
						</div>
					</CardContent>
				</Card>
				<Card className="bg-[#1A1C1E] border-[#D4B485]/20">
					<CardContent className="p-4">
						<div className="text-sm text-[#D4B485]/60">
							套餐开始时间
						</div>
						<div className="text-lg text-[#D4B485]">
							{formatDate(consumer.planStartAt)}
						</div>
					</CardContent>
				</Card>
				<Card className="bg-[#1A1C1E] border-[#D4B485]/20">
					<CardContent className="p-4">
						<div className="text-sm text-[#D4B485]/60">
							套餐到期时间
						</div>
						<div className="text-lg text-[#D4B485]">
							{formatDate(consumer.planExpireAt)}
						</div>
					</CardContent>
				</Card>
				<Card className="col-span-2 bg-[#1A1C1E] border-[#D4B485]/20">
					<CardContent className="p-4">
						<div className="text-sm text-[#D4B485]/60">
							注册时间
						</div>
						<div className="text-lg text-[#D4B485]">
							{formatDate(consumer.createdAt)}
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
