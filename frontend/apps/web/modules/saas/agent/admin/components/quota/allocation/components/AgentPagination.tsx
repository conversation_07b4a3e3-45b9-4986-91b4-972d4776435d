import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";
import { ChevronLeft, ChevronRight } from "lucide-react";

interface AgentPaginationProps {
	currentPage: number;
	totalPages: number;
	total: number;
	pageSize: number;
	onPageChange: (page: number) => void;
	loading?: boolean;
	className?: string;
}

export function AgentPagination({
	currentPage,
	totalPages,
	total,
	pageSize,
	onPageChange,
	loading = false,
	className,
}: AgentPaginationProps) {
	// 计算显示的页码范围
	const getPageNumbers = () => {
		const pages: number[] = [];
		const maxPagesToShow = 5;

		if (totalPages <= maxPagesToShow) {
			// 如果总页数小于等于最大显示页码数，显示所有页码
			for (let i = 1; i <= totalPages; i++) {
				pages.push(i);
			}
		} else {
			// 否则，显示当前页附近的页码
			let startPage = Math.max(
				1,
				currentPage - Math.floor(maxPagesToShow / 2),
			);
			let endPage = startPage + maxPagesToShow - 1;

			if (endPage > totalPages) {
				endPage = totalPages;
				startPage = Math.max(1, endPage - maxPagesToShow + 1);
			}

			for (let i = startPage; i <= endPage; i++) {
				pages.push(i);
			}
		}

		return pages;
	};

	// 计算当前显示的记录范围
	const startRecord = Math.min(total, (currentPage - 1) * pageSize + 1);
	const endRecord = Math.min(total, currentPage * pageSize);

	if (totalPages <= 1) {
		return null; // 只有一页或没有数据时不显示分页
	}

	return (
		<div
			className={cn(
				"flex items-center justify-between pt-6 border-t border-[#D4B485]/10",
				className,
			)}
		>
			{/* 记录信息 */}
			<div className="text-sm text-[#D4B485]/60">
				显示 {startRecord}-{endRecord} 条，共 {total} 条记录
				{loading && (
					<span className="ml-2 inline-flex items-center">
						<svg
							className="animate-spin h-4 w-4 text-[#D4B485]"
							xmlns="http://www.w3.org/2000/svg"
							fill="none"
							viewBox="0 0 24 24"
							aria-label="加载中"
						>
							<title>加载中</title>
							<circle
								className="opacity-25"
								cx="12"
								cy="12"
								r="10"
								stroke="currentColor"
								strokeWidth="4"
							/>
							<path
								className="opacity-75"
								fill="currentColor"
								d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
							/>
						</svg>
						<span className="ml-1">加载中...</span>
					</span>
				)}
			</div>

			{/* 分页控件 */}
			<div className="flex items-center space-x-2">
				{/* 上一页按钮 */}
				<Button
					variant="outline"
					size="sm"
					className={cn(
						"h-8 w-8 p-0",
						"bg-[#1A1C1E]",
						"border-[#D4B485]/20",
						"text-[#D4B485]",
						"hover:bg-[#D4B485]/10",
						"hover:border-[#D4B485]/40",
						"disabled:opacity-50 disabled:cursor-not-allowed",
					)}
					disabled={currentPage === 1 || loading}
					onClick={() => onPageChange(currentPage - 1)}
				>
					<ChevronLeft className="h-4 w-4" />
				</Button>

				{/* 页码按钮 */}
				<div className="flex items-center space-x-1">
					{getPageNumbers().map((page, index, array) => {
						// 检查是否需要添加省略号
						const needEllipsisBefore = index === 0 && page > 1;
						const needEllipsisAfter =
							index === array.length - 1 && page < totalPages;

						return (
							<div key={page} className="flex items-center">
								{needEllipsisBefore && (
									<span className="px-2 text-[#D4B485]/40">
										...
									</span>
								)}
								<Button
									variant={
										page === currentPage
											? "primary"
											: "outline"
									}
									size="sm"
									className={cn(
										"h-8 w-8 p-0",
										page === currentPage
											? "bg-[#D4B485] text-white border-[#D4B485]"
											: "bg-[#1A1C1E] border-[#D4B485]/20 text-[#D4B485] hover:bg-[#D4B485]/10 hover:border-[#D4B485]/40",
									)}
									disabled={loading}
									onClick={() => onPageChange(page)}
								>
									{page}
								</Button>
								{needEllipsisAfter && (
									<span className="px-2 text-[#D4B485]/40">
										...
									</span>
								)}
							</div>
						);
					})}
				</div>

				{/* 下一页按钮 */}
				<Button
					variant="outline"
					size="sm"
					className={cn(
						"h-8 w-8 p-0",
						"bg-[#1A1C1E]",
						"border-[#D4B485]/20",
						"text-[#D4B485]",
						"hover:bg-[#D4B485]/10",
						"hover:border-[#D4B485]/40",
						"disabled:opacity-50 disabled:cursor-not-allowed",
					)}
					disabled={currentPage === totalPages || loading}
					onClick={() => onPageChange(currentPage + 1)}
				>
					<ChevronRight className="h-4 w-4" />
				</Button>
			</div>
		</div>
	);
}
