"use client";

import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Input } from "@ui/components/input";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import {
	ArrowLeft,
	Calendar,
	CheckCircle2,
	Crown,
	DollarSign,
	Loader2,
	Minus,
	Phone,
	Plus,
	Send,
	Star,
	User,
	Zap,
} from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { useAdminActivate } from "../hooks/use-admin-activate";
import { useAdminPlans } from "../hooks/use-admin-plans";
import type { ActivationResult, Plan, UserProfile } from "../types";

interface PlanSelectionProps {
	user: UserProfile;
	onActivationSuccess: (result: ActivationResult) => void;
	onCancel: () => void;
}

export function PlanSelection({
	user,
	onActivationSuccess,
	onCancel,
}: PlanSelectionProps) {
	const { plans, isLoading: isLoadingPlans } = useAdminPlans();
	const { activatePlan, isPending: isActivating } = useAdminActivate();

	const [selectedPlan, setSelectedPlan] = useState<Plan | null>(null);
	const [quantity, setQuantity] = useState(1);

	const handleActivate = () => {
		if (!selectedPlan || !user) {
			toast.error("请先选择一个套餐");
			return;
		}

		// 直接激活，不需要二次确认
		console.log("开始激活套餐:", {
			userId: user.id,
			planId: selectedPlan.id,
			quantity,
		});

		activatePlan(
			{
				userId: user.id,
				planId: selectedPlan.id,
				quantity,
			},
			{
				onSuccess: (data: ActivationResult) => {
					console.log("激活成功:", data);
					toast.success("开通成功！", {
						description: `已为用户 ${user.name} 开通 ${selectedPlan.name}。`,
					});
					onActivationSuccess(data);
				},
				onError: (error) => {
					console.error("激活失败:", error);
					toast.error("开通失败", {
						description: error.message || "未知错误，请稍后再试。",
					});
				},
			},
		);
	};

	const handleQuantityChange = (delta: number) => {
		const newQuantity = quantity + delta;
		console.log("数量变更:", {
			current: quantity,
			delta,
			new: newQuantity,
		});
		if (newQuantity >= 1 && newQuantity <= 100) {
			setQuantity(newQuantity);
		}
	};

	const handleQuantityInputChange = (value: string) => {
		const numValue = Number.parseInt(value) || 1;
		console.log("输入数量变更:", { input: value, parsed: numValue });
		if (numValue >= 1 && numValue <= 100) {
			setQuantity(numValue);
		}
	};

	return (
		<motion.div
			initial={{ opacity: 0, x: 20 }}
			animate={{ opacity: 1, x: 0 }}
			exit={{ opacity: 0, x: -20 }}
		>
			{/* 返回按钮 */}
			<Button
				variant="ghost"
				onClick={onCancel}
				className={cn(
					"mb-6 text-[#D4B485] hover:bg-[#D4B485]/10",
					"transition-all duration-200 hover:translate-x-1",
				)}
			>
				<ArrowLeft className="mr-2 h-4 w-4" />
				返回用户选择
			</Button>

			{/* 用户信息卡片 */}
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				className={cn(
					"mb-8 p-6 rounded-xl",
					"bg-gradient-to-r from-[#D4B485]/10 via-[#D4B485]/5 to-transparent",
					"border border-[#D4B485]/20",
				)}
			>
				<div className="flex items-center gap-4">
					<div className="flex items-center justify-center w-12 h-12 rounded-full bg-[#D4B485]/20">
						<User className="h-6 w-6 text-[#D4B485]" />
					</div>
					<div className="flex-1">
						<h3 className="font-semibold text-xl text-white">
							{user.name}
						</h3>
						<div className="flex items-center gap-4 mt-1 text-sm text-[#D4B485]/80">
							<div className="flex items-center gap-1">
								<Phone className="h-3 w-3" />
								{user.phone}
							</div>
							<div className="flex items-center gap-1">
								<Zap className="h-3 w-3" />
								{user.computingPower.toLocaleString()} 算力
							</div>
							{user.currentPlanName && (
								<div className="flex items-center gap-1">
									<Crown className="h-3 w-3" />
									{user.currentPlanName}
								</div>
							)}
						</div>
					</div>
				</div>
			</motion.div>

			{/* 套餐列表 */}
			{isLoadingPlans ? (
				<motion.div
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					className="flex flex-col justify-center items-center h-60 space-y-4"
				>
					<div className="relative">
						<div className="w-12 h-12 border-4 border-[#D4B485]/20 border-t-[#D4B485] rounded-full animate-spin" />
					</div>
					<p className="text-[#D4B485]/80 text-sm">正在加载套餐...</p>
				</motion.div>
			) : (
				<div className="space-y-6">
					<h4 className="text-lg font-semibold text-white flex items-center gap-2">
						<Crown className="h-5 w-5 text-[#D4B485]" />
						选择套餐
					</h4>

					<div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
						{plans.map((plan: Plan, index) => {
							const isSelected = selectedPlan?.id === plan.id;
							const levelColors = {
								BASIC: "from-blue-500/20 to-blue-600/20 border-blue-500/30",
								PREMIUM:
									"from-purple-500/20 to-purple-600/20 border-purple-500/30",
								PRO: "from-orange-500/20 to-orange-600/20 border-orange-500/30",
								ENTERPRISE:
									"from-red-500/20 to-red-600/20 border-red-500/30",
							};
							const levelColor =
								levelColors[
									plan.level as keyof typeof levelColors
								] || levelColors.BASIC;

							return (
								<motion.div
									key={plan.id}
									initial={{ opacity: 0, y: 20 }}
									animate={{ opacity: 1, y: 0 }}
									transition={{ delay: index * 0.1 }}
								>
									<Card
										className={cn(
											"group cursor-pointer transition-all duration-300 h-full",
											"bg-gradient-to-br from-[#1E2023] via-[#1C1F22] to-[#1A1C1E]",
											"border-2 hover:shadow-xl hover:shadow-[#D4B485]/10",
											"hover:-translate-y-1 hover:scale-[1.02]",
											isSelected
												? "border-[#D4B485] ring-4 ring-[#D4B485]/20 shadow-lg shadow-[#D4B485]/20"
												: "border-white/10 hover:border-[#D4B485]/50",
										)}
										onClick={() => setSelectedPlan(plan)}
									>
										<CardHeader className="p-6">
											<div className="flex items-center justify-between">
												<CardTitle className="flex items-center gap-2 text-white group-hover:text-[#D4B485] transition-colors">
													<div
														className={cn(
															"p-2 rounded-lg bg-gradient-to-br",
															levelColor,
														)}
													>
														<Crown className="h-4 w-4" />
													</div>
													{plan.name}
												</CardTitle>
												{isSelected && (
													<motion.div
														initial={{ scale: 0 }}
														animate={{ scale: 1 }}
														className="w-6 h-6 bg-[#D4B485] rounded-full flex items-center justify-center"
													>
														<CheckCircle2 className="h-4 w-4 text-black" />
													</motion.div>
												)}
											</div>
											<Badge
												className={cn(
													"w-fit text-xs",
													`${levelColor.replace(/from-|to-|\/20|\/30/g, "").split(" ")[0]}/10`,
													`border-${levelColor.replace(/from-|to-|\/20|\/30/g, "").split(" ")[0]}/30`,
												)}
											>
												{plan.level}
											</Badge>
										</CardHeader>

										<CardContent className="p-6 pt-0 space-y-4">
											<div className="text-center">
												<div className="flex items-center justify-center gap-2 mb-2">
													<Zap className="h-5 w-5 text-[#D4B485]" />
													<span className="text-3xl font-bold text-white">
														{plan.computingPower.toLocaleString()}
													</span>
													<span className="text-[#D4B485]/80">
														算力
													</span>
												</div>
											</div>

											<div className="space-y-3">
												<div className="flex items-center justify-between text-sm">
													<span className="text-[#D4B485]/60 flex items-center gap-1">
														<Calendar className="h-3 w-3" />
														有效期
													</span>
													<span className="text-white font-medium">
														{plan.validityDays} 天
													</span>
												</div>

												<div className="flex items-center justify-between text-sm">
													<span className="text-[#D4B485]/60 flex items-center gap-1">
														<DollarSign className="h-3 w-3" />
														原价
													</span>
													<span className="text-white/60 line-through">
														¥{plan.price}
													</span>
												</div>

												<div className="flex items-center justify-between text-sm">
													<span className="text-[#D4B485]/60 flex items-center gap-1">
														<Star className="h-3 w-3" />
														现价
													</span>
													<span className="text-[#D4B485] font-bold text-lg">
														¥{plan.discountPrice}
													</span>
												</div>
											</div>

											{plan.description && (
												<p className="text-xs text-white/60 mt-4 p-3 bg-white/5 rounded-lg">
													{plan.description}
												</p>
											)}
										</CardContent>
									</Card>
								</motion.div>
							);
						})}
					</div>
				</div>
			)}

			{/* 套餐确认和数量选择 */}
			{selectedPlan && (
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					className="mt-8 space-y-6"
				>
					{/* 套餐摘要 */}
					<div
						className={cn(
							"p-6 rounded-xl",
							"bg-gradient-to-r from-[#D4B485]/10 via-[#D4B485]/5 to-transparent",
							"border border-[#D4B485]/30",
						)}
					>
						<h5 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
							<CheckCircle2 className="h-5 w-5 text-[#D4B485]" />
							已选择套餐
						</h5>

						<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
							<div className="space-y-3">
								<div className="flex items-center justify-between">
									<span className="text-[#D4B485]/60">
										套餐名称
									</span>
									<span className="text-white font-medium">
										{selectedPlan.name}
									</span>
								</div>
								<div className="flex items-center justify-between">
									<span className="text-[#D4B485]/60">
										单次算力
									</span>
									<span className="text-white font-medium">
										{selectedPlan.computingPower.toLocaleString()}
									</span>
								</div>
								<div className="flex items-center justify-between">
									<span className="text-[#D4B485]/60">
										有效期
									</span>
									<span className="text-white font-medium">
										{selectedPlan.validityDays} 天
									</span>
								</div>
							</div>

							<div className="space-y-3">
								<div className="flex items-center justify-between">
									<span className="text-[#D4B485]/60">
										单价
									</span>
									<span className="text-white font-medium">
										¥{selectedPlan.discountPrice}
									</span>
								</div>
								<div className="flex items-center justify-between">
									<span className="text-[#D4B485]/60">
										开通数量
									</span>
									<div className="flex items-center gap-2">
										<Button
											variant="ghost"
											size="sm"
											onClick={() =>
												handleQuantityChange(-1)
											}
											disabled={
												quantity <= 1 || isActivating
											}
											className={cn(
												"h-8 w-8 p-0 text-[#D4B485] hover:bg-[#D4B485]/10",
												"disabled:opacity-50 disabled:cursor-not-allowed",
											)}
										>
											<Minus className="h-4 w-4" />
										</Button>
										<Input
											type="number"
											value={quantity}
											onChange={(e) =>
												handleQuantityInputChange(
													e.target.value,
												)
											}
											disabled={isActivating}
											className={cn(
												"w-20 text-center bg-white/5 border-white/10 text-white",
												"focus:border-[#D4B485]/50 focus:ring-2 focus:ring-[#D4B485]/20",
												"disabled:opacity-50 disabled:cursor-not-allowed",
											)}
											min={1}
											max={100}
										/>
										<Button
											variant="ghost"
											size="sm"
											onClick={() =>
												handleQuantityChange(1)
											}
											disabled={
												quantity >= 100 || isActivating
											}
											className={cn(
												"h-8 w-8 p-0 text-[#D4B485] hover:bg-[#D4B485]/10",
												"disabled:opacity-50 disabled:cursor-not-allowed",
											)}
										>
											<Plus className="h-4 w-4" />
										</Button>
									</div>
								</div>
								<div className="flex items-center justify-between border-t border-white/10 pt-3">
									<span className="text-[#D4B485] font-medium">
										总算力
									</span>
									<span className="text-[#D4B485] font-bold text-lg">
										{(
											selectedPlan.computingPower *
											quantity
										).toLocaleString()}
									</span>
								</div>
							</div>
						</div>
					</div>

					{/* 激活按钮 */}
					<div className="flex items-center justify-between">
						<div className="text-sm text-[#D4B485]/60">
							<p>
								确认为用户{" "}
								<span className="text-white font-medium">
									{user.name}
								</span>{" "}
								开通套餐
							</p>
							<p>开通后将立即生效，无法撤销</p>
						</div>

						<Button
							onClick={handleActivate}
							disabled={isActivating || !selectedPlan}
							className={cn(
								"h-12 px-8 font-medium text-lg",
								"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
								"hover:from-[#E5C9A5] hover:to-[#D4B485]",
								"text-black",
								"disabled:opacity-50 disabled:cursor-not-allowed",
								"transition-all duration-200",
								"shadow-lg shadow-[#D4B485]/25",
							)}
						>
							{isActivating ? (
								<>
									<Loader2 className="mr-2 h-5 w-5 animate-spin" />
									激活中...
								</>
							) : (
								<>
									<Send className="mr-2 h-5 w-5" />
									确认激活套餐
								</>
							)}
						</Button>
					</div>
				</motion.div>
			)}
		</motion.div>
	);
}
