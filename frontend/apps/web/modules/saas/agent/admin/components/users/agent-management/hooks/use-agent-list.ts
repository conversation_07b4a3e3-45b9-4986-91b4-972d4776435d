import type { <PERSON><PERSON><PERSON> } from "@prisma/client";
import { logger } from "@repo/logs";
import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";

// 代理商列表项
export interface AgentListItem {
	id: string;
	name: string;
	role: AgentRole;
	email: string;
	phone: string;
	status: string;
	teamSize: number;
	performance: number;
	avatar: string | null;
	hasChildren: boolean;
	createdAt: string;
}

// API响应类型
interface ApiResponse<T> {
	code?: number;
	message?: string;
	data?: T;
	error?: string;
	items?: AgentListItem[];
	total?: number;
	page?: number;
	pageSize?: number;
	totalPages?: number;
}

// 查询参数
export interface AgentListQuery {
	page: number;
	pageSize: number;
	searchTerm?: string;
	role?: AgentRole | "ALL";
	status?: string;
	sortBy?: "createdAt" | "name" | "teamSize" | "performance" | "role";
	sortOrder?: "asc" | "desc";
}

export function useAgentList() {
	const [loading, setLoading] = useState(false);
	const [agents, setAgents] = useState<AgentListItem[]>([]);
	const [total, setTotal] = useState(0);
	const [currentPage, setCurrentPage] = useState(1);
	const [totalPages, setTotalPages] = useState(1);

	const fetchAgents = useCallback(async (query: AgentListQuery) => {
		setLoading(true);
		try {
			const response =
				await apiClient.v1.agent.admin.user.agent.list.$get({
					query: {
						page: query.page,
						pageSize: query.pageSize,
						searchTerm: query.searchTerm,
						role: query.role,
						status: query.status,
						sortBy: query.sortBy,
						sortOrder: query.sortOrder,
					},
				});

			const result = (await response.json()) as ApiResponse<unknown>;

			if (response.ok && result.items) {
				setAgents(result.items);
				setTotal(result.total || 0);
				setCurrentPage(result.page || 1);
				setTotalPages(result.totalPages || 1);
			} else {
				toast.error("获取代理商列表失败");
				logger.error("[Admin] 获取代理商列表失败", {
					error: result.message || result.error,
				});
			}
		} catch (error) {
			toast.error("获取代理商列表失败");
			logger.error("[Admin] 获取代理商列表失败", {
				error: error instanceof Error ? error.message : "未知错误",
			});
		} finally {
			setLoading(false);
		}
	}, []);

	return {
		loading,
		agents,
		total,
		currentPage,
		totalPages,
		fetchAgents,
	};
}
