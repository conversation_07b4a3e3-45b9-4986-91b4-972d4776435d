import type { <PERSON><PERSON><PERSON> } from "@prisma/client";
import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { CheckCircle } from "lucide-react";
import { calculatePurchaseAmount, getQuotaRule } from "../config/quota-rules";

interface Agent {
	id: string;
	name: string;
	role: string;
	email: string;
	phone: string;
	teamQuota: number;
	usedTeamQuota: number;
	remainingQuota: number;
	teamSize: number;
	totalSales: number;
	monthSales: number;
	createdAt: string;
}

interface SuccessViewProps {
	selectedAgent: Agent;
	quantity: number;
	amount?: number;
	unitPrice?: number;
	onBackToList: () => void;
}

export function SuccessView({
	selectedAgent,
	quantity,
	amount,
	unitPrice,
	onBackToList,
}: SuccessViewProps) {
	// 计算购买金额
	const purchaseAmount = calculatePurchaseAmount(
		selectedAgent.role as <PERSON><PERSON><PERSON>,
		quantity,
	);
	// 获取代理商规则
	const quotaRule = getQuotaRule(selectedAgent.role as Agent<PERSON><PERSON>);

	return (
		<div className="space-y-8">
			{/* 成功图标 */}
			<div className="flex flex-col items-center justify-center space-y-4">
				<motion.div
					initial={{ scale: 0 }}
					animate={{ scale: 1 }}
					transition={{
						type: "spring",
						stiffness: 260,
						damping: 20,
					}}
					className={cn(
						"flex h-24 w-24 items-center justify-center rounded-full",
						"bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
						"ring-1 ring-[#D4B485]/20",
						"shadow-[0_0_0_8px_rgba(212,180,133,0.03)]",
					)}
				>
					<CheckCircle className="h-12 w-12 text-[#D4B485]" />
				</motion.div>
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ delay: 0.2 }}
					className="text-center"
				>
					<h3
						className={cn(
							"text-2xl font-semibold",
							zywhFont.className,
						)}
						style={{
							background:
								"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
							WebkitBackgroundClip: "text",
							WebkitTextFillColor: "transparent",
							backgroundClip: "text",
							textShadow:
								"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
						}}
					>
						名额分配成功
					</h3>
					<p className="mt-2 text-[#D4B485]/60">
						已成功为 {selectedAgent.name} 分配 {quantity} 个名额
					</p>
				</motion.div>
			</div>

			{/* 分配信息 */}
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.4 }}
				className="space-y-4"
			>
				<div className="text-base font-medium text-[#D4B485]">
					分配详情
				</div>
				<div
					className={cn(
						"rounded-lg p-4",
						"bg-[#1E2023]/50",
						"border border-[#D4B485]/20",
						"shadow-[inset_0_0_0_1px_rgba(212,180,133,0.05)]",
					)}
				>
					<div className="space-y-4">
						<div className="flex items-center justify-between">
							<div className="text-sm text-[#D4B485]/60">
								代理商
							</div>
							<div className="text-sm text-[#D4B485]">
								{selectedAgent.name}
								<span className="ml-2 text-[#D4B485]/60">
									({quotaRule.roleName})
								</span>
							</div>
						</div>
						<div className="flex items-center justify-between">
							<div className="text-sm text-[#D4B485]/60">
								分配数量
							</div>
							<div className="text-sm text-[#D4B485]">
								{quantity}个
							</div>
						</div>
						<div className="flex items-center justify-between">
							<div className="text-sm text-[#D4B485]/60">
								单价
							</div>
							<div className="text-sm text-[#D4B485]">
								¥
								{unitPrice?.toLocaleString() ||
									quotaRule.basePrice.toLocaleString()}
							</div>
						</div>
						<div className="flex items-center justify-between">
							<div className="text-sm text-[#D4B485]/60">
								原价金额
							</div>
							<div className="text-sm text-[#D4B485]">
								¥{purchaseAmount.baseAmount.toLocaleString()}
							</div>
						</div>
						<div className="flex items-center justify-between">
							<div className="text-sm text-[#D4B485]/60">
								优惠金额
							</div>
							<div className="text-sm text-emerald-500">
								-¥
								{purchaseAmount.discountAmount.toLocaleString()}
							</div>
						</div>
						<div className="flex items-center justify-between border-t border-[#D4B485]/10 pt-2">
							<div className="text-base font-medium text-[#D4B485]">
								最终金额
							</div>
							<div className="text-lg font-medium text-[#D4B485]">
								¥
								{amount?.toLocaleString() ||
									purchaseAmount.finalAmount.toLocaleString()}
							</div>
						</div>
						<div className="mt-4 space-y-2">
							<div className="text-sm font-medium text-[#D4B485]">
								代理商权益
							</div>
							<div className="flex flex-wrap gap-2">
								{quotaRule.benefits.map((benefit) => (
									<span
										key={`${selectedAgent.id}-${benefit}`}
										className={cn(
											"rounded-full px-2 py-0.5 text-xs",
											"bg-[#D4B485]/5",
											"text-[#D4B485]/60",
											"ring-1 ring-[#D4B485]/10",
										)}
									>
										{benefit}
									</span>
								))}
							</div>
						</div>
					</div>
				</div>
			</motion.div>

			{/* 底部按钮 */}
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.6 }}
				className="flex justify-center pt-4"
			>
				<Button
					className={cn(
						"px-8",
						"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
						"text-white",
						"border-none",
						"shadow-[0_8px_16px_-4px_rgba(212,180,133,0.3)]",
						"hover:shadow-[0_12px_20px_-4px_rgba(212,180,133,0.4)]",
						"transition-all duration-200",
					)}
					onClick={onBackToList}
				>
					返回列表
				</Button>
			</motion.div>
		</div>
	);
}
