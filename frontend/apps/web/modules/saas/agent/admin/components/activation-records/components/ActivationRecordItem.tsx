"use client";

import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { Calendar, Package, User, Zap } from "lucide-react";
import type { ActivationRecordItem as ActivationRecordData } from "../hooks";

interface ActivationRecordItemProps {
	record: ActivationRecordData;
	index: number;
	onRecordClick?: (recordId: string) => void;
}

const ACTIVATION_STATUS_LABELS = {
	SUCCESS: "激活成功",
	FAILED: "激活失败",
} as const;

const _ACTIVATION_STATUS_COLORS = {
	SUCCESS: "text-emerald-500",
	FAILED: "text-red-500",
} as const;

const PLAN_LEVEL_LABELS = {
	TRIAL: "体验版",
	BASIC: "基础版",
	ENHANCED: "增强版",
	ENTERPRISE: "企业版",
	PREMIUM: "招财进宝版",
} as const;

const ACTIVATION_METHOD_LABELS = {
	ADMIN: "管理员激活",
	AGENT: "代理商激活",
	AUTO: "系统自动激活",
} as const;

export function ActivationRecordItem({
	record,
	index,
	onRecordClick,
}: ActivationRecordItemProps) {
	const handleClick = () => {
		onRecordClick?.(record.id);
	};

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ delay: index * 0.1 }}
			onClick={handleClick}
			className={cn(
				"rounded-lg p-6",
				"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
				"border border-[#D4B485]/20",
				"group",
				"hover:border-[#D4B485]/40",
				"transition-all duration-200",
				"relative overflow-hidden",
				"cursor-pointer",
			)}
		>
			{/* 装饰性渐变 */}
			<div className="absolute inset-0 bg-gradient-to-r from-[#D4B485]/5 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

			<div className="relative grid grid-cols-1 gap-4 lg:grid-cols-4">
				{/* 基本信息 */}
				<div className="space-y-3">
					<div className="flex items-center gap-3">
						<div className="flex h-10 w-10 items-center justify-center rounded-lg bg-[#D4B485]/10">
							<Package className="h-5 w-5 text-[#D4B485]" />
						</div>
						<div>
							<p className="font-medium text-[#D4B485]">
								{record.id}
							</p>
							<p className="text-sm text-[#D4B485]/60">
								激活记录
							</p>
						</div>
					</div>
					<div
						className={cn(
							"inline-flex items-center rounded-full px-2 py-1 text-xs font-medium",
							record.status === "SUCCESS" &&
								"bg-emerald-500/10 text-emerald-500",
							record.status === "FAILED" &&
								"bg-red-500/10 text-red-500",
						)}
					>
						{ACTIVATION_STATUS_LABELS[record.status]}
					</div>
				</div>

				{/* 用户信息 */}
				<div className="space-y-2">
					<div className="flex items-center gap-2">
						<User className="h-4 w-4 text-[#D4B485]/60" />
						<span className="text-sm font-medium text-[#D4B485]">
							消费者信息
						</span>
					</div>
					<div className="space-y-1">
						<p className="text-sm text-[#D4B485]/80">
							{record.consumer.name}
						</p>
						<p className="text-xs text-[#D4B485]/60">
							{record.consumer.phone}
						</p>
					</div>
					<div className="space-y-1">
						<p className="text-xs text-[#D4B485]/60">
							激活代理商：{record.agent.name}
						</p>
						<p className="text-xs text-[#D4B485]/40">
							{record.agent.phone}
						</p>
					</div>
				</div>

				{/* 套餐信息 */}
				<div className="space-y-2">
					<div className="flex items-center gap-2">
						<Zap className="h-4 w-4 text-[#D4B485]/60" />
						<span className="text-sm font-medium text-[#D4B485]">
							套餐信息
						</span>
					</div>
					<div className="space-y-1">
						<p className="text-sm text-[#D4B485]/80">
							{record.plan.name}
						</p>
						<p className="text-xs text-[#D4B485]/60">
							等级：{PLAN_LEVEL_LABELS[record.plan.level]}
						</p>
						<p className="text-xs text-[#D4B485]/60">
							算力：{record.plan.computingPower.toLocaleString()}
						</p>
						<p className="text-xs text-[#D4B485]/60">
							有效期：{record.plan.validityDays}天
						</p>
					</div>
				</div>

				{/* 时间和备注 */}
				<div className="space-y-2">
					<div className="flex items-center gap-2">
						<Calendar className="h-4 w-4 text-[#D4B485]/60" />
						<span className="text-sm font-medium text-[#D4B485]">
							激活信息
						</span>
					</div>
					<div className="space-y-1">
						<p className="text-xs text-[#D4B485]/60">
							激活时间：
							{new Date(record.activatedAt).toLocaleString()}
						</p>
						{record.expiresAt && (
							<p className="text-xs text-[#D4B485]/60">
								到期时间：
								{new Date(record.expiresAt).toLocaleString()}
							</p>
						)}
						<p className="text-xs text-[#D4B485]/60">
							激活方式：
							{ACTIVATION_METHOD_LABELS[record.activationMethod]}
						</p>
						{record.activation?.activatedBy && (
							<p className="text-xs text-[#D4B485]/60">
								操作人：{record.activation.activatedBy}
							</p>
						)}
						{record.remark && (
							<p className="text-xs text-[#D4B485]/40">
								备注：{record.remark}
							</p>
						)}
					</div>
				</div>
			</div>
		</motion.div>
	);
}
