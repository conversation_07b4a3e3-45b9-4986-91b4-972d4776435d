import type { AgentRole } from "@prisma/client";

export interface QuotaRule {
	role: AgentRole;
	roleName: string;
	basePrice: number; // 基础价格(元)
	minPurchase: number; // 最小购买数量
	discount: number; // 折扣比例
	discountedPrice: number; // 折后单价(元)
	totalInvestment: number; // 总投资(元)
	benefits: string[]; // 权益列表
	description: string; // 角色描述
	canPurchaseQuota: boolean; // 是否可以加购名额
}

// 代理商配额规则配置
export const QUOTA_RULES: Record<AgentRole, QuotaRule> = {
	SALES: {
		role: "SALES",
		roleName: "超级个体",
		basePrice: 9800,
		minPurchase: 1,
		discount: 0,
		discountedPrice: 9800,
		totalInvestment: 9800,
		canPurchaseQuota: false,
		benefits: [
			"9000AI智能体分销权",
			"C端产品直接销售分润约40%",
			"推荐超级个体分润20%(1960元)",
		],
		description: "基础代理商角色，不可加购名额",
	},
	PARTNER: {
		role: "PARTNER",
		roleName: "高级合伙人",
		basePrice: 9800,
		minPurchase: 2,
		discount: 0.6,
		discountedPrice: 5880,
		totalInvestment: 69800,
		canPurchaseQuota: true,
		benefits: [
			"9000AI智能体招商运营权",
			"C端产品直接销售分润约40%",
			"推荐超级个体分润40%(3920元)",
			"推荐合伙人分润20%(13960元)",
			"交付收益20元/小时",
			"可加购名额：2个起购，单价5880元",
		],
		description: "附带12个超级个体名额，可以加购名额",
	},
	DIRECTOR: {
		role: "DIRECTOR",
		roleName: "联席董事",
		basePrice: 9800,
		minPurchase: 3,
		discount: 0.4,
		discountedPrice: 3920,
		totalInvestment: 298000,
		canPurchaseQuota: true,
		benefits: [
			"9000AI智能体战略运营权",
			"C端产品直接销售分润约40%",
			"推荐超级个体分润60%(5880元)",
			"推荐合伙人分润40%(27920元)",
			"推荐联席董事分润20%(59600元)",
			"交付收益20+20元/小时",
			"可加购名额：3个起购，单价3920元",
		],
		description: "附带76个个体或7个合伙人名额，可以加购名额",
	},
	BRANCH: {
		role: "BRANCH",
		roleName: "分公司",
		basePrice: 9800,
		minPurchase: 10,
		discount: 0.2,
		discountedPrice: 1960,
		totalInvestment: 2000000,
		canPurchaseQuota: true,
		benefits: [
			"9000AI智能体城市交付中心",
			"C端产品直接销售分润约40%",
			"推荐超级个体分润80%(7840元)",
			"推荐合伙人分润60%(41880元)",
			"推荐联席董事分润40%(119200元)",
			"推荐分公司分润20%(40万)",
			"交付收益20+20+20元/小时",
			"可加购名额：10个起购，单价1960元",
		],
		description:
			"附带1020个个体或72个合伙人名额或11个联席董事名额，可以加购名额",
	},
	ADMIN: {
		role: "ADMIN",
		roleName: "管理员",
		basePrice: 0,
		minPurchase: 0,
		discount: 0,
		discountedPrice: 0,
		totalInvestment: 0,
		canPurchaseQuota: false,
		benefits: ["系统最高权限"],
		description: "系统管理员，不参与名额分配",
	},
};

// 获取代理商配额规则
export function getQuotaRule(role: AgentRole): QuotaRule {
	return QUOTA_RULES[role];
}

// 计算购买金额
export function calculatePurchaseAmount(
	role: AgentRole,
	quantity: number,
): {
	baseAmount: number; // 原价金额
	discountAmount: number; // 优惠金额
	finalAmount: number; // 最终金额
} {
	const rule = QUOTA_RULES[role];
	const baseAmount = rule.basePrice * quantity;
	const finalAmount = rule.discountedPrice * quantity;
	const discountAmount = baseAmount - finalAmount;

	return {
		baseAmount,
		discountAmount,
		finalAmount,
	};
}

// 验证购买数量是否有效
export function validatePurchaseQuantity(
	role: AgentRole,
	quantity: number,
): {
	isValid: boolean;
	message?: string;
} {
	const rule = QUOTA_RULES[role];

	if (!rule.canPurchaseQuota) {
		return {
			isValid: false,
			message: `${rule.roleName}不可加购名额`,
		};
	}

	if (quantity < rule.minPurchase) {
		return {
			isValid: false,
			message: `${rule.roleName}最少需要购买${rule.minPurchase}个名额`,
		};
	}

	return {
		isValid: true,
	};
}
