import type { AgentR<PERSON> } from "@prisma/client";
import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";
import { useState } from "react";
import { calculatePurchaseAmount, getQuotaRule } from "../config/quota-rules";
import { useAllocation } from "../hooks";

interface Agent {
	id: string;
	name: string;
	role: string;
	email: string;
	phone: string;
	teamQuota: number;
	usedTeamQuota: number;
	remainingQuota: number;
	teamSize: number;
	totalSales: number;
	monthSales: number;
	createdAt: string;
}

interface FormData {
	agent: Agent | null;
	quantity: number | null;
}

interface AllocationInfo {
	recordId: string;
	agent: Agent;
	quantity: number;
	amount?: number;
	unitPrice?: number;
	createdAt: string;
}

interface ConfirmAllocationProps {
	onPrev: () => void;
	formData: FormData;
	onAllocationSuccess: (allocationInfo: AllocationInfo) => void;
}

// 计算可购买的下级代理商数量
function calculatePurchaseableAgents(
	role: AgentRole,
	quantity: number,
	remainingQuota: number,
) {
	// 总可用名额 = 剩余名额 + 加购名额
	const totalQuotas = remainingQuota + quantity;

	// 根据代理商等级，计算可以购买的下级代理商数量
	switch (role) {
		case "BRANCH": // 分公司可以购买所有下级
			return {
				SALES: totalQuotas, // 超级个体
				PARTNER: Math.floor(totalQuotas / 12), // 12个超级个体 = 1个高级合伙人
				DIRECTOR: Math.floor(totalQuotas / 76), // 76个超级个体 = 1个联席董事
			};
		case "DIRECTOR": // 联席董事只能购买合伙人和超级个体
			return {
				SALES: totalQuotas,
				PARTNER: Math.floor(totalQuotas / 12),
			};
		case "PARTNER": // 合伙人只能购买超级个体
			return {
				SALES: totalQuotas,
			};
		default:
			return null;
	}
}

export function ConfirmAllocation({
	onPrev,
	formData,
	onAllocationSuccess,
}: ConfirmAllocationProps) {
	const { allocateQuota } = useAllocation();
	const [isSubmitting, setIsSubmitting] = useState(false);

	// 计算购买金额
	const purchaseAmount =
		formData.agent && formData.quantity
			? calculatePurchaseAmount(
					formData.agent.role as AgentRole,
					formData.quantity,
				)
			: null;

	// 获取代理商规则
	const quotaRule = formData.agent
		? getQuotaRule(formData.agent.role as AgentRole)
		: null;

	// 计算可购买的下级代理商数量
	const purchaseableAgents =
		formData.agent && formData.quantity
			? calculatePurchaseableAgents(
					formData.agent.role as AgentRole,
					formData.quantity,
					formData.agent.remainingQuota,
				)
			: null;

	// 获取应该显示的代理商类型
	const getVisibleAgentTypes = (role: AgentRole) => {
		switch (role) {
			case "BRANCH":
				return ["SALES", "PARTNER", "DIRECTOR"];
			case "DIRECTOR":
				return ["SALES", "PARTNER"];
			case "PARTNER":
				return ["SALES"];
			default:
				return [];
		}
	};

	// 获取代理商类型的显示名称
	const getAgentTypeLabel = (type: string) => {
		switch (type) {
			case "SALES":
				return "超级个体";
			case "PARTNER":
				return "高级合伙人";
			case "DIRECTOR":
				return "联席董事";
			default:
				return "";
		}
	};

	// 处理提交
	const handleSubmit = async () => {
		if (
			!formData.agent ||
			!formData.quantity ||
			!purchaseAmount ||
			!quotaRule
		) {
			return;
		}

		setIsSubmitting(true);
		try {
			const result = await allocateQuota({
				agentId: formData.agent.id,
				quantity: formData.quantity,
				amount: purchaseAmount.finalAmount,
				unitPrice: quotaRule.basePrice,
				remark: `分配${formData.quantity}个名额`,
				tradeRemark: `原价：¥${purchaseAmount.baseAmount}，优惠：¥${purchaseAmount.discountAmount}，最终：¥${purchaseAmount.finalAmount}`,
			});

			if (result) {
				onAllocationSuccess({
					recordId: result.id,
					agent: formData.agent,
					quantity: formData.quantity,
					amount: purchaseAmount.finalAmount,
					unitPrice: quotaRule.basePrice,
					createdAt: result.createdAt,
				});
			}
		} finally {
			setIsSubmitting(false);
		}
	};

	return (
		<div className="space-y-8">
			{/* 大标题 */}
			<div className="space-y-2 text-center">
				<h3
					className={cn("text-2xl font-semibold", zywhFont.className)}
					style={{
						background:
							"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
						WebkitBackgroundClip: "text",
						WebkitTextFillColor: "transparent",
						backgroundClip: "text",
						textShadow:
							"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
					}}
				>
					确认分配信息
				</h3>
				<p className="text-[#D4B485]/60">
					请确认以下分配信息，确认无误后点击提交
				</p>
			</div>

			{/* 代理商信息 */}
			{formData.agent && (
				<div className="space-y-4">
					<div className="text-base font-medium text-[#D4B485]">
						代理商信息
					</div>
					<div
						className={cn(
							"rounded-lg p-4",
							"bg-[#1E2023]/50",
							"border border-[#D4B485]/20",
							"shadow-[inset_0_0_0_1px_rgba(212,180,133,0.05)]",
						)}
					>
						<div className="space-y-4">
							<div className="flex items-center justify-between">
								<div
									className={cn(
										"text-lg font-medium",
										zywhFont.className,
									)}
									style={{
										background:
											"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
										WebkitBackgroundClip: "text",
										WebkitTextFillColor: "transparent",
										backgroundClip: "text",
										textShadow:
											"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
									}}
								>
									{formData.agent.name}
								</div>
								<div className="text-sm text-[#D4B485]/60">
									{quotaRule?.roleName}
								</div>
							</div>
							<div className="space-y-1">
								<div className="text-sm text-[#D4B485]/40">
									{formData.agent.email}
								</div>
								<div className="text-sm text-[#D4B485]/40">
									{formData.agent.phone}
								</div>
							</div>
						</div>
					</div>
				</div>
			)}

			{/* 分配信息 */}
			<div className="space-y-4">
				<div className="text-base font-medium text-[#D4B485]">
					分配信息
				</div>
				<div
					className={cn(
						"rounded-lg p-4",
						"bg-[#1E2023]/50",
						"border border-[#D4B485]/20",
						"shadow-[inset_0_0_0_1px_rgba(212,180,133,0.05)]",
					)}
				>
					<div className="space-y-4">
						<div className="flex items-center justify-between">
							<div className="text-sm text-[#D4B485]/60">
								分配数量
							</div>
							<div className="text-lg font-medium text-[#D4B485]">
								{formData.quantity}个
							</div>
						</div>

						{/* 可购买的下级代理商数量 */}
						{formData.agent && purchaseableAgents && (
							<div className="space-y-2 rounded-lg bg-[#D4B485]/5 p-4">
								<div className="text-sm font-medium text-[#D4B485]">
									分配后可购买的下级代理商
								</div>
								<div className="grid gap-2">
									{getVisibleAgentTypes(
										formData.agent.role as AgentRole,
									).map((type) => (
										<div
											key={type}
											className="flex items-center justify-between text-sm"
										>
											<span className="text-[#D4B485]/60">
												{getAgentTypeLabel(type)}
											</span>
											<span className="text-[#D4B485]">
												{purchaseableAgents[
													type as keyof typeof purchaseableAgents
												] || 0}
												个
											</span>
										</div>
									))}
								</div>
							</div>
						)}

						{/* 金额信息 */}
						{purchaseAmount && (
							<>
								<div className="flex items-center justify-between">
									<div className="text-sm text-[#D4B485]/60">
										原价金额
									</div>
									<div className="text-sm text-[#D4B485]">
										¥
										{purchaseAmount.baseAmount.toLocaleString()}
									</div>
								</div>
								<div className="flex items-center justify-between">
									<div className="text-sm text-[#D4B485]/60">
										优惠金额
									</div>
									<div className="text-sm text-emerald-500">
										-¥
										{purchaseAmount.discountAmount.toLocaleString()}
									</div>
								</div>
								<div className="flex items-center justify-between border-t border-[#D4B485]/10 pt-2">
									<div className="text-base font-medium text-[#D4B485]">
										最终金额
									</div>
									<div className="text-lg font-medium text-[#D4B485]">
										¥
										{purchaseAmount.finalAmount.toLocaleString()}
									</div>
								</div>
							</>
						)}
					</div>
				</div>
			</div>

			{/* 底部按钮 */}
			<div className="flex justify-between pt-4">
				<Button
					variant="outline"
					className={cn(
						"px-8",
						"bg-[#1E2023]/50 border-[#D4B485]/20",
						"text-[#D4B485] hover:text-[#E5C9A5]",
						"hover:bg-[#D4B485]/10",
						"transition duration-200",
					)}
					onClick={onPrev}
					disabled={isSubmitting}
				>
					上一步
				</Button>
				<Button
					className={cn(
						"px-8",
						"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
						"text-white",
						"border-none",
						"shadow-[0_8px_16px_-4px_rgba(212,180,133,0.3)]",
						"hover:shadow-[0_12px_20px_-4px_rgba(212,180,133,0.4)]",
						"transition-all duration-200",
						"disabled:opacity-50 disabled:cursor-not-allowed",
					)}
					onClick={handleSubmit}
					disabled={
						isSubmitting || !formData.agent || !formData.quantity
					}
				>
					{isSubmitting ? "提交中..." : "确认提交"}
				</Button>
			</div>
		</div>
	);
}
