import type { AgentRole } from "@prisma/client";

// 订单状态
export const OrderStatus = {
	PENDING: "PENDING",
	COMPLETED: "COMPLETED",
	CANCELLED: "CANCELLED",
} as const;

// 订单状态标签
export const OrderStatusLabels: Record<string, string> = {
	[OrderStatus.PENDING]: "待处理",
	[OrderStatus.COMPLETED]: "已完成",
	[OrderStatus.CANCELLED]: "已取消",
};

// 订单状态颜色
export const OrderStatusColors: Record<string, string> = {
	[OrderStatus.PENDING]: "text-yellow-500",
	[OrderStatus.COMPLETED]: "text-emerald-500",
	[OrderStatus.CANCELLED]: "text-red-500",
};

// 代理商角色标签
export const AgentRoleLabels: Record<AgentRole, string> = {
	BRANCH: "分公司",
	DIRECTOR: "联席董事",
	PARTNER: "高级合伙人",
	SALES: "超级个体",
	ADMIN: "管理员",
};

// 代理商角色颜色
export const AgentRoleColors: Record<AgentRole, string> = {
	BRANCH: "text-purple-500",
	DIRECTOR: "text-blue-500",
	PARTNER: "text-green-500",
	SALES: "text-orange-500",
	ADMIN: "text-red-500",
};
