"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Button } from "@ui/components/button";
import { Card } from "@ui/components/card";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import {
	BarChart3,
	Calendar,
	Download,
	LineChart,
	<PERSON><PERSON>hart,
	TrendingUp,
	Users,
	Wallet,
} from "lucide-react";

// Mock数据
const mockStatistics = {
	totalRevenue: 12567890,
	monthlyGrowth: 15.8,
	totalAgents: 1234,
	agentGrowth: 12.5,
	totalOrders: 5678,
	orderGrowth: 8.5,
	avgOrderValue: 2345,
	orderValueGrowth: 5.2,
};

export function StatisticsView() {
	return (
		<div className="w-full space-y-6 p-6">
			{/* 标题和操作栏 */}
			<div className="flex items-center justify-between">
				<h1
					className={cn("text-2xl font-bold", zywhFont.className)}
					style={{
						background:
							"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
						WebkitBackgroundClip: "text",
						WebkitTextFillColor: "transparent",
						backgroundClip: "text",
						textShadow:
							"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
					}}
				>
					数据统计
				</h1>
				<div className="flex items-center gap-4">
					<div className="flex items-center gap-2">
						<Calendar className="h-4 w-4 text-[#D4B485]/60" />
						<span className="text-[#D4B485]/60">
							2024-01-01 至 2024-12-31
						</span>
					</div>
					<Button
						variant="outline"
						className={cn(
							"gap-2",
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"hover:bg-[#D4B485]/10",
						)}
					>
						<Download className="h-4 w-4" />
						<span>导出报表</span>
					</Button>
				</div>
			</div>

			{/* 统计卡片 */}
			<div className="grid grid-cols-4 gap-6">
				<StatCard
					title="总营收"
					value={`¥${(mockStatistics.totalRevenue / 100).toLocaleString()}`}
					change={mockStatistics.monthlyGrowth}
					icon={Wallet}
					delay={0.1}
				/>
				<StatCard
					title="代理商数"
					value={mockStatistics.totalAgents.toLocaleString()}
					change={mockStatistics.agentGrowth}
					icon={Users}
					delay={0.2}
				/>
				<StatCard
					title="订单总量"
					value={mockStatistics.totalOrders.toLocaleString()}
					change={mockStatistics.orderGrowth}
					icon={BarChart3}
					delay={0.3}
				/>
				<StatCard
					title="客单价"
					value={`¥${mockStatistics.avgOrderValue.toLocaleString()}`}
					change={mockStatistics.orderValueGrowth}
					icon={TrendingUp}
					delay={0.4}
				/>
			</div>

			{/* 图表区域 */}
			<div className="grid grid-cols-2 gap-6">
				{/* 营收趋势 */}
				<Card
					className={cn(
						"p-6",
						"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
						"border border-[#D4B485]/20",
						"group",
						"hover:border-[#D4B485]/40",
						"transition-all duration-500",
						"relative overflow-hidden",
					)}
				>
					{/* 背景装饰 */}
					<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
					<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />

					<div className="relative">
						<div className="flex items-center justify-between">
							<h2
								className={cn(
									"text-lg font-semibold",
									zywhFont.className,
								)}
								style={{
									background:
										"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
									WebkitBackgroundClip: "text",
									WebkitTextFillColor: "transparent",
									backgroundClip: "text",
									textShadow:
										"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
								}}
							>
								营收趋势
							</h2>
							<LineChart className="h-5 w-5 text-[#D4B485]/40" />
						</div>
						<div className="mt-4 h-[300px]">
							{/* 这里添加营收趋势图表 */}
							<div className="flex h-full items-center justify-center text-[#D4B485]/40">
								图表加载中...
							</div>
						</div>
					</div>
				</Card>

				{/* 订单分布 */}
				<Card
					className={cn(
						"p-6",
						"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
						"border border-[#D4B485]/20",
						"group",
						"hover:border-[#D4B485]/40",
						"transition-all duration-500",
						"relative overflow-hidden",
					)}
				>
					{/* 背景装饰 */}
					<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
					<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />

					<div className="relative">
						<div className="flex items-center justify-between">
							<h2
								className={cn(
									"text-lg font-semibold",
									zywhFont.className,
								)}
								style={{
									background:
										"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
									WebkitBackgroundClip: "text",
									WebkitTextFillColor: "transparent",
									backgroundClip: "text",
									textShadow:
										"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
								}}
							>
								订单分布
							</h2>
							<PieChart className="h-5 w-5 text-[#D4B485]/40" />
						</div>
						<div className="mt-4 h-[300px]">
							{/* 这里添加订单分布图表 */}
							<div className="flex h-full items-center justify-center text-[#D4B485]/40">
								图表加载中...
							</div>
						</div>
					</div>
				</Card>
			</div>
		</div>
	);
}

interface StatCardProps {
	title: string;
	value: string;
	change: number;
	icon: typeof Wallet;
	delay?: number;
}

function StatCard({
	title,
	value,
	change,
	icon: Icon,
	delay = 0,
}: StatCardProps) {
	const isPositive = change >= 0;

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ delay }}
			className={cn(
				"rounded-xl p-6",
				"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
				"border border-[#D4B485]/20",
				"group",
				"hover:border-[#D4B485]/40",
				"transition-all duration-500",
				"relative overflow-hidden",
			)}
		>
			{/* 背景装饰 */}
			<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
			<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />

			<div className="relative space-y-3">
				<div className="flex items-center justify-between">
					<h3 className="text-sm font-medium text-[#D4B485]/60">
						{title}
					</h3>
					<Icon className="h-4 w-4 text-[#D4B485]/40" />
				</div>

				<div>
					<div
						className={cn(
							"text-2xl font-semibold",
							zywhFont.className,
						)}
						style={{
							background:
								"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
							WebkitBackgroundClip: "text",
							WebkitTextFillColor: "transparent",
							backgroundClip: "text",
							textShadow:
								"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
						}}
					>
						{value}
					</div>
					<div className="mt-1 flex items-center gap-2 text-sm">
						<span
							className={cn(
								"font-medium",
								isPositive
									? "text-emerald-500"
									: "text-rose-500",
							)}
						>
							{isPositive ? "+" : ""}
							{change}%
						</span>
						<span className="text-[#D4B485]/40">较上月</span>
					</div>
				</div>
			</div>
		</motion.div>
	);
}
