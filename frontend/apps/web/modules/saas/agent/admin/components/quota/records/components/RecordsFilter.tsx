import { Button } from "@ui/components/button";
import { Form, FormControl, FormField } from "@ui/components/form";
import { Input } from "@ui/components/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { cn } from "@ui/lib";
import { RefreshCcwIcon, SearchIcon } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { OrderStatus, OrderStatusLabels } from "../config/records-config";
import type { RecordsQuery } from "../hooks/use-records";

interface RecordsFilterProps {
	onFilter: (query: RecordsQuery) => void;
	defaultValues?: Partial<RecordsQuery>;
}

export function RecordsFilter({ onFilter, defaultValues }: RecordsFilterProps) {
	const [debouncedFilter, setDebouncedFilter] = useState<RecordsQuery | null>(
		null,
	);

	const form = useForm<RecordsQuery>({
		defaultValues: {
			page: 1,
			pageSize: 10,
			...defaultValues,
		},
	});

	const onSubmit = (values: RecordsQuery) => {
		setDebouncedFilter(values);
	};

	// 处理搜索按钮点击
	const handleSearch = () => {
		form.handleSubmit(onSubmit)();
	};

	// 处理刷新按钮点击
	const handleRefresh = () => {
		form.reset(defaultValues);
		form.handleSubmit(onSubmit)();
	};

	useEffect(() => {
		if (debouncedFilter) {
			const timer = setTimeout(() => {
				onFilter(debouncedFilter);
			}, 500);

			return () => clearTimeout(timer);
		}
	}, [debouncedFilter, onFilter]);

	return (
		<Form {...form}>
			<form
				onSubmit={form.handleSubmit(onSubmit)}
				className="space-y-4 rounded-lg bg-[#1E2023]/50 p-4"
			>
				<div className="flex items-center gap-4">
					{/* 代理商ID */}
					<div className="relative flex-1">
						<SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-[#D4B485]/40" />
						<FormField
							control={form.control}
							name="fromAgentId"
							render={({ field }) => (
								<FormControl>
									<Input
										placeholder="输入来源代理商ID搜索"
										className={cn(
											"pl-9",
											"bg-[#1E2023]/50",
											"border-[#D4B485]/20",
											"text-[#D4B485]",
											"placeholder:text-[#D4B485]/40",
											"focus:border-[#D4B485]/40",
											"focus:ring-[#D4B485]/20",
										)}
										{...field}
										onChange={(e) => {
											field.onChange(e);
											form.handleSubmit(onSubmit)();
										}}
										onKeyDown={(e) => {
											if (e.key === "Enter") {
												handleSearch();
											}
										}}
									/>
								</FormControl>
							)}
						/>
					</div>

					{/* 目标代理商ID */}
					<div className="relative flex-1">
						<SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-[#D4B485]/40" />
						<FormField
							control={form.control}
							name="toAgentId"
							render={({ field }) => (
								<FormControl>
									<Input
										placeholder="输入目标代理商ID搜索"
										className={cn(
											"pl-9",
											"bg-[#1E2023]/50",
											"border-[#D4B485]/20",
											"text-[#D4B485]",
											"placeholder:text-[#D4B485]/40",
											"focus:border-[#D4B485]/40",
											"focus:ring-[#D4B485]/20",
										)}
										{...field}
										onChange={(e) => {
											field.onChange(e);
											form.handleSubmit(onSubmit)();
										}}
										onKeyDown={(e) => {
											if (e.key === "Enter") {
												handleSearch();
											}
										}}
									/>
								</FormControl>
							)}
						/>
					</div>

					{/* 订单状态 */}
					<FormField
						control={form.control}
						name="status"
						render={({ field }) => (
							<Select
								onValueChange={(value) => {
									field.onChange(
										value === "ALL" ? undefined : value,
									);
									form.handleSubmit(onSubmit)();
								}}
								defaultValue={field.value || "ALL"}
							>
								<FormControl>
									<SelectTrigger
										className={cn(
											"w-[160px]",
											"bg-[#1E2023]/50",
											"border-[#D4B485]/20",
											"text-[#D4B485]",
											"focus:border-[#D4B485]/40",
											"focus:ring-[#D4B485]/20",
										)}
									>
										<SelectValue placeholder="选择订单状态" />
									</SelectTrigger>
								</FormControl>
								<SelectContent>
									<SelectItem
										value="ALL"
										className={cn(
											"text-[#D4B485]",
											"focus:bg-[#D4B485]/10",
											"focus:text-[#E5C9A5]",
										)}
									>
										全部状态
									</SelectItem>
									{Object.entries(OrderStatus).map(
										([key, value]) => (
											<SelectItem
												key={key}
												value={value}
												className={cn(
													"text-[#D4B485]",
													"focus:bg-[#D4B485]/10",
													"focus:text-[#E5C9A5]",
												)}
											>
												{OrderStatusLabels[value]}
											</SelectItem>
										),
									)}
								</SelectContent>
							</Select>
						)}
					/>

					{/* 搜索按钮 */}
					<Button
						type="submit"
						className={cn(
							"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
							"text-white",
							"border-none",
							"shadow-[0_8px_16px_-4px_rgba(212,180,133,0.3)]",
							"hover:shadow-[0_12px_20px_-4px_rgba(212,180,133,0.4)]",
							"transition-all duration-200",
						)}
						onClick={handleSearch}
					>
						搜索
					</Button>

					{/* 刷新按钮 */}
					<Button
						variant="outline"
						size="icon"
						className={cn(
							"bg-[#1E2023]/50 border-[#D4B485]/20",
							"text-[#D4B485] hover:text-[#E5C9A5]",
							"hover:bg-[#D4B485]/10",
							"transition duration-200",
							"w-10 h-10",
							"flex items-center justify-center",
						)}
						onClick={handleRefresh}
					>
						<RefreshCcwIcon className="h-4 w-4" />
					</Button>
				</div>
			</form>
		</Form>
	);
}
