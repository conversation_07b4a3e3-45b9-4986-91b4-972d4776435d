"use client";

import { But<PERSON> } from "@ui/components/button";
import { Card } from "@ui/components/card";
import { Input } from "@ui/components/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { cn } from "@ui/lib";
import { Calendar, Filter, Search } from "lucide-react";
import { useState } from "react";
import type { OrderListQuery } from "../hooks";

interface OrderFiltersProps {
	onFiltersChange: (filters: Partial<OrderListQuery>) => void;
	loading?: boolean;
}

export function OrderFilters({ onFiltersChange, loading }: OrderFiltersProps) {
	const [searchTerm, setSearchTerm] = useState("");
	const [orderType, setOrderType] = useState<string>("ALL");
	const [orderStatus, setOrderStatus] = useState<string>("ALL");
	const [paymentStatus, setPaymentStatus] = useState<string>("ALL");

	const handleSearchChange = (value: string) => {
		setSearchTerm(value);
		onFiltersChange({ searchTerm: value });
	};

	const handleOrderTypeChange = (value: string) => {
		setOrderType(value);
		onFiltersChange({ orderType: value as any });
	};

	const handleOrderStatusChange = (value: string) => {
		setOrderStatus(value);
		onFiltersChange({ orderStatus: value as any });
	};

	const handlePaymentStatusChange = (value: string) => {
		setPaymentStatus(value);
		onFiltersChange({ paymentStatus: value as any });
	};

	const handleDateRangeChange = () => {
		// TODO: 实现日期范围选择
		console.log("Date range change");
	};

	return (
		<Card
			className={cn(
				"p-4",
				"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
				"border border-[#D4B485]/20",
				"group",
				"hover:border-[#D4B485]/40",
				"transition-all duration-500",
			)}
		>
			<div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-5">
				{/* 搜索框 */}
				<div className="relative">
					<Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-[#D4B485]/40" />
					<Input
						placeholder="搜索订单号、用户名或手机号"
						value={searchTerm}
						onChange={(e) => handleSearchChange(e.target.value)}
						disabled={loading}
						className={cn(
							"pl-10",
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"placeholder:text-[#D4B485]/40",
							"focus:border-[#D4B485]/60",
						)}
					/>
				</div>

				{/* 订单类型筛选 */}
				<Select
					value={orderType}
					onValueChange={handleOrderTypeChange}
					disabled={loading}
				>
					<SelectTrigger
						className={cn(
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"focus:border-[#D4B485]/60",
						)}
					>
						<SelectValue placeholder="订单类型" />
					</SelectTrigger>
					<SelectContent className="bg-[#1A1C1E] border-[#D4B485]/20">
						<SelectItem value="ALL">全部类型</SelectItem>
						<SelectItem value="AGENT_PLAN">代理商套餐</SelectItem>
						<SelectItem value="DIGITAL_HUMAN">数字人</SelectItem>
					</SelectContent>
				</Select>

				{/* 订单状态筛选 */}
				<Select
					value={orderStatus}
					onValueChange={handleOrderStatusChange}
					disabled={loading}
				>
					<SelectTrigger
						className={cn(
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"focus:border-[#D4B485]/60",
						)}
					>
						<SelectValue placeholder="订单状态" />
					</SelectTrigger>
					<SelectContent className="bg-[#1A1C1E] border-[#D4B485]/20">
						<SelectItem value="ALL">全部状态</SelectItem>
						<SelectItem value="PENDING">待支付</SelectItem>
						<SelectItem value="PAID">已支付</SelectItem>
						<SelectItem value="COMPLETED">已完成</SelectItem>
						<SelectItem value="FAILED">失败</SelectItem>
						<SelectItem value="CANCELLED">已取消</SelectItem>
						<SelectItem value="REFUNDED">已退款</SelectItem>
					</SelectContent>
				</Select>

				{/* 支付状态筛选 */}
				<Select
					value={paymentStatus}
					onValueChange={handlePaymentStatusChange}
					disabled={loading}
				>
					<SelectTrigger
						className={cn(
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"focus:border-[#D4B485]/60",
						)}
					>
						<SelectValue placeholder="支付状态" />
					</SelectTrigger>
					<SelectContent className="bg-[#1A1C1E] border-[#D4B485]/20">
						<SelectItem value="ALL">全部状态</SelectItem>
						<SelectItem value="PENDING">待支付</SelectItem>
						<SelectItem value="SUCCESS">支付成功</SelectItem>
						<SelectItem value="FAILED">支付失败</SelectItem>
						<SelectItem value="REFUNDED">已退款</SelectItem>
					</SelectContent>
				</Select>

				{/* 日期范围和高级筛选 */}
				<div className="flex gap-2">
					<Button
						variant="outline"
						size="sm"
						onClick={handleDateRangeChange}
						disabled={loading}
						className={cn(
							"flex-1 gap-2",
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"hover:bg-[#D4B485]/10",
						)}
					>
						<Calendar className="h-4 w-4" />
						<span className="hidden sm:inline">日期</span>
					</Button>
					<Button
						variant="outline"
						size="sm"
						disabled={loading}
						className={cn(
							"gap-2",
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"hover:bg-[#D4B485]/10",
						)}
					>
						<Filter className="h-4 w-4" />
						<span className="hidden sm:inline">高级</span>
					</Button>
				</div>
			</div>
		</Card>
	);
}
