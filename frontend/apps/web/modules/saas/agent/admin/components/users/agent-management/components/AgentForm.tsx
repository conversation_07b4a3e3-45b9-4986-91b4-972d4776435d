"use client";

import type { Agent<PERSON><PERSON> } from "@prisma/client";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { cn } from "@ui/lib";
import { useEffect, useId, useState } from "react";
import type { CreateAgentParams } from "../hooks/use-agent-create";
import type { AgentDetail } from "../hooks/use-agent-detail";
import type { UpdateAgentParams } from "../hooks/use-agent-update";

// 角色名称映射
const roleNameMap: Record<AgentRole, string> = {
	ADMIN: "管理员",
	BRANCH: "分公司",
	DIRECTOR: "联席董事",
	PARTNER: "合伙人",
	SALES: "超级个体",
};

// 状态名称映射
const statusNameMap: Record<string, string> = {
	ACTIVE: "已激活",
	DISABLED: "已禁用",
	PENDING: "待审核",
};

export interface AgentFormProps {
	agent?: AgentDetail | null;
	isLoading?: boolean;
	onSubmit?: (data: CreateAgentParams | UpdateAgentParams) => void;
	onCancel?: () => void;
}

export function AgentForm({
	agent,
	isLoading,
	onSubmit,
	onCancel,
}: AgentFormProps) {
	const isEditMode = !!agent;
	const id = useId();

	// 表单状态
	const [formData, setFormData] = useState<
		CreateAgentParams | UpdateAgentParams
	>({
		name: "",
		email: "",
		phoneNumber: "",
		role: "SALES",
		realName: "",
		idCardNumber: "",
	});

	// 确定status的值，仅在编辑模式下使用
	const status = isEditMode
		? (formData as UpdateAgentParams).status
		: undefined;

	// 表单验证错误
	const [errors, setErrors] = useState<Record<string, string>>({});

	// 初始化表单数据
	useEffect(() => {
		if (agent) {
			// 确保status值符合定义的字面量类型
			const agentStatus =
				agent.status === "ACTIVE" ||
				agent.status === "DISABLED" ||
				agent.status === "PENDING"
					? (agent.status as "ACTIVE" | "DISABLED" | "PENDING")
					: "ACTIVE"; // 默认值

			const data: UpdateAgentParams = {
				id: agent.id,
				name: agent.name,
				email: agent.email,
				phoneNumber: agent.phone.replace(/^\+\d+/, ""), // 移除国际区号
				role: agent.role,
				status: agentStatus,
				parentId: agent.parentId || undefined,
			};
			setFormData(data);
		}
	}, [agent]);

	// 处理表单字段变化
	const handleChange = (field: string, value: string) => {
		if (field === "status") {
			// 确保status值符合类型定义
			if (
				value === "ACTIVE" ||
				value === "DISABLED" ||
				value === "PENDING"
			) {
				setFormData((prev) => ({
					...prev,
					[field]: value as "ACTIVE" | "DISABLED" | "PENDING",
				}));
			}
		} else {
			setFormData((prev) => ({ ...prev, [field]: value }));
		}

		// 清除该字段的错误
		if (errors[field]) {
			setErrors((prev) => {
				const newErrors = { ...prev };
				delete newErrors[field];
				return newErrors;
			});
		}
	};

	// 验证表单
	const validateForm = (): boolean => {
		const newErrors: Record<string, string> = {};

		if (!formData.name) {
			newErrors.name = "请输入代理商名称";
		}

		if (!formData.email) {
			newErrors.email = "请输入邮箱";
		} else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
			newErrors.email = "请输入有效的邮箱地址";
		}

		if (!formData.phoneNumber) {
			newErrors.phoneNumber = "请输入手机号";
		} else if (!/^\d{11}$/.test(formData.phoneNumber)) {
			newErrors.phoneNumber = "请输入有效的手机号";
		}

		// 身份证号格式验证 (如果填写了)
		if (
			(formData as CreateAgentParams).idCardNumber &&
			!/^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/.test(
				(formData as CreateAgentParams).idCardNumber as string,
			)
		) {
			newErrors.idCardNumber = "请输入有效的身份证号";
		}

		// 如果填了身份证号但没填真实姓名
		if (
			(formData as CreateAgentParams).idCardNumber &&
			!(formData as CreateAgentParams).realName
		) {
			newErrors.realName = "提供身份证号时必须填写真实姓名";
		}

		setErrors(newErrors);
		return Object.keys(newErrors).length === 0;
	};

	// 处理表单提交
	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();

		if (validateForm()) {
			onSubmit?.(formData);
		}
	};

	return (
		<form onSubmit={handleSubmit} className="space-y-6">
			<div className="grid grid-cols-2 gap-6">
				<div className="space-y-2">
					<Label htmlFor={`${id}-name`} className="text-[#D4B485]/60">
						代理商名称
					</Label>
					<Input
						id={`${id}-name`}
						value={formData.name}
						onChange={(e) => handleChange("name", e.target.value)}
						placeholder="请输入代理商名称"
						className={cn(
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"focus-visible:ring-[#D4B485]/40",
							errors.name && "border-red-500",
						)}
						disabled={isLoading}
					/>
					{errors.name && (
						<p className="text-xs text-red-500 mt-1">
							{errors.name}
						</p>
					)}
				</div>

				<div className="space-y-2">
					<Label
						htmlFor={`${id}-email`}
						className="text-[#D4B485]/60"
					>
						邮箱
					</Label>
					<Input
						id={`${id}-email`}
						type="email"
						value={formData.email}
						onChange={(e) => handleChange("email", e.target.value)}
						placeholder="请输入邮箱"
						className={cn(
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"focus-visible:ring-[#D4B485]/40",
							errors.email && "border-red-500",
						)}
						disabled={isLoading}
					/>
					{errors.email && (
						<p className="text-xs text-red-500 mt-1">
							{errors.email}
						</p>
					)}
				</div>

				<div className="space-y-2">
					<Label
						htmlFor={`${id}-phoneNumber`}
						className="text-[#D4B485]/60"
					>
						手机号
					</Label>
					<Input
						id={`${id}-phoneNumber`}
						value={formData.phoneNumber}
						onChange={(e) =>
							handleChange("phoneNumber", e.target.value)
						}
						placeholder="请输入手机号"
						className={cn(
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"focus-visible:ring-[#D4B485]/40",
							errors.phoneNumber && "border-red-500",
						)}
						disabled={isLoading}
					/>
					{errors.phoneNumber && (
						<p className="text-xs text-red-500 mt-1">
							{errors.phoneNumber}
						</p>
					)}
				</div>

				<div className="space-y-2">
					<Label htmlFor={`${id}-role`} className="text-[#D4B485]/60">
						角色
					</Label>
					<Select
						value={formData.role}
						onValueChange={(value) => handleChange("role", value)}
						disabled={isLoading || isEditMode}
					>
						<SelectTrigger
							id={`${id}-role`}
							className={cn(
								"bg-[#1A1C1E]",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
								"focus:ring-[#D4B485]/40",
							)}
						>
							<SelectValue placeholder="选择角色" />
						</SelectTrigger>
						<SelectContent
							className={cn(
								"bg-[#1A1C1E]",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
							)}
						>
							{Object.entries(roleNameMap).map(
								([role, name]) =>
									role !== "ADMIN" && (
										<SelectItem
											key={role}
											value={role}
											className="focus:bg-[#D4B485]/10"
										>
											{name}
										</SelectItem>
									),
							)}
						</SelectContent>
					</Select>
				</div>

				{!isEditMode && (
					<>
						<div className="space-y-2">
							<Label
								htmlFor={`${id}-realName`}
								className="text-[#D4B485]/60"
							>
								真实姓名（实名认证用）
							</Label>
							<Input
								id={`${id}-realName`}
								value={
									(formData as CreateAgentParams).realName ||
									""
								}
								onChange={(e) =>
									handleChange("realName", e.target.value)
								}
								placeholder="请输入真实姓名（选填）"
								className={cn(
									"bg-[#1A1C1E]",
									"border-[#D4B485]/20",
									"text-[#D4B485]",
									"focus-visible:ring-[#D4B485]/40",
									errors.realName && "border-red-500",
								)}
								disabled={isLoading}
							/>
							{errors.realName && (
								<p className="text-xs text-red-500 mt-1">
									{errors.realName}
								</p>
							)}
						</div>

						<div className="space-y-2">
							<Label
								htmlFor={`${id}-idCardNumber`}
								className="text-[#D4B485]/60"
							>
								身份证号码
							</Label>
							<Input
								id={`${id}-idCardNumber`}
								value={
									(formData as CreateAgentParams)
										.idCardNumber || ""
								}
								onChange={(e) =>
									handleChange("idCardNumber", e.target.value)
								}
								placeholder="提供后自动完成实名认证（选填）"
								className={cn(
									"bg-[#1A1C1E]",
									"border-[#D4B485]/20",
									"text-[#D4B485]",
									"focus-visible:ring-[#D4B485]/40",
									errors.idCardNumber && "border-red-500",
								)}
								disabled={isLoading}
							/>
							{errors.idCardNumber && (
								<p className="text-xs text-red-500 mt-1">
									{errors.idCardNumber}
								</p>
							)}
							<p className="text-xs text-[#D4B485]/60 mt-0.5">
								提供身份证号将自动完成实名认证，无需用户额外操作
							</p>
						</div>
					</>
				)}

				{isEditMode && (
					<div className="space-y-2">
						<Label
							htmlFor={`${id}-status`}
							className="text-[#D4B485]/60"
						>
							状态
						</Label>
						<Select
							value={status}
							onValueChange={(value) =>
								handleChange("status", value)
							}
							disabled={isLoading}
						>
							<SelectTrigger
								id={`${id}-status`}
								className={cn(
									"bg-[#1A1C1E]",
									"border-[#D4B485]/20",
									"text-[#D4B485]",
									"focus-visible:ring-[#D4B485]/40",
								)}
							>
								<SelectValue placeholder="选择状态" />
							</SelectTrigger>
							<SelectContent
								className={cn(
									"bg-[#1A1C1E]",
									"border-[#D4B485]/20",
									"text-[#D4B485]",
								)}
							>
								{Object.entries(statusNameMap).map(
									([status, name]) => (
										<SelectItem
											key={status}
											value={status}
											className="focus:bg-[#D4B485]/10"
										>
											{name}
										</SelectItem>
									),
								)}
							</SelectContent>
						</Select>
					</div>
				)}
			</div>

			<div className="flex justify-end gap-4 pt-4">
				<Button
					type="button"
					variant="ghost"
					onClick={onCancel}
					disabled={isLoading}
					className="text-[#D4B485] hover:bg-[#D4B485]/10"
				>
					取消
				</Button>
				<Button
					type="submit"
					disabled={isLoading}
					className={cn(
						"bg-[#D4B485] text-[#1A1C1E] hover:bg-[#D4B485]/90",
						"font-semibold",
					)}
				>
					{isLoading
						? isEditMode
							? "更新中..."
							: "创建中..."
						: isEditMode
							? "保存更新"
							: "确认创建"}
				</Button>
			</div>
		</form>
	);
}
