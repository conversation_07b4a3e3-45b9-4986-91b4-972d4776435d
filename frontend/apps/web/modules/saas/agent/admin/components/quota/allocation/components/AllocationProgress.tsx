"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { CheckIcon } from "lucide-react";

interface Step {
	title: string;
	description: string;
}

interface AllocationProgressProps {
	steps: Step[];
	currentStep: number;
}

export function AllocationProgress({
	steps,
	currentStep,
}: AllocationProgressProps) {
	return (
		<div className="relative">
			{/* 背景线条 */}
			<div
				className={cn(
					"absolute left-[calc(50%/3)] right-[calc(50%/3)] top-[22px]",
					"h-[2px]",
					"bg-[#1E2023]",
					"rounded-full",
				)}
			/>

			{/* 发光进度条 */}
			<motion.div
				className={cn(
					"absolute left-[calc(50%/3)] top-[22px]",
					"h-[2px]",
					"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
					"rounded-full",
				)}
				initial={{ width: "0%" }}
				animate={{
					width: `${(currentStep / steps.length) * 100}%`,
				}}
				transition={{ duration: 0.5, ease: "easeInOut" }}
			/>

			{/* 步骤点 */}
			<div className="relative z-10 flex justify-between">
				{steps.map((step, index) => {
					const isCompleted = index < currentStep;
					const isCurrent = index === currentStep;

					return (
						<div
							key={step.title}
							className="flex flex-col items-center"
						>
							{/* 步骤圆点 */}
							<motion.div
								className={cn(
									"flex h-11 w-11 items-center justify-center rounded-full",
									"transition-all duration-500",
									"relative z-10",
									isCompleted
										? "bg-gradient-to-r from-[#D4B485] to-[#B08968] text-white"
										: isCurrent
											? "bg-[#D4B485] text-white ring-4 ring-[#D4B485]/20"
											: "bg-[#1E2023] text-[#D4B485]/40 ring-2 ring-[#D4B485]/20",
									"shadow-[0_4px_12px_-2px_rgba(212,180,133,0.2)]",
								)}
								initial={false}
								animate={{
									scale: isCurrent ? 1.1 : 1,
									transition: {
										duration: 0.3,
										ease: "easeOut",
									},
								}}
							>
								{isCompleted ? (
									<CheckIcon className="h-5 w-5" />
								) : (
									<span
										className={cn(
											"text-lg font-semibold",
											zywhFont.className,
										)}
									>
										{index + 1}
									</span>
								)}
							</motion.div>

							{/* 步骤文字 */}
							<div className="mt-4 space-y-1 text-center">
								<div
									className={cn(
										"text-sm font-medium",
										zywhFont.className,
										isCompleted || isCurrent
											? "text-[#D4B485]"
											: "text-[#D4B485]/40",
									)}
								>
									{step.title}
								</div>
								<div
									className={cn(
										"text-xs",
										isCompleted || isCurrent
											? "text-[#D4B485]/60"
											: "text-[#D4B485]/20",
									)}
								>
									{step.description}
								</div>
							</div>
						</div>
					);
				})}
			</div>
		</div>
	);
}
