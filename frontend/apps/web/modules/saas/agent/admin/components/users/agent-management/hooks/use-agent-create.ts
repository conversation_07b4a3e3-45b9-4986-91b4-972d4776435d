import type { Agent<PERSON><PERSON> } from "@prisma/client";
import { logger } from "@repo/logs";
import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";
import { type ApiResult, isAgentResponse, isErrorResponse } from "../types";
import type { AgentDetail } from "./use-agent-detail";

// 创建代理商请求参数
export interface CreateAgentParams {
	name: string;
	email: string;
	phoneNumber: string;
	role: AgentRole;
	parentId?: string;
	realName?: string;
	idCardNumber?: string;
}

export function useAgentCreate() {
	const [loading, setLoading] = useState(false);
	const [agent, setAgent] = useState<AgentDetail | null>(null);

	// 扩展API调用的类型
	type PostOptions = {
		json: CreateAgentParams;
	};

	const createAgent = useCallback(
		async (params: CreateAgentParams): Promise<ApiResult> => {
			setLoading(true);
			try {
				const response =
					await apiClient.v1.agent.admin.user.agent.create.$post({
						json: params,
					} as PostOptions);

				// 无论HTTP状态码是什么，都尝试解析响应体
				const responseData = await response.json();

				// 检查是否是成功响应
				if (isAgentResponse(responseData)) {
					setAgent(responseData.agent);
					toast.success("代理商创建成功");
					return { success: true, agent: responseData.agent };
				}

				// 处理错误响应
				// 显示更友好的错误消息，使用响应体中的错误消息
				if (isErrorResponse(responseData)) {
					const errorMessage = responseData.message;
					toast.error(errorMessage);

					logger.error("[Admin] 创建代理商失败", {
						error: errorMessage,
						errorCode: responseData.error,
						params,
					});

					return {
						success: false,
						error: errorMessage,
						errorCode: responseData.error,
					};
				}

				// 处理未预期的响应格式
				toast.error("创建代理商失败");
				logger.error("[Admin] 创建代理商失败（未知响应格式）", {
					responseData,
					params,
				});

				return {
					success: false,
					error: "创建代理商失败（服务器响应异常）",
					errorCode: "UNEXPECTED_RESPONSE",
				};
			} catch (error) {
				toast.error("创建代理商失败");
				logger.error("[Admin] 创建代理商失败", {
					error: error instanceof Error ? error.message : "未知错误",
					params,
				});
				return {
					success: false,
					error: error instanceof Error ? error.message : "未知错误",
					errorCode: "UNKNOWN_ERROR",
				};
			} finally {
				setLoading(false);
			}
		},
		[],
	);

	return {
		loading,
		agent,
		createAgent,
	};
}
