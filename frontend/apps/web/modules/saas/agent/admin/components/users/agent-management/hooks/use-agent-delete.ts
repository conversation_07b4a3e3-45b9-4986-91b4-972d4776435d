import { logger } from "@repo/logs";
import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";
import { type ApiResult, isDeleteResponse, isErrorResponse } from "../types";

export function useAgentDelete() {
	const [loading, setLoading] = useState(false);

	const deleteAgent = useCallback(async (id: string): Promise<ApiResult> => {
		setLoading(true);
		try {
			const response = await apiClient.v1.agent.admin.user.agent.delete[
				":id"
			].$delete({
				param: { id },
			});

			// 无论HTTP状态码是什么，都尝试解析响应体
			const responseData = await response.json();

			// 检查是否是成功响应
			if (isDeleteResponse(responseData) && responseData.success) {
				toast.success("代理商删除成功");
				return { success: true };
			}

			// 处理错误响应
			if (isErrorResponse(responseData)) {
				const errorMessage = responseData.message;
				toast.error(errorMessage);

				logger.error("[Admin] 删除代理商失败", {
					error: errorMessage,
					errorCode: responseData.error,
					id,
				});

				return {
					success: false,
					error: errorMessage,
					errorCode: responseData.error,
				};
			}

			// 处理未预期的响应格式
			toast.error("删除代理商失败");
			logger.error("[Admin] 删除代理商失败（未知响应格式）", {
				responseData,
				id,
			});

			return {
				success: false,
				error: "删除代理商失败（服务器响应异常）",
				errorCode: "UNEXPECTED_RESPONSE",
			};
		} catch (error) {
			toast.error("删除代理商失败");
			logger.error("[Admin] 删除代理商失败", {
				error: error instanceof Error ? error.message : "未知错误",
				id,
			});
			return {
				success: false,
				error: error instanceof Error ? error.message : "未知错误",
				errorCode: "UNKNOWN_ERROR",
			};
		} finally {
			setLoading(false);
		}
	}, []);

	return {
		loading,
		deleteAgent,
	};
}
