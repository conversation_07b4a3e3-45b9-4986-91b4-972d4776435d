"use client";

import { type DateValue, parseDate } from "@internationalized/date";
import type { AgentRole } from "@prisma/client";
import { Button } from "@ui/components/button";
import { JollyRangeCalendar } from "@ui/components/calendar";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { cn } from "@ui/lib";
import { format } from "date-fns";
import { CalendarIcon, SearchIcon, XCircleIcon } from "lucide-react";
import { useEffect, useId, useState } from "react";
import type { DateRange } from "react-day-picker";

// 角色名称映射
const roleNameMap: Record<AgentRole, string> = {
	ADMIN: "管理员",
	BRANCH: "分公司",
	DIRECTOR: "联席董事",
	PARTNER: "合伙人",
	SALES: "超级个体",
};

export interface AgentFilterOptions {
	searchTerm: string;
	role: AgentRole | "ALL";
	status: string;
	dateRange: DateRange | undefined;
}

export interface AgentFilterBarProps {
	onChange?: (filters: AgentFilterOptions) => void;
}

const defaultFilters: AgentFilterOptions = {
	searchTerm: "",
	role: "ALL",
	status: "ALL",
	dateRange: undefined,
};

export function AgentFilterBar({ onChange }: AgentFilterBarProps) {
	const [filters, setFilters] = useState<AgentFilterOptions>(defaultFilters);
	const [date, setDate] = useState<DateRange | undefined>(undefined);
	const id = useId();

	// 当筛选条件变化时触发onChange回调
	useEffect(() => {
		onChange?.(filters);
	}, [filters, onChange]);

	// 日期转换函数 - 将普通Date转为DateValue
	const dateToDateValue = (date?: Date): DateValue | undefined => {
		if (!date) {
			return undefined;
		}
		return parseDate(date.toISOString().split("T")[0]);
	};

	// 日期转换函数 - 将DateValue转为普通Date
	const dateValueToDate = (date: DateValue): Date => {
		return new Date(date.toString());
	};

	// 获取RangeCalendar需要的日期格式
	const getAriaDateRange = () => {
		if (!date?.from) {
			return undefined;
		}

		const start = dateToDateValue(date.from);
		if (!start) {
			return undefined;
		}

		return {
			start,
			end: date.to ? dateToDateValue(date.to) || start : start,
		};
	};

	// 处理日期选择 - 将aria range值转换为旧的DateRange格式
	const handleRangeChange = (
		newRange: { start: DateValue; end?: DateValue } | null,
	) => {
		if (!newRange) {
			const newDateRange = undefined;
			setDate(newDateRange);
			updateDateRange(newDateRange);
			return;
		}

		const legacyRange: DateRange = {
			from: dateValueToDate(newRange.start),
			to: newRange.end ? dateValueToDate(newRange.end) : undefined,
		};

		setDate(legacyRange);
		updateDateRange(legacyRange);
	};

	// 更新搜索词
	const updateSearchTerm = (value: string) => {
		setFilters((prev) => ({ ...prev, searchTerm: value }));
	};

	// 更新角色筛选
	const updateRole = (value: AgentRole | "ALL") => {
		setFilters((prev) => ({ ...prev, role: value }));
	};

	// 更新状态筛选
	const updateStatus = (value: string) => {
		setFilters((prev) => ({ ...prev, status: value }));
	};

	// 更新日期范围
	const updateDateRange = (range: DateRange | undefined) => {
		setDate(range);
		setFilters((prev) => ({ ...prev, dateRange: range }));
	};

	// 重置所有筛选
	const resetFilters = () => {
		setFilters(defaultFilters);
		setDate(undefined);
	};

	return (
		<div className="space-y-4">
			<div className="flex flex-wrap gap-4">
				<div className="flex-1 min-w-[240px]">
					<Label
						htmlFor={`${id}-search`}
						className="mb-2 block text-[#D4B485]/60 text-sm"
					>
						搜索
					</Label>
					<div className="relative">
						<SearchIcon className="absolute left-3 top-2.5 h-4 w-4 text-[#D4B485]/40" />
						<Input
							id={`${id}-search`}
							placeholder="搜索名称、邮箱或电话"
							value={filters.searchTerm}
							onChange={(e) => updateSearchTerm(e.target.value)}
							className={cn(
								"pl-9",
								"bg-[#1A1C1E]",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
								"placeholder:text-[#D4B485]/40",
								"focus-visible:ring-[#D4B485]/40",
							)}
						/>
						{filters.searchTerm && (
							<button
								type="button"
								onClick={() => updateSearchTerm("")}
								className="absolute right-3 top-2.5"
							>
								<XCircleIcon className="h-4 w-4 text-[#D4B485]/40" />
							</button>
						)}
					</div>
				</div>

				<div className="w-[180px]">
					<Label
						htmlFor={`${id}-role`}
						className="mb-2 block text-[#D4B485]/60 text-sm"
					>
						角色
					</Label>
					<Select
						value={filters.role}
						onValueChange={(value) =>
							updateRole(value as AgentRole | "ALL")
						}
					>
						<SelectTrigger
							id={`${id}-role`}
							className={cn(
								"bg-[#1A1C1E]",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
								"focus:ring-[#D4B485]/40",
							)}
						>
							<SelectValue placeholder="选择角色" />
						</SelectTrigger>
						<SelectContent
							className={cn(
								"bg-[#1A1C1E]",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
							)}
						>
							<SelectItem value="ALL">全部角色</SelectItem>
							{Object.entries(roleNameMap).map(([role, name]) => (
								<SelectItem key={role} value={role}>
									{name}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>

				<div className="w-[180px]">
					<Label
						htmlFor={`${id}-status`}
						className="mb-2 block text-[#D4B485]/60 text-sm"
					>
						状态
					</Label>
					<Select value={filters.status} onValueChange={updateStatus}>
						<SelectTrigger
							id={`${id}-status`}
							className={cn(
								"bg-[#1A1C1E]",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
								"focus:ring-[#D4B485]/40",
							)}
						>
							<SelectValue placeholder="选择状态" />
						</SelectTrigger>
						<SelectContent
							className={cn(
								"bg-[#1A1C1E]",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
							)}
						>
							<SelectItem value="ALL">全部状态</SelectItem>
							<SelectItem value="ACTIVE">已激活</SelectItem>
							<SelectItem value="INACTIVE">未激活</SelectItem>
							<SelectItem value="SUSPENDED">已暂停</SelectItem>
						</SelectContent>
					</Select>
				</div>

				<div className="w-[250px]">
					<Label
						htmlFor={`${id}-date`}
						className="mb-2 block text-[#D4B485]/60 text-sm"
					>
						注册日期
					</Label>
					<Popover>
						<PopoverTrigger asChild>
							<Button
								id={`${id}-date`}
								variant="outline"
								className={cn(
									"w-full justify-start text-left font-normal",
									"bg-[#1A1C1E]",
									"border-[#D4B485]/20",
									"text-[#D4B485]",
									"hover:bg-[#D4B485]/10",
									!date?.from &&
										!date?.to &&
										"text-[#D4B485]/40",
								)}
							>
								<CalendarIcon className="mr-2 h-4 w-4" />
								{date?.from ? (
									date?.to ? (
										<>
											{format(date.from, "yyyy/MM/dd")} -{" "}
											{format(date.to, "yyyy/MM/dd")}
										</>
									) : (
										format(date.from, "yyyy/MM/dd")
									)
								) : (
									"选择日期范围"
								)}
							</Button>
						</PopoverTrigger>
						<PopoverContent
							className={cn(
								"w-auto p-0",
								"bg-[#1A1C1E]",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
							)}
							align="start"
						>
							<JollyRangeCalendar
								className="bg-[#1A1C1E]"
								value={getAriaDateRange()}
								onChange={handleRangeChange}
							/>
						</PopoverContent>
					</Popover>
				</div>

				<div className="flex items-end">
					<Button
						variant="outline"
						className={cn(
							"h-10",
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"hover:bg-[#D4B485]/10",
						)}
						onClick={resetFilters}
					>
						重置筛选
					</Button>
				</div>
			</div>
		</div>
	);
}
