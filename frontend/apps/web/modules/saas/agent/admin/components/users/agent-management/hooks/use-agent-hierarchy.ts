import type { <PERSON><PERSON><PERSON> } from "@prisma/client";
import { logger } from "@repo/logs";
import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";

// 代理商层级节点
export interface AgentNode {
	id: string;
	name: string;
	role: AgentR<PERSON>;
	teamSize: number;
	children?: AgentNode[];
	parent?: AgentNode;
}

// API响应类型
interface ApiResponse<T> {
	code?: number;
	message?: string;
	data?: T;
	error?: string;
	hierarchy?: AgentNode;
}

export function useAgentHierarchy() {
	const [loading, setLoading] = useState(false);
	const [hierarchy, setHierarchy] = useState<AgentNode | null>(null);

	const fetchHierarchy = useCallback(async (rootId?: string) => {
		setLoading(true);
		try {
			const response =
				await apiClient.v1.agent.admin.user.agent.hierarchy.$get({
					query: { rootId },
				});

			const result = (await response.json()) as ApiResponse<unknown>;

			if (response.ok && result.hierarchy) {
				setHierarchy(result.hierarchy);
			} else {
				toast.error("获取代理商层级结构失败");
				logger.error("[Admin] 获取代理商层级结构失败", {
					error: result.message || result.error,
					rootId,
				});
			}
		} catch (error) {
			toast.error("获取代理商层级结构失败");
			logger.error("[Admin] 获取代理商层级结构失败", {
				error: error instanceof Error ? error.message : "未知错误",
				rootId,
			});
		} finally {
			setLoading(false);
		}
	}, []);

	return {
		loading,
		hierarchy,
		fetchHierarchy,
	};
}
