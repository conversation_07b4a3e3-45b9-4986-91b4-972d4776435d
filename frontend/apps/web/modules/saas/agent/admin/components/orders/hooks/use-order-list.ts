import { logger } from "@repo/logs";
import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";

// 订单列表项类型
export interface OrderListItem {
	id: string;
	type: "AGENT_PLAN" | "DIGITAL_HUMAN";
	status:
		| "PENDING"
		| "PAID"
		| "COMPLETED"
		| "FAILED"
		| "CANCELLED"
		| "REFUNDED";
	paymentStatus: "PENDING" | "SUCCESS" | "FAILED" | "REFUNDED";
	amount: number;
	quantity: number;
	createdAt: string;
	paidAt?: string;
	completedAt?: string;
	cancelledAt?: string;
	remark?: string;

	// 消费者信息
	consumer?: {
		id: string;
		name: string;
		phone: string;
		avatar?: string;
	};

	// 代理商信息
	agent?: {
		id: string;
		name: string;
		phone: string;
		avatar?: string;
		role: string;
	};

	// 套餐信息
	plan?: {
		id: string;
		name: string;
		level: string;
		computingPower: number;
		validityDays: number;
	};

	// 支付信息
	payment?: {
		method: string;
		transactionId?: string;
		paidAmount?: number;
	};
}

// 查询参数类型
export interface OrderListQuery {
	page?: number;
	pageSize?: number;
	searchTerm?: string;
	orderType?: "ALL" | "AGENT_PLAN" | "DIGITAL_HUMAN";
	orderStatus?:
		| "ALL"
		| "PENDING"
		| "PAID"
		| "COMPLETED"
		| "FAILED"
		| "CANCELLED"
		| "REFUNDED";
	paymentStatus?: "ALL" | "PENDING" | "SUCCESS" | "FAILED" | "REFUNDED";
	startDate?: string;
	endDate?: string;
	sortBy?: "createdAt" | "amount" | "paidAt";
	sortOrder?: "asc" | "desc";
}

// API响应类型
interface ApiResponse {
	code: number;
	message?: string;
	data?: {
		items: OrderListItem[];
		total: number;
		stats?: {
			totalAmount: number;
			totalOrders: number;
			successRate: number;
		};
	};
	error?: string;
}

export function useOrderList() {
	const [loading, setLoading] = useState(false);
	const [orders, setOrders] = useState<OrderListItem[]>([]);
	const [total, setTotal] = useState(0);
	const [stats, setStats] = useState<{
		totalAmount: number;
		totalOrders: number;
		successRate: number;
	} | null>(null);

	const fetchOrders = useCallback(async (query: OrderListQuery) => {
		setLoading(true);
		try {
			logger.info("[Admin] 开始获取订单列表", { query });

			const response = await apiClient.v1.agent.admin.orders.list.$get({
				query: {
					page: query.page || 1,
					pageSize: query.pageSize || 10,
					searchTerm: query.searchTerm,
					orderType: query.orderType,
					orderStatus: query.orderStatus,
					paymentStatus: query.paymentStatus,
					startDate: query.startDate,
					endDate: query.endDate,
					sortBy: query.sortBy || "createdAt",
					sortOrder: query.sortOrder || "desc",
				},
			});

			const result = (await response.json()) as ApiResponse;

			logger.info("[Admin] 订单列表响应", { result });

			if (response.ok && result.code === 200 && result.data) {
				setOrders(result.data.items);
				setTotal(result.data.total);
				setStats(result.data.stats || null);
			} else {
				const errorMsg =
					result.message || result.error || "获取订单列表失败";
				toast.error(errorMsg);
				logger.error("[Admin] 获取订单列表失败", {
					error: errorMsg,
					code: result.code,
				});
			}
		} catch (error) {
			const errorMsg =
				error instanceof Error ? error.message : "获取订单列表失败";
			toast.error(errorMsg);
			logger.error("[Admin] 获取订单列表异常", {
				error: errorMsg,
			});
		} finally {
			setLoading(false);
		}
	}, []);

	return {
		loading,
		orders,
		total,
		stats,
		fetchOrders,
	};
}
