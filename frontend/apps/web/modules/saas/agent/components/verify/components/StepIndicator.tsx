"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { CheckCircle2, type LucideIcon } from "lucide-react";

interface Step {
	icon: LucideIcon;
	title: string;
	description: string;
}

interface StepIndicatorProps {
	steps: Step[];
	currentStep: number;
}

export function StepIndicator({ steps, currentStep }: StepIndicatorProps) {
	return (
		<div className="relative">
			{/* 背景线条 */}
			<div
				className={cn(
					"absolute left-[calc(50%_+_10px)] right-[calc(50%_+_10px)] top-[42px]",
					"h-[2px]",
					"bg-[#1E2023]",
					"rounded-full",
					"transform -translate-x-1/2",
				)}
			/>

			{/* 发光进度条 */}
			<motion.div
				className={cn(
					"absolute left-[calc(0%_+_20px)] top-[42px]",
					"h-[2px]",
					"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
					"rounded-full",
				)}
				initial={{ width: "0%" }}
				animate={{
					width:
						currentStep === 1
							? "0%"
							: currentStep === 2
								? "45%"
								: "90%",
				}}
				transition={{
					duration: 0.8,
					ease: "easeInOut",
				}}
			/>

			{/* 步骤点 */}
			<div className="relative z-10 flex justify-between">
				{steps.map((step, index) => {
					const Icon = step.icon;
					const stepNumber = index + 1;
					const isCompleted = currentStep > stepNumber;
					const isCurrent = currentStep === stepNumber;

					return (
						<motion.div
							key={step.title}
							initial={{ opacity: 0, y: 20 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ delay: index * 0.2 }}
							className="flex flex-col items-center"
						>
							{/* 步骤圆点 */}
							<motion.div
								className={cn(
									"flex h-20 w-20 items-center justify-center rounded-2xl",
									"transition-all duration-500",
									"relative z-10",
									isCompleted
										? "bg-gradient-to-r from-[#D4B485] to-[#B08968] text-white"
										: isCurrent
											? "bg-[#D4B485] text-white ring-4 ring-[#D4B485]/20"
											: "bg-[#1E2023] text-[#D4B485]/40 ring-2 ring-[#D4B485]/20",
									"shadow-[0_4px_12px_-2px_rgba(212,180,133,0.2)]",
								)}
								initial={false}
								animate={{
									scale: isCurrent ? 1.1 : 1,
									transition: {
										duration: 0.3,
										ease: "easeOut",
									},
								}}
							>
								{isCompleted ? (
									<CheckCircle2 className="h-8 w-8" />
								) : (
									<Icon className="h-8 w-8" />
								)}
							</motion.div>

							{/* 步骤文字 */}
							<div className="mt-6 space-y-2 text-center">
								<div
									className={cn(
										"text-base font-medium",
										zywhFont.className,
										isCompleted || isCurrent
											? "text-[#D4B485]"
											: "text-[#D4B485]/40",
									)}
								>
									{step.title}
								</div>
								<div
									className={cn(
										"text-sm",
										isCompleted || isCurrent
											? "text-[#D4B485]/60"
											: "text-[#D4B485]/20",
									)}
								>
									{step.description}
								</div>
							</div>
						</motion.div>
					);
				})}
			</div>
		</div>
	);
}
