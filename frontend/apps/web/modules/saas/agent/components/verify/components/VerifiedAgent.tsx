"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { CheckCircle2 } from "lucide-react";

export function VerifiedAgent() {
	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			className="w-full"
		>
			{/* 标题区域 */}
			<motion.div
				initial={{ opacity: 0, x: -20 }}
				animate={{ opacity: 1, x: 0 }}
				transition={{ delay: 0.2 }}
				className="relative mt-8 mb-6"
			>
				<div className="flex items-center gap-4">
					<div
						className={cn(
							"flex h-14 w-14 shrink-0 items-center justify-center",
							"rounded-xl bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
							"ring-1 ring-[#D4B485]/20",
							"shadow-[0_0_0_8px_rgba(212,180,133,0.03)]",
							"transform-gpu transition-transform duration-300",
							"hover:scale-105 hover:shadow-[0_0_0_10px_rgba(212,180,133,0.05)]",
						)}
					>
						<CheckCircle2 className="h-7 w-7 text-[#D4B485]" />
					</div>
					<div className="space-y-1">
						<h2
							className={cn(
								"font-semibold text-3xl",
								zywhFont.className,
								"leading-none",
								"tracking-[0.05em]",
								"relative",
								"after:absolute after:inset-0",
								"after:bg-[radial-gradient(circle_at_50%_50%,rgba(212,180,133,0.15),transparent_70%)]",
								"after:blur-xl after:-z-10",
							)}
							style={{
								background:
									"linear-gradient(135deg, rgba(229,201,165,0.95) 0%, rgba(212,180,133,0.9) 50%, rgba(176,137,104,0.85) 100%)",
								WebkitBackgroundClip: "text",
								WebkitTextFillColor: "transparent",
								backgroundClip: "text",
								textShadow:
									"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.1)",
								filter: "contrast(1.1) brightness(1.05)",
							}}
						>
							实名认证
							<span className="ml-3 font-normal text-base text-[#D4B485]/80 tracking-wide">
								您已完成实名认证并成为代理商
							</span>
						</h2>
						<p className="text-sm leading-relaxed text-white/40 group-hover:text-white/60 transition-colors duration-300">
							您已通过实名认证，可以开始使用完整的代理商功能
						</p>
					</div>
				</div>
			</motion.div>

			{/* 主要内容区域 */}
			<motion.div
				initial={{ opacity: 0, y: 40 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.3 }}
				className="mt-6"
			>
				<div
					className={cn(
						"rounded-xl",
						"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
						"border border-[#D4B485]/20",
						"p-8",
						"shadow-[0_8px_32px_-4px_rgba(0,0,0,0.3)]",
						"backdrop-blur-xl",
					)}
				>
					<div
						className={cn(
							"flex items-start gap-3 p-4 rounded-lg",
							"bg-gradient-to-r from-emerald-500/10 to-emerald-600/10",
							"border border-emerald-500/20",
							"relative z-[1]",
						)}
					>
						<CheckCircle2 className="w-5 h-5 text-emerald-500 mt-0.5" />
						<div className="space-y-1 flex-1">
							<div className="text-sm font-medium text-emerald-500">
								您已是代理商
							</div>
							<div className="text-xs text-emerald-500/90">
								您已完成实名认证并成为代理商，可以开始使用完整功能。
							</div>
						</div>
					</div>
				</div>
			</motion.div>
		</motion.div>
	);
}
