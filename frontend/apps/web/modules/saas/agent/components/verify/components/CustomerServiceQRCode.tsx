"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Dialog, DialogContent, DialogTitle } from "@ui/components/dialog";
import { cn } from "@ui/lib";
import Image from "next/image";

interface CustomerServiceQRCodeProps {
	isOpen: boolean;
	onClose: () => void;
}

export function CustomerServiceQRCode({
	isOpen,
	onClose,
}: CustomerServiceQRCodeProps) {
	return (
		<Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
			<DialogContent
				className={cn(
					"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
					"border border-[#D4B485]/20",
					"p-0 sm:max-w-md",
					"shadow-[0_8px_32px_-4px_rgba(0,0,0,0.5)]",
				)}
			>
				<DialogTitle
					className={cn(
						"px-6 pt-6 pb-0 text-xl font-semibold",
						zywhFont.className,
						"relative",
					)}
					style={{
						background:
							"linear-gradient(135deg, rgba(229,201,165,0.95) 0%, rgba(212,180,133,0.9) 50%, rgba(176,137,104,0.85) 100%)",
						WebkitBackgroundClip: "text",
						WebkitTextFillColor: "transparent",
						backgroundClip: "text",
					}}
				>
					客服联系方式
				</DialogTitle>

				<div className="p-6 pt-3 space-y-4 text-center">
					<div
						className={cn(
							"w-48 h-48 mx-auto",
							"bg-white p-4 rounded-lg",
							"flex items-center justify-center",
							"border-4 border-[#D4B485]/30",
							"shadow-[0_4px_16px_rgba(212,180,133,0.2)]",
						)}
					>
						<div className="relative w-full h-full rounded">
							<Image
								src="https://eco.9000aigc.com/static/QRCode/%E5%AE%A2%E6%9C%8D%E4%BA%8C%E7%BB%B4%E7%A0%81.png"
								alt="客服二维码"
								fill
								unoptimized
								className="object-contain"
								onError={(e) => {
									const target = e.target as HTMLImageElement;
									target.style.display = "none";
									if (target.parentElement) {
										target.parentElement.innerHTML = `<div class="w-full h-full flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-[#D4B485]/80"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg></div>`;
									}
								}}
							/>
						</div>
					</div>

					<div className="space-y-3">
						<p
							className={cn(
								"text-base font-medium",
								"bg-gradient-to-r from-amber-500 via-amber-300 to-amber-500 bg-clip-text text-transparent",
							)}
						>
							扫描二维码联系客服
						</p>
						<p className="text-sm text-white/60">
							工作时间: 周一至周五 9:00-18:00
						</p>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
}
