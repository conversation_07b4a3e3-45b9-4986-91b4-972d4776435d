import { useSession } from "@saas/auth/hooks/use-session";
import { apiClient } from "@shared/lib/api-client";
import { useCallback, useEffect, useState } from "react";
import { toast } from "sonner";

// 实名认证请求参数
export interface VerifyParams {
	realName: string;
	idCardNumber: string;
}

// 实名认证响应数据
export interface VerifyResult {
	realName: string;
	idCardNumber: string;
	idCardVerified: boolean;
	verifiedAt: Date;
}

// 认证步骤响应数据
export interface VerifyStepResult {
	currentStep: 1 | 2;
	status: "NONE" | "PENDING" | "ACTIVE" | "DISABLED";
	agentId?: string;
	referralId?: string;
	expiresAt?: string;
	processorRole?: string;
	processNote?: string;
}

// API响应类型
interface ApiResponse<T> {
	code: number;
	message?: string;
	data?: T;
	error?: string;
}

export function useVerify() {
	const { reloadSession } = useSession();
	const [loading, setLoading] = useState(false);
	const [result, setResult] = useState<VerifyResult | null>(null);
	const [stepResult, setStepResult] = useState<VerifyStepResult | null>(null);
	const [stepLoading, setStepLoading] = useState(false);

	// 获取认证步骤
	const getStep = useCallback(async () => {
		setStepLoading(true);

		try {
			const response = await apiClient.v1.agent.verify.step.$get();
			// 解析响应内容
			const responseText = await response.text();

			let result: ApiResponse<VerifyStepResult>;
			try {
				result = JSON.parse(responseText);
			} catch (_parseError) {
				throw new Error("响应解析失败");
			}

			if (response.ok && result.code === 200) {
				if (!result.data) {
					toast.error("获取认证步骤失败: 响应数据格式不正确");
					return null;
				}

				setStepResult(result.data);
				return result.data;
			}

			const _errorCode = result.code || response.status;
			const errorMsg =
				result.message || result.error || response.statusText;

			toast.error(`获取认证步骤失败: ${errorMsg}`);
			return null;
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : String(error);
			const _errorStack =
				error instanceof Error ? error.stack : undefined;

			toast.error(`获取认证步骤失败: ${errorMessage}`);
			return null;
		} finally {
			setStepLoading(false);
		}
	}, []);

	// 实名认证
	const verify = useCallback(
		async (params: VerifyParams) => {
			setLoading(true);

			try {
				const response =
					await apiClient.v1.agent.verify.verify_by_tencent.$post({
						json: params,
					});

				// 解析响应内容
				const responseText = await response.text();

				let result: ApiResponse<VerifyResult>;
				try {
					result = JSON.parse(responseText);
				} catch (_parseError) {
					throw new Error("响应解析失败");
				}

				if (response.ok && result.code === 200) {
					if (!result.data) {
						toast.error("实名认证失败: 响应数据格式不正确");
						return null;
					}

					setResult(result.data);
					toast.success("实名认证成功");
					await reloadSession();
					return result.data;
				}

				const _errorCode = result.code || response.status;
				const errorMsg =
					result.message || result.error || response.statusText;

				toast.error(`实名认证失败: ${errorMsg}`);
				return null;
			} catch (error) {
				const errorMessage =
					error instanceof Error ? error.message : String(error);
				const _errorStack =
					error instanceof Error ? error.stack : undefined;

				toast.error(`实名认证失败: ${errorMessage}`);
				return null;
			} finally {
				setLoading(false);
			}
		},
		[reloadSession],
	);

	// 组件挂载时获取认证步骤
	useEffect(() => {
		getStep();
	}, [getStep]);

	return {
		loading,
		result,
		verify,
		stepLoading,
		stepResult,
		getStep,
	};
}
