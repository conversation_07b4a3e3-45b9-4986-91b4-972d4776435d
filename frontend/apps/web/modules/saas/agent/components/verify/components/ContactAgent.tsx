"use client";

import { But<PERSON> } from "@ui/components/button";
import { cn } from "@ui/lib";
import { ExternalLink, MessageCircle, Users } from "lucide-react";

interface ContactAgentProps {
	processNote?: string;
	onOpenQRCode: () => void;
	onNext: () => void;
}

export function ContactAgent({
	processNote,
	onOpenQRCode,
	onNext,
}: ContactAgentProps) {
	return (
		<div className="space-y-6 mt-10">
			{/* 状态提示 */}
			<div
				className={cn(
					"flex items-start gap-3 p-6 rounded-lg",
					"bg-gradient-to-r from-[#D4B485]/10 to-[#B08968]/10",
					"border border-[#D4B485]/20",
				)}
			>
				<MessageCircle className="w-6 h-6 text-[#D4B485] mt-0.5" />
				<div className="space-y-2 flex-1">
					<div className="text-base font-medium text-[#D4B485]">
						联系代理商或开始认证
					</div>
					<div className="text-sm text-[#D4B485]/90">
						成为代理商需要先完成实名认证并获得邀请。
						您可以先联系已加入的代理商好友获取邀请，或直接点击下方按钮开始实名认证。
						{processNote && (
							<div className="mt-2 text-[#D4B485]/80">
								处理备注: {processNote}
							</div>
						)}
					</div>
				</div>
			</div>

			{/* 代理商联系卡片 */}
			<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
				{/* 代理商联系方式 */}
				<div
					className={cn(
						"rounded-lg p-5",
						"bg-gradient-to-br from-white/5 to-white/3",
						"border border-white/10",
						"shadow-lg hover:shadow-xl transition-all duration-300",
						"flex flex-col items-center text-center",
						"hover:border-[#D4B485]/30 hover:bg-white/7",
					)}
				>
					<Users className="w-8 h-8 text-[#D4B485] mb-3" />
					<h3 className="text-base font-medium text-white mb-2">
						已认识代理商？
					</h3>
					<p className="text-sm text-white/60 mb-4">
						请联系您已认识的代理商好友，请他们通过系统邀请您加入
					</p>
					<div className="text-xs text-white/40 italic">
						代理商可在后台操作 → 邀请新代理
					</div>
				</div>

				{/* 客服咨询 */}
				<div
					className={cn(
						"rounded-lg p-5",
						"bg-gradient-to-br from-white/5 to-white/3",
						"border border-white/10",
						"shadow-lg hover:shadow-xl transition-all duration-300",
						"flex flex-col items-center text-center",
						"hover:border-[#D4B485]/30 hover:bg-white/7",
					)}
				>
					<MessageCircle className="w-8 h-8 text-[#D4B485] mb-3" />
					<h3 className="text-base font-medium text-white mb-2">
						没有代理商资源？
					</h3>
					<p className="text-sm text-white/60 mb-4">
						如果您没有认识的代理商，可以联系我们的客服获取帮助
					</p>
					<Button
						variant="outline"
						size="sm"
						className={cn(
							"border-[#D4B485]/30 text-[#D4B485]",
							"hover:bg-[#D4B485]/10 hover:text-[#D4B485]",
							"flex items-center gap-2",
						)}
						onClick={onOpenQRCode}
					>
						<MessageCircle className="w-4 h-4" />
						联系客服
						<ExternalLink className="w-3 h-3 ml-1 opacity-70" />
					</Button>
				</div>
			</div>

			{/* 新增：下一步按钮区域 */}
			<div className="pt-4 border-t border-[#D4B485]/10 flex justify-center">
				<Button
					className={cn(
						"h-10 px-6",
						"bg-gradient-to-r from-[#D4B485]/80 to-[#B08968]/80",
						"text-white font-medium",
						"border-none",
						"shadow-[0_6px_12px_-3px_rgba(212,180,133,0.25)]",
						"hover:shadow-[0_10px_16px_-3px_rgba(212,180,133,0.35)]",
						"transition-all duration-200",
						"flex items-center justify-center gap-2",
					)}
					onClick={onNext}
				>
					下一步：进行实名认证
				</Button>
			</div>
		</div>
	);
}
