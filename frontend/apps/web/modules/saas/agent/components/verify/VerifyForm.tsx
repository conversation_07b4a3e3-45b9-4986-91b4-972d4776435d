"use client";

import { useSession } from "@saas/auth/hooks/use-session";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { VerifyProcess } from "./components/VerifiedWaiting";
import { useVerify } from "./hooks/use-verify";

export function VerifyForm() {
	const { user, reloadSession } = useSession();
	const router = useRouter();
	const [realName, setRealName] = useState("");
	const [idCardNumber, setIdCardNumber] = useState("");
	const { loading, verify } = useVerify();

	// 判断用户状态
	const isVerified = user?.idCardVerified;
	const isFullyActiveAgent = user?.agentId && user?.agentStatus === "ACTIVE";

	// 如果已经是代理商，直接跳转到代理商仪表盘
	useEffect(() => {
		if (isFullyActiveAgent) {
			router.push("/app/agent");
		}
	}, [isFullyActiveAgent, router]);

	// 防止页面闪烁，如果已是激活的代理商，显示加载中状态
	if (isFullyActiveAgent) {
		return (
			<div className="w-full h-screen flex items-center justify-center">
				<div className="animate-pulse text-[#D4B485]">
					正在跳转到代理商系统...
				</div>
			</div>
		);
	}

	// 使用统一的VerifyProcess组件处理所有步骤
	return (
		<VerifyProcess
			isVerified={isVerified || false}
			realName={realName}
			idCardNumber={idCardNumber}
			loading={loading}
			onRealNameChange={setRealName}
			onIdCardNumberChange={setIdCardNumber}
			onVerifySubmit={verify}
			onSessionReload={reloadSession}
		/>
	);
}
