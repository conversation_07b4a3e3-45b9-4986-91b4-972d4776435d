"use client";

import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { cn } from "@ui/lib";
import { AlertCircle, Loader2 } from "lucide-react";
import { useId } from "react";

interface AuthenticationFormProps {
	realName: string;
	idCardNumber: string;
	loading: boolean;
	onRealNameChange: (value: string) => void;
	onIdCardNumberChange: (value: string) => void;
	onSubmit: () => void;
}

export function AuthenticationForm({
	realName,
	idCardNumber,
	loading,
	onRealNameChange,
	onIdCardNumberChange,
	onSubmit,
}: AuthenticationFormProps) {
	const id = useId();
	return (
		<div className="space-y-6 mt-10">
			{/* 提示信息 */}
			<div
				className={cn(
					"flex items-start gap-3 p-4 rounded-lg",
					"bg-gradient-to-r from-[#D4B485]/10 to-[#B08968]/10",
					"border border-[#D4B485]/20",
				)}
			>
				<AlertCircle className="w-5 h-5 text-[#D4B485] mt-0.5" />
				<div className="space-y-1 flex-1">
					<div className="text-sm font-medium text-[#D4B485]">
						实名认证说明
					</div>
					<div className="text-xs text-[#D4B485]/90">
						请填写真实的身份信息，认证通过后将无法修改。认证信息仅用于平台实名制管理，我们会严格保护您的隐私。
					</div>
				</div>
			</div>

			{/* 表单区域 */}
			<div className="space-y-4">
				{/* 真实姓名 */}
				<div className="space-y-2">
					<Label
						htmlFor={`${id}-realName`}
						className="text-sm text-[#D4B485]/70"
					>
						真实姓名
					</Label>
					<Input
						id={`${id}-realName`}
						value={realName}
						onChange={(e) => onRealNameChange(e.target.value)}
						placeholder="请输入您的真实姓名"
						className={cn(
							"h-11",
							"bg-[#1E2023]/50",
							"border-[#D4B485]/20",
							"text-[#E5C9A5]",
							"placeholder:text-[#D4B485]/40",
							"focus:border-[#D4B485]/40",
							"focus:ring-[#D4B485]/20",
							"disabled:opacity-50",
							"disabled:cursor-not-allowed",
						)}
						disabled={loading}
					/>
				</div>

				{/* 身份证号 */}
				<div className="space-y-2">
					<Label
						htmlFor={`${id}-idCardNumber`}
						className="text-sm text-[#D4B485]/70"
					>
						身份证号
					</Label>
					<Input
						id={`${id}-idCardNumber`}
						value={idCardNumber}
						onChange={(e) => onIdCardNumberChange(e.target.value)}
						placeholder="请输入您的身份证号码"
						className={cn(
							"h-11",
							"bg-[#1E2023]/50",
							"border-[#D4B485]/20",
							"text-[#E5C9A5]",
							"placeholder:text-[#D4B485]/40",
							"focus:border-[#D4B485]/40",
							"focus:ring-[#D4B485]/20",
							"disabled:opacity-50",
							"disabled:cursor-not-allowed",
						)}
						disabled={loading}
					/>
				</div>
			</div>

			{/* 提交按钮 */}
			<Button
				className={cn(
					"w-full h-11",
					"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
					"text-white font-medium",
					"border-none",
					"shadow-[0_8px_16px_-4px_rgba(212,180,133,0.3)]",
					"hover:shadow-[0_12px_20px_-4px_rgba(212,180,133,0.4)]",
					"transition-all duration-200",
					"disabled:opacity-50 disabled:cursor-not-allowed",
					"flex items-center justify-center gap-2",
				)}
				disabled={!realName || !idCardNumber || loading}
				onClick={onSubmit}
			>
				{loading ? (
					<>
						<Loader2 className="w-4 h-4 animate-spin" />
						提交中...
					</>
				) : (
					"提交认证"
				)}
			</Button>
		</div>
	);
}
