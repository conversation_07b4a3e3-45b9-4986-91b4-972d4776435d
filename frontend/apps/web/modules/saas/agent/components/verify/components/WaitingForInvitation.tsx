"use client";

import { But<PERSON> } from "@ui/components/button";
import { cn } from "@ui/lib";
import { Clock, MessageCircle } from "lucide-react";

interface WaitingForInvitationProps {
	processNote?: string;
	expiresAt?: string;
	onOpenQRCode: () => void;
}

export function WaitingForInvitation({
	processNote,
	expiresAt,
	onOpenQRCode,
}: WaitingForInvitationProps) {
	return (
		<div className="space-y-6 mt-10">
			{/* 状态提示 */}
			<div
				className={cn(
					"flex items-start gap-3 p-6 rounded-lg",
					"bg-gradient-to-r from-[#D4B485]/10 to-[#B08968]/10",
					"border border-[#D4B485]/20",
				)}
			>
				<Clock className="w-6 h-6 text-[#D4B485] mt-0.5" />
				<div className="space-y-2 flex-1">
					<div className="text-base font-medium text-[#D4B485]">
						等待邀请或审核
					</div>
					<div className="text-sm text-[#D4B485]/90">
						您已完成实名认证。如果您已联系代理商并收到邀请，请耐心等待审核。
						如果还未联系，请联系代理商好友发送邀请。
						审核或邀请通过后，您将成为正式代理商。
						<span className="block mt-1">
							如果没有认识的代理商，可以联系客服获取帮助。
						</span>
						{processNote && (
							<div className="mt-2 text-[#D4B485]/80">
								处理备注: {processNote}
							</div>
						)}
						{expiresAt && (
							<div className="mt-2 text-[#D4B485]/80">
								处理截止时间:{" "}
								{new Date(expiresAt).toLocaleString()}
							</div>
						)}
					</div>
				</div>
			</div>

			{/* 等待进度提示 */}
			<div className="flex items-center justify-center py-4">
				<div className="relative w-28 h-28 flex items-center justify-center">
					{/* 圆形进度背景 */}
					<div className="absolute inset-0 rounded-full border-4 border-[#D4B485]/20" />

					{/* 旋转动画 */}
					<div className="absolute inset-0 rounded-full border-4 border-transparent border-t-[#D4B485] animate-spin [animation-duration:3s]" />

					{/* 中心图标 */}
					<Clock className="w-10 h-10 text-[#D4B485]/80" />
				</div>
			</div>

			{/* 审核中说明 */}
			<div className="text-center text-white/60 text-sm">
				请耐心等待邀请审核。如果您还未获得邀请，请先联系代理商。
				<div className="mt-2">
					<Button
						variant="link"
						size="sm"
						className={cn(
							"text-[#D4B485]/80 hover:text-[#D4B485]",
							"font-medium",
						)}
						onClick={onOpenQRCode}
					>
						<MessageCircle className="w-4 h-4 mr-1" />
						遇到问题？联系客服
					</Button>
				</div>
			</div>
		</div>
	);
}
