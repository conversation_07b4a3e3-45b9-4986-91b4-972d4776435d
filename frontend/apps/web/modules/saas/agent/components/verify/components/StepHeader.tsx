"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { cn } from "@ui/lib";
import { Clock, FileCheck } from "lucide-react";

interface StepHeaderProps {
	isVerified: boolean;
	currentStep: number;
}

export function StepHeader({ isVerified, currentStep }: StepHeaderProps) {
	let subtitle = "";
	let description = "";

	switch (currentStep) {
		case 1:
			subtitle = "联系代理商或开始认证";
			description = "联系已认识的代理商或直接进行下一步实名认证";
			break;
		case 2:
			subtitle = "请完成实名认证";
			description = "请填写您的真实身份信息，我们将严格保护您的隐私安全";
			break;
		case 3:
			subtitle = "等待邀请或审核";
			description = isVerified
				? "您已通过实名认证，请等待现有代理商邀请您加入"
				: "实名认证已提交，请等待邀请或审核";
			break;
		default:
			subtitle = "正在处理...";
			description = "请稍候";
	}

	return (
		<div className="flex items-center gap-4">
			<div
				className={cn(
					"flex h-14 w-14 shrink-0 items-center justify-center",
					"rounded-xl bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
					"ring-1 ring-[#D4B485]/20",
					"shadow-[0_0_0_8px_rgba(212,180,133,0.03)]",
					"transform-gpu transition-transform duration-300",
					"hover:scale-105 hover:shadow-[0_0_0_10px_rgba(212,180,133,0.05)]",
				)}
			>
				{isVerified ? (
					<Clock className="h-7 w-7 text-[#D4B485]" />
				) : (
					<FileCheck className="h-7 w-7 text-[#D4B485]" />
				)}
			</div>
			<div className="space-y-1">
				<h2
					className={cn(
						"font-semibold text-3xl",
						zywhFont.className,
						"leading-none",
						"tracking-[0.05em]",
						"relative",
						"after:absolute after:inset-0",
						"after:bg-[radial-gradient(circle_at_50%_50%,rgba(212,180,133,0.15),transparent_70%)]",
						"after:blur-xl after:-z-10",
					)}
					style={{
						background:
							"linear-gradient(135deg, rgba(229,201,165,0.95) 0%, rgba(212,180,133,0.9) 50%, rgba(176,137,104,0.85) 100%)",
						WebkitBackgroundClip: "text",
						WebkitTextFillColor: "transparent",
						backgroundClip: "text",
						textShadow:
							"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.1)",
						filter: "contrast(1.1) brightness(1.05)",
					}}
				>
					代理商认证
					<span className="ml-3 font-normal text-base text-[#D4B485]/80 tracking-wide">
						{subtitle}
					</span>
				</h2>
				<p className="text-sm leading-relaxed text-white/40 group-hover:text-white/60 transition-colors duration-300">
					{description}
				</p>
			</div>
		</div>
	);
}
