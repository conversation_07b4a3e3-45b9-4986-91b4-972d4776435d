"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { AlertCircle, Loader2, UserPlus } from "lucide-react";
import { useId } from "react";

interface VerifyFormProps {
	realName: string;
	idCardNumber: string;
	loading: boolean;
	onRealNameChange: (value: string) => void;
	onIdCardNumberChange: (value: string) => void;
	onSubmit: () => void;
}

export function VerifyForm({
	realName,
	idCardNumber,
	loading,
	onRealNameChange,
	onIdCardNumberChange,
	onSubmit,
}: VerifyFormProps) {
	const id = useId();
	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			className="w-full"
		>
			{/* 标题区域 */}
			<motion.div
				initial={{ opacity: 0, x: -20 }}
				animate={{ opacity: 1, x: 0 }}
				transition={{ delay: 0.2 }}
				className="relative mt-8 mb-6"
			>
				<div className="flex items-center gap-4">
					<div
						className={cn(
							"flex h-14 w-14 shrink-0 items-center justify-center",
							"rounded-xl bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
							"ring-1 ring-[#D4B485]/20",
							"shadow-[0_0_0_8px_rgba(212,180,133,0.03)]",
							"transform-gpu transition-transform duration-300",
							"hover:scale-105 hover:shadow-[0_0_0_10px_rgba(212,180,133,0.05)]",
						)}
					>
						<UserPlus className="h-7 w-7 text-[#D4B485]" />
					</div>
					<div className="space-y-1">
						<h2
							className={cn(
								"font-semibold text-3xl",
								zywhFont.className,
								"leading-none",
								"tracking-[0.05em]",
								"relative",
								"after:absolute after:inset-0",
								"after:bg-[radial-gradient(circle_at_50%_50%,rgba(212,180,133,0.15),transparent_70%)]",
								"after:blur-xl after:-z-10",
							)}
							style={{
								background:
									"linear-gradient(135deg, rgba(229,201,165,0.95) 0%, rgba(212,180,133,0.9) 50%, rgba(176,137,104,0.85) 100%)",
								WebkitBackgroundClip: "text",
								WebkitTextFillColor: "transparent",
								backgroundClip: "text",
								textShadow:
									"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.1)",
								filter: "contrast(1.1) brightness(1.05)",
							}}
						>
							实名认证
							<span className="ml-3 font-normal text-base text-[#D4B485]/80 tracking-wide">
								完成认证即可使用完整功能
							</span>
						</h2>
						<p className="text-sm leading-relaxed text-white/40 group-hover:text-white/60 transition-colors duration-300">
							请填写您的真实身份信息，我们将严格保护您的隐私安全
						</p>
					</div>
				</div>
			</motion.div>

			{/* 主要内容区域 */}
			<motion.div
				initial={{ opacity: 0, y: 40 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.3 }}
				className="mt-6"
			>
				<div
					className={cn(
						"rounded-xl",
						"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
						"border border-[#D4B485]/20",
						"p-8",
						"shadow-[0_8px_32px_-4px_rgba(0,0,0,0.3)]",
						"backdrop-blur-xl",
					)}
				>
					<div className="space-y-6">
						{/* 提示信息 */}
						<div
							className={cn(
								"flex items-start gap-3 p-4 rounded-lg",
								"bg-gradient-to-r from-[#D4B485]/10 to-[#B08968]/10",
								"border border-[#D4B485]/20",
							)}
						>
							<AlertCircle className="w-5 h-5 text-[#D4B485] mt-0.5" />
							<div className="space-y-1 flex-1">
								<div className="text-sm font-medium text-[#D4B485]">
									实名认证说明
								</div>
								<div className="text-xs text-[#D4B485]/90">
									请填写真实的身份信息，认证通过后将无法修改。认证信息仅用于平台实名制管理，我们会严格保护您的隐私。
								</div>
							</div>
						</div>

						{/* 表单区域 */}
						<div className="space-y-4">
							{/* 真实姓名 */}
							<div className="space-y-2">
								<Label
									htmlFor={`${id}-realName`}
									className="text-sm text-[#D4B485]/70"
								>
									真实姓名
								</Label>
								<Input
									id={`${id}-realName`}
									value={realName}
									onChange={(e) =>
										onRealNameChange(e.target.value)
									}
									placeholder="请输入您的真实姓名"
									className={cn(
										"h-11",
										"bg-[#1E2023]/50",
										"border-[#D4B485]/20",
										"text-[#E5C9A5]",
										"placeholder:text-[#D4B485]/40",
										"focus:border-[#D4B485]/40",
										"focus:ring-[#D4B485]/20",
										"disabled:opacity-50",
										"disabled:cursor-not-allowed",
									)}
									disabled={loading}
								/>
							</div>

							{/* 身份证号 */}
							<div className="space-y-2">
								<Label
									htmlFor={`${id}-idCardNumber`}
									className="text-sm text-[#D4B485]/70"
								>
									身份证号
								</Label>
								<Input
									id={`${id}-idCardNumber`}
									value={idCardNumber}
									onChange={(e) =>
										onIdCardNumberChange(e.target.value)
									}
									placeholder="请输入您的身份证号码"
									className={cn(
										"h-11",
										"bg-[#1E2023]/50",
										"border-[#D4B485]/20",
										"text-[#E5C9A5]",
										"placeholder:text-[#D4B485]/40",
										"focus:border-[#D4B485]/40",
										"focus:ring-[#D4B485]/20",
										"disabled:opacity-50",
										"disabled:cursor-not-allowed",
									)}
									disabled={loading}
								/>
							</div>
						</div>

						{/* 提交按钮 */}
						<Button
							className={cn(
								"w-full h-11",
								"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
								"text-white font-medium",
								"border-none",
								"shadow-[0_8px_16px_-4px_rgba(212,180,133,0.3)]",
								"hover:shadow-[0_12px_20px_-4px_rgba(212,180,133,0.4)]",
								"transition-all duration-200",
								"disabled:opacity-50 disabled:cursor-not-allowed",
								"flex items-center justify-center gap-2",
							)}
							disabled={!realName || !idCardNumber || loading}
							onClick={onSubmit}
						>
							{loading ? (
								<>
									<Loader2 className="w-4 h-4 animate-spin" />
									提交中...
								</>
							) : (
								"提交认证"
							)}
						</Button>
					</div>
				</div>
			</motion.div>
		</motion.div>
	);
}
