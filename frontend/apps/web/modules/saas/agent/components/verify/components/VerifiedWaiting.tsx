"use client";

import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { FileCheck, Loader2, MessageCircle, UserPlus } from "lucide-react";
import { useEffect, useState } from "react";
import {
	useVerify,
	type VerifyParams,
	type VerifyResult,
} from "../hooks/use-verify";

import { AuthenticationForm } from "./AuthenticationForm";
import { ContactAgent } from "./ContactAgent";
import { CustomerServiceQRCode } from "./CustomerServiceQRCode";
import { StepHeader } from "./StepHeader";
import { StepIndicator } from "./StepIndicator";
import { WaitingForInvitation } from "./WaitingForInvitation";

// 重新定义前端步骤
const FRONTEND_STEPS = [
	{
		icon: MessageCircle,
		title: "联系代理商",
		description: "联系已加入的代理商好友",
	},
	{
		icon: FileCheck,
		title: "实名认证",
		description: "填写真实身份信息",
	},
	{
		icon: UserPlus,
		title: "等待邀请/审核",
		description: "等待邀请或审核通过",
	},
];

interface VerifyProcessProps {
	isVerified: boolean; // 来自 session 的 user.idCardVerified
	realName: string;
	idCardNumber: string;
	loading: boolean; // 实名认证 API 调用状态
	onRealNameChange: (value: string) => void;
	onIdCardNumberChange: (value: string) => void;
	onVerifySubmit: (params: VerifyParams) => Promise<VerifyResult | null>; // 接收原始 verify 函数
	onSessionReload: () => Promise<void>; // 接收 reloadSession 函数
}

export function VerifyProcess({
	isVerified,
	realName,
	idCardNumber,
	loading,
	onRealNameChange,
	onIdCardNumberChange,
	onVerifySubmit,
	onSessionReload,
}: VerifyProcessProps) {
	const { stepResult, stepLoading } = useVerify();
	const [showQRCode, setShowQRCode] = useState(false);
	// 强制跳转到实名认证表单的状态
	const [forceShowAuthForm, setForceShowAuthForm] = useState(false);
	// 当前前端显示的步骤 (1, 2, or 3)
	const [currentDisplayStep, setCurrentDisplayStep] = useState<number | null>(
		null,
	);

	// 计算当前应该显示的步骤
	useEffect(() => {
		// 正在加载步骤信息时，不计算
		if (stepLoading) {
			setCurrentDisplayStep(null); // 显示加载状态
			return;
		}
		// 没有获取到步骤信息时，也不计算（可能出错）
		if (!stepResult) {
			setCurrentDisplayStep(null); // 或显示错误状态
			return;
		}

		let step: number;

		if (isVerified) {
			// 如果已实名认证
			if (
				stepResult.status === "PENDING" ||
				stepResult.status === "NONE"
			) {
				// 代理状态是 PENDING 或 NONE (需要邀请/等待) -> 显示第 3 步
				step = 3;
			} else {
				// 其他状态（理论上应该是 ACTIVE 或 DISABLED，已被外部逻辑处理）
				// 为保险起见，也定位到第 3 步
				step = 3;
			}
		} else {
			// 如果未实名认证
			if (stepResult.status === "PENDING") {
				// 代理状态是 PENDING (有代理资格但未实名) -> 直接显示第 2 步
				step = 2;
			} else if (stepResult.status === "NONE") {
				// 代理状态是 NONE (无代理资格)
				if (forceShowAuthForm) {
					// 如果用户点击了"下一步" -> 显示第 2 步
					step = 2;
				} else {
					// 默认 -> 显示第 1 步
					step = 1;
				}
			} else {
				// 其他状态 (理论上不应出现) -> 默认显示第 1 步
				step = 1;
			}
		}
		setCurrentDisplayStep(step);
	}, [stepLoading, stepResult, isVerified, forceShowAuthForm]);

	// 点击"下一步"从步骤1到步骤2
	const handleGoToAuth = () => {
		setForceShowAuthForm(true);
	};

	// 重写 handleAuthenticationSubmit
	const handleAuthenticationSubmit = async () => {
		// 直接调用从 VerifyForm 传来的 verify 函数
		const result = await onVerifySubmit({ realName, idCardNumber });
		// 如果 verify 成功 (返回了数据)
		if (result) {
			// 则调用从 VerifyForm 传来的 reloadSession 函数
			await onSessionReload();
			// Session 刷新后，isVerified 会更新，useEffect 会自动计算并设置 currentDisplayStep 为 3
		}
	};

	// --- 渲染逻辑 ---
	if (currentDisplayStep === null) {
		// 加载状态或错误状态
		return (
			<div className="w-full h-64 flex items-center justify-center">
				<Loader2 className="w-8 h-8 text-[#D4B485] animate-spin" />
			</div>
		);
	}

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			className="w-full"
		>
			{/* 标题区域 */}
			<motion.div
				initial={{ opacity: 0, x: -20 }}
				animate={{ opacity: 1, x: 0 }}
				transition={{ delay: 0.2 }}
				className="relative mt-12 mb-6"
			>
				{/* 传递 currentDisplayStep 给 StepHeader */}
				<StepHeader
					isVerified={isVerified} // isVerified 仍然可以用于图标或其他逻辑
					currentStep={currentDisplayStep} // 传递当前步骤
				/>
			</motion.div>

			{/* 主要内容区域 */}
			<motion.div
				initial={{ opacity: 0, y: 40 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.3 }}
				className="mt-10"
			>
				<div
					className={cn(
						"rounded-xl",
						"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
						"border border-[#D4B485]/20",
						"p-8",
						"shadow-[0_8px_32px_-4px_rgba(0,0,0,0.3)]",
						"backdrop-blur-xl",
					)}
				>
					<div className="space-y-8">
						{/* 步骤指示器 - 使用新的步骤定义 */}
						<StepIndicator
							steps={FRONTEND_STEPS}
							currentStep={currentDisplayStep}
						/>

						{/* 根据当前显示的步骤渲染不同内容 */}
						{currentDisplayStep === 1 && (
							<ContactAgent
								processNote={stepResult?.processNote}
								onOpenQRCode={() => setShowQRCode(true)}
								onNext={handleGoToAuth}
							/>
						)}

						{currentDisplayStep === 2 && (
							<AuthenticationForm
								realName={realName}
								idCardNumber={idCardNumber}
								loading={loading}
								onRealNameChange={onRealNameChange}
								onIdCardNumberChange={onIdCardNumberChange}
								onSubmit={handleAuthenticationSubmit}
							/>
						)}

						{currentDisplayStep === 3 && (
							<WaitingForInvitation
								processNote={stepResult?.processNote}
								expiresAt={stepResult?.expiresAt}
								onOpenQRCode={() => setShowQRCode(true)}
							/>
						)}
					</div>
				</div>
			</motion.div>

			{/* 客服二维码模态框 */}
			<CustomerServiceQRCode
				isOpen={showQRCode}
				onClose={() => setShowQRCode(false)}
			/>
		</motion.div>
	);
}
