"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Button } from "@ui/components/button";
import {
	Select,
	SelectContent,
	SelectGroup,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { cn } from "@ui/lib";
import { Calendar, Download } from "lucide-react";
import { useState } from "react";

interface HeaderProps {
	title: string;
	subtitle?: string;
	onTimeRangeChange?: (range: string) => void;
	onExport?: () => void;
}

export function Header({
	title,
	subtitle,
	onTimeRangeChange,
	onExport,
}: HeaderProps) {
	const [timeRange, setTimeRange] = useState("month");

	const handleTimeRangeChange = (value: string) => {
		setTimeRange(value);
		onTimeRangeChange?.(value);
	};

	// 根据选择的时间返回日期范围文本
	const getTimeRangeText = () => {
		const now = new Date();
		const currentYear = now.getFullYear();
		const currentMonth = now.getMonth() + 1;

		switch (timeRange) {
			case "today": {
				return `${currentYear}-${currentMonth.toString().padStart(2, "0")}-${now.getDate().toString().padStart(2, "0")}`;
			}
			case "week": {
				return "本周";
			}
			case "month": {
				return `${currentYear}-${currentMonth.toString().padStart(2, "0")}`;
			}
			case "quarter": {
				const quarter = Math.floor((currentMonth - 1) / 3) + 1;
				return `${currentYear}年第${quarter}季度`;
			}
			case "year": {
				return `${currentYear}年`;
			}
			default: {
				return `${currentYear}-${currentMonth.toString().padStart(2, "0")}`;
			}
		}
	};

	return (
		<div className="flex flex-col gap-2 w-full">
			<div className="flex items-center justify-between">
				<h1
					className={cn("text-2xl font-bold", zywhFont.className)}
					style={{
						background:
							"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
						WebkitBackgroundClip: "text",
						WebkitTextFillColor: "transparent",
						backgroundClip: "text",
						textShadow:
							"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
					}}
				>
					{title}
				</h1>
				<div className="flex items-center gap-4">
					<div className="flex items-center gap-2">
						<Calendar className="h-4 w-4 text-[#D4B485]/60" />
						<span className="text-[#D4B485]/60">
							{getTimeRangeText()}
						</span>
					</div>
					<Button
						variant="outline"
						className={cn(
							"gap-2",
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"hover:bg-[#D4B485]/10",
						)}
						onClick={onExport}
					>
						<Download className="h-4 w-4" />
						<span>导出报表</span>
					</Button>
				</div>
			</div>

			{subtitle && (
				<p className="text-[#D4B485]/60 text-sm">{subtitle}</p>
			)}

			<div className="flex items-center justify-end mt-4">
				<Select value={timeRange} onValueChange={handleTimeRangeChange}>
					<SelectTrigger
						className={cn(
							"w-[160px]",
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"hover:bg-[#D4B485]/10",
						)}
					>
						<SelectValue placeholder="选择时间范围" />
					</SelectTrigger>
					<SelectContent
						className={cn("bg-[#1E2023]", "border-[#D4B485]/20")}
					>
						<SelectGroup>
							<SelectItem value="today">今日</SelectItem>
							<SelectItem value="week">本周</SelectItem>
							<SelectItem value="month">本月</SelectItem>
							<SelectItem value="quarter">本季度</SelectItem>
							<SelectItem value="year">本年度</SelectItem>
						</SelectGroup>
					</SelectContent>
				</Select>
			</div>
		</div>
	);
}
