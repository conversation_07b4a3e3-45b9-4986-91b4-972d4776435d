"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Card } from "@ui/components/card";
import { cn } from "@ui/lib";
import type { TooltipProps } from "recharts";
import {
	Area,
	AreaChart,
	CartesianGrid,
	Legend,
	ResponsiveContainer,
	Tooltip,
	XAxis,
	YAxis,
} from "recharts";

export interface RevenueDataPoint {
	date: string;
	totalRevenue: number;
	agentFeeRevenue: number;
	quotaMarginRevenue: number;
	planMarginRevenue: number;
}

interface RevenueTrendChartProps {
	data: RevenueDataPoint[];
	title?: string;
	height?: number;
	formatter?: (value: number) => string;
}

// 自定义Tooltip和Legend的Props类型
type CustomTooltipProps = TooltipProps<number, string> & {
	payload?: Array<{
		name: string;
		value: number;
		color: string;
		dataKey: string;
	}>;
	label?: string;
	formatter?: (value: number) => string;
};

type CustomLegendProps = {
	payload?: Array<{
		value: string;
		color: string;
		type?: string;
		id?: string;
	}>;
};

const CustomTooltip = ({
	active,
	payload,
	label,
	formatter,
}: CustomTooltipProps) => {
	if (active && payload && payload.length) {
		// 按数值大小排序
		const sortedPayload = [...payload].sort(
			(a, b) => (b.value || 0) - (a.value || 0),
		);

		return (
			<div className="bg-[#0F1114]/95 backdrop-blur-sm border border-[#D4B485]/30 p-4 rounded-lg shadow-2xl min-w-[200px]">
				<p className="text-[#D4B485] font-semibold text-sm mb-3 border-b border-[#D4B485]/20 pb-2">
					📊 {label}
				</p>
				{sortedPayload.map((entry, index) => (
					<div
						key={`tooltip-${entry.dataKey}-${index}`}
						className="flex items-center justify-between gap-6 mb-2 last:mb-0"
					>
						<div className="flex items-center gap-2">
							<div
								className="w-3 h-3 rounded-full shadow-lg"
								style={{
									backgroundColor: entry.color,
									boxShadow: `0 0 8px ${entry.color}40`,
								}}
							/>
							<span className="text-xs text-[#D4B485]/90 font-medium">
								{entry.name}
							</span>
						</div>
						<span className="text-xs text-white">
							{/* Apply formatter from props */}
							{formatter
								? formatter(entry.value || 0)
								: Number(entry.value || 0).toLocaleString()}
						</span>
					</div>
				))}
			</div>
		);
	}
	return null;
};

const CustomLegend = ({ payload }: CustomLegendProps) => {
	if (!payload) {
		return null;
	}

	return (
		<ul className="flex flex-wrap gap-6 justify-center mb-4">
			{payload.map((entry) => (
				<li
					key={`legend-${entry.value}`}
					className="flex items-center gap-1"
				>
					<div
						className="w-3 h-3 rounded-full"
						style={{ backgroundColor: entry.color }}
					/>
					<span className="text-xs text-[#D4B485]/80">
						{entry.value}
					</span>
				</li>
			))}
		</ul>
	);
};

/**
 * 收入趋势分析图表 - 高级版本
 */
export function RevenueTrendChart({
	data,
	title = "收入趋势分析",
	height = 400,
	formatter = (value) => `¥${value.toLocaleString()}`,
}: RevenueTrendChartProps) {
	// 计算趋势指标
	const totalRevenue = data.reduce((sum, item) => sum + item.totalRevenue, 0);
	const _avgRevenue = totalRevenue / data.length;
	const _maxRevenue = Math.max(...data.map((item) => item.totalRevenue));

	// 计算增长率
	const _growthRate =
		data.length > 1
			? ((data[data.length - 1].totalRevenue - data[0].totalRevenue) /
					data[0].totalRevenue) *
				100
			: 0;

	// 高级配色方案
	const _colorScheme = {
		totalRevenue: {
			stroke: "#FFD700",
			fill: "url(#totalRevenueGradient)",
		},
		agentFeeRevenue: {
			stroke: "#00D4AA",
			fill: "url(#agentFeeGradient)",
		},
		quotaMarginRevenue: {
			stroke: "#4F93FF",
			fill: "url(#quotaMarginGradient)",
		},
		planMarginRevenue: {
			stroke: "#FF6B9D",
			fill: "url(#planMarginGradient)",
		},
	};

	return (
		<Card
			className={cn(
				"p-6",
				"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
				"border border-[#D4B485]/20",
				"group",
				"hover:border-[#D4B485]/40",
				"transition-all duration-500",
				"relative overflow-hidden",
			)}
		>
			{/* 简化背景装饰 */}
			<div className="absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(212,180,133,0.08),transparent_50%)]" />

			<div className="relative">
				<h2
					className={cn(
						"text-lg font-semibold mb-6",
						zywhFont.className,
					)}
					style={{
						background:
							"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
						WebkitBackgroundClip: "text",
						WebkitTextFillColor: "transparent",
						backgroundClip: "text",
						textShadow:
							"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
					}}
				>
					{title}
				</h2>

				<ResponsiveContainer width="100%" height={height}>
					<AreaChart
						data={data}
						margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
					>
						<CartesianGrid strokeDasharray="3 3" stroke="#2A2D31" />
						<XAxis
							dataKey="date"
							tick={{ fill: "#D4B485", fontSize: 12 }}
							tickLine={{ stroke: "#D4B485" }}
							axisLine={{ stroke: "#2A2D31" }}
						/>
						<YAxis
							tick={{ fill: "#D4B485", fontSize: 12 }}
							tickFormatter={(value) => formatter(value)}
							tickLine={{ stroke: "#D4B485" }}
							axisLine={{ stroke: "#2A2D31" }}
							width={80}
						/>
						<Tooltip
							content={<CustomTooltip formatter={formatter} />}
						/>
						<Legend content={<CustomLegend />} />
						<Area
							type="monotone"
							dataKey="totalRevenue"
							stackId="1"
							stroke="#D4B485"
							fill="#D4B485"
							fillOpacity={0.5}
							name="总收入"
						/>
						<Area
							type="monotone"
							dataKey="agentFeeRevenue"
							stackId="2"
							stroke="#26A69A"
							fill="#26A69A"
							fillOpacity={0.5}
							name="渠道合作收益"
						/>
						<Area
							type="monotone"
							dataKey="quotaMarginRevenue"
							stackId="3"
							stroke="#42A5F5"
							fill="#42A5F5"
							fillOpacity={0.5}
							name="配额分成收益"
						/>
						<Area
							type="monotone"
							dataKey="planMarginRevenue"
							stackId="4"
							stroke="#EF5350"
							fill="#EF5350"
							fillOpacity={0.5}
							name="方案销售收益"
						/>
					</AreaChart>
				</ResponsiveContainer>
			</div>
		</Card>
	);
}
