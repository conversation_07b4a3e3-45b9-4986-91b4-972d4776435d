import { AgentRole } from "@prisma/client";
import {
	type PerformanceStatsResponse,
	useRevenueStats,
} from "./useRevenueStats";

export interface GrowthFundTier {
	tier: number;
	amountPerHour: number;
	totalAmount: number;
}

export interface BusinessTotals {
	totalRevenue: number;
	totalQuotaAllocated: number;
	totalSubAgents: number;
	totalCustomers: number;
	totalPlanSales: number;
}

interface UseGrowthFundProps {
	agentId?: string;
	role?: AgentRole;
	timeRange: string;
}

/**
 * 获取交付收益和业务总数据
 */
export function useGrowthFund({
	agentId,
	role,
	timeRange,
}: UseGrowthFundProps) {
	const statsQuery = useRevenueStats({ agentId, role, timeRange });

	return {
		...statsQuery,
		data: statsQuery.data
			? {
					tiers: transformToGrowthFundData(
						statsQuery.data.rawData,
						role,
					),
					businessTotals: extractBusinessTotals(
						statsQuery.data.rawData,
					),
				}
			: { tiers: [], businessTotals: null },
	};
}

/**
 * 提取业务总数据
 */
function extractBusinessTotals(
	apiData: PerformanceStatsResponse,
): BusinessTotals {
	return {
		totalRevenue: apiData.totals.totalRevenue,
		totalQuotaAllocated: apiData.totals.totalQuotaAllocated,
		totalSubAgents: apiData.totals.totalSubAgents,
		totalCustomers: apiData.totals.totalCustomers,
		totalPlanSales: apiData.totals.totalPlanSales,
	};
}

/**
 * 将API数据转换为交付收益数据格式
 */
function transformToGrowthFundData(
	apiData: PerformanceStatsResponse,
	role: AgentRole = AgentRole.PARTNER,
): GrowthFundTier[] {
	// 计算计划销售总收入
	const planMarginTotal = apiData.trends.planMarginRevenue.reduce(
		(sum, point) => sum + point.value,
		0,
	);

	// 根据角色级别确定交付收益档位数
	let tierCount = 1;
	let hourlyRateBase = 100; // 基础每小时收益（分）

	switch (role) {
		case AgentRole.SALES:
			tierCount = 1;
			hourlyRateBase = 50;
			break;
		case AgentRole.PARTNER:
			tierCount = 2;
			hourlyRateBase = 100;
			break;
		case AgentRole.DIRECTOR:
			tierCount = 3;
			hourlyRateBase = 200;
			break;
		case AgentRole.BRANCH:
			tierCount = 5;
			hourlyRateBase = 500;
			break;
		default:
			tierCount = 2;
			hourlyRateBase = 100;
	}

	// 基于计划销售收入计算交付收益档位
	const totalGrowthFund = Math.floor(planMarginTotal * 0.1); // 假设交付收益是计划销售的10%

	// 构建档位数据
	const tiers: GrowthFundTier[] = [];
	const avgPerTier = Math.floor(totalGrowthFund / tierCount);

	for (let i = 0; i < tierCount; i++) {
		const tierMultiplier = (i + 1) * 0.8; // 每级递增80%
		const tierAmount = Math.floor(avgPerTier * tierMultiplier);

		tiers.push({
			tier: i + 1,
			amountPerHour: Math.floor(hourlyRateBase * tierMultiplier),
			totalAmount: tierAmount,
		});
	}

	// 如果没有收益数据，返回默认档位
	if (totalGrowthFund === 0) {
		return [
			{
				tier: 1,
				amountPerHour: hourlyRateBase,
				totalAmount: 0,
			},
		];
	}

	return tiers;
}
