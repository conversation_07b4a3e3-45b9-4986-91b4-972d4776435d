"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Card } from "@ui/components/card";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { cn } from "@ui/lib";
import {
	DollarSignIcon,
	InfoIcon,
	PieChartIcon,
	TargetIcon,
	TrendingUpIcon,
	UsersIcon,
} from "lucide-react";

// 业务总数据接口
export interface BusinessTotals {
	totalRevenue: number;
	totalQuotaAllocated: number;
	totalSubAgents: number;
	totalCustomers: number;
	totalPlanSales: number;
}

interface GrowthFundCardProps {
	businessTotals?: BusinessTotals;
	title?: string;
	formatter?: (value: number) => string;
}

/**
 * 业务数据总览卡片组件 - 展示关键业务指标
 */
export function GrowthFundCard({
	businessTotals,
	title = "业务数据总览",
	formatter = (value) => `¥${value.toLocaleString()}`,
}: GrowthFundCardProps) {
	// 业务指标配置
	const businessMetrics = businessTotals
		? [
				{
					id: "revenue",
					icon: DollarSignIcon,
					label: "累计总收入",
					value: businessTotals.totalRevenue,
					format: formatter,
					color: "#D4B485",
					bgColor: "from-amber-500/10 to-amber-600/5",
					borderColor: "border-amber-500/20",
				},
				{
					id: "quota",
					icon: TargetIcon,
					label: "名额分配",
					value: businessTotals.totalQuotaAllocated,
					format: (val: number) => `${val.toLocaleString()} 个`,
					color: "#00D4AA",
					bgColor: "from-emerald-500/10 to-emerald-600/5",
					borderColor: "border-emerald-500/20",
				},
				{
					id: "agents",
					icon: UsersIcon,
					label: "下级代理",
					value: businessTotals.totalSubAgents,
					format: (val: number) => `${val.toLocaleString()} 人`,
					color: "#4F93FF",
					bgColor: "from-blue-500/10 to-blue-600/5",
					borderColor: "border-blue-500/20",
				},
				{
					id: "customers",
					icon: PieChartIcon,
					label: "客户数量",
					value: businessTotals.totalCustomers,
					format: (val: number) => `${val.toLocaleString()} 人`,
					color: "#FF6B9D",
					bgColor: "from-pink-500/10 to-pink-600/5",
					borderColor: "border-pink-500/20",
				},
				{
					id: "plans",
					icon: TrendingUpIcon,
					label: "方案销售",
					value: businessTotals.totalPlanSales,
					format: formatter,
					color: "#9F7AEA",
					bgColor: "from-purple-500/10 to-purple-600/5",
					borderColor: "border-purple-500/20",
				},
			]
		: [];

	return (
		<Card
			className={cn(
				"p-6",
				"bg-gradient-to-br from-[#1E2023] via-[#1A1C1E] to-[#161719]",
				"border border-[#D4B485]/30",
				"group",
				"hover:border-[#D4B485]/50",
				"transition-all duration-700",
				"relative overflow-hidden",
				"shadow-xl",
			)}
		>
			{/* 简化背景装饰 */}
			<div className="absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(212,180,133,0.08),transparent_50%)]" />

			<div className="relative space-y-6">
				<div className="flex items-center gap-3">
					<h2
						className={cn("text-xl font-bold", zywhFont.className)}
						style={{
							background:
								"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
							WebkitBackgroundClip: "text",
							WebkitTextFillColor: "transparent",
							backgroundClip: "text",
							textShadow:
								"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
						}}
					>
						{title}
					</h2>

					<TooltipProvider>
						<Tooltip>
							<TooltipTrigger>
								<InfoIcon className="h-5 w-5 text-[#D4B485]/60 hover:text-[#D4B485] transition-colors" />
							</TooltipTrigger>
							<TooltipContent
								side="right"
								className="max-w-[300px] bg-[#1E2023] border-[#D4B485]/20 text-sm"
							>
								<p>
									实时业务数据总览，包含收入、名额、代理、客户等关键指标
								</p>
								<p className="mt-1">
									数据每日更新，帮助您掌握业务全貌
								</p>
							</TooltipContent>
						</Tooltip>
					</TooltipProvider>
				</div>

				{/* 业务指标网格 */}
				{businessTotals && (
					<div className="space-y-4">
						<p className="text-sm text-[#D4B485]/70 font-medium">
							📈 核心业务指标
						</p>

						<div className="grid grid-cols-1 gap-3">
							{businessMetrics.map((metric, index) => {
								const Icon = metric.icon;
								return (
									<div
										key={metric.id}
										className={cn(
											"bg-gradient-to-r",
											metric.bgColor,
											"border",
											metric.borderColor,
											"rounded-lg p-4",
											"hover:shadow-lg transition-all duration-300",
											"group/metric cursor-pointer",
											"hover:scale-[1.02]",
										)}
										style={{
											animationDelay: `${index * 0.1}s`,
										}}
									>
										<div className="flex items-center justify-between">
											<div className="flex items-center gap-3">
												<div
													className="p-2 rounded-lg shadow-lg"
													style={{
														backgroundColor: `${metric.color}20`,
														border: `1px solid ${metric.color}30`,
													}}
												>
													<Icon
														className="h-4 w-4"
														style={{
															color: metric.color,
														}}
													/>
												</div>
												<div>
													<p className="text-xs text-[#D4B485]/60 mb-1">
														{metric.label}
													</p>
													<p
														className="text-lg font-bold"
														style={{
															color: metric.color,
														}}
													>
														{metric.format(
															metric.value,
														)}
													</p>
												</div>
											</div>

											{/* 增长指示器 */}
											<div className="opacity-0 group-hover/metric:opacity-100 transition-opacity duration-300">
												<TrendingUpIcon
													className="h-4 w-4"
													style={{
														color: metric.color,
													}}
												/>
											</div>
										</div>
									</div>
								);
							})}
						</div>
					</div>
				)}

				{/* 如果没有业务数据 */}
				{!businessTotals && (
					<div className="text-center py-8">
						<p className="text-[#D4B485]/60">暂无业务数据</p>
						<p className="text-xs text-[#D4B485]/40 mt-2">
							数据正在加载中，请稍后刷新
						</p>
					</div>
				)}
			</div>
		</Card>
	);
}
