import type { AgentR<PERSON> } from "@prisma/client";
import { apiClient } from "@shared/lib/api-client";
import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";

// API响应数据类型定义
export interface TrendDataPoint {
	date: string; // YYYY-MM-DD格式
	value: number;
}

export interface PerformanceStatsResponse {
	trends: {
		periodRevenue: TrendDataPoint[];
		agentFeeRevenue: TrendDataPoint[];
		quotaMarginRevenue: TrendDataPoint[];
		planMarginRevenue: TrendDataPoint[];
	};
	totals: {
		totalRevenue: number;
		totalQuotaAllocated: number;
		totalSubAgents: number;
		totalCustomers: number;
		totalPlanSales: number;
	};
	timeRange: {
		startTime: string;
		endTime: string;
		days: number;
	};
}

interface UseRevenueStatsProps {
	agentId?: string;
	role?: AgentRole;
	timeRange: string;
	select?: (data: any) => any;
}

// 计算时间范围对应的时间戳
const calculateTimeRange = (range: string) => {
	const now = new Date();
	const endTime = new Date(now);
	endTime.setHours(23, 59, 59, 999); // 设置为当天结束时间

	const startTime = new Date(now);

	switch (range) {
		case "today": {
			startTime.setHours(0, 0, 0, 0);
			break;
		}
		case "week": {
			const dayOfWeek = now.getDay();
			const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
			startTime.setDate(now.getDate() - daysToMonday);
			startTime.setHours(0, 0, 0, 0);
			break;
		}
		case "month": {
			startTime.setDate(1);
			startTime.setHours(0, 0, 0, 0);
			break;
		}
		case "quarter": {
			const currentMonth = now.getMonth();
			const quarterStartMonth = Math.floor(currentMonth / 3) * 3;
			startTime.setMonth(quarterStartMonth, 1);
			startTime.setHours(0, 0, 0, 0);
			break;
		}
		case "year": {
			startTime.setMonth(0, 1);
			startTime.setHours(0, 0, 0, 0);
			break;
		}
		default: {
			// 默认本月
			startTime.setDate(1);
			startTime.setHours(0, 0, 0, 0);
		}
	}

	return {
		startTime: startTime.getTime(),
		endTime: endTime.getTime(),
	};
};

// 获取性能统计数据的API函数
const fetchPerformanceStats = async (
	timeRange: string,
): Promise<PerformanceStatsResponse> => {
	const { startTime, endTime } = calculateTimeRange(timeRange);

	// logger.info("[Agent][Performance] 开始获取性能统计数据", {
	// 	timeRange,
	// 	startTime,
	// 	endTime,
	// 	timestamp: new Date().toLocaleString(),
	// });

	try {
		const response =
			await apiClient.v1.agent.performance.overview.stats.$get({
				query: {
					startTime: startTime.toString(),
					endTime: endTime.toString(),
				},
			});

		// logger.info("[Agent][Performance] 收到响应", {
		// 	status: response.status,
		// 	statusText: response.statusText,
		// });

		const result = await response.json();

		if (response.ok && result.code === 200) {
			if ("error" in result || !result.data) {
				// logger.error("[Agent][Performance] 响应数据格式不正确", {
				// 	result,
				// 	expectedFields: "data",
				// });
				throw new Error("响应数据格式不正确");
			}

			// logger.info("[Agent][Performance] 获取性能统计数据成功", {
			// 	totalRevenue: result.data.totals?.totalRevenue,
			// 	timeRange: result.data.timeRange,
			// });

			return result.data;
		}
		const _errorCode = result.code || response.status;
		const errorMsg = result.message || response.statusText;

		// logger.error("[Agent][Performance] 获取性能统计数据失败", {
		// 	code: errorCode,
		// 	message: errorMsg,
		// 	timeRange,
		// });

		throw new Error(errorMsg || "获取统计数据失败");
	} catch (error) {
		const errorMessage =
			error instanceof Error ? error.message : String(error);

		// logger.error("[Agent][Performance] 获取性能统计数据异常", {
		// 	error: errorMessage,
		// 	timeRange,
		// });

		toast.error(`获取统计数据失败: ${errorMessage}`);
		throw error;
	}
};

export function useRevenueStats<_T = any>({
	timeRange,
	select,
}: UseRevenueStatsProps) {
	return useQuery({
		queryKey: ["revenueStats", timeRange],
		queryFn: () => fetchPerformanceStats(timeRange),
		staleTime: 5 * 60 * 1000, // 5分钟内不重新获取
		retry: 3,
		retryDelay: 1000,
		select:
			select ||
			((data) => {
				// 从API数据中提取我们需要的统计信息
				const latestTotalRevenue = data.totals.totalRevenue;

				// 计算期间收入（趋势数据的总和）
				const periodRevenue = data.trends.periodRevenue.reduce(
					(sum, point) => sum + point.value,
					0,
				);

				// 计算总渠道合作收益
				const totalChannelRevenue = data.trends.agentFeeRevenue.reduce(
					(sum, point) => sum + point.value,
					0,
				);

				// 计算总销售额
				const totalPlanSales = data.trends.planMarginRevenue.reduce(
					(sum, point) => sum + point.value,
					0,
				);

				// TODO: 变化率计算需要API提供上一个周期的数据，当前暂时返回0
				const monthlyChange = 0;
				const yearlyChange = 0;
				const salesChange = 0; // for totalPlanSales
				const channelRevenueChange = 0; // for totalChannelRevenue

				return {
					totalRevenue: latestTotalRevenue,
					monthlyRevenue: periodRevenue,
					totalPlanSales: totalPlanSales,
					totalChannelRevenue: totalChannelRevenue,
					yearlyChange,
					monthlyChange,
					salesChange,
					channelRevenueChange,
					// 原始数据也返回，供其他hook使用
					rawData: data,
				};
			}),
	});
}
