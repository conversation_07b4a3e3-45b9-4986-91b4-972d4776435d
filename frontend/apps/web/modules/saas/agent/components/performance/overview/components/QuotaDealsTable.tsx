"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Badge } from "@ui/components/badge";
import { Card } from "@ui/components/card";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@ui/components/table";
import { cn } from "@ui/lib";

// 订单状态枚举
export enum OrderStatus {
	PENDING = "PENDING", // 待处理
	COMPLETED = "COMPLETED", // 已完成
	CANCELLED = "CANCELLED", // 已取消
}

export interface QuotaDealItem {
	id: string;
	fromAgentId: string;
	fromAgentName: string;
	fromAgentRole: string;
	toAgentId: string;
	toAgentName: string;
	toAgentRole: string;
	quantity: number;
	amount?: number;
	unitPrice?: number;
	status: "COMPLETED";
	createdAt: string;
	completedAt?: string;
	cancelledAt?: string;
	remark?: string;
}

interface QuotaDealsTableProps {
	data: QuotaDealItem[];
	title?: string;
	formatter?: (value: number) => string;
}

/**
 * 名额交易明细表格
 */
export function QuotaDealsTable({
	data,
	title = "名额交易明细",
	formatter = (value) => `¥${value.toLocaleString()}`,
}: QuotaDealsTableProps) {
	// 友好的时间格式化函数
	const formatFriendlyTime = (timeString: string) => {
		const date = new Date(timeString);
		const now = new Date();
		const diffInMs = now.getTime() - date.getTime();
		const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
		const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
		const diffInMinutes = Math.floor(diffInMs / (1000 * 60));

		// 如果是今天
		if (diffInDays === 0) {
			if (diffInHours === 0) {
				if (diffInMinutes === 0) {
					return "刚刚";
				}
				return `${diffInMinutes}分钟前`;
			}
			return `${diffInHours}小时前`;
		}

		// 如果是昨天
		if (diffInDays === 1) {
			return `昨天 ${date.toLocaleTimeString("zh-CN", { hour: "2-digit", minute: "2-digit" })}`;
		}

		// 如果是一周内
		if (diffInDays <= 7) {
			return `${diffInDays}天前`;
		}

		// 超过一周，显示具体日期
		return date.toLocaleDateString("zh-CN", {
			year: "numeric",
			month: "2-digit",
			day: "2-digit",
			hour: "2-digit",
			minute: "2-digit",
		});
	};

	// 状态标签颜色映射
	const statusColors: Record<OrderStatus, string> = {
		[OrderStatus.PENDING]: "bg-blue-500/20 text-blue-400",
		[OrderStatus.COMPLETED]: "bg-green-500/20 text-green-400",
		[OrderStatus.CANCELLED]: "bg-gray-500/20 text-gray-400",
	};

	// 状态标签文本映射
	const statusLabels: Record<OrderStatus, string> = {
		[OrderStatus.PENDING]: "待处理",
		[OrderStatus.COMPLETED]: "已完成",
		[OrderStatus.CANCELLED]: "已取消",
	};

	return (
		<Card
			className={cn(
				"p-6",
				"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
				"border border-[#D4B485]/20",
				"group",
				"hover:border-[#D4B485]/40",
				"transition-all duration-500",
				"relative overflow-hidden",
			)}
		>
			{/* 背景装饰 */}
			<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
			<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />

			<div className="relative">
				<div className="mb-4">
					<h2
						className={cn(
							"text-lg font-semibold",
							zywhFont.className,
						)}
						style={{
							background:
								"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
							WebkitBackgroundClip: "text",
							WebkitTextFillColor: "transparent",
							backgroundClip: "text",
							textShadow:
								"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
						}}
					>
						{title}
					</h2>
				</div>

				<div className="overflow-x-auto">
					<Table>
						<TableHeader>
							<TableRow className="hover:bg-transparent border-b border-[#D4B485]/20">
								<TableHead className="text-[#D4B485]">
									交易来源
								</TableHead>
								<TableHead className="text-[#D4B485]">
									交易去向
								</TableHead>
								<TableHead className="text-[#D4B485]">
									名额数量
								</TableHead>
								<TableHead className="text-[#D4B485]">
									交易金额
								</TableHead>
								<TableHead className="text-[#D4B485]">
									单价
								</TableHead>
								<TableHead className="text-[#D4B485]">
									状态
								</TableHead>
								<TableHead className="text-[#D4B485]">
									创建时间
								</TableHead>
								<TableHead className="text-[#D4B485]">
									完成时间
								</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{data.length === 0 ? (
								<TableRow>
									<TableCell
										colSpan={8}
										className="text-center text-[#D4B485]/60 py-10"
									>
										暂无名额交易记录
									</TableCell>
								</TableRow>
							) : (
								data.map((item) => (
									<TableRow
										key={item.id}
										className="hover:bg-[#D4B485]/5 hover:border-[#D4B485]/10 transition-all duration-300 cursor-pointer group border-b border-[#D4B485]/10"
									>
										<TableCell className="text-white group-hover:text-[#F8D8B9] transition-colors duration-300">
											{item.fromAgentName}
										</TableCell>
										<TableCell className="text-white group-hover:text-[#F8D8B9] transition-colors duration-300">
											{item.toAgentName}
										</TableCell>
										<TableCell className="text-[#D4B485]/80 group-hover:text-[#D4B485] transition-colors duration-300">
											{item.quantity} 个
										</TableCell>
										<TableCell className="text-[#D4B485] group-hover:text-[#F8D8B9] transition-colors duration-300">
											{item.amount !== undefined
												? formatter(item.amount)
												: "-"}
										</TableCell>
										<TableCell className="text-[#D4B485]/80 group-hover:text-[#D4B485] transition-colors duration-300">
											{item.unitPrice !== undefined
												? formatter(item.unitPrice)
												: "-"}
										</TableCell>
										<TableCell>
											<Badge
												className={cn(
													statusColors[item.status],
													"transition-all duration-300 group-hover:shadow-sm",
												)}
											>
												{statusLabels[item.status]}
											</Badge>
										</TableCell>
										<TableCell className="text-[#D4B485]/60 group-hover:text-[#D4B485]/80 transition-colors duration-300">
											<div className="flex flex-col">
												<span className="text-sm">
													{formatFriendlyTime(
														item.createdAt,
													)}
												</span>
												<span className="text-xs text-[#D4B485]/40">
													{new Date(
														item.createdAt,
													).toLocaleDateString(
														"zh-CN",
													)}
												</span>
											</div>
										</TableCell>
										<TableCell className="text-[#D4B485]/60 group-hover:text-[#D4B485]/80 transition-colors duration-300">
											{item.completedAt ? (
												<div className="flex flex-col">
													<span className="text-sm">
														{formatFriendlyTime(
															item.completedAt,
														)}
													</span>
													<span className="text-xs text-[#D4B485]/40">
														{new Date(
															item.completedAt,
														).toLocaleDateString(
															"zh-CN",
														)}
													</span>
												</div>
											) : (
												<span className="text-[#D4B485]/40">
													-
												</span>
											)}
										</TableCell>
									</TableRow>
								))
							)}
						</TableBody>
					</Table>
				</div>
			</div>
		</Card>
	);
}
