import type { AgentR<PERSON> } from "@prisma/client";
import {
	type PerformanceStatsResponse,
	useRevenueStats,
} from "./useRevenueStats";

export interface RevenueSource {
	name: string;
	value: number;
	color: string;
	id: string;
}

interface UseRevenueBreakdownProps {
	agentId?: string;
	role?: AgentRole;
	timeRange: string;
}

/**
 * 获取收入构成数据
 */
export function useRevenueBreakdown({
	agentId,
	role,
	timeRange,
}: UseRevenueBreakdownProps) {
	return useRevenueStats({
		agentId,
		role,
		timeRange,
		select: (data: PerformanceStatsResponse) => {
			if (!data?.trends) {
				return [];
			}

			const { agentFeeRevenue, quotaMarginRevenue, planMarginRevenue } =
				data.trends;

			const breakdown: RevenueSource[] = [
				{
					id: "channel",
					name: "渠道合作收益",
					value: agentFeeRevenue.reduce(
						(sum: number, item: { value: number }) =>
							sum + item.value,
						0,
					),
					color: "#D4B485", // 主色调
				},
				{
					id: "quota",
					name: "名额交易收益",
					value: quotaMarginRevenue.reduce(
						(sum: number, item: { value: number }) =>
							sum + item.value,
						0,
					),
					color: "#00D4AA", // 绿色系
				},
				{
					id: "plan",
					name: "方案销售收益",
					value: planMarginRevenue.reduce(
						(sum: number, item: { value: number }) =>
							sum + item.value,
						0,
					),
					color: "#4F93FF", // 蓝色系
				},
			];

			// 过滤掉值为0的项
			return breakdown.filter((item) => item.value > 0);
		},
	});
}

/**
 * 将API数据转换为收入构成图表需要的数据格式
 */
function _transformToBreakdownData(
	apiData: PerformanceStatsResponse,
): RevenueSource[] {
	// 计算各类收入的总额
	const agentFeeTotal = apiData.trends.agentFeeRevenue.reduce(
		(sum, point) => sum + point.value,
		0,
	);
	const quotaMarginTotal = apiData.trends.quotaMarginRevenue.reduce(
		(sum, point) => sum + point.value,
		0,
	);
	const planMarginTotal = apiData.trends.planMarginRevenue.reduce(
		(sum, point) => sum + point.value,
		0,
	);

	// 构建收入来源数据 - 显示所有收入类型，即使为0
	const revenueBreakdown: RevenueSource[] = [
		{
			name: "渠道合作收益",
			value: agentFeeTotal,
			color: "#D4B485", // 香槟色
			id: "agent_fee",
		},
		{
			name: "配额分成收益",
			value: quotaMarginTotal,
			color: "#00D4AA", // 青绿色
			id: "quota_margin",
		},
		{
			name: "方案销售收益",
			value: planMarginTotal,
			color: "#4F93FF", // 蓝色
			id: "plan_margin",
		},
	];

	// 过滤掉值为0的项目，但至少保留一个
	const nonZeroBreakdown = revenueBreakdown.filter((item) => item.value > 0);

	// 如果所有收入都为0，返回默认显示结构
	if (nonZeroBreakdown.length === 0) {
		return [
			{
				name: "渠道合作收益",
				value: 1,
				color: "#D4B485",
				id: "agent_fee",
			},
			{
				name: "配额分成收益",
				value: 1,
				color: "#00D4AA",
				id: "quota_margin",
			},
			{
				name: "方案销售收益",
				value: 1,
				color: "#4F93FF",
				id: "plan_margin",
			},
		];
	}

	// 如果只有部分收入为0，为了显示完整结构，给0值项目分配最小值
	const totalRevenue = revenueBreakdown.reduce(
		(sum, item) => sum + item.value,
		0,
	);
	const minValue = Math.max(totalRevenue * 0.01, 100); // 最小值为总收入的1%或100分

	return revenueBreakdown.map((item) => ({
		...item,
		value: item.value > 0 ? item.value : minValue,
	}));
}
