import type { AgentR<PERSON> } from "@prisma/client";
import {
	type PerformanceStatsResponse,
	useRevenueStats,
} from "./useRevenueStats";

export interface RevenueDataPoint {
	date: string;
	totalRevenue: number;
	agentFeeRevenue: number;
	quotaMarginRevenue: number;
	planMarginRevenue: number;
}

interface UseRevenueTrendProps {
	agentId?: string;
	role?: AgentRole;
	timeRange: string;
}

/**
 * 获取收入趋势数据
 */
export function useRevenueTrend({
	agentId,
	role,
	timeRange,
}: UseRevenueTrendProps) {
	const statsQuery = useRevenueStats({ agentId, role, timeRange });

	return {
		...statsQuery,
		data: statsQuery.data
			? transformToTrendData(statsQuery.data.rawData)
			: [],
	};
}

/**
 * 将API数据转换为趋势图表需要的数据格式
 */
function transformToTrendData(
	apiData: PerformanceStatsResponse,
): RevenueDataPoint[] {
	// 获取所有日期
	const dates = apiData.trends.periodRevenue.map((point) => point.date);

	// 为每个日期构建数据点
	return dates.map((date) => {
		const periodRevenuePoint = apiData.trends.periodRevenue.find(
			(p) => p.date === date,
		);
		const agentFeePoint = apiData.trends.agentFeeRevenue.find(
			(p) => p.date === date,
		);
		const quotaMarginPoint = apiData.trends.quotaMarginRevenue.find(
			(p) => p.date === date,
		);
		const planMarginPoint = apiData.trends.planMarginRevenue.find(
			(p) => p.date === date,
		);

		return {
			date,
			totalRevenue: periodRevenuePoint?.value || 0,
			agentFeeRevenue: agentFeePoint?.value || 0,
			quotaMarginRevenue: quotaMarginPoint?.value || 0,
			planMarginRevenue: planMarginPoint?.value || 0,
		};
	});
}
