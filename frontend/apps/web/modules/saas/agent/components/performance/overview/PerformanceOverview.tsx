"use client";

import { AgentR<PERSON> } from "@prisma/client";
import { Skeleton } from "@ui/components/skeleton";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@ui/components/tabs";
import { Bar<PERSON>hart3, Handshake, TrendingUp, Wallet } from "lucide-react";
import { useEffect, useState } from "react";
import {
	GrowthFundCard,
	Header,
	QuotaDealsTable,
	RevenueBreakdownChart,
	RevenueTrendChart,
	StatsCard,
} from "./components";
import {
	useQuotaDeals,
	useRevenueBreakdown,
	useRevenueStats,
	useRevenueTrend,
} from "./hooks";

interface PerformanceOverviewProps {
	agentId?: string;
	role?: AgentRole;
	className?: string;
}

export function PerformanceOverview({
	agentId,
	role = AgentRole.PARTNER,
	className,
}: PerformanceOverviewProps) {
	const [timeRange, setTimeRange] = useState("month");

	// 获取收益统计数据
	const { data: revenueStats, isLoading: isLoadingStats } = useRevenueStats({
		agentId,
		role,
		timeRange,
	});

	// 获取收入趋势数据
	const { data: revenueTrend, isLoading: isLoadingTrend } = useRevenueTrend({
		agentId,
		role,
		timeRange,
	});

	// 获取收入构成数据
	const { data: revenueBreakdown, isLoading: isLoadingBreakdown } =
		useRevenueBreakdown({
			agentId,
			role,
			timeRange,
		});

	// 获取名额交易数据
	const {
		data: quotaDeals,
		isLoading: isLoadingQuotaDeals,
		fetchQuotaDeals,
	} = useQuotaDeals({
		agentId,
		timeRange,
	});

	// 初始化数据加载
	useEffect(() => {
		fetchQuotaDeals();
	}, [fetchQuotaDeals]);

	// 格式化金额，从分转为元
	const formatCurrency = (value: number) =>
		`¥${(value / 100).toLocaleString(undefined, { maximumFractionDigits: 0 })}`;

	// 处理时间范围变化
	const handleTimeRangeChange = (range: string) => {
		setTimeRange(range);
		// 时间范围变化时重新获取数据
		fetchQuotaDeals();
	};

	// 处理导出报表
	const handleExport = () => {
		alert("报表导出功能将在后续版本中实现");
	};

	return (
		<div className={`w-full space-y-6 ${className}`}>
			{/* 标题和操作栏 */}
			<Header
				title="业绩收益面板"
				subtitle="查看您的代理业绩和收益报表"
				onTimeRangeChange={handleTimeRangeChange}
				onExport={handleExport}
			/>

			{/* 统计卡片 */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
				{isLoadingStats ? (
					[0, 1, 2, 3].map((index) => (
						<Skeleton
							key={index}
							className="h-[150px] w-full rounded-xl"
						/>
					))
				) : revenueStats ? (
					<>
						<StatsCard
							title="总收益"
							value={revenueStats.totalRevenue}
							change={revenueStats.yearlyChange}
							icon={Wallet}
							formatter={formatCurrency}
							delay={0.1}
							description="历史累计总收入"
						/>
						<StatsCard
							title="月度收益"
							value={revenueStats.monthlyRevenue}
							change={revenueStats.monthlyChange}
							icon={TrendingUp}
							formatter={formatCurrency}
							delay={0.2}
							description="本月累计收入"
						/>
						<StatsCard
							title="总销售额"
							value={revenueStats.totalPlanSales}
							change={revenueStats.salesChange}
							icon={BarChart3}
							formatter={formatCurrency}
							delay={0.3}
							description="所有方案的累计销售总额"
						/>
						<StatsCard
							title="总渠道合作收益"
							value={revenueStats.totalChannelRevenue}
							change={revenueStats.channelRevenueChange}
							icon={Handshake}
							formatter={formatCurrency}
							delay={0.4}
							description="通过代理渠道获得的累计收益"
						/>
					</>
				) : (
					<div className="col-span-3 text-center text-[#D4B485]/60">
						暂无数据
					</div>
				)}
			</div>

			{/* 图表和数据列表 */}
			<Tabs defaultValue="all" className="space-y-6">
				<TabsList className="border-[#D4B485]/20">
					<TabsTrigger
						value="all"
						className="text-[#D4B485] data-[state=active]:border-[#D4B485] data-[state=active]:text-[#D4B485] hover:text-[#D4B485]/80"
					>
						全部收益
					</TabsTrigger>
					<TabsTrigger
						value="deals"
						className="text-[#D4B485] data-[state=active]:border-[#D4B485] data-[state=active]:text-[#D4B485] hover:text-[#D4B485]/80"
					>
						名额交易
					</TabsTrigger>
					{/* <TabsTrigger
						value="sales"
						className="text-[#D4B485] data-[state=active]:border-[#D4B485] data-[state=active]:text-[#D4B485] hover:text-[#D4B485]/80"
					>
						销售业绩
					</TabsTrigger> */}
				</TabsList>

				{/* 全部收益 Tab - 包含趋势图和收益构成 */}
				<TabsContent value="all" className="space-y-6">
					{/* 趋势图 */}
					{isLoadingTrend ? (
						<Skeleton className="h-[400px] w-full rounded-xl" />
					) : revenueTrend && revenueTrend.length > 0 ? (
						<RevenueTrendChart
							data={revenueTrend}
							formatter={formatCurrency}
						/>
					) : (
						<div className="h-[400px] flex items-center justify-center bg-[#1E2023] rounded-xl border border-[#D4B485]/20">
							<p className="text-[#D4B485]/60">暂无趋势数据</p>
						</div>
					)}

					{/* 收益构成和交付收益 */}
					<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
						<div className="md:col-span-2">
							{isLoadingBreakdown ? (
								<Skeleton className="h-[400px] w-full rounded-xl" />
							) : revenueBreakdown &&
								revenueBreakdown.length > 0 ? (
								<RevenueBreakdownChart
									data={revenueBreakdown}
									formatter={formatCurrency}
								/>
							) : (
								<div className="h-[400px] flex items-center justify-center bg-[#1E2023] rounded-xl border border-[#D4B485]/20">
									<p className="text-[#D4B485]/60">
										暂无收入构成数据
									</p>
								</div>
							)}
						</div>
						<div>
							{isLoadingStats ? (
								<Skeleton className="h-[400px] w-full rounded-xl" />
							) : revenueStats?.rawData?.totals ? (
								<GrowthFundCard
									businessTotals={revenueStats.rawData.totals}
									formatter={formatCurrency}
								/>
							) : (
								<div className="h-[400px] flex items-center justify-center bg-[#1E2023] rounded-xl border border-[#D4B485]/20">
									<p className="text-[#D4B485]/60">
										暂无业务数据
									</p>
								</div>
							)}
						</div>
					</div>
				</TabsContent>

				{/* 名额交易 Tab - 显示名额交易列表 */}
				<TabsContent value="deals">
					{isLoadingQuotaDeals ? (
						<Skeleton className="h-[400px] w-full rounded-xl" />
					) : quotaDeals ? (
						<QuotaDealsTable
							data={quotaDeals}
							formatter={formatCurrency}
						/>
					) : (
						<div className="h-[400px] flex items-center justify-center bg-[#1E2023] rounded-xl border border-[#D4B485]/20">
							<p className="text-[#D4B485]/60">
								暂无名额交易数据
							</p>
						</div>
					)}
				</TabsContent>

				{/* 销售业绩 Tab - 显示销售业绩列表（需要替换为真实的销售业绩数据） */}
			</Tabs>
		</div>
	);
}
