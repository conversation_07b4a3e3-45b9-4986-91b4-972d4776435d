"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Card } from "@ui/components/card";
import { Progress } from "@ui/components/progress";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";

interface GoalProgressProps {
	currentValue: number;
	targetValue: number;
	title?: string;
	formatter?: (value: number) => string;
}

/**
 * 收益目标达成进度组件
 */
export function GoalProgress({
	currentValue,
	targetValue,
	title = "收益目标进度",
	formatter = (value) => `¥${value.toLocaleString()}`,
}: GoalProgressProps) {
	// 计算进度百分比
	const percentage = Math.min(
		Math.round((currentValue / targetValue) * 100),
		100,
	);
	const remaining = Math.max(targetValue - currentValue, 0);

	return (
		<Card
			className={cn(
				"p-6",
				"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
				"border border-[#D4B485]/20",
				"group",
				"hover:border-[#D4B485]/40",
				"transition-all duration-500",
				"relative overflow-hidden",
			)}
		>
			{/* 背景装饰 */}
			<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
			<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />

			<div className="relative space-y-4">
				<h2
					className={cn("text-lg font-semibold", zywhFont.className)}
					style={{
						background:
							"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
						WebkitBackgroundClip: "text",
						WebkitTextFillColor: "transparent",
						backgroundClip: "text",
						textShadow:
							"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
					}}
				>
					{title}
				</h2>

				<div className="mt-4 space-y-6">
					<div className="flex justify-between items-center">
						<span
							className="text-3xl font-bold text-[#D4B485]"
							style={{
								textShadow:
									"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
							}}
						>
							{percentage}%
						</span>
						<div className="text-right">
							<p className="text-sm text-[#D4B485]/80">
								当前收益: {formatter(currentValue)}
							</p>
							<p className="text-sm text-[#D4B485]/60">
								目标收益: {formatter(targetValue)}
							</p>
						</div>
					</div>

					<div className="space-y-2">
						<div className="h-3 relative">
							<Progress
								value={percentage}
								className="h-3 bg-[#2A2D31]"
								style={
									{
										"--progress-background":
											"linear-gradient(90deg, #D4B485, #B08968)",
									} as React.CSSProperties
								}
							/>

							{/* 动画装饰 */}
							{percentage > 5 && (
								<motion.div
									className="absolute top-0 left-0 h-full overflow-hidden"
									style={{ width: `${percentage}%` }}
								>
									<div
										className="absolute top-0 right-0 h-full w-3 bg-white opacity-70 blur-sm"
										style={{
											animation: "shimmer 2s infinite",
										}}
									/>
								</motion.div>
							)}
						</div>

						<p className="text-[#D4B485]/60 text-sm">
							{percentage < 100
								? `距离目标还差 ${formatter(remaining)}`
								: "恭喜您已达成收益目标！"}
						</p>
					</div>
				</div>
			</div>
		</Card>
	);
}
