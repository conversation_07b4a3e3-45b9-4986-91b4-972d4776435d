import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";

export interface QuotaDealItem {
	id: string;
	fromAgentId: string;
	fromAgentName: string;
	fromAgentRole: string;
	toAgentId: string;
	toAgentName: string;
	toAgentRole: string;
	quantity: number;
	amount?: number;
	unitPrice?: number;
	status: "COMPLETED"; // 后端返回的状态字段
	createdAt: string;
	completedAt?: string;
	cancelledAt?: string;
	remark?: string;
	tradeRemark?: string;
	adminRemark?: string;
}

// API查询参数类型
export interface QuotaDealsQuery {
	page?: number;
	pageSize?: number;
	keyword?: string;
	status?: "PENDING" | "COMPLETED" | "CANCELLED";
	fromAgentId?: string;
	toAgentId?: string;
	startDate?: string;
	endDate?: string;
	sortField?:
		| "fromAgentName"
		| "toAgentName"
		| "quantity"
		| "amount"
		| "createdAt"
		| "status";
	sortDirection?: "asc" | "desc";
}

// API响应类型
export interface ApiResponse<T> {
	code: number;
	data: T;
	message?: string;
	error?: string;
}

export interface QuotaDealsResponse {
	total: number;
	items: QuotaDealItem[];
}

interface UseQuotaDealsProps {
	agentId?: string;
	timeRange?: string;
}

/**
 * 获取名额交易数据
 */
export function useQuotaDeals({ agentId, timeRange }: UseQuotaDealsProps) {
	const [loading, setLoading] = useState(false);
	const [data, setData] = useState<QuotaDealItem[]>([]);
	const [total, setTotal] = useState(0);
	const [currentPage, setCurrentPage] = useState(1);
	const [totalPages, setTotalPages] = useState(0);

	const fetchQuotaDeals = useCallback(
		async (query: QuotaDealsQuery = {}) => {
			setLoading(true);
			// logger.info("[Agent][QuotaDeals] 开始获取名额交易列表", {
			// 	query,
			// 	agentId,
			// 	timeRange,
			// 	timestamp: new Date().toLocaleString(),
			// });

			try {
				// 设置默认查询参数
				const queryParams = {
					page: 1,
					pageSize: 10,
					sortField: "createdAt" as const,
					sortDirection: "desc" as const,
					...query,
				};

				// 根据时间范围设置日期过滤
				if (timeRange) {
					const now = new Date();
					let startDate: string | undefined;

					switch (timeRange) {
						case "week":
							startDate = new Date(
								now.getTime() - 7 * 24 * 60 * 60 * 1000,
							).toISOString();
							break;
						case "month":
							startDate = new Date(
								now.getFullYear(),
								now.getMonth(),
								1,
							).toISOString();
							break;
						case "quarter": {
							const quarter = Math.floor(now.getMonth() / 3);
							startDate = new Date(
								now.getFullYear(),
								quarter * 3,
								1,
							).toISOString();
							break;
						}
						case "year":
							startDate = new Date(
								now.getFullYear(),
								0,
								1,
							).toISOString();
							break;
					}

					if (startDate) {
						queryParams.startDate = startDate;
					}
				}

				const response = await apiClient.v1.agent.performance.overview[
					"quota-deals"
				].$get({
					query: queryParams,
				});

				// logger.info("[Agent][QuotaDeals] 收到响应", {
				// 	status: response.status,
				// 	statusText: response.statusText,
				// });

				const result = await response.json();

				if (response.ok && result.code === 200) {
					if (!("data" in result) || !result.data?.items) {
						// logger.error("[Agent][QuotaDeals] 响应数据格式不正确", {
						// 	result,
						// 	expectedFields: "data.items",
						// });
						toast.error("获取名额交易列表失败: 响应数据格式不正确");
						return;
					}

					// 类型断言确保数据兼容性
					setData(result.data.items as QuotaDealItem[]);
					setTotal(result.data.total ?? 0);
					setCurrentPage(queryParams.page);
					setTotalPages(
						Math.ceil(
							(result.data.total ?? 0) / queryParams.pageSize,
						),
					);

					// logger.info("[Agent][QuotaDeals] 获取名额交易列表成功", {
					// 	total: result.data.total,
					// 	count: result.data.items.length,
					// 	page: queryParams.page,
					// 	pageSize: queryParams.pageSize,
					// });
				} else {
					const _errorCode = result.code || response.status;
					const errorMsg =
						result.message || result.error || response.statusText;

					// logger.error("[Agent][QuotaDeals] 获取名额交易列表失败", {
					// 	code: errorCode,
					// 	message: errorMsg,
					// 	query: queryParams,
					// });

					toast.error(`获取名额交易列表失败: ${errorMsg}`);
				}
			} catch (error) {
				const errorMessage =
					error instanceof Error ? error.message : String(error);
				// logger.error("[Agent][QuotaDeals] 获取名额交易列表异常", {
				// 	error: errorMessage,
				// 	query,
				// 	agentId,
				// 	timeRange,
				// });
				toast.error(`获取名额交易列表失败: ${errorMessage}`);
			} finally {
				setLoading(false);
			}
		},
		[agentId, timeRange],
	);

	return {
		loading,
		data,
		total,
		currentPage,
		totalPages,
		fetchQuotaDeals,
		setData,
		// 兼容原有接口
		isLoading: loading,
	};
}
