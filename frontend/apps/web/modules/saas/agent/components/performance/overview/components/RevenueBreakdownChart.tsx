"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Card } from "@ui/components/card";
import { cn } from "@ui/lib";
import type { TooltipProps } from "recharts";
import { <PERSON>, Pie, PieChart, ResponsiveContainer, Tooltip } from "recharts";

export interface RevenueSource {
	name: string;
	value: number;
	color: string;
	id: string;
}

interface EnhancedRevenueSource extends RevenueSource {
	percentage: string;
}

interface RevenueBreakdownChartProps {
	data: RevenueSource[];
	title?: string;
	height?: number;
	formatter?: (value: number) => string;
}

type CustomTooltipProps = TooltipProps<number, string> & {
	payload?: Array<{
		name: string;
		value: number;
		payload: EnhancedRevenueSource;
	}>;
};

const CustomTooltip = ({ active, payload }: CustomTooltipProps) => {
	if (active && payload && payload.length) {
		const item = payload[0].payload;
		const formatter = (value: number) => `¥${value.toLocaleString()}`;

		return (
			<div className="bg-[#0F1114]/95 backdrop-blur-sm border border-[#D4B485]/30 p-4 rounded-lg shadow-2xl min-w-[180px]">
				<div className="flex items-center gap-3 mb-3">
					<div
						className="w-4 h-4 rounded-full shadow-lg"
						style={{
							backgroundColor: item.color,
							boxShadow: `0 0 8px ${item.color}40`,
						}}
					/>
					<p className="text-[#D4B485] font-semibold text-sm">
						{item.name}
					</p>
				</div>

				<div className="space-y-2">
					<div className="flex justify-between items-center">
						<span className="text-xs text-[#D4B485]/80">金额</span>
						<span className="text-sm text-white font-bold">
							{formatter(item.value)}
						</span>
					</div>
					<div className="flex justify-between items-center">
						<span className="text-xs text-[#D4B485]/80">占比</span>
						<span
							className="text-sm font-bold"
							style={{ color: item.color }}
						>
							{item.percentage}%
						</span>
					</div>
				</div>
			</div>
		);
	}
	return null;
};

/**
 * 收入来源构成分析图表 - 高级版本
 */
export function RevenueBreakdownChart({
	data,
	title = "收入构成分析",
	height = 400,
	formatter = (value) => `¥${value.toLocaleString()}`,
}: RevenueBreakdownChartProps) {
	// 计算总收入和百分比
	const totalRevenue = data.reduce((sum, item) => sum + item.value, 0);
	const dataWithPercentage = data.map((item) => ({
		...item,
		percentage:
			totalRevenue > 0
				? ((item.value / totalRevenue) * 100).toFixed(1)
				: "0",
	}));

	// 高级配色方案 - 如果没有指定颜色则使用默认方案
	const enhancedData = dataWithPercentage.map((item, index) => {
		const defaultColors = [
			"#D4B485", // 香槟色
			"#00D4AA", // 青绿色
			"#4F93FF", // 蓝色
			"#FF6B9D", // 粉色
			"#9F7AEA", // 紫色
			"#F6AD55", // 橙色
		];

		return {
			...item,
			color: item.color || defaultColors[index % defaultColors.length],
		};
	});

	return (
		<Card
			className={cn(
				"p-8",
				"bg-gradient-to-br from-[#1E2023] via-[#1A1C1E] to-[#161719]",
				"border border-[#D4B485]/30",
				"group",
				"hover:border-[#D4B485]/50",
				"transition-all duration-700",
				"relative overflow-hidden",
				"shadow-xl",
			)}
		>
			{/* 简化背景装饰 */}
			<div className="absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(212,180,133,0.08),transparent_50%)]" />

			<div className="relative">
				{/* 标题和总收入展示 */}
				<div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
					<div className="mb-4 lg:mb-0">
						<h2
							className={cn(
								"text-2xl font-bold mb-2",
								zywhFont.className,
							)}
							style={{
								background:
									"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
								WebkitBackgroundClip: "text",
								WebkitTextFillColor: "transparent",
								backgroundClip: "text",
								textShadow:
									"0 2px 4px rgba(0,0,0,0.3), 0 0 20px rgba(212,180,133,0.15)",
							}}
						>
							{title}
						</h2>
						<p className="text-[#D4B485]/70 text-sm">
							各收入来源占比分析与构成详情
						</p>
					</div>

					{/* 总收入卡片 */}
					<div className="bg-[#0F1114]/60 backdrop-blur-sm border border-[#D4B485]/20 rounded-lg p-4 min-w-[160px]">
						<p className="text-xs text-[#D4B485]/80 mb-1">总收入</p>
						<p className="text-2xl font-bold text-[#D4B485]">
							{formatter(totalRevenue)}
						</p>
						<p className="text-xs text-[#D4B485]/60 mt-1">
							{enhancedData.length} 种收入来源
						</p>
					</div>
				</div>

				<div className="flex items-center justify-center py-4">
					<ResponsiveContainer width="100%" height={height}>
						<PieChart
							margin={{
								top: 20,
								right: 20,
								bottom: 20,
								left: 20,
							}}
						>
							<Pie
								data={enhancedData}
								cx="50%"
								cy="55%"
								innerRadius={60}
								outerRadius={95}
								paddingAngle={3}
								dataKey="value"
								stroke="none"
							>
								{enhancedData.map((entry, index) => (
									<Cell
										key={
											entry.id ||
											`cell-${entry.name}-${index}`
										}
										fill={entry.color}
										style={{
											filter: `drop-shadow(0 0 6px ${entry.color}40)`,
										}}
									/>
								))}
							</Pie>
							<Tooltip content={<CustomTooltip />} />
						</PieChart>
					</ResponsiveContainer>
				</div>

				{/* 底部统计摘要 */}
				<div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
					<div className="bg-[#0F1114]/40 backdrop-blur-sm border border-[#D4B485]/10 rounded-lg p-3">
						<p className="text-xs text-[#D4B485]/60 mb-1">
							主要收入源
						</p>
						<p
							className="text-lg font-bold"
							style={{
								color: enhancedData[0]?.color || "#D4B485",
							}}
						>
							{enhancedData[0]?.name || "暂无数据"}
						</p>
						<p className="text-xs text-[#D4B485]/60">
							{enhancedData[0]?.percentage || "0"}% 占比
						</p>
					</div>
					<div className="bg-[#0F1114]/40 backdrop-blur-sm border border-[#D4B485]/10 rounded-lg p-3">
						<p className="text-xs text-[#D4B485]/60 mb-1">
							收入来源数
						</p>
						<p className="text-lg font-bold text-[#4F93FF]">
							{enhancedData.length} 种
						</p>
						<p className="text-xs text-[#D4B485]/60">
							多元化收入结构
						</p>
					</div>
					<div className="bg-[#0F1114]/40 backdrop-blur-sm border border-[#D4B485]/10 rounded-lg p-3">
						<p className="text-xs text-[#D4B485]/60 mb-1">
							平均占比
						</p>
						<p className="text-lg font-bold text-[#00D4AA]">
							{enhancedData.length > 0
								? (100 / enhancedData.length).toFixed(1)
								: "0"}
							%
						</p>
						<p className="text-xs text-[#D4B485]/60">理论均衡值</p>
					</div>
				</div>
			</div>
		</Card>
	);
}
