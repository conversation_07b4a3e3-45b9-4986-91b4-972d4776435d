"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import type { LucideIcon } from "lucide-react";
import { useEffect, useState } from "react";

export interface StatsCardProps {
	title: string;
	value: number;
	change?: number;
	icon: LucideIcon;
	formatter?: (value: number) => string;
	delay?: number;
	description?: string;
	isNegativeChangeGood?: boolean;
}

// AnimatedNumber组件的自定义实现
function AnimatedNumber({
	value,
	duration = 1,
	delay = 0,
	formatter = (val: number) => val.toString(),
	className = "",
	style = {},
}: {
	value: number;
	duration?: number;
	delay?: number;
	formatter?: (val: number) => string;
	className?: string;
	style?: React.CSSProperties;
}) {
	const [displayValue, setDisplayValue] = useState(0);

	useEffect(() => {
		const startTime = Date.now() + delay * 1000;
		const endTime = startTime + duration * 1000;
		let animationFrame: number;

		const updateValue = () => {
			const now = Date.now();
			if (now < startTime) {
				animationFrame = requestAnimationFrame(updateValue);
				return;
			}

			if (now >= endTime) {
				setDisplayValue(value);
				return;
			}

			const elapsed = now - startTime;
			const progress = elapsed / (duration * 1000);
			setDisplayValue(Math.floor(value * progress));
			animationFrame = requestAnimationFrame(updateValue);
		};

		animationFrame = requestAnimationFrame(updateValue);

		return () => {
			cancelAnimationFrame(animationFrame);
		};
	}, [value, duration, delay]);

	return (
		<span className={className} style={style}>
			{formatter(displayValue)}
		</span>
	);
}

export function StatsCard({
	title,
	value,
	change = 0,
	icon: Icon,
	formatter = (val) => val.toLocaleString(),
	delay = 0,
	description,
	isNegativeChangeGood = false,
}: StatsCardProps) {
	const isPositive = change >= 0;
	const isGoodChange = isNegativeChangeGood ? !isPositive : isPositive;

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ delay }}
			className={cn(
				"rounded-xl p-6",
				"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
				"border border-[#D4B485]/20",
				"group",
				"hover:border-[#D4B485]/40",
				"transition-all duration-500",
				"relative overflow-hidden",
			)}
		>
			{/* 背景装饰 */}
			<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
			<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />

			<div className="relative space-y-3">
				<div className="flex items-center justify-between">
					<h3 className="text-sm font-medium text-[#D4B485]/60">
						{title}
					</h3>
					<Icon className="h-4 w-4 text-[#D4B485]/40" />
				</div>

				<div>
					<AnimatedNumber
						value={value}
						duration={2}
						delay={delay}
						formatter={formatter}
						className={cn(
							"text-2xl font-semibold",
							zywhFont.className,
						)}
						style={{
							background:
								"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
							WebkitBackgroundClip: "text",
							WebkitTextFillColor: "transparent",
							backgroundClip: "text",
							textShadow:
								"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
						}}
					/>

					{change !== undefined && (
						<div className="mt-1 flex items-center gap-2 text-sm">
							<span
								className={cn(
									"font-medium",
									isGoodChange
										? "text-emerald-500"
										: "text-rose-500",
								)}
							>
								{isPositive ? "+" : ""}
								{change}%
							</span>
							<span className="text-[#D4B485]/40">较上月</span>
						</div>
					)}

					{description && (
						<p className="mt-2 text-xs text-[#D4B485]/40">
							{description}
						</p>
					)}
				</div>
			</div>
		</motion.div>
	);
}
