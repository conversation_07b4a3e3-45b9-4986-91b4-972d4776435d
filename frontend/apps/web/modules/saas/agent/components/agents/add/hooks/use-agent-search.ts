import { apiClient } from "@shared/lib/api-client";
import { useCallback, useRef, useState } from "react";
import { toast } from "sonner";
import type { ApiResponse, SearchUsersResponseData, User } from "../types";

/**
 * 代理商用户搜索Hook
 * 负责用户搜索和缓存管理 - 支持全球手机号精准搜索
 */
export function useAgentSearch() {
	const [loading, setLoading] = useState(false);
	const [users, setUsers] = useState<User[]>([]);
	const [pagination, setPagination] = useState({
		total: 0,
		page: 1,
		pageSize: 8, // 一行4个，默认显示2行
		totalPages: 0,
	});

	// 用户搜索缓存 - 使用useRef确保在整个生命周期中保持同一个引用
	const cacheRef = useRef(
		new Map<string, { timestamp: number; data: SearchUsersResponseData }>(),
	);
	const CACHE_LIFETIME = 30000; // 30秒缓存

	/**
	 * 搜索用户 - 支持全球手机号精准搜索
	 * @param phoneNumber 手机号码（支持全球格式）
	 * @param page 页码
	 * @param pageSize 每页数量
	 */
	const searchUsers = useCallback(
		async (
			phoneNumber: string,
			page = 1,
			pageSize = 8,
		): Promise<User[]> => {
			// 检查手机号是否为空
			if (phoneNumber.trim() === "") {
				toast.error("请输入手机号码进行搜索");
				return [];
			}

			// 空关键词直接返回空结果
			if (!phoneNumber) {
				setUsers([]);
				setPagination({
					total: 0,
					page: 1,
					pageSize,
					totalPages: 0,
				});
				return [];
			}

			// 确保page和pageSize是数字类型
			const pageNum = Number(page);
			const pageSizeNum = Number(pageSize);

			// 生成缓存键（包含分页信息）
			const cacheKey = `${phoneNumber.trim()}_${pageNum}_${pageSizeNum}`;
			const cache = cacheRef.current;

			// 检查缓存
			const now = Date.now();
			if (cache.has(cacheKey)) {
				const cacheData = cache.get(cacheKey);

				// 处理空结果缓存的情况
				if (cacheData && cacheData.data.items.length === 0) {
					// 如果缓存是空结果，但在2秒内尝试了同一搜索，不重复请求避免打爆服务器
					if (now - cacheData.timestamp < 2000) {
						return [];
					}
					// 否则清除空结果缓存，重新请求
					cache.delete(cacheKey);
				}
				// 处理非空缓存的情况
				else if (
					cacheData &&
					now - cacheData.timestamp < CACHE_LIFETIME
				) {
					setUsers(cacheData.data.items);
					setPagination({
						total: cacheData.data.total,
						page: cacheData.data.page,
						pageSize: cacheData.data.pageSize,
						totalPages: cacheData.data.totalPages,
					});
					return cacheData.data.items;
				}
			}

			setLoading(true);
			try {
				const response =
					await apiClient.v1.agent.agents.add.searchUsers.$get({
						query: {
							phoneNumber,
							page: pageNum,
							pageSize: pageSizeNum,
						},
					});

				const result =
					(await response.json()) as ApiResponse<SearchUsersResponseData>;

				if ("error" in result || result.code !== 0) {
					throw new Error(result.message || "搜索用户失败");
				}

				// 更新缓存和状态
				const responseData = result.data;

				cache.set(cacheKey, { timestamp: now, data: responseData });
				setUsers(responseData.items);
				setPagination({
					total: responseData.total,
					page: responseData.page,
					pageSize: responseData.pageSize,
					totalPages: responseData.totalPages,
				});
				return responseData.items;
			} catch (error) {
				toast.error("搜索用户失败", {
					description:
						error instanceof Error ? error.message : "请重试",
				});
				return [];
			} finally {
				setLoading(false);
			}
		},
		[],
	);

	/**
	 * 清除搜索缓存
	 * 用于调试和测试，强制重新搜索
	 */
	const clearSearchCache = useCallback(() => {
		cacheRef.current.clear();
	}, []);

	return {
		loading,
		users,
		pagination,
		searchUsers,
		clearSearchCache,
	};
}
