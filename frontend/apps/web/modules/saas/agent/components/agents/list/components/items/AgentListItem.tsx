"use client";
import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { Ban, Edit, Eye, RefreshCcw, Users } from "lucide-react";
import type { AgentListItem as AgentListItemType } from "../../types";
import { ROLE_LABELS } from "../../types";

// 状态映射
const statusMap: Record<string, { label: string; color: string }> = {
	active: { label: "已激活", color: "bg-green-500/10 text-green-500" },
	pending: { label: "待激活", color: "bg-orange-500/10 text-orange-500" },
	inactive: { label: "未激活", color: "bg-yellow-500/10 text-yellow-500" },
	suspended: { label: "已禁用", color: "bg-red-500/10 text-red-500" },
	disabled: { label: "已禁用", color: "bg-red-500/10 text-red-500" },
};

export interface AgentListItemProps {
	agent: AgentListItemType;
	index: number;
	onViewDetails?: (agent: AgentListItemType) => void;
	onEdit?: (agent: AgentListItemType) => void;
	onDisable?: (agent: AgentListItemType) => void;
	onViewConsumers?: (agent: AgentListItemType) => void;
}

export function AgentListItemComponent({
	agent,
	index,
	onViewDetails,
	onEdit,
	onDisable,
	onViewConsumers,
}: AgentListItemProps) {
	// 格式化状态 - 统一转小写进行匹配
	const formattedStatus = statusMap[agent.status.toLowerCase()] || {
		label: agent.status, // 如果没有匹配，显示原始状态
		color: "bg-[#D4B485]/10 text-[#D4B485]/60", // 默认颜色
	};

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ delay: index * 0.1 }}
			className={cn(
				"rounded-lg p-6",
				"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
				"border border-[#D4B485]/20",
				"group",
				"hover:border-[#D4B485]/40",
				"transition-all duration-200",
				"relative overflow-hidden",
			)}
		>
			{/* 背景装饰 */}
			<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
			<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />

			<div className="relative flex items-center gap-4">
				<Avatar
					className={cn("h-12 w-12", "border border-[#D4B485]/20")}
				>
					<AvatarImage
						src={agent.avatar || undefined}
						alt={agent.name}
					/>
					<AvatarFallback
						className={cn(
							"text-[#1E2023] font-semibold",
							"bg-gradient-to-br from-[#D4B485] to-[#B08968]",
						)}
					>
						{agent.name.slice(0, 1)}
					</AvatarFallback>
				</Avatar>
				<div className="flex-1">
					<div className="flex items-center gap-2">
						<span className="font-medium text-[#D4B485]">
							{agent.name}
						</span>
						<span
							className={cn(
								"rounded-full px-2 py-0.5 text-xs",
								"bg-[#D4B485]/10",
								"text-[#D4B485]/60",
							)}
						>
							{ROLE_LABELS[agent.role]}
						</span>
						<span
							className={cn(
								"rounded-full px-2 py-0.5 text-xs",
								formattedStatus.color,
							)}
						>
							{formattedStatus.label}
						</span>
					</div>
					<div className="mt-1 text-sm text-[#D4B485]/40">
						{agent.phone}
					</div>
				</div>
				<div className="flex items-center gap-8">
					<div className="text-right">
						<div className="text-sm text-[#D4B485]/60">
							团队规模
						</div>
						<div className="font-medium text-[#D4B485]">
							{agent.teamSize}人
						</div>
					</div>
					<div className="text-right">
						<div className="text-sm text-[#D4B485]/60">
							本月业绩
						</div>
						<div className="font-medium text-[#D4B485]">
							¥{(agent.performance || 0).toLocaleString()}
						</div>
					</div>
					<div className="flex flex-wrap gap-2 mt-auto">
						<div className="flex flex-wrap gap-2">
							{agent.hasChildren && (
								<div className="bg-[#1A1C1E] text-[#D4B485]/80 text-xs border border-[#D4B485]/20 rounded-full px-2.5 py-0.5 inline-flex items-center">
									{`${agent.teamSize}个直属下级`}
								</div>
							)}

							{onViewConsumers && (
								<Button
									variant="outline"
									size="sm"
									className={cn(
										"gap-2",
										"bg-[#1A1C1E]",
										"border-[#D4B485]/20",
										"text-[#D4B485]",
										"hover:bg-[#D4B485]/10",
									)}
									onClick={() => onViewConsumers(agent)}
								>
									<Users className="h-4 w-4" />
									<span>客户列表</span>
								</Button>
							)}

							{onViewDetails && (
								<Button
									variant="outline"
									size="sm"
									className={cn(
										"gap-2",
										"bg-[#1A1C1E]",
										"border-[#D4B485]/20",
										"text-[#D4B485]",
										"hover:bg-[#D4B485]/10",
									)}
									onClick={() => onViewDetails(agent)}
								>
									<Eye className="h-4 w-4" />
									<span>查看详情</span>
								</Button>
							)}

							{onEdit && (
								<Button
									variant="outline"
									size="sm"
									className={cn(
										"gap-2",
										"bg-[#1A1C1E]",
										"border-[#D4B485]/20",
										"text-[#D4B485]",
										"hover:bg-[#D4B485]/10",
									)}
									onClick={() => onEdit(agent)}
								>
									<Edit className="h-4 w-4" />
									<span>编辑</span>
								</Button>
							)}

							{onDisable && (
								<Button
									variant="outline"
									size="sm"
									className={cn(
										"gap-2",
										"bg-[#1A1C1E]",
										agent.status === "disabled"
											? "border-green-500/30 text-green-500 hover:bg-green-500/10"
											: "border-red-500/30 text-red-500 hover:bg-red-500/10",
									)}
									onClick={() => onDisable(agent)}
								>
									{agent.status === "disabled" ? (
										<>
											<RefreshCcw className="h-4 w-4" />
											<span>启用</span>
										</>
									) : (
										<>
											<Ban className="h-4 w-4" />
											<span>禁用</span>
										</>
									)}
								</Button>
							)}

							{/* 省略号菜单已移除 */}
						</div>
					</div>
				</div>
			</div>
		</motion.div>
	);
}

// 导出重命名的组件
export { AgentListItemComponent as AgentListItem };
