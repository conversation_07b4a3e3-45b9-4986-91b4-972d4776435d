"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Avatar, AvatarFallback } from "@ui/components/avatar";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@ui/components/tooltip";
import { cn } from "@ui/lib";
import { AnimatePresence, motion } from "framer-motion";
import {
	AlertCircle,
	ChevronLeft,
	ChevronRight,
	Info,
	PhoneCall,
	RefreshCw,
	Search,
	Sparkles,
	Users,
	UserX,
} from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";
import type { User } from "../types";

interface SelectUserProps {
	onNext: (user: User) => void;
	onPrev: () => void;
	searchUsers: (
		phoneNumber: string,
		page?: number,
		pageSize?: number,
	) => Promise<User[]>;
	isLoading?: boolean;
	users: User[];
	pagination?: {
		total: number;
		page: number;
		pageSize: number;
		totalPages: number;
	};
	clearCache?: () => void;
	onCreateNewUser?: (phoneNumber: string) => void;
}

export function SelectUser({
	onNext,
	onPrev,
	searchUsers,
	isLoading,
	users,
	pagination = { total: 0, page: 1, pageSize: 8, totalPages: 0 },
	clearCache,
}: SelectUserProps) {
	const [phoneNumber, setPhoneNumber] = useState("");
	const [searchTerm, setSearchTerm] = useState("");
	const [selectedUser, setSelectedUser] = useState<User | null>(null);
	const _timerRef = useRef<NodeJS.Timeout | null>(null);
	const searchInputRef = useRef<HTMLInputElement>(null);
	const [animateSearch, _setAnimateSearch] = useState(false);
	const [_currentPage, setCurrentPage] = useState(1);
	const [isSearching, setIsSearching] = useState(false);
	const [hasPerformedSearch, setHasPerformedSearch] = useState(false);
	const phoneInputRef = useRef<HTMLInputElement>(null);

	const roleNameMap = {
		ADMIN: "管理员",
		BRANCH: "分公司",
		DIRECTOR: "联席董事",
		PARTNER: "合伙人",
		SALES: "超级个体",
	};

	useEffect(() => {
		if (searchInputRef.current) {
			searchInputRef.current.focus();
		}
	}, []);

	const handleSearch = useCallback(async () => {
		if (!phoneNumber.trim()) {
			return;
		}

		setIsSearching(true);
		try {
			setSearchTerm(phoneNumber.trim());
			await searchUsers(phoneNumber);
			setHasPerformedSearch(true);
		} finally {
			setIsSearching(false);
		}
	}, [phoneNumber, searchUsers]);

	const handleKeyDown = useCallback(
		(e: React.KeyboardEvent<HTMLInputElement>) => {
			if (e.key === "Enter" && !isSearching && phoneNumber.trim()) {
				void handleSearch();
			}
		},
		[handleSearch, isSearching, phoneNumber],
	);

	const handlePageChange = useCallback(
		(newPage: number) => {
			if (isLoading || isSearching) {
				return;
			}

			if (newPage >= 1 && newPage <= pagination.totalPages) {
				void searchUsers(phoneNumber, newPage, pagination.pageSize);
			}
		},
		[isLoading, isSearching, pagination, phoneNumber, searchUsers],
	);

	const handleInputChange = useCallback(
		(e: React.ChangeEvent<HTMLInputElement>) => {
			const value = e.target.value;
			setPhoneNumber(value);
		},
		[],
	);

	const handleRefreshSearch = useCallback(() => {
		if (clearCache && phoneNumber.trim()) {
			clearCache();
			void searchUsers(phoneNumber);
		}
	}, [clearCache, phoneNumber, searchUsers]);

	const _handleCreateNewUser = useCallback(() => {
		if (!phoneNumber.trim()) {
			return;
		}

		const newUser: User = {
			id: `new-${Date.now()}`,
			name: "新用户",
			email: "",
			phoneNumber: phoneNumber,
			isAgent: false,
			idCardVerified: false,
			isNewUser: true,
		};

		onNext(newUser);
	}, [phoneNumber, onNext]);

	return (
		<div className="space-y-8">
			<motion.div
				className="space-y-2 text-center"
				initial={{ opacity: 0, y: -20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.5 }}
			>
				<div className="inline-block relative">
					<h3
						className={cn(
							"text-2xl font-semibold",
							zywhFont.className,
						)}
						style={{
							background:
								"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
							WebkitBackgroundClip: "text",
							WebkitTextFillColor: "transparent",
							backgroundClip: "text",
							textShadow:
								"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
						}}
					>
						选择/创建用户
					</h3>
					<motion.div
						className="absolute -right-8 top-0 text-[#D4B485]"
						initial={{ rotate: -10, scale: 0.8 }}
						animate={{ rotate: 5, scale: 1 }}
						transition={{ duration: 0.5, delay: 0.2 }}
					>
						<Sparkles className="h-5 w-5" />
					</motion.div>
				</div>
				<p className="text-[#D4B485]/60 max-w-lg mx-auto">
					搜索现有用户或创建新用户开通代理商
				</p>
			</motion.div>

			<motion.div
				className="relative"
				initial={{ opacity: 0, scale: 0.95 }}
				animate={{ opacity: 1, scale: 1 }}
				transition={{ duration: 0.4, delay: 0.1 }}
			>
				<div
					className={cn(
						"relative flex flex-col",
						"rounded-xl overflow-hidden",
						"bg-gradient-to-r from-[#D4B485]/10 via-[#D4B485]/5 to-[#D4B485]/10",
						"backdrop-blur-sm",
						"shadow-[0_10px_30px_-5px_rgba(0,0,0,0.3)]",
						"after:absolute after:inset-0 after:bg-gradient-to-r after:from-transparent after:via-[#D4B485]/5 after:to-transparent after:opacity-70",
						"after:animate-[gradient-shine_3s_ease_infinite]",
						animateSearch
							? "ring-2 ring-[#D4B485]/30 transition-all duration-300 scale-[1.02]"
							: "",
					)}
				>
					<div className="relative z-10 flex items-center p-2">
						<div
							className={cn(
								"flex items-center justify-center w-10 h-10 rounded-full bg-[#1A1C1E]",
								"border border-[#D4B485]/10 shadow-inner",
								"transition-all duration-300 mr-3",
								animateSearch
									? "text-[#D4B485] bg-[#D4B485]/10 scale-110"
									: "text-[#D4B485]/40",
							)}
						>
							<PhoneCall
								className={cn(
									"h-4 w-4 transition-all duration-300",
									animateSearch &&
										"animate-[pulse_1s_ease_infinite]",
								)}
							/>
						</div>
						<div className="flex-1 relative">
							<Input
								placeholder="输入手机号后点击搜索或按Enter..."
								className={cn(
									"w-full bg-transparent",
									"text-[#D4B485] text-base",
									"placeholder:text-[#D4B485]/40",
									"border-0 border-b border-[#D4B485]/20 rounded-none",
									"h-12 px-0 py-2",
									"focus-visible:ring-0 focus-visible:border-[#D4B485]/40",
									"transition-all duration-300",
								)}
								value={phoneNumber}
								onChange={handleInputChange}
								disabled={isLoading || isSearching}
								ref={phoneInputRef}
								onKeyDown={handleKeyDown}
								onBlur={() => {
									if (
										phoneNumber &&
										phoneNumber.trim() !== searchTerm
									) {
										setSearchTerm(phoneNumber.trim());
										setCurrentPage(1);
										searchUsers(
											phoneNumber.trim(),
											1,
											pagination.pageSize,
										);
									}
								}}
							/>
							{phoneNumber && !isLoading && (
								<button
									className="absolute right-0 top-1/2 -translate-y-1/2 text-[#D4B485]/40 hover:text-[#D4B485] transition-colors"
									onClick={() => setPhoneNumber("")}
									type="button"
								>
									<span className="sr-only">清除</span>
									<svg
										width="16"
										height="16"
										viewBox="0 0 16 16"
										fill="none"
										xmlns="http://www.w3.org/2000/svg"
										aria-hidden="true"
									>
										<title>清除搜索</title>
										<path
											d="M12 4L4 12M4 4L12 12"
											stroke="currentColor"
											strokeWidth="1.5"
											strokeLinecap="round"
											strokeLinejoin="round"
										/>
									</svg>
								</button>
							)}
						</div>

						<Button
							type="button"
							variant="outline"
							className={cn(
								"ml-2 bg-[#1A1C1E] border-[#D4B485]/20 text-[#D4B485]",
								"hover:bg-[#D4B485]/10",
								"transition-all duration-300 px-4 h-10",
								"shadow-[0_5px_15px_-3px_rgba(0,0,0,0.3)]",
								"flex items-center gap-2",
								isLoading && "opacity-50 cursor-not-allowed",
							)}
							onClick={handleSearch}
							disabled={
								isLoading || isSearching || !phoneNumber.trim()
							}
						>
							<Search className="h-4 w-4" />
							搜索
						</Button>

						{clearCache && (
							<TooltipProvider>
								<Tooltip>
									<TooltipTrigger asChild>
										<Button
											type="button"
											variant="outline"
											size="icon"
											className={cn(
												"ml-2 bg-[#1A1C1E] border-[#D4B485]/20 text-[#D4B485]",
												"hover:bg-[#D4B485]/10 rounded-md",
												"transition-all duration-300 h-10 w-10",
												"shadow-[0_5px_15px_-3px_rgba(0,0,0,0.3)]",
												isLoading &&
													"opacity-50 cursor-not-allowed",
											)}
											onClick={handleRefreshSearch}
											disabled={
												isLoading || !phoneNumber.trim()
											}
										>
											<RefreshCw
												className={cn(
													"h-4 w-4",
													isLoading && "animate-spin",
												)}
											/>
										</Button>
									</TooltipTrigger>
									<TooltipContent
										side="top"
										className="bg-[#1E2023] border-[#D4B485]/20 text-[#D4B485]"
									>
										<p>重新搜索</p>
									</TooltipContent>
								</Tooltip>
							</TooltipProvider>
						)}
					</div>

					{!phoneNumber && !isLoading && (
						<motion.div
							className="px-4 pb-3 pt-0 flex flex-wrap gap-2"
							initial={{ opacity: 0, y: -10 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ delay: 0.3 }}
						>
							<span className="text-xs text-[#D4B485]/40 mr-1 self-center">
								示例:
							</span>
							{[
								"13800138000",
								"+12025550179",
								"+447911123456",
							].map((term, _i) => (
								<button
									key={term}
									type="button"
									className={cn(
										"px-2 py-1 rounded-full text-xs",
										"bg-[#D4B485]/5 text-[#D4B485]/70",
										"border border-[#D4B485]/10",
										"hover:bg-[#D4B485]/10 hover:text-[#D4B485]",
										"transition-all duration-200 transform hover:-translate-y-0.5",
									)}
									onClick={() => setPhoneNumber(term)}
								>
									{term}
								</button>
							))}
						</motion.div>
					)}
				</div>

				<motion.div
					className="mt-3 flex items-center justify-between text-xs"
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					transition={{ delay: 0.5 }}
				>
					<div className="flex items-center text-[#D4B485]/40">
						<Info className="h-3 w-3 mr-1.5" />
						支持全球手机号精准搜索
					</div>
					<div className="text-[#D4B485]/40 italic">
						用户未注册可先引导用户注册
					</div>
				</motion.div>
			</motion.div>

			<div className="min-h-[250px] relative">
				<AnimatePresence mode="wait">
					{isSearching ? (
						<motion.div
							key="loading"
							initial={{ opacity: 0 }}
							animate={{ opacity: 1 }}
							exit={{ opacity: 0 }}
							className="flex flex-col h-[250px] items-center justify-center text-[#D4B485]/60 gap-3"
						>
							<div className="relative">
								<div className="w-10 h-10 rounded-full border-2 border-[#D4B485]/20 border-t-[#D4B485] animate-spin" />
								<div className="absolute inset-0 flex items-center justify-center">
									<Users className="h-4 w-4 text-[#D4B485]/40" />
								</div>
							</div>
							<p className="text-sm animate-pulse">
								正在搜索用户...
							</p>
						</motion.div>
					) : hasPerformedSearch && users.length > 0 ? (
						<motion.div
							key="search-results"
							initial={{ opacity: 0 }}
							animate={{ opacity: 1 }}
							exit={{ opacity: 0 }}
							transition={{ staggerChildren: 0.05 }}
							className="grid gap-4"
						>
							<div className="flex justify-between items-center text-sm text-[#D4B485]/60 mb-2 mt-1 px-1">
								<div>
									找到 {pagination.total} 个匹配用户
									{pagination.totalPages > 1 &&
										` (第${pagination.page}页)`}
								</div>
								<div className="flex items-center">
									<span className="inline-flex items-center mr-4">
										<span className="w-2 h-2 rounded-full bg-red-400 mr-1.5" />
										红色-已是代理商
									</span>
									<span className="inline-flex items-center">
										<span className="w-2 h-2 rounded-full bg-orange-400 mr-1.5" />
										黄色-未实名认证
									</span>
								</div>
							</div>

							<div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
								{users.map((user, index) => {
									const isExistingAgent = user.isAgent;
									const isNotVerified = !user.idCardVerified;
									const isDisabled =
										isExistingAgent || isNotVerified;

									return (
										<TooltipProvider
											key={user.id}
											delayDuration={300}
										>
											<Tooltip>
												<TooltipTrigger asChild>
													<motion.div
														initial={{
															opacity: 0,
															y: 20,
														}}
														animate={{
															opacity: 1,
															y: 0,
														}}
														transition={{
															delay: index * 0.05,
														}}
														className={cn(
															"rounded-lg p-4",
															"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
															"border border-[#D4B485]/20",
															"group",
															"transition-all duration-300",
															"relative overflow-hidden",
															"transform hover:-translate-y-1",
															isLoading ||
																isDisabled
																? "cursor-not-allowed opacity-60"
																: "cursor-pointer hover:border-[#D4B485]/40 hover:shadow-[0_5px_15px_rgba(212,180,133,0.1)]",
															selectedUser?.id ===
																user.id &&
																!isDisabled &&
																"border-[#D4B485] bg-[#D4B485]/5 shadow-[0_0_15px_rgba(212,180,133,0.15)]",
															isExistingAgent &&
																"border-red-500/30 bg-red-500/5",
															isNotVerified &&
																"border-orange-500/30 bg-orange-500/5",
														)}
														onClick={() => {
															if (
																!isLoading &&
																!isDisabled
															) {
																setSelectedUser(
																	user,
																);
															}
														}}
													>
														<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
														<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />

														{/* 状态标记 */}
														{isExistingAgent && (
															<div className="absolute right-2 top-2 flex items-center bg-red-500/10 px-1.5 py-0.5 rounded-full">
																<AlertCircle className="mr-0.5 h-2.5 w-2.5 text-red-400" />
																<span className="text-[10px] text-red-400">
																	已是代理商
																</span>
															</div>
														)}

														{isNotVerified && (
															<div className="absolute right-2 top-2 flex items-center bg-orange-500/10 px-1.5 py-0.5 rounded-full">
																<UserX className="mr-0.5 h-2.5 w-2.5 text-orange-400" />
																<span className="text-[10px] text-orange-400">
																	未实名认证
																</span>
															</div>
														)}

														<div className="relative flex flex-col items-center text-center gap-3">
															<div className="relative">
																<Avatar
																	className={cn(
																		"h-16 w-16", // 大头像
																		"border border-[#D4B485]/20",
																		"group-hover:border-[#D4B485]/40 transition-all duration-300",
																		selectedUser?.id ===
																			user.id &&
																			!isDisabled &&
																			"ring-2 ring-[#D4B485]/30",
																	)}
																>
																	<AvatarFallback
																		className={cn(
																			"text-[#1E2023] font-semibold",
																			"bg-gradient-to-br from-[#D4B485] to-[#B08968]",
																		)}
																	>
																		{user.name.slice(
																			0,
																			1,
																		)}
																	</AvatarFallback>
																</Avatar>
																{!isDisabled &&
																	selectedUser?.id ===
																		user.id && (
																		<motion.div
																			initial={{
																				scale: 0,
																			}}
																			animate={{
																				scale: 1,
																			}}
																			className="absolute -bottom-1 -right-1 bg-[#D4B485] rounded-full w-5 h-5 flex items-center justify-center text-white text-xs shadow-md"
																		>
																			✓
																		</motion.div>
																	)}
															</div>
															<div>
																<div className="font-medium text-[#D4B485] group-hover:text-[#E5C9A5] transition-colors duration-300">
																	{user.name}
																</div>
																<div className="mt-1 text-sm text-[#D4B485]/40">
																	{
																		user.phoneNumber
																	}
																</div>
																{isExistingAgent &&
																	user.agentRole && (
																		<div className="mt-1.5 text-xs text-red-400/80 bg-red-400/10 inline-block px-2 py-0.5 rounded">
																			当前角色:{" "}
																			{(user.agentRole &&
																				roleNameMap[
																					user.agentRole as keyof typeof roleNameMap
																				]) ||
																				user.agentRole}
																		</div>
																	)}
															</div>
														</div>
													</motion.div>
												</TooltipTrigger>
												{isExistingAgent && (
													<TooltipContent
														side="top"
														className="max-w-xs bg-[#1E2023] border-[#D4B485]/20 text-[#D4B485]"
													>
														<p>
															该用户已经是代理商，无法重复开通。如需修改其角色，请前往代理商管理页面操作。
														</p>
													</TooltipContent>
												)}
												{isNotVerified && (
													<TooltipContent
														side="top"
														className="max-w-xs bg-[#1E2023] border-[#D4B485]/20 text-[#D4B485]"
													>
														<p>
															该用户尚未完成实名认证，将以"待激活"状态创建代理商账号。用户完成实名认证后将自动激活。
														</p>
													</TooltipContent>
												)}
											</Tooltip>
										</TooltipProvider>
									);
								})}
							</div>

							{/* 分页组件 */}
							{pagination.totalPages > 1 && (
								<div className="flex justify-center mt-6">
									<nav className="flex items-center gap-1">
										<Button
											variant="outline"
											size="icon"
											className={cn(
												"h-8 w-8 bg-[#1A1C1E] border-[#D4B485]/20 text-[#D4B485]",
												"hover:bg-[#D4B485]/10",
												"disabled:opacity-50 disabled:cursor-not-allowed",
											)}
											onClick={() =>
												handlePageChange(
													pagination.page - 1,
												)
											}
											disabled={
												pagination.page <= 1 ||
												isLoading
											}
										>
											<ChevronLeft className="h-4 w-4" />
										</Button>

										{Array.from(
											{ length: pagination.totalPages },
											(_, i) => i + 1,
										)
											.filter((page) => {
												// 显示第一页、最后一页，以及当前页前后各1页
												return (
													page === 1 ||
													page ===
														pagination.totalPages ||
													Math.abs(
														page - pagination.page,
													) <= 1
												);
											})
											.map((page, index, array) => {
												// 检查是否需要添加省略号
												const needEllipsis =
													index > 0 &&
													array[index - 1] !==
														page - 1;

												return (
													<div
														key={page}
														className="flex items-center"
													>
														{needEllipsis && (
															<span className="px-2 text-[#D4B485]/40">
																...
															</span>
														)}
														<Button
															variant={
																page ===
																pagination.page
																	? "primary"
																	: "outline"
															}
															size="icon"
															className={cn(
																"h-8 w-8",
																page ===
																	pagination.page
																	? "bg-[#D4B485] text-white"
																	: "bg-[#1A1C1E] border-[#D4B485]/20 text-[#D4B485] hover:bg-[#D4B485]/10",
															)}
															onClick={() =>
																handlePageChange(
																	page,
																)
															}
															disabled={isLoading}
														>
															{page}
														</Button>
													</div>
												);
											})}

										<Button
											variant="outline"
											size="icon"
											className={cn(
												"h-8 w-8 bg-[#1A1C1E] border-[#D4B485]/20 text-[#D4B485]",
												"hover:bg-[#D4B485]/10",
												"disabled:opacity-50 disabled:cursor-not-allowed",
											)}
											onClick={() =>
												handlePageChange(
													pagination.page + 1,
												)
											}
											disabled={
												pagination.page >=
													pagination.totalPages ||
												isLoading
											}
										>
											<ChevronRight className="h-4 w-4" />
										</Button>
									</nav>
								</div>
							)}
						</motion.div>
					) : hasPerformedSearch && searchTerm ? (
						<motion.div
							key="no-results"
							initial={{ opacity: 0 }}
							animate={{ opacity: 1 }}
							exit={{ opacity: 0 }}
							className="flex flex-col h-[250px] items-center justify-center gap-4"
						>
							<div className="p-5 rounded-full bg-[#D4B485]/5 border border-[#D4B485]/10">
								<Users className="h-8 w-8 text-[#D4B485]/30" />
							</div>
							<div className="text-center space-y-2">
								<div className="text-[#D4B485]/60 font-medium">
									未找到匹配的用户
								</div>
								<p className="text-xs text-[#D4B485]/40 max-w-xs">
									该手机号码可能尚未注册，您可以直接通过手机号创建代理商
								</p>
							</div>
							<div className="flex gap-3">
								{clearCache && (
									<Button
										variant="outline"
										size="sm"
										className="mt-2 bg-[#1A1C1E] border-[#D4B485]/20 text-[#D4B485] hover:bg-[#D4B485]/10"
										onClick={handleRefreshSearch}
										disabled={isLoading}
									>
										<RefreshCw className="mr-2 h-4 w-4" />
										重新搜索
									</Button>
								)}
								<Button
									variant="primary"
									size="sm"
									className="mt-2 bg-gradient-to-r from-[#D4B485] to-[#B08968] text-white"
									onClick={() =>
										onNext({
											id: "new-user",
											phoneNumber: searchTerm,
											name: "新用户",
											email: "",
											phoneNumberFull: "",
											isAgent: false,
											idCardVerified: false,
											isNewUser: true,
										})
									}
									disabled={isLoading}
								>
									<PhoneCall className="mr-2 h-4 w-4" />
									通过此手机号创建
								</Button>
							</div>
						</motion.div>
					) : (
						<motion.div
							key="empty"
							initial={{ opacity: 0 }}
							animate={{ opacity: 1 }}
							exit={{ opacity: 0 }}
							className="flex flex-col h-[250px] items-center justify-center gap-4"
						>
							<div className="p-5 rounded-full bg-[#D4B485]/5 border border-[#D4B485]/10">
								<PhoneCall className="h-8 w-8 text-[#D4B485]/30" />
							</div>
							<div className="text-[#D4B485]/60 text-center">
								<p>请输入手机号后点击搜索</p>
								<p className="text-xs text-[#D4B485]/40 mt-2">
									支持全球手机号码格式
								</p>
							</div>
						</motion.div>
					)}
				</AnimatePresence>
			</div>

			{/* 底部按钮 */}
			<motion.div
				className="flex justify-between pt-4"
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.2 }}
			>
				<Button
					variant="outline"
					className={cn(
						"px-8",
						"bg-[#1E2023]/50 border-[#D4B485]/20",
						"text-[#D4B485] hover:text-[#E5C9A5]",
						"hover:bg-[#D4B485]/10",
						"transition duration-200",
						"disabled:opacity-50 disabled:cursor-not-allowed",
					)}
					onClick={onPrev}
					disabled={isLoading}
				>
					上一步
				</Button>
				<Button
					className={cn(
						"px-8",
						"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
						"text-white",
						"border-none",
						"shadow-[0_8px_16px_-4px_rgba(212,180,133,0.3)]",
						"hover:shadow-[0_12px_20px_-4px_rgba(212,180,133,0.4)]",
						"transition-all duration-200",
						"disabled:opacity-50 disabled:cursor-not-allowed",
					)}
					onClick={() => selectedUser && onNext(selectedUser)}
					disabled={!selectedUser || isLoading}
				>
					下一步
				</Button>
			</motion.div>
		</div>
	);
}
