import type { <PERSON><PERSON><PERSON> } from "@prisma/client";
import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";
import type { AgentDetail } from "./use-agent-detail";

// 编辑代理商的请求参数
export interface UpdateAgentParams {
	id: string;
	name?: string;
	role?: AgentRole;
	teamQuota?: number;
	joinFee?: number;
	hourlyRevenue?: number;
}

// API响应类型
interface ApiResponse {
	code: number;
	message: string;
	data?: {
		agent?: AgentDetail;
	} | null;
}

export function useAgentEdit() {
	const [loading, setLoading] = useState(false);

	const updateAgent = useCallback(async (params: UpdateAgentParams) => {
		setLoading(true);

		try {
			const response = await apiClient.v1.agent.agents.update.info.$put({
				json: params,
			});

			const result = (await response.json()) as ApiResponse;

			if (response.ok && result.code === 0) {
				if (!result.data?.agent) {
					toast.error("更新代理商信息失败: 响应数据格式不正确");
					return null;
				}

				return result.data.agent;
			}
			const errorMsg = result.message;

			toast.error(`更新代理商信息失败: ${errorMsg}`);
			return null;
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : String(error);

			toast.error(`更新代理商信息失败: ${errorMessage}`);
			return null;
		} finally {
			setLoading(false);
		}
	}, []);

	return {
		loading,
		updateAgent,
	};
}
