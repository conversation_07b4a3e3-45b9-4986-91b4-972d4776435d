"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import type { Agent<PERSON>ole } from "@prisma/client";
import { <PERSON><PERSON> } from "@ui/components/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	<PERSON><PERSON>Footer,
	<PERSON><PERSON>Header,
	DialogTitle,
} from "@ui/components/dialog";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { cn } from "@ui/lib";
import { useCallback, useEffect } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { useAgentDetail } from "../../hooks/use-agent-detail";
import { useAgentEdit } from "../../hooks/use-agent-edit";
import type { AgentListItem } from "../../types/index";
import { ROLE_LABELS } from "../../types/index";

// 表单验证逻辑
const formSchema = z.object({
	id: z.string(),
	name: z.string().min(2, {
		message: "姓名至少需要2个字符",
	}),
	role: z.string().optional(),
	teamQuota: z.coerce
		.number()
		.min(0, {
			message: "团队名额必须是非负数",
		})
		.optional(),
	joinFee: z.coerce
		.number()
		.min(0, {
			message: "加盟费必须是非负数",
		})
		.optional(),
	hourlyRevenue: z.coerce
		.number()
		.min(0, {
			message: "小时收益必须是非负数",
		})
		.optional(),
});

export interface AgentEditModalProps {
	agent: AgentListItem | null;
	open: boolean;
	onClose: () => void;
	onSuccess?: (agent: AgentListItem) => void;
}

export function AgentEditModal({
	agent,
	open,
	onClose,
	onSuccess,
}: AgentEditModalProps) {
	const {
		loading: detailLoading,
		agent: agentDetail,
		fetchAgentDetail,
	} = useAgentDetail();
	const { loading: editLoading, updateAgent } = useAgentEdit();

	const loading = detailLoading || editLoading;

	// 初始化表单
	const form = useForm<z.infer<typeof formSchema>>({
		resolver: zodResolver(formSchema) as any,
		defaultValues: {
			id: "",
			name: "",
			role: "",
			teamQuota: 0,
			joinFee: 0,
			hourlyRevenue: 0,
		},
	});

	// 获取代理商详情
	useEffect(() => {
		if (open && agent) {
			fetchAgentDetail(agent.id);
		}
	}, [open, agent, fetchAgentDetail]);

	// 填充表单数据
	useEffect(() => {
		if (agentDetail) {
			form.reset({
				id: agentDetail.id,
				name: agentDetail.name,
				role: agentDetail.role || "",
				teamQuota: agentDetail.teamQuota || 0,
				joinFee: agentDetail.joinFee !== null ? agentDetail.joinFee : 0,
				hourlyRevenue:
					agentDetail.hourlyRevenue !== null
						? agentDetail.hourlyRevenue
						: 0,
			});
		}
	}, [agentDetail, form]);

	// 处理提交
	const onSubmit = useCallback(
		async (values: z.infer<typeof formSchema>) => {
			try {
				const result = await updateAgent({
					id: values.id,
					name: values.name,
					role: values.role as AgentRole | undefined,
					teamQuota: values.teamQuota,
					joinFee: values.joinFee,
					hourlyRevenue: values.hourlyRevenue,
				});

				if (result) {
					// 将API返回的详情转换为列表项格式
					const agentListItem: AgentListItem = {
						id: result.id,
						userId: result.userId,
						name: result.name,
						role: result.role,
						email: result.email,
						phone: result.phone,
						status: result.status,
						teamSize: result.teamSize,
						performance: result.monthSales
							? result.monthSales / 100
							: result.performance || 0,
						avatar: null,
						hasChildren: result.parent !== null,
						createdAt: result.createdAt,
						parentId: result.parentId,
						path: result.path,
					};

					onSuccess?.(agentListItem);
					onClose();
				}
			} catch (error) {
				toast.error("更新代理商信息失败", {
					description:
						error instanceof Error ? error.message : "未知错误",
				});
			}
		},
		[updateAgent, onSuccess, onClose],
	);

	return (
		<Dialog open={open} onOpenChange={(open) => !open && onClose()}>
			<DialogContent className="max-w-2xl">
				<DialogHeader>
					<DialogTitle className="text-2xl font-semibold text-[#D4B485]">
						编辑代理商
					</DialogTitle>
				</DialogHeader>

				<Form {...form}>
					<form
						onSubmit={form.handleSubmit(onSubmit)}
						className="space-y-4"
					>
						<FormField
							control={form.control}
							name="name"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="text-[#D4B485]">
										姓名
									</FormLabel>
									<FormControl>
										<Input
											placeholder="请输入代理商姓名"
											{...field}
											className={cn(
												"bg-[#1E2023]/50",
												"border-[#D4B485]/20",
												"text-[#D4B485]",
												"placeholder:text-[#D4B485]/40",
												"focus:border-[#D4B485]/40",
												"focus:ring-[#D4B485]/20",
											)}
										/>
									</FormControl>
									<FormMessage className="text-red-500" />
								</FormItem>
							)}
						/>

						<div className="grid grid-cols-2 gap-4">
							<FormField
								control={form.control}
								name="role"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-[#D4B485]">
											角色
										</FormLabel>
										<Select
											disabled
											onValueChange={field.onChange}
											value={field.value}
										>
											<FormControl>
												<SelectTrigger
													className={cn(
														"bg-[#1E2023]/50",
														"border-[#D4B485]/20",
														"text-[#D4B485]",
														"focus:border-[#D4B485]/40",
														"focus:ring-[#D4B485]/20",
													)}
												>
													<SelectValue placeholder="选择角色" />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												{Object.entries(
													ROLE_LABELS,
												).map(([value, label]) => (
													<SelectItem
														key={value}
														value={value}
														className="text-[#D4B485] focus:bg-[#D4B485]/10"
													>
														{label}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
										<FormMessage className="text-red-500" />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="teamQuota"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-[#D4B485]">
											团队名额
										</FormLabel>
										<FormControl>
											<Input
												type="number"
												placeholder="请输入团队名额"
												{...field}
												className={cn(
													"bg-[#1E2023]/50",
													"border-[#D4B485]/20",
													"text-[#D4B485]",
													"placeholder:text-[#D4B485]/40",
													"focus:border-[#D4B485]/40",
													"focus:ring-[#D4B485]/20",
												)}
											/>
										</FormControl>
										<FormMessage className="text-red-500" />
									</FormItem>
								)}
							/>
						</div>

						<div className="grid grid-cols-2 gap-4">
							<FormField
								control={form.control}
								name="joinFee"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-[#D4B485]">
											加盟费(元)
										</FormLabel>
										<FormControl>
											<Input
												type="number"
												placeholder="请输入加盟费"
												{...field}
												className={cn(
													"bg-[#1E2023]/50",
													"border-[#D4B485]/20",
													"text-[#D4B485]",
													"placeholder:text-[#D4B485]/40",
													"focus:border-[#D4B485]/40",
													"focus:ring-[#D4B485]/20",
												)}
											/>
										</FormControl>
										<FormMessage className="text-red-500" />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="hourlyRevenue"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-[#D4B485]">
											小时收益(元)
										</FormLabel>
										<FormControl>
											<Input
												type="number"
												placeholder="请输入小时收益"
												{...field}
												className={cn(
													"bg-[#1E2023]/50",
													"border-[#D4B485]/20",
													"text-[#D4B485]",
													"placeholder:text-[#D4B485]/40",
													"focus:border-[#D4B485]/40",
													"focus:ring-[#D4B485]/20",
												)}
											/>
										</FormControl>
										<FormMessage className="text-red-500" />
									</FormItem>
								)}
							/>
						</div>

						<DialogFooter className="mt-6">
							<Button
								type="button"
								variant="outline"
								onClick={onClose}
								className={cn(
									"border-[#D4B485]/20",
									"text-[#D4B485]",
									"hover:bg-[#D4B485]/10",
									"hover:text-[#D4B485]",
								)}
								disabled={loading}
							>
								取消
							</Button>
							<Button
								type="submit"
								className={cn(
									"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
									"text-white",
									"border-none",
									"shadow-[0_8px_16px_-4px_rgba(212,180,133,0.3)]",
									"hover:shadow-[0_12px_20px_-4px_rgba(212,180,133,0.4)]",
									"transition-all duration-200",
								)}
								disabled={loading}
							>
								{loading ? "保存中..." : "保存更改"}
							</Button>
						</DialogFooter>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
