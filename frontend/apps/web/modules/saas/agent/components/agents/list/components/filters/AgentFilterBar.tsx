"use client";

import type { Agent<PERSON><PERSON> } from "@prisma/client";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { cn } from "@ui/lib";
import type { AgentFilterOptions } from "../../types";

// 角色名称映射
const _roleNameMap: Record<AgentRole, string> = {
	ADMIN: "管理员",
	BRANCH: "分公司",
	DIRECTOR: "联席董事",
	PARTNER: "合伙人",
	SALES: "超级个体",
};

export interface AgentFilterBarProps {
	onChange: (filters: AgentFilterOptions) => void;
	value?: Partial<AgentFilterOptions>;
	className?: string;
}

const roleOptions = [
	{ value: "ALL", label: "全部角色" },
	{ value: "ADMIN", label: "管理员" },
	{ value: "BRANCH", label: "分公司" },
	{ value: "DIRECTOR", label: "联席董事" },
	{ value: "PARTNER", label: "合伙人" },
	{ value: "SALES", label: "超级个体" },
];

const statusOptions = [
	{ value: "ALL", label: "全部状态" },
	{ value: "ACTIVE", label: "正常" },
	{ value: "DISABLED", label: "禁用" },
	{ value: "PENDING", label: "待审核" },
];

export function AgentFilterBar({
	onChange,
	value = {},
	className,
}: AgentFilterBarProps) {
	return (
		<div
			className={cn(
				"flex items-center gap-4 p-4",
				"bg-white/5 backdrop-blur-sm",
				"border border-white/10 rounded-lg",
				className,
			)}
		>
			<Input
				placeholder="搜索代理商..."
				value={value.searchTerm ?? ""}
				onChange={(e) =>
					onChange({
						searchTerm: e.target.value,
						role: value.role ?? "ALL",
						status: value.status ?? "ALL",
					})
				}
				className={cn(
					"max-w-xs",
					"bg-[#1E2023]/50",
					"border-[#D4B485]/20",
					"text-[#D4B485]",
					"placeholder:text-[#D4B485]/40",
					"focus:border-[#D4B485]/40",
					"focus:ring-[#D4B485]/20",
				)}
			/>
			<Select
				value={value.role ?? "ALL"}
				onValueChange={(role) =>
					onChange({
						searchTerm: value.searchTerm ?? "",
						role: role as AgentRole | "ALL",
						status: value.status ?? "ALL",
					})
				}
			>
				<SelectTrigger className="w-[180px]">
					<SelectValue placeholder="选择角色" />
				</SelectTrigger>
				<SelectContent>
					{roleOptions.map((option) => (
						<SelectItem key={option.value} value={option.value}>
							{option.label}
						</SelectItem>
					))}
				</SelectContent>
			</Select>
			<Select
				value={value.status ?? "ALL"}
				onValueChange={(status) =>
					onChange({
						searchTerm: value.searchTerm ?? "",
						role: value.role ?? "ALL",
						status,
					})
				}
			>
				<SelectTrigger className="w-[180px]">
					<SelectValue placeholder="选择状态" />
				</SelectTrigger>
				<SelectContent>
					{statusOptions.map((option) => (
						<SelectItem key={option.value} value={option.value}>
							{option.label}
						</SelectItem>
					))}
				</SelectContent>
			</Select>
			<Button
				variant="outline"
				onClick={() =>
					onChange({
						searchTerm: "",
						role: "ALL",
						status: "ALL",
					})
				}
			>
				重置
			</Button>
		</div>
	);
}
