"use client";

import type { Agent<PERSON><PERSON> } from "@prisma/client";
import { Card } from "@ui/components/card";
import { cn } from "@ui/lib";
import {
	Award,
	BarChart2,
	Building2,
	ChevronDown,
	ChevronRight,
	Handshake,
	Settings,
} from "lucide-react";
import { useCallback } from "react";
import type { AgentNode } from "../../types";
import { ROLE_LABELS } from "../../types";

// 角色颜色映射
const _roleColors = {
	ADMIN: "#ff4d4f",
	BRANCH: "#1890ff",
	DIRECTOR: "#52c41a",
	PARTNER: "#722ed1",
	SALES: "#faad14",
};

// 角色名称映射
export const roleNameMap: Record<AgentRole, string> = {
	ADMIN: "管理员",
	BRANCH: "分公司",
	DIRECTOR: "联席董事",
	PARTNER: "合伙人",
	SALES: "超级个体",
};

// 角色图标映射
const RoleIcon: Record<
	AgentRole,
	React.ComponentType<React.SVGProps<SVGSVGElement>>
> = {
	ADMIN: Settings,
	BRANCH: Building2,
	DIRECTOR: Award,
	PARTNER: Handshake,
	SALES: BarChart2,
};

export interface AgentNodeProps {
	agent: AgentNode;
	onClick?: (agent: AgentNode) => void;
	isExpanded?: boolean;
	onToggleExpand?: () => void;
	isHighlighted?: boolean;
	zoomLevel?: number;
	isFullscreen?: boolean;
}

// 单个代理节点组件
export function AgentNodeComponent({
	agent,
	onClick,
	isExpanded,
	onToggleExpand,
	isHighlighted,
	isFullscreen = false,
}: AgentNodeProps) {
	const handleClick = useCallback(
		(e: React.MouseEvent) => {
			e.stopPropagation();
			onClick?.(agent);
		},
		[agent, onClick],
	);

	const handleExpandClick = useCallback(
		(e: React.MouseEvent) => {
			e.stopPropagation();
			onToggleExpand?.();
		},
		[onToggleExpand],
	);

	const hasChildren = agent.children && agent.children.length > 0;
	const IconComponent = RoleIcon[agent.role];

	// 全屏模式下的宽度调整
	const cardWidth = isFullscreen ? "w-[220px]" : "w-[180px]";

	return (
		<div className="flex flex-col items-center">
			<Card
				onClick={handleClick}
				className={cn(
					cardWidth,
					"p-3",
					"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
					"border border-[#D4B485]/20",
					"group",
					"hover:border-[#D4B485]/40",
					"transition-all duration-300",
					"relative overflow-hidden",
					"cursor-pointer",
					isHighlighted &&
						"ring-2 ring-[#D4B485] shadow-lg shadow-[#D4B485]/20",
					{
						"border-l-[3px] border-l-[#ff4d4f]":
							agent.role === "ADMIN",
						"border-l-[3px] border-l-[#1890ff]":
							agent.role === "BRANCH",
						"border-l-[3px] border-l-[#52c41a]":
							agent.role === "DIRECTOR",
						"border-l-[3px] border-l-[#722ed1]":
							agent.role === "PARTNER",
						"border-l-[3px] border-l-[#faad14]":
							agent.role === "SALES",
					},
				)}
			>
				<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />

				<div className="relative">
					<div
						className={cn("text-sm font-medium")}
						style={{
							background:
								"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
							WebkitBackgroundClip: "text",
							WebkitTextFillColor: "transparent",
							backgroundClip: "text",
							textShadow:
								"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
						}}
					>
						{agent.name}
					</div>

					<div className="mt-2 flex items-center justify-between">
						<span
							className={cn(
								"inline-flex items-center gap-1 px-2 py-0.5 text-xs rounded-sm",
								{
									"bg-[#ff4d4f]/20 text-[#ff4d4f]":
										agent.role === "ADMIN",
									"bg-[#1890ff]/20 text-[#1890ff]":
										agent.role === "BRANCH",
									"bg-[#52c41a]/20 text-[#52c41a]":
										agent.role === "DIRECTOR",
									"bg-[#722ed1]/20 text-[#722ed1]":
										agent.role === "PARTNER",
									"bg-[#faad14]/20 text-[#faad14]":
										agent.role === "SALES",
								},
							)}
						>
							<IconComponent className="h-3 w-3" />
							{ROLE_LABELS[agent.role]}
						</span>
						{agent.teamSize > 0 && (
							<span className="text-xs text-[#D4B485]/40">
								{agent.teamSize}人
							</span>
						)}
					</div>
				</div>
			</Card>

			{hasChildren && (
				<button
					type="button"
					onClick={handleExpandClick}
					className={cn(
						"mt-1 rounded-full p-1",
						"bg-[#1A1C1E]/80 hover:bg-[#1A1C1E]",
						"border border-[#D4B485]/20 hover:border-[#D4B485]/40",
						"text-[#D4B485]/60 hover:text-[#D4B485]",
						"transition-all duration-300",
					)}
				>
					{isExpanded ? (
						<ChevronDown className="h-3 w-3" />
					) : (
						<ChevronRight className="h-3 w-3" />
					)}
				</button>
			)}
		</div>
	);
}
