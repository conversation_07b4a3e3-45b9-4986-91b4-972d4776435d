import type { AgentRole } from "@prisma/client";

export interface RoleRule {
	role: AgentRole;
	roleName: string;
	quotaCost: number; // 消耗名额数
	canDevelopRoles: AgentRole[]; // 可以发展的角色
	benefits: string[]; // 权益列表
	description: string; // 角色描述
	minTeamQuota: number; // 最小团队名额要求
}

// 代理商角色规则配置
export const ROLE_RULES: Record<AgentRole, RoleRule> = {
	BRANCH: {
		role: "BRANCH",
		roleName: "分公司",
		quotaCost: 1020,
		canDevelopRoles: ["DIRECTOR", "PARTNER", "SALES"],
		benefits: [
			"9000AI智能体城市交付中心",
			"C端产品直接销售分润约40%",
			"推荐超级个体分润80%(7840元)",
			"推荐合伙人分润60%(41880元)",
			"推荐联席董事分润40%(119200元)",
			"推荐分公司分润20%(40万)",
			"交付收益20+20+20元/小时",
		],
		description: "附带1020个个体或72个合伙人或11个联席董事名额",
		minTeamQuota: 1020,
	},
	DIRECTOR: {
		role: "DIRECTOR",
		roleName: "联席董事",
		quotaCost: 76,
		canDevelopRoles: ["PARTNER", "SALES"],
		benefits: [
			"9000AI智能体战略运营权",
			"C端产品直接销售分润约40%",
			"推荐超级个体分润60%(5880元)",
			"推荐合伙人分润40%(27920元)",
			"推荐联席董事分润20%(59600元)",
			"交付收益20+20元/小时",
		],
		description: "附带76个个体或7个合伙人名额",
		minTeamQuota: 76,
	},
	PARTNER: {
		role: "PARTNER",
		roleName: "高级合伙人",
		quotaCost: 12,
		canDevelopRoles: ["SALES"],
		benefits: [
			"9000AI智能体招商运营权",
			"C端产品直接销售分润约40%",
			"推荐超级个体分润40%(3920元)",
			"推荐合伙人分润20%(13960元)",
			"交付收益20元/小时",
		],
		description: "附带12个超级个体名额",
		minTeamQuota: 12,
	},
	SALES: {
		role: "SALES",
		roleName: "超级个体",
		quotaCost: 1,
		canDevelopRoles: [],
		benefits: [
			"9000AI智能体分销权",
			"C端产品直接销售分润约40%",
			"推荐超级个体分润20%(1960元)",
		],
		description: "基础代理商角色",
		minTeamQuota: 1,
	},
	ADMIN: {
		role: "ADMIN",
		roleName: "管理员",
		quotaCost: 0,
		canDevelopRoles: ["BRANCH", "DIRECTOR", "PARTNER", "SALES"],
		benefits: ["系统最高权限"],
		description: "系统管理员",
		minTeamQuota: 0,
	},
};

// 获取代理商角色规则
export function getRoleRule(role: AgentRole): RoleRule {
	return ROLE_RULES[role];
}

// 获取当前角色可以开通的下级角色
export function getAvailableRoles(currentRole: AgentRole): AgentRole[] {
	return ROLE_RULES[currentRole].canDevelopRoles;
}

// 检查是否有足够的名额
export function hasEnoughQuota(
	currentQuota: number,
	targetRole: AgentRole,
): {
	isEnough: boolean;
	message?: string;
} {
	const rule = ROLE_RULES[targetRole];
	if (currentQuota < rule.quotaCost) {
		return {
			isEnough: false,
			message: `开通${rule.roleName}需要${rule.quotaCost}个名额，当前剩余${currentQuota}个名额`,
		};
	}
	return { isEnough: true };
}
