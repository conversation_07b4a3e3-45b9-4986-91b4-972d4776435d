"use client";

import {
	Di<PERSON>,
	<PERSON>alogContent,
	<PERSON><PERSON>Header,
	DialogTitle,
} from "@ui/components/dialog";
import type { AgentListItem } from "../../types";

export interface ConsumerListModalProps {
	agent: AgentListItem | null;
	open: boolean;
	onClose: () => void;
}

export function ConsumerListModal({
	agent,
	open,
	onClose,
}: ConsumerListModalProps) {
	if (!agent) {
		return null;
	}

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent>
				<DialogHeader>
					<DialogTitle>{agent.name} 的消费者列表</DialogTitle>
				</DialogHeader>
				<div className="py-4">
					{/* TODO: 实现消费者列表 */}
					<p className="text-[#D4B485]/60">消费者列表功能开发中...</p>
				</div>
			</DialogContent>
		</Dialog>
	);
}
