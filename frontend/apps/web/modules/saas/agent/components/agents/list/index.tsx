"use client";

import type { Agent<PERSON><PERSON> } from "@prisma/client";
import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { Network } from "lucide-react";
import { useSearchParams } from "next/navigation";
import { useCallback, useMemo, useState } from "react";
import { toast } from "sonner";
import { AgentList } from "./features/list";
import {
	AgentDetailModal,
	AgentEditModal,
	ToggleAgentStatusModal,
} from "./features/modals";
import { ConsumerListModal } from "./features/modals/ConsumerListModal";
import type { AgentListItem, AgentListQuery } from "./types";

export interface AgentManagementProps {
	className?: string;
}

export function AgentManagement({ className }: AgentManagementProps) {
	const [selectedAgentId, setSelectedAgentId] = useState<string | null>(null);
	const [showDetailModal, setShowDetailModal] = useState(false);
	const [showEditModal, setShowEditModal] = useState(false);
	const [showDisableModal, setShowDisableModal] = useState(false);
	const [showConsumerModal, setShowConsumerModal] = useState(false);
	const [selectedAgent, setSelectedAgent] = useState<AgentListItem | null>(
		null,
	);

	// 添加刷新和更新函数的引用
	const [refreshAgentList, setRefreshAgentList] = useState<
		(() => void) | null
	>(null);
	const [updateAgentInList, setUpdateAgentInList] = useState<
		((agent: AgentListItem) => void) | null
	>(null);

	// 获取URL查询参数
	const searchParams = useSearchParams();
	const query = useMemo<AgentListQuery>(
		() => ({
			page: Number(searchParams?.get("page")) || 1,
			pageSize: 10,
			searchTerm: searchParams?.get("searchTerm") || undefined,
			role:
				(searchParams?.get("role") as AgentRole | undefined) ||
				undefined,
			status: searchParams?.get("status") || undefined,
		}),
		[searchParams],
	);

	// 处理查看详情
	const handleViewDetails = useCallback((agent: AgentListItem) => {
		setSelectedAgentId(agent.id);
		setShowDetailModal(true);
	}, []);

	// 处理编辑代理商
	const handleEditAgent = useCallback((agent: AgentListItem) => {
		setSelectedAgent(agent);
		setShowEditModal(true);
	}, []);

	// 处理禁用/启用代理商
	const handleToggleAgentStatus = useCallback((agent: AgentListItem) => {
		setSelectedAgent(agent);
		setShowDisableModal(true);
	}, []);

	// 处理查看消费者列表
	const handleViewConsumers = useCallback((agent: AgentListItem) => {
		setSelectedAgent(agent);
		setShowConsumerModal(true);
	}, []);

	// 编辑成功后刷新列表
	const handleEditSuccess = useCallback(
		(updatedAgent: AgentListItem) => {
			// 如果更新函数可用，直接更新列表中的单个代理商
			if (updateAgentInList) {
				updateAgentInList(updatedAgent);
				toast.success("代理商信息已更新");
				return;
			}

			// 备用方案：刷新整个列表
			if (refreshAgentList) {
				refreshAgentList();
			} else {
				// 最后的备选：完整API请求
				// fetchAgents(query); // 这里的 fetchAgents 已经不存在
			}
			toast.success("代理商信息已更新");
		},
		[updateAgentInList, refreshAgentList, query],
	);

	// 禁用成功后刷新列表
	const handleDisableSuccess = useCallback(() => {
		// 直接刷新整个列表
		if (refreshAgentList) {
			refreshAgentList();
		} else {
			// fetchAgents(query); // 这里的 fetchAgents 已经不存在
		}
		toast.success("代理商已禁用");
	}, [refreshAgentList, query]);

	// 处理 AgentList 组件提供的刷新方法
	const handleRefreshReady = useCallback((refreshFn: () => void) => {
		setRefreshAgentList(() => refreshFn);
	}, []);

	// 处理 AgentList 组件提供的更新方法
	const handleUpdateAgentReady = useCallback(
		(updateFn: (agent: AgentListItem) => void) => {
			setUpdateAgentInList(() => updateFn);
		},
		[],
	);

	return (
		<div className={cn("space-y-6 p-6", className)}>
			{/* 标题区域 */}
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.1 }}
				className="relative mb-6"
			>
				<div className="flex items-center gap-4">
					<div
						className={cn(
							"flex h-14 w-14 shrink-0 items-center justify-center",
							"rounded-xl bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
							"ring-1 ring-[#D4B485]/20",
							"shadow-[0_0_0_8px_rgba(212,180,133,0.03)]",
							"transform-gpu transition-transform duration-300",
							"hover:scale-105 hover:shadow-[0_0_0_10px_rgba(212,180,133,0.05)]",
						)}
					>
						<Network className="h-7 w-7 text-[#D4B485]" />
					</div>
					<div className="space-y-1">
						<h2
							className={cn(
								"font-semibold text-3xl",
								zywhFont.className,
								"leading-none tracking-[0.05em]",
							)}
							style={{
								background:
									"linear-gradient(135deg, rgba(229,201,165,0.95) 0%, rgba(212,180,133,0.9) 50%, rgba(176,137,104,0.85) 100%)",
								WebkitBackgroundClip: "text",
								WebkitTextFillColor: "transparent",
							}}
						>
							代理商管理
						</h2>
						<p className="text-sm text-muted-foreground">
							查看和管理代理商信息，维护代理商层级关系
						</p>
					</div>
				</div>
			</motion.div>

			{/* 代理商列表 */}
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.2 }}
			>
				<AgentList
					onViewDetails={handleViewDetails}
					onEdit={handleEditAgent}
					onDisable={handleToggleAgentStatus}
					onViewConsumers={handleViewConsumers}
					onRefreshReady={handleRefreshReady}
					onUpdateAgentReady={handleUpdateAgentReady}
				/>
			</motion.div>

			{/* 详情模态框 */}
			<AgentDetailModal
				agentId={selectedAgentId}
				open={showDetailModal}
				onClose={() => setShowDetailModal(false)}
			/>

			{/* 编辑模态框 */}
			<AgentEditModal
				agent={selectedAgent}
				open={showEditModal}
				onClose={() => {
					setShowEditModal(false);
					setSelectedAgent(null);
				}}
				onSuccess={handleEditSuccess}
			/>

			{/* 状态切换模态框 */}
			<ToggleAgentStatusModal
				agent={selectedAgent}
				open={showDisableModal}
				onClose={() => {
					setShowDisableModal(false);
					setSelectedAgent(null);
				}}
				onSuccess={handleDisableSuccess}
			/>

			{/* 消费者列表模态框 */}
			<ConsumerListModal
				agent={selectedAgent}
				open={showConsumerModal}
				onClose={() => {
					setShowConsumerModal(false);
					setSelectedAgent(null);
				}}
			/>
		</div>
	);
}

// 只导出AgentManagement组件
export default AgentManagement;
