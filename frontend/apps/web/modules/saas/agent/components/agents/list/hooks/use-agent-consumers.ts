import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";

// 消费者列表项类型
export interface ConsumerListItem {
	id: string;
	name: string;
	email: string;
	phone: string;
	status: string;
	agentId: string;
	agentName: string;
	lastLoginAt: string | null;
	createdAt: string;
	updatedAt: string;
}

// 查询参数类型
export interface ConsumerListQuery {
	page?: number;
	pageSize?: number;
	keyword?: string;
	status?: string;
	sortBy?: string;
	sortOrder?: "asc" | "desc";
	agentId: string;
}

// API响应类型
interface ApiResponse {
	code: number;
	message: string;
	data?: {
		items: ConsumerListItem[];
		total: number;
		page: number;
		pageSize: number;
		totalPages: number;
	};
}

export function useAgentConsumers() {
	const [loading, setLoading] = useState(false);
	const [consumers, setConsumers] = useState<ConsumerListItem[]>([]);
	const [total, setTotal] = useState(0);
	const [currentPage, setCurrentPage] = useState(1);
	const [totalPages, setTotalPages] = useState(1);

	const fetchConsumers = useCallback(async (query: ConsumerListQuery) => {
		setLoading(true);

		try {
			const response =
				await apiClient.v1.agent.agents.list.consumers.$get({
					query: {
						agentId: query.agentId,
						page: query.page || 1,
						pageSize: query.pageSize || 10,
						keyword: query.keyword,
						status: query.status,
						sortBy: query.sortBy || "createdAt",
						sortOrder: query.sortOrder || "desc",
					},
				});

			const responseText = await response.text();

			let result: ApiResponse;
			try {
				result = JSON.parse(responseText);
			} catch (_parseError) {
				throw new Error("响应解析失败");
			}

			if (response.ok && result.data) {
				setConsumers(result.data.items);
				setTotal(result.data.total);
				setCurrentPage(result.data.page);
				setTotalPages(result.data.totalPages);
			} else {
				const errorMsg = result.message;

				toast.error(`获取消费者列表失败: ${errorMsg}`);
			}
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : String(error);

			toast.error(`获取消费者列表失败: ${errorMessage}`);
		} finally {
			setLoading(false);
		}
	}, []);

	return {
		loading,
		consumers,
		total,
		currentPage,
		totalPages,
		fetchConsumers,
	};
}
