"use client";

import type { Agent<PERSON><PERSON> } from "@prisma/client";
import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { Info } from "lucide-react";
import { getAvailableRoles, getRoleRule } from "../config/role-rules";

interface SelectRoleProps {
	currentRole: AgentRole;
	onNext: (role: AgentRole) => void;
	isLoading?: boolean;
}

export function SelectRole({
	currentRole,
	onNext,
	isLoading,
}: SelectRoleProps) {
	// 获取可选角色
	const availableRoles = getAvailableRoles(currentRole);

	return (
		<div className="space-y-8">
			{/* 大标题 */}
			<div className="space-y-2 text-center">
				<h3
					className={cn("text-2xl font-semibold", zywhFont.className)}
					style={{
						background:
							"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
						WebkitBackgroundClip: "text",
						WebkitTextFillColor: "transparent",
						backgroundClip: "text",
						textShadow:
							"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
					}}
				>
					请选择代理商角色
				</h3>
				<p className="text-[#D4B485]/60">
					点击下方卡片，选择要开通的代理商角色，不同角色拥有不同的权限和收益
				</p>
			</div>

			{availableRoles.length === 0 ? (
				// 无可选角色时显示提示
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					className="p-6 rounded-lg bg-[#D4B485]/5 border border-[#D4B485]/20 text-center"
				>
					<div className="flex flex-col items-center gap-4">
						<div className="p-3 rounded-full bg-[#D4B485]/10">
							<Info className="h-8 w-8 text-[#D4B485]/60" />
						</div>
						<div className="space-y-2">
							<h4 className="text-lg font-medium text-[#D4B485]">
								暂无可添加的代理商角色
							</h4>
							<p className="text-[#D4B485]/60 max-w-md mx-auto">
								您当前的角色无法添加下级代理商，或者您需要升级为更高级别的代理商才能添加下级。
							</p>
						</div>
					</div>
				</motion.div>
			) : (
				// 角色列表 - 使用flex布局使卡片居中
				<div className="flex flex-wrap justify-center gap-6">
					{availableRoles.map((role, index) => {
						const rule = getRoleRule(role);
						return (
							<motion.div
								key={role}
								initial={{ opacity: 0, y: 20 }}
								animate={{ opacity: 1, y: 0 }}
								transition={{ delay: index * 0.1 }}
								className={cn(
									"rounded-lg p-4 lg:p-6", // 调整内边距
									"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
									"border border-[#D4B485]/20",
									"group",
									"hover:border-[#D4B485]/40",
									"transition-all duration-200",
									"relative overflow-hidden",
									"flex flex-col", // 设置弹性布局
									"w-full md:w-[280px]", // 固定宽度，确保一致性
									isLoading
										? "cursor-not-allowed opacity-50"
										: "cursor-pointer",
								)}
								onClick={() => {
									if (!isLoading) {
										onNext(role);
									}
								}}
							>
								{/* 背景装饰 */}
								<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
								<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />

								<div className="relative space-y-3 flex-1">
									{" "}
									{/* 减少垂直间距 */}
									<div className="flex items-center justify-between">
										<div className="space-y-1">
											<div className="flex items-center gap-2">
												<span className="font-medium text-[#D4B485]">
													{rule.roleName}
												</span>
												{/* <span
													className={cn(
														"rounded-full px-2 py-0.5 text-xs",
														"bg-[#D4B485]/10",
														"text-[#D4B485]/60",
													)}
												>
													附带{rule.quotaCost}个超级个体名额
												</span> */}
											</div>
											<div className="text-sm text-[#D4B485]/40">
												{rule.description}
											</div>
										</div>
									</div>
									{/* 权益列表 */}
									<div className="space-y-2 mt-auto">
										{" "}
										{/* 添加mt-auto使权益列表靠下 */}
										<div className="text-sm font-medium text-[#D4B485]">
											角色权益
										</div>
										<div className="flex flex-wrap gap-2">
											{rule.benefits.map((benefit) => (
												<span
													key={benefit}
													className={cn(
														"rounded-full px-2 py-0.5 text-xs",
														"bg-[#D4B485]/5",
														"text-[#D4B485]/60",
														"ring-1 ring-[#D4B485]/10",
													)}
												>
													{benefit}
												</span>
											))}
										</div>
									</div>
								</div>
							</motion.div>
						);
					})}
				</div>
			)}
		</div>
	);
}
