"use client";

import { <PERSON><PERSON> } from "@ui/components/button";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import { Textarea } from "@ui/components/textarea";
import { cn } from "@ui/lib";
import { AlertTriangle, RefreshCcw } from "lucide-react";
import { useCallback, useId, useState } from "react";
import { useAgentDisable } from "../../hooks/use-agent-disable";
import { useAgentEnable } from "../../hooks/use-agent-enable";
import type { AgentListItem } from "../../types/index";

export interface ToggleAgentStatusModalProps {
	agent: AgentListItem | null;
	open: boolean;
	onClose: () => void;
	onSuccess?: () => void;
}

export function ToggleAgentStatusModal({
	agent,
	open,
	onClose,
	onSuccess,
}: ToggleAgentStatusModalProps) {
	const [reason, setReason] = useState("");
	const { loading: disableLoading, disableAgent } = useAgentDisable();
	const { loading: enableLoading, enableAgent } = useAgentEnable();
	const id = useId();

	const loading = disableLoading || enableLoading;
	const isDisabled = agent?.status === "disabled";

	const handleToggleStatus = useCallback(async () => {
		if (!agent) {
			return;
		}

		let success = false;

		if (isDisabled) {
			// 启用代理商
			success = await enableAgent({
				id: agent.id,
				reason: reason.trim() || undefined,
			});
		} else {
			// 禁用代理商
			success = await disableAgent({
				id: agent.id,
				reason: reason.trim() || undefined,
			});
		}

		if (success) {
			onSuccess?.();
			onClose();
		}
	}, [
		agent,
		reason,
		isDisabled,
		disableAgent,
		enableAgent,
		onSuccess,
		onClose,
	]);

	if (!agent) {
		return null;
	}

	return (
		<Dialog open={open} onOpenChange={(open) => !open && onClose()}>
			<DialogContent
				className="max-w-md"
				onCloseAutoFocus={() => {
					if (!open) {
						onClose();
					}
				}}
			>
				<DialogHeader>
					<div
						className={cn(
							"mx-auto flex h-12 w-12 items-center justify-center rounded-full",
							isDisabled ? "bg-green-100" : "bg-red-100",
						)}
					>
						{isDisabled ? (
							<RefreshCcw className="h-6 w-6 text-green-600" />
						) : (
							<AlertTriangle className="h-6 w-6 text-red-600" />
						)}
					</div>
					<DialogTitle className="text-center text-xl font-semibold text-[#D4B485]">
						{isDisabled ? "启用代理商确认" : "禁用代理商确认"}
					</DialogTitle>
					<DialogDescription className="text-center text-[#D4B485]/70">
						您确定要{isDisabled ? "启用" : "禁用"}代理商{" "}
						<span className="font-medium text-[#D4B485]">
							{agent.name}
						</span>{" "}
						吗？
						{!isDisabled &&
							"禁用后该代理商将无法登录系统或进行任何操作。"}
					</DialogDescription>
				</DialogHeader>

				<div className="mt-4">
					<label
						htmlFor={id}
						className="mb-2 block text-sm font-medium text-[#D4B485]"
					>
						{isDisabled ? "启用" : "禁用"}原因 (可选)
					</label>
					<Textarea
						id={id}
						placeholder={`请输入${isDisabled ? "启用" : "禁用"}原因...`}
						value={reason}
						onChange={(e) => setReason(e.target.value)}
						className={cn(
							"resize-none",
							"bg-[#1E2023]/50",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"placeholder:text-[#D4B485]/40",
							"focus:border-[#D4B485]/40",
							"focus:ring-[#D4B485]/20",
						)}
						rows={3}
					/>
				</div>

				<DialogFooter className="mt-6 gap-3">
					<Button
						type="button"
						variant="outline"
						onClick={onClose}
						className={cn(
							"flex-1",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"hover:bg-[#D4B485]/10",
							"hover:text-[#D4B485]",
						)}
						disabled={loading}
					>
						取消
					</Button>
					<Button
						type="button"
						onClick={handleToggleStatus}
						className={cn(
							"flex-1",
							isDisabled
								? "bg-green-600 hover:bg-green-700"
								: "bg-red-600 hover:bg-red-700",
							"text-white",
							"border-none",
						)}
						disabled={loading}
					>
						{loading
							? isDisabled
								? "启用中..."
								: "禁用中..."
							: isDisabled
								? "确认启用"
								: "确认禁用"}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
