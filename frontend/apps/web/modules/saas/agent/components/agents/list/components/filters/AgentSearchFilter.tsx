"use client";

import type { Agent<PERSON><PERSON> } from "@prisma/client";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { cn } from "@ui/lib";
import { RefreshCcw, Search } from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";

// 角色映射表
const ROLE_LABELS: Record<AgentRole, string> = {
	ADMIN: "管理员",
	BRANCH: "分公司",
	DIRECTOR: "联席董事",
	PARTNER: "合伙人",
	SALES: "超级个体",
};

// 状态选项
const statusOptions = [
	{ value: "all", label: "全部状态" },
	{ value: "active", label: "已激活" },
	{ value: "inactive", label: "未激活" },
	{ value: "suspended", label: "已暂停" },
];

export interface AgentFilterOptions {
	searchTerm?: string;
	role?: AgentRole | "ALL";
	status?: string;
}

export interface AgentSearchFilterProps {
	onFilter: (filters: AgentFilterOptions) => void;
}

export function AgentSearchFilter({ onFilter }: AgentSearchFilterProps) {
	// 搜索和筛选状态
	const [searchTerm, setSearchTerm] = useState("");
	const [role, setRole] = useState<string>("ALL");
	const [status, setStatus] = useState<string>("all");
	const [debouncedSearch, setDebouncedSearch] = useState("");

	// 使用ref追踪过滤条件变化，避免无限循环
	const debouncedSearchRef = useRef(debouncedSearch);
	const roleRef = useRef(role);
	const statusRef = useRef(status);

	// 处理搜索防抖
	useEffect(() => {
		const timer = setTimeout(() => {
			setDebouncedSearch(searchTerm);
		}, 500);

		return () => clearTimeout(timer);
	}, [searchTerm]);

	// 处理快速筛选
	const handleQuickFilter = useCallback(() => {
		onFilter({
			searchTerm: debouncedSearch,
			role: role as AgentRole | "ALL",
			status: status,
		});
	}, [debouncedSearch, onFilter, role, status]);

	// 当防抖搜索词变化时触发筛选
	useEffect(() => {
		// 只有在组件挂载后执行一次，以及debouncedSearch、role或status变化时才执行筛选
		// 使用ref跟踪变化，避免直接依赖debouncedSearch、role和status
		const hasFiltersChanged =
			debouncedSearchRef.current !== debouncedSearch ||
			roleRef.current !== role ||
			statusRef.current !== status;

		// 只有当过滤条件确实发生变化时才调用API
		if (hasFiltersChanged) {
			debouncedSearchRef.current = debouncedSearch;
			roleRef.current = role;
			statusRef.current = status;

			onFilter({
				searchTerm: debouncedSearch,
				role: role as AgentRole | "ALL",
				status: status,
			});
		}
	}, [debouncedSearch, role, status, onFilter]);

	// 重置所有筛选
	const handleReset = () => {
		setSearchTerm("");
		setRole("ALL");
		setStatus("all");
		onFilter({});
	};

	return (
		<div className="w-full space-y-4">
			{/* 搜索和快速筛选栏 */}
			<div className="rounded-lg bg-[#1E2023]/50 p-4">
				<div className="flex flex-wrap items-center gap-4">
					{/* 搜索框 */}
					<div className="relative flex-1">
						<Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-[#D4B485]/40" />
						<Input
							placeholder="搜索代理商名称、邮箱或手机号"
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className={cn(
								"pl-9",
								"bg-[#1E2023]/50",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
								"placeholder:text-[#D4B485]/40",
								"focus:border-[#D4B485]/40",
								"focus:ring-[#D4B485]/20",
							)}
							onKeyDown={(e) => {
								if (e.key === "Enter") {
									handleQuickFilter();
								}
							}}
						/>
					</div>

					{/* 角色筛选 */}
					<Select
						value={role}
						onValueChange={(value) => setRole(value)}
					>
						<SelectTrigger
							className={cn(
								"w-[160px]",
								"bg-[#1E2023]/50",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
								"focus:border-[#D4B485]/40",
								"focus:ring-[#D4B485]/20",
							)}
						>
							<SelectValue placeholder="选择角色" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem
								value="ALL"
								className="text-[#D4B485] focus:bg-[#D4B485]/10"
							>
								全部角色
							</SelectItem>
							{Object.entries(ROLE_LABELS).map(
								([value, label]) => (
									<SelectItem
										key={value}
										value={value}
										className="text-[#D4B485] focus:bg-[#D4B485]/10"
									>
										{label}
									</SelectItem>
								),
							)}
						</SelectContent>
					</Select>

					{/* 状态筛选 */}
					<Select
						value={status}
						onValueChange={(value) => setStatus(value)}
					>
						<SelectTrigger
							className={cn(
								"w-[160px]",
								"bg-[#1E2023]/50",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
								"focus:border-[#D4B485]/40",
								"focus:ring-[#D4B485]/20",
							)}
						>
							<SelectValue placeholder="选择状态" />
						</SelectTrigger>
						<SelectContent>
							{statusOptions.map((option) => (
								<SelectItem
									key={option.value}
									value={option.value}
									className="text-[#D4B485] focus:bg-[#D4B485]/10"
								>
									{option.label}
								</SelectItem>
							))}
						</SelectContent>
					</Select>

					{/* 搜索按钮 */}
					<Button
						type="button"
						className={cn(
							"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
							"text-white",
							"border-none",
							"shadow-[0_8px_16px_-4px_rgba(212,180,133,0.3)]",
							"hover:shadow-[0_12px_20px_-4px_rgba(212,180,133,0.4)]",
							"transition-all duration-200",
						)}
						onClick={handleQuickFilter}
					>
						搜索
					</Button>

					{/* 刷新按钮 */}
					<Button
						variant="outline"
						size="icon"
						className={cn(
							"bg-[#1E2023]/50 border-[#D4B485]/20",
							"text-[#D4B485] hover:text-[#E5C9A5]",
							"hover:bg-[#D4B485]/10",
							"transition duration-200",
							"w-10 h-10",
							"flex items-center justify-center",
						)}
						onClick={handleReset}
					>
						<RefreshCcw className="h-4 w-4" />
					</Button>
				</div>
			</div>
		</div>
	);
}
