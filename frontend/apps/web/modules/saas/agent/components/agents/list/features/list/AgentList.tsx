"use client";

import type { <PERSON><PERSON><PERSON> } from "@prisma/client";
import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Card } from "@ui/components/card";
import { Skeleton } from "@ui/components/skeleton";
import { cn } from "@ui/lib";
import { ChevronLeft, ChevronRight, Filter } from "lucide-react";
import {
	useCallback,
	useEffect,
	useLayoutEffect,
	useRef,
	useState,
} from "react";

import { AgentFilterBar } from "../../components/filters";
import { AgentListItem } from "../../components/items";
import { useAgentList } from "../../hooks";
import type {
	AgentFilterOptions,
	AgentListItem as AgentListItemType,
	AgentListQuery,
} from "../../types";
import { ROLE_WEIGHTS } from "../../types";

// 回调函数类型
type AgentCallback = (agent: AgentListItemType) => void;

// 状态选项
const _statusOptions = [
	{ value: "all", label: "全部状态" },
	{ value: "active", label: "已激活" },
	{ value: "inactive", label: "未激活" },
	{ value: "suspended", label: "已暂停" },
];

export interface AgentListProps {
	onViewDetails?: AgentCallback;
	onEdit?: AgentCallback;
	onDisable?: AgentCallback;
	onViewConsumers?: AgentCallback;
	parentId?: string;
	onRefreshReady?: (refreshFn: () => void) => void;
	onUpdateAgentReady?: (updateFn: (agent: AgentListItemType) => void) => void;
}

export function AgentList({
	onViewDetails,
	onEdit,
	onDisable,
	onViewConsumers,
	parentId,
	onRefreshReady,
	onUpdateAgentReady,
}: AgentListProps) {
	// 查询参数
	const [query, setQuery] = useState<AgentListQuery>({
		page: 1,
		pageSize: 5,
		parentId,
	});

	// 使用ref存储pageSize，避免循环依赖
	const pageSizeRef = useRef(query.pageSize);

	// 是否显示筛选栏
	const [showFilters, setShowFilters] = useState(false);

	// 搜索和筛选状态
	const [searchTerm, setSearchTerm] = useState("");
	const [role, setRole] = useState<string>("ALL");
	const [status, setStatus] = useState<string>("all");
	const [debouncedSearch, setDebouncedSearch] = useState("");

	// 使用hooks获取数据
	const {
		loading: listLoading,
		agents,
		total,
		currentPage,
		totalPages,
		fetchAgents,
		setAgents,
	} = useAgentList();

	// 使用 ref 存储 fetchAgents，避免触发 useEffect
	const fetchAgentsRef = useRef(fetchAgents);
	useLayoutEffect(() => {
		fetchAgentsRef.current = fetchAgents;
	}, [fetchAgents]);

	// 初始化加载
	useEffect(() => {
		const initialQuery = {
			page: 1,
			pageSize: 5,
			parentId,
		};
		setQuery(initialQuery);
		fetchAgentsRef.current(initialQuery);
	}, [parentId]);

	// 处理搜索防抖
	useEffect(() => {
		const timer = setTimeout(() => {
			setDebouncedSearch(searchTerm);
		}, 500);

		return () => clearTimeout(timer);
	}, [searchTerm]);

	// 处理快速筛选
	const _handleQuickFilter = useCallback(() => {
		const newQuery: AgentListQuery = {
			...query,
			page: 1, // 重置页码
			searchTerm: debouncedSearch,
			role: role as AgentRole | "ALL",
			status: status,
		};
		setQuery(newQuery);
		fetchAgentsRef.current(newQuery);
	}, [query, debouncedSearch, role, status]);

	// 当防抖搜索词变化时触发筛选
	useEffect(() => {
		// 跳过初始化时的请求
		if (debouncedSearch === "" && role === "ALL" && status === "all") {
			return;
		}

		const newQuery: AgentListQuery = {
			page: 1, // 重置页码
			pageSize: pageSizeRef.current,
			searchTerm: debouncedSearch,
			role: role as AgentRole | "ALL",
			status: status,
		};
		setQuery(newQuery);
		fetchAgentsRef.current(newQuery);
	}, [debouncedSearch, role, status]);

	// 更新pageSize ref当query.pageSize变化时
	useEffect(() => {
		pageSizeRef.current = query.pageSize;
	}, [query.pageSize]);

	// 处理高级筛选
	const handleFilter = useCallback(
		(filters: AgentFilterOptions) => {
			const newQuery: AgentListQuery = {
				...query,
				page: 1, // 重置页码
				searchTerm: filters.searchTerm,
				role: filters.role,
				status: filters.status,
			};
			setQuery(newQuery);
			fetchAgentsRef.current(newQuery);
		},
		[query],
	);

	// 处理分页
	const handlePageChange = useCallback(
		(page: number) => {
			const newQuery = { ...query, page };
			setQuery(newQuery);
			fetchAgentsRef.current(newQuery);
		},
		[query],
	);

	// 重置所有筛选
	const _handleReset = () => {
		setSearchTerm("");
		setRole("ALL");
		setStatus("all");
		const newQuery: AgentListQuery = {
			page: 1,
			pageSize: 5,
		};
		setQuery(newQuery);
		fetchAgentsRef.current(newQuery);
	};

	// 生成页码数组
	const getPageNumbers = () => {
		const pageNumbers = [];
		const maxPagesToShow = 5; // 最多显示的页码数

		if (totalPages <= maxPagesToShow) {
			// 如果总页数小于等于最大显示页码数，显示所有页码
			for (let i = 1; i <= totalPages; i++) {
				pageNumbers.push(i);
			}
		} else {
			// 否则，显示当前页附近的页码
			let startPage = Math.max(
				1,
				currentPage - Math.floor(maxPagesToShow / 2),
			);
			let endPage = startPage + maxPagesToShow - 1;

			if (endPage > totalPages) {
				endPage = totalPages;
				startPage = Math.max(1, endPage - maxPagesToShow + 1);
			}

			for (let i = startPage; i <= endPage; i++) {
				pageNumbers.push(i);
			}
		}

		return pageNumbers;
	};

	// 添加强制刷新列表的方法
	const refreshList = useCallback(() => {
		fetchAgentsRef.current(query);
	}, [query]);

	// 对传入更新后的单个代理商进行处理的方法
	const updateSingleAgent = useCallback(
		(updatedAgent: AgentListItemType) => {
			if (!updatedAgent) {
				return;
			}

			setAgents((prevAgents) =>
				prevAgents.map((agent) =>
					agent.id === updatedAgent.id
						? { ...agent, ...updatedAgent }
						: agent,
				),
			);
		},
		[setAgents],
	);

	// 向父组件提供刷新和更新方法
	useEffect(() => {
		if (onRefreshReady) {
			onRefreshReady(refreshList);
		}

		if (onUpdateAgentReady) {
			onUpdateAgentReady(updateSingleAgent);
		}
	}, [refreshList, updateSingleAgent, onRefreshReady, onUpdateAgentReady]);

	return (
		<div className="w-full space-y-6">
			{/* 标题和操作栏 */}
			<div className="flex items-center justify-between">
				<h1
					className={cn("text-2xl font-bold", zywhFont.className)}
					style={{
						background:
							"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
						WebkitBackgroundClip: "text",
						WebkitTextFillColor: "transparent",
						backgroundClip: "text",
						textShadow:
							"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
					}}
				>
					代理商管理
				</h1>
				<div className="flex items-center gap-2">
					<Button
						variant="outline"
						className={cn(
							"gap-2",
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"hover:bg-[#D4B485]/10",
							showFilters && "bg-[#D4B485]/10",
						)}
						onClick={() => setShowFilters(!showFilters)}
					>
						<Filter className="h-4 w-4" />
						<span>高级筛选</span>
					</Button>
				</div>
			</div>

			{/* 高级筛选栏 */}
			{showFilters && <AgentFilterBar onChange={handleFilter} />}

			{/* 代理商列表 */}
			<div className="space-y-4">
				{listLoading ? (
					// 加载状态
					[
						"skeleton-1",
						"skeleton-2",
						"skeleton-3",
						"skeleton-4",
						"skeleton-5",
					].map((id) => (
						<Skeleton
							key={id}
							className="h-[92px] w-full rounded-lg bg-[#1E2023]/60"
						/>
					))
				) : agents.length > 0 ? (
					<>
						{/* 显示代理商列表 */}
						{[...agents]
							.sort((a, b) => {
								// 首先按角色权重排序
								const roleWeightDiff =
									ROLE_WEIGHTS[a.role] - ROLE_WEIGHTS[b.role];
								if (roleWeightDiff !== 0) {
									return roleWeightDiff;
								}

								// 如果角色相同，按创建时间倒序
								return (
									new Date(b.createdAt).getTime() -
									new Date(a.createdAt).getTime()
								);
							})
							.map((agent, index) => (
								<AgentListItem
									key={agent.id}
									agent={agent}
									index={index}
									onViewDetails={onViewDetails}
									onEdit={onEdit}
									onDisable={onDisable}
									onViewConsumers={onViewConsumers}
								/>
							))}

						{/* 分页控件 */}
						{totalPages > 1 && (
							<div className="flex items-center justify-between pt-4 border-t border-[#D4B485]/10">
								<div className="text-sm text-[#D4B485]/60">
									显示{" "}
									{Math.min(total, (currentPage - 1) * 5 + 1)}
									-{Math.min(total, currentPage * 5)} 条，共{" "}
									{total} 条
								</div>
								<div className="flex items-center space-x-2">
									<Button
										variant="outline"
										size="sm"
										className={cn(
											"bg-[#1A1C1E]",
											"border-[#D4B485]/20",
											"text-[#D4B485]",
											"hover:bg-[#D4B485]/10",
											"h-8 w-8 p-0",
										)}
										disabled={currentPage === 1}
										onClick={() =>
											handlePageChange(currentPage - 1)
										}
									>
										<ChevronLeft className="h-4 w-4" />
									</Button>

									{getPageNumbers().map((page) => (
										<Button
											key={page}
											variant={
												page === currentPage
													? "primary"
													: "outline"
											}
											size="sm"
											className={cn(
												page === currentPage
													? "bg-[#D4B485] text-[#1A1C1E]"
													: "bg-[#1A1C1E] border-[#D4B485]/20 text-[#D4B485] hover:bg-[#D4B485]/10",
												"h-8 w-8 p-0",
											)}
											onClick={() =>
												handlePageChange(page)
											}
										>
											{page}
										</Button>
									))}

									<Button
										variant="outline"
										size="sm"
										className={cn(
											"bg-[#1A1C1E]",
											"border-[#D4B485]/20",
											"text-[#D4B485]",
											"hover:bg-[#D4B485]/10",
											"h-8 w-8 p-0",
										)}
										disabled={currentPage === totalPages}
										onClick={() =>
											handlePageChange(currentPage + 1)
										}
									>
										<ChevronRight className="h-4 w-4" />
									</Button>
								</div>
							</div>
						)}
					</>
				) : (
					// 没有匹配的结果
					<Card
						className={cn(
							"py-16 flex flex-col items-center justify-center text-center",
							"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
							"border border-[#D4B485]/20",
						)}
					>
						<div className="rounded-full bg-[#D4B485]/10 p-4 mb-4">
							<Filter className="h-8 w-8 text-[#D4B485]/40" />
						</div>
						<h3 className="text-lg font-medium text-[#D4B485] mb-1">
							无匹配结果
						</h3>
						<p className="text-[#D4B485]/60 max-w-sm">
							没有找到符合当前筛选条件的代理商。请尝试调整筛选条件。
						</p>
					</Card>
				)}
			</div>
		</div>
	);
}
