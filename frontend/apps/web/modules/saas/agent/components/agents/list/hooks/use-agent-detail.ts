import type { <PERSON><PERSON><PERSON> } from "@prisma/client";
import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";

// 代理商详情
export interface AgentDetail {
	id: string;
	userId: string;
	name: string;
	role: Agent<PERSON><PERSON>;
	email: string;
	phone: string;
	status: string;
	parentId: string | null;
	parent: {
		id: string;
		name: string;
		role: string;
	} | null;
	path: string | null;
	joinFee: number | null;
	hourlyRevenue: number | null;
	teamQuota: number;
	usedTeamQuota: number;
	teamSize: number;
	totalSales: number;
	monthSales: number;
	performance: number;
	createdAt: string;
	updatedAt: string;
}

// API响应类型
interface ApiResponse<_T> {
	code?: number;
	message?: string;
	data?: {
		agent?: AgentDetail;
	};
	error?: string;
}

export function useAgentDetail() {
	const [loading, setLoading] = useState(false);
	const [agent, setAgent] = useState<AgentDetail | null>(null);

	const fetchAgentDetail = useCallback(async (id: string) => {
		setLoading(true);

		try {
			const response = await apiClient.v1.agent.agents.list.detail[
				":id"
			].$get({
				param: { id },
			});

			const responseText = await response.text();

			let result: ApiResponse<unknown>;
			try {
				result = JSON.parse(responseText);
			} catch (_parseError) {
				throw new Error("响应解析失败");
			}

			if (response.ok && result.code === 0) {
				if (!result.data || !result.data.agent) {
					toast.error("获取代理商详情失败: 响应数据格式不正确");
					return;
				}

				const agentData = result.data.agent;

				setAgent(agentData);
			} else {
				const errorMsg =
					result.message || result.error || response.statusText;

				toast.error(`获取代理商详情失败: ${errorMsg}`);
			}
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : String(error);

			toast.error(`获取代理商详情失败: ${errorMessage}`);
		} finally {
			setLoading(false);
		}
	}, []);

	// 重置代理商数据
	const resetAgentDetail = useCallback(() => {
		setAgent(null);
	}, []);

	return {
		loading,
		agent,
		fetchAgentDetail,
		resetAgentDetail,
	};
}
