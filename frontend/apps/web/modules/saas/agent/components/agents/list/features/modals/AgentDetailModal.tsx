"use client";

import { Dialog, DialogContent, DialogTitle } from "@ui/components/dialog";
import { Skeleton } from "@ui/components/skeleton";
import { cn } from "@ui/lib";
import { format } from "date-fns";
import { zhCN } from "date-fns/locale";
import { Award, Building2, Mail, Phone, User2 } from "lucide-react";
import { useEffect } from "react";
import { roleNameMap } from "../../components/items/AgentNode";
import { useAgentDetail } from "../../hooks/use-agent-detail";

export interface AgentDetailModalProps {
	agentId: string | null;
	open: boolean;
	onClose: () => void;
}

export function AgentDetailModal({
	agentId,
	open,
	onClose,
}: AgentDetailModalProps) {
	// 使用hooks获取代理商详情
	const { loading, agent, fetchAgentDetail, resetAgentDetail } =
		useAgentDetail();

	useEffect(() => {
		if (open && agentId) {
			fetchAgentDetail(agentId);
		} else if (!open) {
			// 当模态框关闭时重置状态
			resetAgentDetail();
		}
	}, [open, agentId, fetchAgentDetail, resetAgentDetail]);

	return (
		<Dialog open={open} onOpenChange={(open) => !open && onClose()}>
			<DialogContent
				className="max-w-2xl"
				onCloseAutoFocus={() => {
					// 确保模态框关闭后清空状态
					if (!open) {
						onClose();
					}
				}}
			>
				<DialogTitle className="text-2xl font-semibold text-[#D4B485]">
					代理商详情
				</DialogTitle>
				<div className="relative">
					{/* 移除原来的标题区域 */}
					{loading ? (
						<AgentDetailSkeleton />
					) : agent ? (
						<div className="space-y-6">
							{/* 基本信息 */}
							<div className="space-y-4">
								<h4 className="text-lg font-medium text-[#D4B485]">
									基本信息
								</h4>
								<div className="grid grid-cols-2 gap-4">
									<InfoItem
										icon={<User2 className="h-4 w-4" />}
										label="姓名"
										value={agent.name}
									/>
									<InfoItem
										icon={<Award className="h-4 w-4" />}
										label="角色"
										value={
											agent.role in roleNameMap
												? roleNameMap[
														agent.role as keyof typeof roleNameMap
													]
												: agent.role
										}
									/>
									<InfoItem
										icon={<Mail className="h-4 w-4" />}
										label="邮箱"
										value={agent.email}
									/>
									<InfoItem
										icon={<Phone className="h-4 w-4" />}
										label="电话"
										value={agent.phone}
									/>
									{agent.parent && (
										<InfoItem
											icon={
												<Building2 className="h-4 w-4" />
											}
											label="上级代理"
											value={`${agent.parent.name} (${agent.parent.role in roleNameMap ? roleNameMap[agent.parent.role as keyof typeof roleNameMap] : agent.parent.role})`}
										/>
									)}
								</div>
							</div>

							{/* 团队数据 */}
							<div className="space-y-4">
								<h4 className="text-lg font-medium text-[#D4B485]">
									团队数据
								</h4>
								<div className="grid grid-cols-3 gap-4">
									<DataCard
										label="团队规模"
										value={agent.teamSize}
										unit="人"
									/>
									<DataCard
										label="团队名额"
										value={agent.usedTeamQuota}
										total={agent.teamQuota}
										unit="个"
									/>
									<DataCard
										label="本月业绩"
										value={agent.monthSales / 100}
										unit="元"
									/>
								</div>
							</div>

							{/* 其他信息 */}
							<div className="space-y-4">
								<h4 className="text-lg font-medium text-[#D4B485]">
									其他信息
								</h4>
								<div className="grid grid-cols-2 gap-4">
									<InfoItem
										label="加入时间"
										value={format(
											new Date(agent.createdAt),
											"yyyy年MM月dd日 HH:mm",
											{
												locale: zhCN,
											},
										)}
									/>
									<InfoItem
										label="状态"
										value={
											<span
												className={cn(
													"px-2 py-1 rounded text-sm",
													agent.status === "active"
														? "bg-green-500/10 text-green-500"
														: "bg-red-500/10 text-red-500",
												)}
											>
												{agent.status === "active"
													? "正常"
													: "已禁用"}
											</span>
										}
									/>
									{agent.joinFee !== null && (
										<InfoItem
											label="加盟费"
											value={`${agent.joinFee / 100}元`}
										/>
									)}
									{agent.hourlyRevenue !== null && (
										<InfoItem
											label="每小时收益"
											value={`${agent.hourlyRevenue / 100}元`}
										/>
									)}
								</div>
							</div>
						</div>
					) : (
						<div className="py-8 text-center text-[#D4B485]/60">
							未找到代理商信息
						</div>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
}

function InfoItem({
	icon,
	label,
	value,
}: {
	icon?: React.ReactNode;
	label: string;
	value: React.ReactNode;
}) {
	return (
		<div className="space-y-1.5">
			<div className="flex items-center gap-2 text-sm text-[#D4B485]/60">
				{icon}
				<span>{label}</span>
			</div>
			<div className="text-[#D4B485]">{value}</div>
		</div>
	);
}

function DataCard({
	label,
	value,
	total,
	unit,
}: {
	label: string;
	value: number;
	total?: number;
	unit: string;
}) {
	return (
		<div
			className={cn(
				"p-4 rounded-xl",
				"bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
				"border border-[#D4B485]/20",
			)}
		>
			<div className="text-sm text-[#D4B485]/60">{label}</div>
			<div className="mt-1 text-xl font-semibold text-[#D4B485]">
				{(value || 0).toLocaleString()}{" "}
				{total ? (
					<span className="text-sm font-normal text-[#D4B485]/60">
						/ {(total || 0).toLocaleString()}
					</span>
				) : (
					<span className="text-sm font-normal">{unit}</span>
				)}
			</div>
		</div>
	);
}

function AgentDetailSkeleton() {
	return (
		<div className="space-y-6">
			<div className="space-y-4">
				<Skeleton className="h-7 w-32" />
				<div className="grid grid-cols-2 gap-4">
					{Array.from({ length: 4 }).map((_, i) => (
						<div
							key={`basic-info-skeleton-field-${i + 1}`}
							className="space-y-2"
						>
							<Skeleton className="h-5 w-20" />
							<Skeleton className="h-6 w-full" />
						</div>
					))}
				</div>
			</div>
			<div className="space-y-4">
				<Skeleton className="h-7 w-32" />
				<div className="grid grid-cols-3 gap-4">
					{Array.from({ length: 3 }).map((_, i) => (
						<Skeleton
							key={`team-data-skeleton-card-${i + 1}`}
							className="h-24 w-full rounded-xl"
						/>
					))}
				</div>
			</div>
		</div>
	);
}
