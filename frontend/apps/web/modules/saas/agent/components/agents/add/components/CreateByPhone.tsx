"use client";

import type { AgentR<PERSON> } from "@prisma/client";
import { CountryCodeSelect } from "@saas/auth/components/CountryCodeSelect";
import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { Info, Smartphone } from "lucide-react";
import { useId, useState } from "react";
import { getRoleRule } from "../config/role-rules";

interface CreateByPhoneProps {
	selectedRole: AgentRole;
	onPrev: () => void;
	onConfirm: (phoneCountryCode: string, phoneNumber: string) => void;
	isLoading?: boolean;
	initialPhoneNumber?: string;
}

// 常用国家/地区区号
const _COUNTRY_CODES = [
	{ code: "+86", name: "中国" },
	{ code: "+852", name: "香港" },
	{ code: "+853", name: "澳门" },
	{ code: "+886", name: "台湾" },
	{ code: "+1", name: "美国/加拿大" },
	{ code: "+65", name: "新加坡" },
	{ code: "+81", name: "日本" },
	{ code: "+82", name: "韩国" },
	{ code: "+44", name: "英国" },
];

export function CreateByPhone({
	selectedRole,
	onPrev,
	onConfirm,
	isLoading,
	initialPhoneNumber = "",
}: CreateByPhoneProps) {
	const [phoneCountryCode, setPhoneCountryCode] = useState("+86");
	const [phoneNumber, setPhoneNumber] = useState(initialPhoneNumber);
	const [error, setError] = useState("");
	const id = useId();

	const role = getRoleRule(selectedRole);

	// 验证手机号
	const validatePhone = () => {
		if (!phoneNumber.trim()) {
			setError("请输入手机号码");
			return false;
		}

		// 简单验证手机号格式
		if (phoneCountryCode === "+86" && !/^1\d{10}$/.test(phoneNumber)) {
			setError("请输入正确的中国大陆手机号码(11位)");
			return false;
		}

		// 其他国家/地区手机号格式验证 - 简单长度检查
		if (phoneNumber.length < 5) {
			setError("手机号长度不正确");
			return false;
		}

		setError("");
		return true;
	};

	// 提交表单
	const handleSubmit = () => {
		// 再次验证手机号
		if (!validatePhone()) {
			return;
		}

		// 检查表单是否已经在提交中
		if (isLoading) {
			return;
		}

		onConfirm(phoneCountryCode, phoneNumber);
	};

	return (
		<div className="space-y-8">
			{/* 大标题 */}
			<motion.div
				className="space-y-2 text-center"
				initial={{ opacity: 0, y: -10 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.5 }}
			>
				<h3
					className={cn("text-2xl font-semibold", zywhFont.className)}
					style={{
						background:
							"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
						WebkitBackgroundClip: "text",
						WebkitTextFillColor: "transparent",
						backgroundClip: "text",
						textShadow:
							"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
					}}
				>
					创建新代理商
				</h3>
				<p className="text-[#D4B485]/60">
					创建新用户并开通为代理商，手机号将作为账号登录凭证
				</p>
			</motion.div>

			{/* 信息确认卡片 */}
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.5, delay: 0.2 }}
				className={cn(
					"rounded-lg p-6",
					"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
					"border border-[#D4B485]/20",
					"relative overflow-hidden",
				)}
			>
				{/* 背景装饰 */}
				<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
				<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />

				<div className="relative space-y-6">
					{/* 角色信息 */}
					<motion.div
						className="space-y-4"
						initial={{ opacity: 0, y: 10 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.5, delay: 0.3 }}
					>
						<h4 className="text-sm font-medium text-[#D4B485]/60">
							开通角色信息
						</h4>
						<div className="space-y-2">
							<div className="flex items-center justify-between">
								<div className="font-medium text-[#D4B485]">
									{role.roleName}
								</div>
							</div>
							<div className="text-sm text-[#D4B485]/40">
								{role.description}
							</div>
						</div>
					</motion.div>

					{/* 权益列表 */}
					<motion.div
						className="space-y-4"
						initial={{ opacity: 0, y: 10 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.5, delay: 0.4 }}
					>
						<h4 className="text-sm font-medium text-[#D4B485]/60">
							角色权益
						</h4>
						<div className="flex flex-wrap gap-2">
							{role.benefits.map((benefit, index) => (
								<motion.span
									key={benefit}
									initial={{ opacity: 0, scale: 0.9 }}
									animate={{ opacity: 1, scale: 1 }}
									transition={{
										duration: 0.3,
										delay: 0.5 + index * 0.1,
									}}
									className={cn(
										"rounded-full px-2 py-0.5 text-xs",
										"bg-[#D4B485]/5",
										"text-[#D4B485]/60",
										"ring-1 ring-[#D4B485]/10",
									)}
								>
									{benefit}
								</motion.span>
							))}
						</div>
					</motion.div>

					{/* 输入表单 */}
					<motion.div
						className="space-y-4 pt-2"
						initial={{ opacity: 0, y: 10 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.5, delay: 0.5 }}
					>
						<h4 className="text-sm font-medium text-[#D4B485]/60">
							手机号信息
						</h4>

						<div className="space-y-5">
							<motion.div className="flex space-x-4">
								{/* 区号选择 */}
								<motion.div className="w-[140px]">
									<label
										htmlFor="phoneCountryCode"
										className="block font-medium text-base text-[#D4B485]/80 mb-2"
									>
										区号
									</label>
									<CountryCodeSelect
										value={phoneCountryCode}
										onChange={setPhoneCountryCode}
										disabled={isLoading}
									/>
								</motion.div>

								{/* 手机号输入框 */}
								<motion.div className="flex-1">
									<label
										htmlFor={`${id}-phoneNumber`}
										className="block font-medium text-base text-[#D4B485]/80 mb-2"
									>
										手机号
									</label>
									<Input
										id={`${id}-phoneNumber`}
										placeholder="请输入手机号码"
										className={cn(
											"!placeholder-[#D4B485]/40 flex h-12 w-full rounded-xl border-none",
											"bg-white/5 px-6 py-3 text-[#D4B485] text-lg transition-all duration-300",
											"ease-in-out hover:bg-white/10 focus:bg-white/20 focus:ring-2 focus:ring-[#D4B485]",
											"disabled:opacity-50",
											error && "ring-2 ring-red-500/50",
										)}
										value={phoneNumber}
										onChange={(e) => {
											setPhoneNumber(e.target.value);
											if (error) {
												setError("");
											}
										}}
										onBlur={validatePhone}
										disabled={isLoading}
										aria-label="手机号码输入框"
									/>
									{error && (
										<motion.div
											initial={{ opacity: 0, x: -10 }}
											animate={{ opacity: 1, x: 0 }}
											className="mt-1 text-sm text-red-400"
										>
											{error}
										</motion.div>
									)}
								</motion.div>
							</motion.div>
						</div>
					</motion.div>

					{/* 提示信息 */}
					<motion.div
						initial={{ opacity: 0, y: 10 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.5, delay: 0.6 }}
						className="flex items-start space-x-2 rounded-md bg-blue-500/5 p-3 border border-blue-500/10"
					>
						<Info className="h-4 w-4 text-blue-400 mt-0.5" />
						<div className="text-sm text-blue-400">
							<p>
								系统将自动创建新用户并开通代理商权限，默认密码为
								123456，请提醒用户及时修改密码。
							</p>
							<p className="mt-1">
								新创建的代理商账号将处于待激活状态，需用户完成实名认证后才能正常使用。
							</p>
						</div>
					</motion.div>
				</div>
			</motion.div>

			{/* 底部按钮 */}
			<motion.div
				className="flex justify-between pt-4"
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.5, delay: 0.7 }}
			>
				<Button
					variant="outline"
					className={cn(
						"px-8",
						"bg-[#1E2023]/50 border-[#D4B485]/20",
						"text-[#D4B485] hover:text-[#E5C9A5]",
						"hover:bg-[#D4B485]/10",
						"transition duration-200",
						"disabled:opacity-50 disabled:cursor-not-allowed",
					)}
					onClick={onPrev}
					disabled={isLoading}
				>
					上一步
				</Button>
				<motion.div
					whileHover={{ scale: 1.02 }}
					whileTap={{ scale: 0.98 }}
				>
					<Button
						className={cn(
							"px-8",
							"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
							"text-white",
							"border-none",
							"shadow-[0_8px_16px_-4px_rgba(212,180,133,0.3)]",
							"hover:shadow-[0_12px_20px_-4px_rgba(212,180,133,0.4)]",
							"transition-all duration-200",
							"disabled:opacity-50 disabled:cursor-not-allowed",
							"h-14 rounded-xl font-semibold text-xl tracking-wide",
						)}
						onClick={handleSubmit}
						disabled={isLoading || !phoneNumber.trim() || !!error}
					>
						{isLoading ? (
							<>
								<div className="w-5 h-5 rounded-full border-2 border-white/20 border-t-white animate-spin mr-2" />
								处理中...
							</>
						) : (
							<>
								<Smartphone className="mr-2 h-5 w-5" />
								确认开通
							</>
						)}
					</Button>
				</motion.div>
			</motion.div>
		</div>
	);
}
