import type { Agent<PERSON><PERSON> } from "@prisma/client";
import { apiClient } from "@shared/lib/api-client";
import { useRouter } from "next/navigation";
import { useCallback, useState } from "react";
import { toast } from "sonner";
import type {
	AgentAddState,
	ApiResponse,
	CreateAgentResponseData,
	User,
} from "../types";

/**
 * 代理商创建Hook
 * 负责代理商状态管理和创建功能
 */
export function useAgentCreate() {
	const router = useRouter();
	const [loading, setLoading] = useState(false);
	const [state, setState] = useState<AgentAddState>({
		selectedRole: null,
		selectedUser: null,
	});

	/**
	 * 创建代理商
	 */
	const createAgent = useCallback(async (): Promise<boolean> => {
		if (!state.selectedRole || !state.selectedUser) {
			toast.error("请选择角色和用户");
			return false;
		}

		setLoading(true);
		try {
			// 判断是新用户还是已有用户
			if (state.selectedUser.isNewUser) {
				// 新用户的情况 - 通过手机号创建用户并设置为代理商
				const data = {
					phoneNumber: state.selectedUser.phoneNumber,
					// 如果有完整手机号(包含区号)，则使用它
					phoneNumberFull: state.selectedUser.phoneNumberFull,
					role: state.selectedRole,
				};

				const response =
					await apiClient.v1.agent.agents.add.createByPhone.$post({
						json: data,
					});

				const result =
					(await response.json()) as ApiResponse<CreateAgentResponseData>;

				if ("error" in result || result.code !== 0) {
					throw new Error(result.message || "创建新用户代理商失败");
				}

				toast.success("成功创建新用户并开通代理商");
			} else {
				// 已有用户的情况 - 仅设置为代理商
				const data = {
					userId: state.selectedUser.id,
					role: state.selectedRole,
				};

				const response =
					await apiClient.v1.agent.agents.add.create.$post({
						json: data,
					});

				const result =
					(await response.json()) as ApiResponse<CreateAgentResponseData>;

				if ("error" in result || result.code !== 0) {
					throw new Error(result.message || "代理商创建失败");
				}

				toast.success("代理商创建成功");
			}

			// 成功后跳转到列表页
			router.push("/app/agent/agents/list");
			return true;
		} catch (error) {
			toast.error("代理商创建失败", {
				description: error instanceof Error ? error.message : "请重试",
			});
			return false;
		} finally {
			setLoading(false);
		}
	}, [router, state.selectedRole, state.selectedUser]);

	/**
	 * 更新选中的角色
	 */
	const updateSelectedRole = useCallback((role: AgentRole | null) => {
		setState((prev) => ({ ...prev, selectedRole: role }));
	}, []);

	/**
	 * 更新选中的用户
	 */
	const updateSelectedUser = useCallback((user: User | null) => {
		setState((prev) => ({ ...prev, selectedUser: user }));
	}, []);

	/**
	 * 重置状态
	 */
	const reset = useCallback(() => {
		setState({
			selectedRole: null,
			selectedUser: null,
		});
	}, []);

	return {
		loading,
		state,
		createAgent,
		updateSelectedRole,
		updateSelectedUser,
		reset,
	};
}
