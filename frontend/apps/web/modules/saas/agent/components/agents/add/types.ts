import type { AgentRole } from "@prisma/client";

export interface User {
	id: string;
	name: string;
	email: string;
	phoneNumber: string;
	phoneNumberFull?: string;
	isAgent?: boolean;
	agentRole?: string;
	idCardVerified: boolean;
	isNewUser?: boolean;
}

export interface AgentAddState {
	selectedRole: AgentRole | null;
	selectedUser: User | null;
}

// API响应类型定义
export interface ApiSuccessResponse<T> {
	code: number;
	message?: string;
	data: T;
}

export interface ApiErrorResponse {
	code: number;
	error: string;
	message: string;
}

export type ApiResponse<T> = ApiSuccessResponse<T> | ApiErrorResponse;

// 创建代理商响应数据
export interface CreateAgentResponseData {
	id: string;
	userId: string;
	role: AgentRole;
	teamQuota: number;
	usedTeamQuota: number;
	createdAt: string;
}

// 搜索用户响应数据
export interface SearchUsersResponseData {
	items: User[];
	total: number;
	page: number;
	pageSize: number;
	totalPages: number;
}
