"use client";

import type { AgentR<PERSON> } from "@prisma/client";
import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Avatar, AvatarFallback } from "@ui/components/avatar";
import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";
import { UserCheck, UserX } from "lucide-react";
import { getRoleRule } from "../config/role-rules";
import type { User } from "../types";

interface ConfirmAgentProps {
	selectedRole: AgentRole;
	selectedUser: User;
	onPrev: () => void;
	onConfirm: () => void;
	isLoading?: boolean;
}

export function ConfirmAgent({
	selectedRole,
	selectedUser,
	onPrev,
	onConfirm,
	isLoading,
}: ConfirmAgentProps) {
	const role = getRoleRule(selectedRole);

	// 检查用户是否已经是代理商，不再检查实名认证
	const isExistingAgent = selectedUser.isAgent;
	const isNotVerified = !selectedUser.idCardVerified;
	const isNewUser = selectedUser.isNewUser;
	// 只要不是代理商就可以开通，去掉实名认证限制
	const canConfirm = !isExistingAgent;

	return (
		<div className="space-y-8">
			{/* 大标题 */}
			<div className="space-y-2 text-center">
				<h3
					className={cn("text-2xl font-semibold", zywhFont.className)}
					style={{
						background:
							"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
						WebkitBackgroundClip: "text",
						WebkitTextFillColor: "transparent",
						backgroundClip: "text",
						textShadow:
							"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
					}}
				>
					确认代理商信息
				</h3>
				<p className="text-[#D4B485]/60">
					确认开通代理商的信息无误，点击"确认开通"开通代理商
				</p>
			</div>

			{/* 信息确认卡片 */}
			<div
				className={cn(
					"rounded-lg p-6",
					"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
					"border border-[#D4B485]/20",
					"relative overflow-hidden",
				)}
			>
				{/* 背景装饰 */}
				<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
				<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />

				<div className="relative space-y-6">
					{/* 用户信息 */}
					<div className="space-y-4">
						<h4 className="text-sm font-medium text-[#D4B485]/60">
							代理商用户信息
						</h4>
						<div className="flex items-center gap-4">
							<Avatar
								className={cn(
									"h-12 w-12",
									"border border-[#D4B485]/20",
								)}
							>
								<AvatarFallback
									className={cn(
										"text-[#1E2023] font-semibold",
										"bg-gradient-to-br from-[#D4B485] to-[#B08968]",
									)}
								>
									{isNewUser
										? "新"
										: selectedUser?.name.slice(0, 1)}
								</AvatarFallback>
							</Avatar>
							<div>
								<div className="font-medium text-[#D4B485]">
									{isNewUser
										? "新建用户"
										: selectedUser?.name}
								</div>
								<div className="mt-1 text-sm text-[#D4B485]/40">
									{isNewUser ? (
										<>
											{selectedUser?.phoneNumberFull ||
												selectedUser?.phoneNumber}
											·{" "}
											<span className="text-green-400">
												新创建用户
											</span>
										</>
									) : (
										<>
											{selectedUser?.email} ·{" "}
											{selectedUser?.phoneNumber}
										</>
									)}
								</div>
								{!isNewUser && (
									<div className="mt-2 flex items-center">
										{selectedUser.idCardVerified ? (
											<div className="flex items-center text-green-400 text-xs">
												<UserCheck className="mr-1 h-3 w-3" />
												已完成实名认证
											</div>
										) : (
											<div className="flex items-center text-orange-400 text-xs">
												<UserX className="mr-1 h-3 w-3" />
												未完成实名认证
											</div>
										)}
									</div>
								)}
							</div>
						</div>
					</div>

					{/* 角色信息 */}
					<div className="space-y-4">
						<h4 className="text-sm font-medium text-[#D4B485]/60">
							开通角色信息
						</h4>
						<div className="space-y-2">
							<div className="flex items-center justify-between">
								<div className="font-medium text-[#D4B485]">
									{role.roleName}
								</div>
								{/* <div
									className={cn(
										"rounded-full px-2 py-0.5 text-xs",
										"bg-[#D4B485]/10",
										"text-[#D4B485]/60",
									)}
								>
									附带{role.quotaCost}个超级个体名额
								</div> */}
							</div>
							<div className="text-sm text-[#D4B485]/40">
								{role.description}
							</div>
						</div>
					</div>

					{/* 权益列表 */}
					<div className="space-y-4">
						<h4 className="text-sm font-medium text-[#D4B485]/60">
							角色权益
						</h4>
						<div className="flex flex-wrap gap-2">
							{role.benefits.map((benefit) => (
								<span
									key={benefit}
									className={cn(
										"rounded-full px-2 py-0.5 text-xs",
										"bg-[#D4B485]/5",
										"text-[#D4B485]/60",
										"ring-1 ring-[#D4B485]/10",
									)}
								>
									{benefit}
								</span>
							))}
						</div>
					</div>

					{/* 警告信息 */}
					{!canConfirm && (
						<div className="mt-4 p-3 rounded-md bg-red-500/10 border border-red-500/20">
							<p className="text-sm text-red-400">
								该用户已经是代理商，无法重复开通。如需修改角色，请前往代理商管理页面操作。
							</p>
						</div>
					)}

					{/* 新用户提示 */}
					{isNewUser && (
						<div className="mt-4 p-3 rounded-md bg-blue-500/10 border border-blue-500/20">
							<p className="text-sm text-blue-400">
								系统将创建新用户并开通代理商权限，默认密码为
								123456，请提醒用户及时修改密码。新用户需要完成实名认证后才能正常使用。
							</p>
						</div>
					)}

					{/* 未实名认证提示 */}
					{isNotVerified && !isNewUser && canConfirm && (
						<div className="mt-4 p-3 rounded-md bg-yellow-500/10 border border-yellow-500/20">
							<p className="text-sm text-yellow-400">
								注意：该用户未完成实名认证，创建后代理商账号将处于待激活状态，需要用户完成实名认证后才能正常使用。
							</p>
						</div>
					)}
				</div>
			</div>

			{/* 底部按钮 */}
			<div className="flex justify-between pt-4">
				<Button
					variant="outline"
					className={cn(
						"px-8",
						"bg-[#1E2023]/50 border-[#D4B485]/20",
						"text-[#D4B485] hover:text-[#E5C9A5]",
						"hover:bg-[#D4B485]/10",
						"transition duration-200",
						"disabled:opacity-50 disabled:cursor-not-allowed",
					)}
					onClick={onPrev}
					disabled={isLoading}
				>
					上一步
				</Button>
				<Button
					className={cn(
						"px-8",
						"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
						"text-white",
						"border-none",
						"shadow-[0_8px_16px_-4px_rgba(212,180,133,0.3)]",
						"hover:shadow-[0_12px_20px_-4px_rgba(212,180,133,0.4)]",
						"transition-all duration-200",
						"disabled:opacity-50 disabled:cursor-not-allowed",
					)}
					onClick={onConfirm}
					disabled={!canConfirm || isLoading}
				>
					确认开通
				</Button>
			</div>
		</div>
	);
}
