"use client";

import { config } from "@repo/config";
// import { logger } from "@repo/logs";
import { useSession } from "@saas/auth/hooks/use-session";
import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { useQuery } from "@tanstack/react-query";
import { Button } from "@ui/components/button";
import { Card } from "@ui/components/card";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { CheckIcon, InfoIcon } from "lucide-react";
import { useEffect, useState } from "react";

interface Policy {
	level: string;
	name: string;
	baseReward: number;
	maxReward: number;
	requirements: string[];
	benefits: string[];
}

// 从config中获取代理角色信息
const agentRoles = config.auth.agent.roles;
const referralRewards = config.auth.agent.referralRewards;
const bulkPurchase = config.auth.agent.bulkPurchase;
const upgradeRules = config.auth.agent.upgradeRules;

// 获取推荐政策
async function fetchReferralPolicies() {
	// logger.info("开始获取推荐政策");

	// 根据config生成政策数据
	const policies = Object.entries(agentRoles).map(([level, roleInfo]) => {
		const rewards =
			referralRewards[level as keyof typeof referralRewards] || {};

		// 获取同级推荐奖励
		const sameLevel = rewards[level as keyof typeof rewards];
		const baseReward = sameLevel?.reward || 0;

		// 获取最大推荐奖励
		const allRewards = Object.values(rewards);
		const maxReward =
			allRewards.length > 0
				? Math.max(...allRewards.map((r) => r.reward || 0))
				: 0;

		// 构建政策
		return {
			level,
			name: `${roleInfo.name}推荐政策`,
			baseReward,
			maxReward,
			requirements: [
				"实名认证完成",
				"被推荐人必须先在平台完成注册",
				"需要有权限审批的上级通过后才可以完成推荐",
			].filter(Boolean),
			benefits: [
				...Object.entries(rewards).map(([targetLevel, rewardInfo]) => {
					const targetRole =
						agentRoles[targetLevel as keyof typeof agentRoles];
					return `推荐${targetRole.name}奖励${(rewardInfo.reward / 100).toLocaleString()}元`;
				}),
				level === "SALES"
					? "享受个人分销权益"
					: level === "PARTNER"
						? "享受团队管理权益"
						: level === "DIRECTOR"
							? "享受区域级运营权"
							: "享有城市级运营权",
				level !== "SALES"
					? `交付收益${level === "PARTNER" ? "20" : level === "DIRECTOR" ? "40" : "60"}元/小时`
					: "",
			].filter(Boolean),
		} as Policy;
	});

	return { policies };
}

// 角色级别映射
const levelMap = Object.fromEntries(
	Object.entries(agentRoles).map(([key, value]) => [key, value.name]),
);

// 角色价格映射（分为单位）
const levelPrices = Object.fromEntries(
	Object.entries(agentRoles).map(([key, value]) => [key, value.price]),
);

// 获取上下级关系
function getRelationship(
	currentLevel: string,
	targetLevel: string,
): "same" | "upper" | "lower" {
	const currentLevelValue =
		agentRoles[currentLevel as keyof typeof agentRoles]?.level || 0;
	const targetLevelValue =
		agentRoles[targetLevel as keyof typeof agentRoles]?.level || 0;

	if (currentLevelValue === targetLevelValue) {
		return "same";
	}
	if (currentLevelValue > targetLevelValue) {
		return "upper"; // 数值越小，级别越高
	}
	return "lower";
}

// 获取推荐奖励
function getRewardAmount(currentLevel: string, targetLevel: string): number {
	try {
		const rewards =
			referralRewards[currentLevel as keyof typeof referralRewards];
		if (!rewards) {
			return 0;
		}

		const targetReward = rewards[targetLevel as keyof typeof rewards];
		return targetReward?.reward || 0;
	} catch (_error) {
		return 0;
	}
}

// 获取推荐比例
function getRewardPercentage(
	currentLevel: string,
	targetLevel: string,
): number {
	const relationship = getRelationship(currentLevel, targetLevel);

	// 如果是上级推荐，返回0（不显示百分比）
	if (relationship === "upper") {
		return 0;
	}

	// 如果是同级推荐，固定返回20%
	if (relationship === "same") {
		return 20;
	}

	// 如果是找不到明确奖励比例，尝试根据价格计算
	const reward = getRewardAmount(currentLevel, targetLevel);
	const targetPrice =
		levelPrices[targetLevel as keyof typeof levelPrices] || 0;

	if (targetPrice && reward) {
		return (reward / targetPrice) * 100;
	}

	// 默认比例（向下推荐）
	return 40;
}

// 计算说明对象接口
interface RewardExplanation {
	relationship: "same" | "upper" | "lower";
	currentLevel: string;
	targetLevel: string;
	currentLevelName: string;
	targetLevelName: string;
	targetPrice: number;
	percentage: number;
	reward: number;
	calculationExplanation: string;
	needQuota: boolean;
	showPercentage: boolean; // 新增标记是否显示百分比
}

// 获取可推荐级别列表
const getRecommendableLevels = (_currentUserLevel: string) => {
	// 不再根据用户级别过滤，直接返回所有代理商角色
	return Object.keys(agentRoles);
};

export function ReferralPolicy() {
	// 获取当前用户会话信息
	const { user } = useSession();
	const currentUserLevel = user?.agentRole || "SALES";

	const [selectedLevel, setSelectedLevel] = useState<string>("SALES");

	const { data, isLoading } = useQuery({
		queryKey: ["referral-policies"],
		queryFn: fetchReferralPolicies,
	});

	const policies = data?.policies || [];
	const selectedPolicy = policies.find((p) => p.level === selectedLevel);

	// 获取可推荐的角色列表
	const recommendableLevels = getRecommendableLevels(currentUserLevel);

	// 如果当前选择的角色不在可推荐列表中，重置为第一个可推荐的角色
	useEffect(() => {
		if (!recommendableLevels.includes(selectedLevel)) {
			setSelectedLevel(recommendableLevels[0] || "SALES");
		}
	}, [selectedLevel, recommendableLevels]);

	// 自动计算推荐关系和奖励
	const rewardExplanation: RewardExplanation = {
		relationship: getRelationship(currentUserLevel, selectedLevel),
		currentLevel: currentUserLevel,
		targetLevel: selectedLevel,
		currentLevelName:
			levelMap[currentUserLevel as keyof typeof levelMap] || "",
		targetLevelName: levelMap[selectedLevel as keyof typeof levelMap] || "",
		targetPrice:
			levelPrices[selectedLevel as keyof typeof levelPrices] / 100 || 0,
		percentage: getRewardPercentage(currentUserLevel, selectedLevel),
		reward: getRewardAmount(currentUserLevel, selectedLevel) / 100,
		calculationExplanation: "",
		needQuota: false,
		showPercentage:
			getRelationship(currentUserLevel, selectedLevel) !== "upper",
	};

	// 构建计算说明
	const targetPrice =
		levelPrices[selectedLevel as keyof typeof levelPrices] / 100;
	const percentage = rewardExplanation.percentage;

	// 根据关系类型构建不同的计算说明
	if (rewardExplanation.relationship === "upper") {
		// 上级推荐直接显示金额，不显示计算过程
		rewardExplanation.calculationExplanation = `推荐${rewardExplanation.targetLevelName}奖励${rewardExplanation.reward.toLocaleString()}元`;
	} else {
		// 同级或下级推荐显示计算过程
		rewardExplanation.calculationExplanation = `${targetPrice.toLocaleString()} × ${percentage.toFixed(1)}% = ${((targetPrice * percentage) / 100).toLocaleString()}元`;
	}

	// 检查是否需要名额
	try {
		const rewards =
			referralRewards[currentUserLevel as keyof typeof referralRewards];
		const targetReward = rewards?.[selectedLevel as keyof typeof rewards];
		rewardExplanation.needQuota = targetReward?.needQuota || false;
	} catch (_error) {}

	if (isLoading) {
		return (
			<Card
				className={cn(
					"bg-[#1E2023]/95 border-[#D4B485]/20 backdrop-blur-xl",
				)}
			>
				<div className="p-6">
					<div className="mb-6 flex items-center justify-between">
						<h2
							className={cn(
								"text-xl font-semibold",
								zywhFont.className,
							)}
							style={{
								background:
									"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
								WebkitBackgroundClip: "text",
								WebkitTextFillColor: "transparent",
								backgroundClip: "text",
								textShadow:
									"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
							}}
						>
							推荐政策
						</h2>
						<div className="w-[160px]">
							<div className="h-10 rounded bg-[#D4B485]/10" />
						</div>
					</div>
					<div className="animate-pulse space-y-4">
						<div className="grid gap-4 grid-cols-3">
							<div className="h-[300px] rounded bg-[#D4B485]/10" />
							<div className="h-[300px] rounded bg-[#D4B485]/10" />
							<div className="h-[300px] rounded bg-[#D4B485]/10" />
						</div>
					</div>
				</div>
			</Card>
		);
	}

	return (
		<Card
			className={cn(
				"bg-[#1E2023]/95 border-[#D4B485]/20 backdrop-blur-xl",
				"shadow-[0_8px_16px_-4px_rgba(0,0,0,0.3)]",
				"transition-all duration-200",
				"hover:shadow-[0_12px_24px_-4px_rgba(212,180,133,0.15)]",
			)}
		>
			<motion.div
				className="p-6"
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.5 }}
			>
				<div className="mb-6 flex items-center justify-between">
					<h2
						className={cn(
							"text-xl font-semibold",
							zywhFont.className,
						)}
						style={{
							background:
								"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
							WebkitBackgroundClip: "text",
							WebkitTextFillColor: "transparent",
							backgroundClip: "text",
							textShadow:
								"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
						}}
					>
						推荐政策
					</h2>
					<div className="flex items-center space-x-3">
						<span
							className={cn(
								"text-[#E5C9A5] text-base font-medium whitespace-nowrap",
								zywhFont.className,
							)}
							style={{
								background:
									"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
								WebkitBackgroundClip: "text",
								WebkitTextFillColor: "transparent",
								backgroundClip: "text",
								textShadow: "0 1px 2px rgba(0,0,0,0.2)",
							}}
						>
							我想推荐：
						</span>
						<Select
							value={selectedLevel}
							onValueChange={setSelectedLevel}
						>
							<SelectTrigger
								className={cn(
									"w-[160px]",
									"bg-[#1E2023]/50 border-[#D4B485]/20",
									"text-[#E5C9A5]",
									"focus:border-[#D4B485]/40 focus:ring-[#D4B485]/20",
								)}
							>
								<SelectValue placeholder="选择代理等级" />
							</SelectTrigger>
							<SelectContent
								className={cn(
									"bg-[#1E2023]/95",
									"border-[#D4B485]/20",
									"backdrop-blur-xl",
								)}
							>
								{recommendableLevels.map((level) => (
									<SelectItem
										key={level}
										value={level}
										className="text-[#E5C9A5] hover:bg-[#D4B485]/10"
									>
										{
											levelMap[
												level as keyof typeof levelMap
											]
										}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>
				</div>

				{selectedPolicy && (
					<div className="grid grid-cols-3 gap-5">
						<motion.div
							className={cn(
								"rounded-lg p-5",
								"bg-[#1E2023]/50 border-[#D4B485]/20",
								"border",
								"relative overflow-hidden",
								"transition-all duration-300",
								// 第一层装饰 - 内部光效
								"before:absolute before:inset-0",
								"before:rounded-lg",
								"before:transition-all before:duration-500",
								"before:ease-out",
								"before:scale-[0.97]",
								"before:opacity-0",
								"before:bg-gradient-to-r",
								"before:from-transparent",
								"before:via-[#1E2532]",
								"before:to-transparent",
								"before:bg-clip-padding",
								"before:backdrop-filter",
								"before:backdrop-blur-[8px]",
								"before:border-transparent",
								"before:bg-opacity-50",
								"hover:before:scale-100 hover:before:opacity-100",
								"before:z-[-1]",
								// 第二层装饰 - 外部光晕
								"after:absolute after:inset-[-1px]",
								"after:rounded-lg",
								"after:transition-all after:duration-500",
								"after:ease-out",
								"after:scale-[1.02]",
								"after:bg-gradient-to-r",
								"after:from-transparent",
								"after:via-[#D4B485]/5",
								"after:to-transparent",
								"after:opacity-0",
								"hover:after:opacity-100",
								"after:z-[-2]",
								"h-full",
							)}
							initial={{ opacity: 0, y: 10 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ duration: 0.3, delay: 0.1 }}
							whileHover={{
								y: -5,
								boxShadow:
									"0 10px 25px -5px rgba(212,180,133,0.25)",
								transition: { duration: 0.2 },
							}}
						>
							<div className="relative z-[1] flex flex-col h-full">
								<div className="mb-4 flex-1">
									<div className="flex items-center justify-between">
										<div className="text-[#D4B485]/60 text-sm">
											推荐奖励计算说明
										</div>
										<div
											className={cn(
												"px-2 py-1 rounded text-xs",
												rewardExplanation.relationship ===
													"same"
													? "bg-[#D4B485]/20 text-[#D4B485]"
													: rewardExplanation.relationship ===
															"upper"
														? "bg-[#D7A550]/20 text-[#D7A550]"
														: "bg-[#50A0D7]/20 text-[#50A0D7]",
											)}
										>
											{rewardExplanation.relationship ===
											"same"
												? "同级推荐"
												: rewardExplanation.relationship ===
														"upper"
													? "上级推荐"
													: "下级推荐"}
										</div>
									</div>

									<div className="mt-4 flex flex-col gap-2">
										<div className="flex items-baseline gap-2">
											<span
												className={cn(
													"font-medium text-3xl tabular-nums",
													zywhFont.className,
												)}
												style={{
													background:
														"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
													WebkitBackgroundClip:
														"text",
													WebkitTextFillColor:
														"transparent",
													backgroundClip: "text",
													textShadow:
														"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
												}}
											>
												¥
												{rewardExplanation.reward.toLocaleString()}
											</span>
											<div className="flex items-center">
												<span className="inline-block h-0.5 w-4 bg-[#D4B485]/20 mx-1" />
												<span className="text-[#D4B485]/40 text-sm">
													{rewardExplanation.relationship ===
													"same"
														? "同级推荐奖励"
														: rewardExplanation.relationship ===
																"upper"
															? `推荐${rewardExplanation.targetLevelName}奖励`
															: "下级推荐奖励"}
												</span>
											</div>
										</div>
									</div>

									<div className="mt-4 mb-4 p-3 rounded-md bg-[#1E2023]/70 border border-[#D4B485]/10">
										<div className="flex justify-between items-center mb-3">
											<span className="text-[#D4B485]/80 text-sm">
												{
													rewardExplanation.targetLevelName
												}
												权益价格
											</span>
											<span
												className={cn(
													"font-medium text-lg tabular-nums",
													zywhFont.className,
												)}
												style={{
													background:
														"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
													WebkitBackgroundClip:
														"text",
													WebkitTextFillColor:
														"transparent",
													backgroundClip: "text",
												}}
											>
												¥
												{rewardExplanation.targetPrice.toLocaleString()}
											</span>
										</div>

										<div className="flex justify-between items-center mb-3">
											<span className="text-[#D4B485]/80 text-sm">
												{rewardExplanation.relationship ===
												"same"
													? "同级推荐比例"
													: rewardExplanation.relationship ===
															"upper"
														? "上级推荐奖励"
														: "下级推荐比例"}
											</span>
											<span className="text-[#E5C9A5] font-medium">
												{rewardExplanation.relationship ===
												"upper"
													? `${rewardExplanation.reward.toLocaleString()}元`
													: `${rewardExplanation.percentage.toFixed(1)}%`}
											</span>
										</div>

										<div className="flex items-center justify-center mt-3 p-2 rounded bg-[#D4B485]/5 text-sm text-[#E5C9A5]">
											{
												rewardExplanation.calculationExplanation
											}
										</div>

										{false &&
											rewardExplanation.needQuota && (
												<div className="flex items-center mt-3 p-2 rounded bg-[#D4B485]/5 text-xs text-[#E5C9A5]">
													<InfoIcon className="h-3 w-3 text-[#D4B485] mr-1" />
													<span>
														需要有权限审批的上级通过后才可以完成推荐
													</span>
												</div>
											)}
									</div>

									<div className="mt-4 pt-4 border-t border-[#D4B485]/10">
										<h3 className="text-[#D4B485] text-sm font-medium mb-2">
											推荐
											{rewardExplanation.targetLevelName}
											政策
										</h3>
										<p className="text-[#D4B485]/40 text-xs">
											{`作为${rewardExplanation.currentLevelName}，推荐${rewardExplanation.targetLevelName}的奖励政策，完成推荐后将获得相应奖励`}
										</p>
									</div>
								</div>

								<Button
									className={cn(
										"bg-gradient-to-r from-[#D4B485] to-[#B08968] text-white",
										"hover:from-[#E5C9A5] hover:to-[#D4B485]",
										"border-0",
										"hidden", // 隐藏按钮
										"shadow-[0_4px_12px_-2px_rgba(212,180,133,0.4)]",
										"transition-all duration-300",
										"hover:shadow-[0_6px_16px_-2px_rgba(212,180,133,0.6)]",
									)}
									onClick={() => {
										// 提交推荐申请的业务逻辑
									}}
								>
									提交推荐申请
								</Button>
							</div>
						</motion.div>

						<motion.div
							className={cn(
								"rounded-lg p-5",
								"bg-[#1E2023]/50 border-[#D4B485]/20",
								"border",
								"relative overflow-hidden",
								"transition-all duration-300",
								"hover:bg-[#1E2023]/60",
								"shadow-[inset_0_0_0_1px_rgba(212,180,133,0.05)]",
								"hover:shadow-[inset_0_0_0_1px_rgba(212,180,133,0.1)]",
								"h-full",
							)}
							initial={{ opacity: 0, y: 10 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ duration: 0.3, delay: 0.2 }}
							whileHover={{
								y: -5,
								boxShadow:
									"0 10px 25px -5px rgba(212,180,133,0.15)",
								transition: { duration: 0.2 },
							}}
						>
							<h3
								className={cn(
									"mb-4 font-medium",
									zywhFont.className,
								)}
								style={{
									background:
										"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
									WebkitBackgroundClip: "text",
									WebkitTextFillColor: "transparent",
									backgroundClip: "text",
								}}
							>
								申请要求
							</h3>

							{/* 替换原来的需求列表为步骤条 */}
							<div className="space-y-0 relative">
								{/* 垂直连接线 */}
								<div className="absolute left-[15px] top-[26px] w-0.5 h-[calc(100%-50px)] bg-gradient-to-b from-[#D4B485]/40 to-[#D4B485]/10 z-0" />

								{/* 步骤1：被推荐人实名认证 */}
								<motion.div
									className="relative z-10 flex mb-8"
									initial={{ opacity: 0, y: 20 }}
									animate={{ opacity: 1, y: 0 }}
									transition={{ duration: 0.5, delay: 0.1 }}
								>
									<div className="flex-shrink-0 flex items-center justify-center w-[30px] h-[30px] rounded-full bg-gradient-to-r from-[#D4B485] to-[#B08968] mr-3 shadow-[0_0_10px_rgba(212,180,133,0.4)]">
										<span className="text-white font-medium">
											1
										</span>
									</div>
									<div className="pt-1">
										<h4 className="text-[#E5C9A5] font-medium text-sm mb-1">
											被推荐人（张总）实名认证
										</h4>
										<p className="text-[#D4B485]/60 text-xs">
											推荐前，确保被推荐人已完成平台实名认证流程
										</p>
									</div>
								</motion.div>

								{/* 步骤2：发起推荐 */}
								<motion.div
									className="relative z-10 flex mb-8"
									initial={{ opacity: 0, y: 20 }}
									animate={{ opacity: 1, y: 0 }}
									transition={{ duration: 0.5, delay: 0.4 }}
								>
									<div className="flex-shrink-0 flex items-center justify-center w-[30px] h-[30px] rounded-full bg-gradient-to-r from-[#D4B485] to-[#B08968] mr-3 shadow-[0_0_10px_rgba(212,180,133,0.4)]">
										<span className="text-white font-medium">
											2
										</span>
									</div>
									<div className="pt-1">
										<h4 className="text-[#E5C9A5] font-medium text-sm mb-1">
											我发起推荐申请
										</h4>
										<p className="text-[#D4B485]/60 text-xs">
											在平台提交推荐申请，指定代理级别和被推荐人
										</p>
									</div>
								</motion.div>

								{/* 步骤3：上级审批 */}
								<motion.div
									className="relative z-10 flex"
									initial={{ opacity: 0, y: 20 }}
									animate={{ opacity: 1, y: 0 }}
									transition={{ duration: 0.5, delay: 0.7 }}
								>
									<div className="flex-shrink-0 flex items-center justify-center w-[30px] h-[30px] rounded-full bg-gradient-to-r from-[#D4B485] to-[#B08968] mr-3 shadow-[0_0_10px_rgba(212,180,133,0.4)]">
										<span className="text-white font-medium">
											3
										</span>
									</div>
									<div className="pt-1">
										<h4 className="text-[#E5C9A5] font-medium text-sm mb-1">
											有权限的上级审批通过
										</h4>
										<p className="text-[#D4B485]/60 text-xs">
											上级收到通知并审核，批准后完成推荐流程
										</p>
									</div>
								</motion.div>

								{/* 特殊情况提示 */}
								<motion.div
									className="mt-5 p-3 rounded-md bg-[#1E2023]/70 border border-[#D4B485]/10"
									initial={{ opacity: 0, y: 10 }}
									animate={{ opacity: 1, y: 0 }}
									transition={{ duration: 0.4, delay: 1 }}
								>
									<div className="flex items-center text-[#D4B485] text-xs">
										<InfoIcon className="h-3.5 w-3.5 mr-1.5" />
										<span>
											特殊情况：上级3天未处理，转总公司审批
										</span>
									</div>
								</motion.div>
							</div>

							<motion.div
								className="absolute bottom-2 right-2 opacity-10"
								animate={{
									rotate: [0, 5, 0, -5, 0],
									scale: [1, 1.05, 1, 0.95, 1],
								}}
								transition={{
									duration: 5,
									repeat: Number.POSITIVE_INFINITY,
									repeatType: "reverse",
								}}
							>
								<div className="h-16 w-16 rounded-full bg-gradient-to-br from-[#D4B485]/5 to-[#D4B485]/30 blur-xl" />
							</motion.div>
						</motion.div>

						<motion.div
							className={cn(
								"rounded-lg p-5",
								"bg-[#1E2023]/50 border-[#D4B485]/20",
								"border",
								"relative overflow-hidden",
								"transition-all duration-300",
								"hover:bg-[#1E2023]/60",
								"shadow-[inset_0_0_0_1px_rgba(212,180,133,0.05)]",
								"hover:shadow-[inset_0_0_0_1px_rgba(212,180,133,0.1)]",
								"h-full",
							)}
							initial={{ opacity: 0, y: 10 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ duration: 0.3, delay: 0.3 }}
							whileHover={{
								y: -5,
								boxShadow:
									"0 10px 25px -5px rgba(212,180,133,0.15)",
								transition: { duration: 0.2 },
							}}
						>
							<h3
								className={cn(
									"mb-4 font-medium",
									zywhFont.className,
								)}
								style={{
									background:
										"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
									WebkitBackgroundClip: "text",
									WebkitTextFillColor: "transparent",
									backgroundClip: "text",
								}}
							>
								{rewardExplanation.targetLevelName}代理权益
							</h3>
							<ul className="space-y-3">
								{/* 根据选择的目标代理等级显示对应的权益 */}
								{selectedLevel === "SALES" && (
									<>
										<AgentBenefit
											icon="user"
											label="获得个人分销权"
										/>
										<AgentBenefit
											icon="money"
											label="销售主力套餐分润"
											highlight="3000元/套"
										/>
										{upgradeRules.SALES && (
											<>
												<AgentBenefit
													icon="level-up"
													label="升级通道"
													isHighlight
												/>
												<AgentBenefit
													icon="level-up"
													label="成功推荐"
													highlight={`${upgradeRules.SALES.requiredReferrals}个超级个体`}
													isSubItem
												/>
												<AgentBenefit
													icon="level-up"
													label="即可升级为"
													highlight="合伙人"
													isSubItem
												/>
											</>
										)}
									</>
								)}
								{selectedLevel === "PARTNER" && (
									<>
										<AgentBenefit
											icon="user"
											label="获得团队管理权"
										/>
										<AgentBenefit
											icon="team"
											label="包含超级个体"
											highlight="12个"
										/>
										<AgentBenefit
											icon="money"
											label="交付收益"
											highlight="20元/小时"
										/>
										{bulkPurchase.PARTNER && (
											<>
												<AgentBenefit
													icon="shopping"
													label="超级个体名额批量购买"
												/>
												<AgentBenefit
													icon="shopping"
													label="起购数量："
													highlight={`${bulkPurchase.PARTNER.minQuantity}个`}
													isSubItem
												/>
												<AgentBenefit
													icon="shopping"
													label="优惠价格："
													highlight={`${(9800 * bulkPurchase.PARTNER.commissionRate).toLocaleString()}元/个`}
													isSubItem
												/>
											</>
										)}
										{upgradeRules.PARTNER && (
											<>
												<AgentBenefit
													icon="level-up"
													label="升级通道"
													isHighlight
												/>
												<AgentBenefit
													icon="level-up"
													label="成功推荐"
													highlight={`${upgradeRules.PARTNER.requiredReferrals}个合伙人`}
													isSubItem
												/>
												<AgentBenefit
													icon="level-up"
													label="即可升级为"
													highlight="联席董事"
													isSubItem
												/>
											</>
										)}
									</>
								)}
								{selectedLevel === "DIRECTOR" && (
									<>
										<AgentBenefit
											icon="user"
											label="获得区域运营权"
										/>
										<AgentBenefit
											icon="team"
											label="包含超级个体"
											highlight="76个"
										/>
										<AgentBenefit
											icon="team"
											label="相当于合伙人"
											highlight="7个"
											isSubItem
										/>
										<AgentBenefit
											icon="money"
											label="交付收益"
											highlight="40元/小时"
										/>
										{bulkPurchase.DIRECTOR && (
											<>
												<AgentBenefit
													icon="shopping"
													label="超级个体名额批量购买"
												/>
												<AgentBenefit
													icon="shopping"
													label="起购数量："
													highlight={`${bulkPurchase.DIRECTOR.minQuantity}个`}
													isSubItem
												/>
												<AgentBenefit
													icon="shopping"
													label="优惠价格："
													highlight={`${(9800 * bulkPurchase.DIRECTOR.commissionRate).toLocaleString()}元/个`}
													isSubItem
												/>
											</>
										)}
										{upgradeRules.DIRECTOR && (
											<>
												<AgentBenefit
													icon="level-up"
													label="升级通道"
													isHighlight
												/>
												<AgentBenefit
													icon="level-up"
													label="成功推荐"
													highlight={`${upgradeRules.DIRECTOR.requiredReferrals}个联席董事`}
													isSubItem
												/>
												<AgentBenefit
													icon="level-up"
													label="即可升级为"
													highlight="分公司"
													isSubItem
												/>
											</>
										)}
									</>
								)}
								{selectedLevel === "BRANCH" && (
									<>
										<AgentBenefit
											icon="user"
											label="获得城市运营权（复制总部）"
										/>
										<AgentBenefit
											icon="team"
											label="包含超级个体"
											highlight="1020个"
										/>
										<AgentBenefit
											icon="team"
											label="相当于合伙人"
											highlight="72个"
											isSubItem
										/>
										<AgentBenefit
											icon="team"
											label="相当于联席董事"
											highlight="11个"
											isSubItem
										/>
										<AgentBenefit
											icon="money"
											label="交付收益"
											highlight="60元/小时"
										/>
										{bulkPurchase.BRANCH && (
											<>
												<AgentBenefit
													icon="shopping"
													label="超级个体名额批量购买"
												/>
												<AgentBenefit
													icon="shopping"
													label="起购数量："
													highlight={`${bulkPurchase.BRANCH.minQuantity}个`}
													isSubItem
												/>
												<AgentBenefit
													icon="shopping"
													label="优惠价格："
													highlight={`${(9800 * bulkPurchase.BRANCH.commissionRate).toLocaleString()}元/个`}
													isSubItem
												/>
											</>
										)}
									</>
								)}
							</ul>

							<motion.div
								className="absolute bottom-2 right-2 opacity-10"
								animate={{
									rotate: [0, -5, 0, 5, 0],
									scale: [1, 0.95, 1, 1.05, 1],
								}}
								transition={{
									duration: 5,
									repeat: Number.POSITIVE_INFINITY,
									repeatType: "reverse",
									delay: 0.5,
								}}
							>
								<div className="h-16 w-16 rounded-full bg-gradient-to-br from-[#D4B485]/5 to-[#D4B485]/30 blur-xl" />
							</motion.div>
						</motion.div>
					</div>
				)}
			</motion.div>
		</Card>
	);
}

// 新增一个AgentBenefit组件放在ReferralPolicy组件外面
function AgentBenefit({
	label,
	highlight,
	isSubItem = false,
	isHighlight = false,
}: {
	icon:
		| "user"
		| "team"
		| "money"
		| "product"
		| "percent"
		| "shopping"
		| "level-up";
	label: string;
	highlight?: string;
	isSubItem?: boolean;
	isHighlight?: boolean;
}) {
	return (
		<motion.li
			className={cn(
				"flex items-baseline gap-2 text-[#D4B485]/60 text-sm",
				isSubItem && "ml-6", // 如果是子项，增加缩进
			)}
			initial={{ opacity: 0, x: -5 }}
			animate={{ opacity: 1, x: 0 }}
			transition={{ duration: 0.2 }}
		>
			<span
				className={cn(
					"flex-shrink-0 h-4 w-4 rounded-full bg-[#D4B485]/10 flex items-center justify-center",
					isSubItem && "h-3 w-3", // 如果是子项，缩小圆点
					isHighlight && "bg-[#D4B485]/20", // 如果是高亮项，加深背景色
				)}
			>
				<motion.div
					initial={{ scale: 0, rotate: -90 }}
					animate={{ scale: 1, rotate: 0 }}
					transition={{
						type: "spring",
						stiffness: 300,
						damping: 10,
					}}
				>
					{!isSubItem ? (
						<CheckIcon
							className={cn(
								"h-2.5 w-2.5",
								isHighlight
									? "text-[#D4B485]"
									: "text-[#D4B485]/60",
							)}
						/>
					) : (
						<div className="h-1.5 w-1.5 rounded-full bg-[#D4B485]/60" />
					)}
				</motion.div>
			</span>
			<span
				className={cn(
					isHighlight && "text-[#D4B485]", // 如果是高亮项，文字颜色加深
				)}
			>
				{label}
				{highlight && (
					<span
						className={cn(
							"ml-1 font-medium",
							zywhFont.className,
							isHighlight && "text-[#D7A550]", // 如果是高亮项，使用金色
						)}
						style={{
							background: isHighlight
								? "linear-gradient(135deg, #FFE4B5 0%, #DEB887 35%, #D2691E 60%, #8B4513 100%)"
								: "linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
							WebkitBackgroundClip: "text",
							WebkitTextFillColor: "transparent",
							backgroundClip: "text",
							textShadow: "0 1px 2px rgba(0,0,0,0.2)",
						}}
					>
						{highlight}
					</span>
				)}
			</span>
		</motion.li>
	);
}
