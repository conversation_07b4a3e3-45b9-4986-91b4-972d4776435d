"use client";

import { AgentRole } from "@prisma/client";
import { useSession } from "@saas/auth/hooks/use-session";
import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { Info, UserPlus } from "lucide-react";
import { memo, useCallback, useMemo, useState } from "react";
import { ConfirmAgent, CreateByPhone, ReferralPolicy } from "./components";
import { SelectRole } from "./components/SelectRole";
import { SelectUser } from "./components/SelectUser";
import { useAgentCreate } from "./hooks/use-agent-create";
import { useAgentSearch } from "./hooks/use-agent-search";
import type { User } from "./types";

// 步骤配置
const STEPS = [
	{
		title: "选择角色",
		description: "选择要开通的代理商角色",
	},
	{
		title: "选择/创建用户",
		description: "选择已有用户或创建新用户",
	},
	{
		title: "确认信息",
		description: "确认开通信息",
	},
];

// 使用memo包装子组件
const MemoizedSelectRole = memo(SelectRole);
const MemoizedSelectUser = memo(SelectUser);
const MemoizedCreateByPhone = memo(CreateByPhone);
const MemoizedConfirmAgent = memo(ConfirmAgent);
const MemoizedReferralPolicy = memo(ReferralPolicy);

function AgentAddFormComponent() {
	const [currentStep, setCurrentStep] = useState(0);
	const { user } = useSession(); // 获取当前用户会话
	const [searchedPhoneNumber, setSearchedPhoneNumber] = useState(""); // 添加状态保存搜索的手机号

	// 使用拆分后的两个hook替代原来的useAgentAdd
	const {
		loading: searchLoading,
		users,
		pagination,
		searchUsers: originalSearchUsers,
		clearSearchCache,
	} = useAgentSearch();

	// 包装searchUsers函数，记录搜索的手机号
	const searchUsers = useCallback(
		async (phoneNumber: string, page?: number, pageSize?: number) => {
			setSearchedPhoneNumber(phoneNumber); // 保存搜索的手机号
			return await originalSearchUsers(phoneNumber, page, pageSize);
		},
		[originalSearchUsers],
	);

	const {
		loading: createLoading,
		state,
		createAgent,
		updateSelectedRole,
		updateSelectedUser,
	} = useAgentCreate();

	// 获取当前用户的代理商角色，默认为ADMIN（如果无法获取）
	const currentRole = useMemo(() => {
		return (user?.agentRole as AgentRole) || AgentRole.ADMIN;
	}, [user?.agentRole]);

	// 获取角色的显示名称
	const currentRoleName = useMemo(() => {
		// 可以从role-rules.ts中获取，这里直接映射
		const roleNameMap = {
			ADMIN: "管理员",
			BRANCH: "分公司",
			DIRECTOR: "联席董事",
			PARTNER: "合伙人",
			SALES: "超级个体",
		};
		return roleNameMap[currentRole] || "未知角色";
	}, [currentRole]);

	// 合并loading状态
	const loading = searchLoading || createLoading;

	// 使用useCallback包装事件处理函数，避免不必要的重新创建
	const handleRoleNext = useCallback(
		(role: AgentRole) => {
			updateSelectedRole(role);
			setCurrentStep(1);
		},
		[updateSelectedRole],
	);

	const handleUserNext = useCallback(
		(user: User) => {
			updateSelectedUser(user);
			// 如果是新用户，携带搜索的手机号进入下一步
			setCurrentStep(2);
		},
		[updateSelectedUser],
	);

	const handleUserPrev = useCallback(() => {
		setCurrentStep(0);
	}, []);

	const handleCreateUserPrev = useCallback(() => {
		setCurrentStep(1);
	}, []);

	const handleCreateUserComplete = useCallback(
		async (phoneCountryCode: string, phoneNumber: string) => {
			// 创建虚拟用户对象
			const newUser: User = {
				id: `new-user-${phoneNumber}`,
				name: "新用户",
				email: "", // 新创建的用户可能没有邮箱
				phoneNumber: phoneNumber,
				phoneNumberFull: `${phoneCountryCode}${phoneNumber}`,
				isAgent: false,
				idCardVerified: false,
				isNewUser: true,
			};

			// 更新用户状态
			updateSelectedUser(newUser);

			// 直接发起创建请求，不再进入确认步骤
			try {
				const success = await createAgent();
				if (success) {
					// 创建成功，会自动跳转到列表页
				}
			} catch (_error) {
				// 失败时返回选择用户页面
				setCurrentStep(1);
			}
		},
		[updateSelectedUser, createAgent],
	);

	const handleConfirmPrev = useCallback(() => {
		// 如果是新用户，回到创建用户步骤
		setCurrentStep(1); // 回到选择/创建用户步骤
	}, [state.selectedUser]);

	const handleConfirmSubmit = useCallback(async () => {
		if (state.selectedUser && state.selectedRole) {
			try {
				const success = await createAgent();
				if (success) {
					// 可以添加成功后的额外处理逻辑
				}
			} catch (_error) {}
		}
	}, [createAgent, state.selectedRole, state.selectedUser]);

	// 使用useMemo包装渲染内容
	const stepContent = useMemo(() => {
		switch (currentStep) {
			case 0:
				return (
					<MemoizedSelectRole
						currentRole={currentRole} // 使用当前用户角色
						onNext={handleRoleNext}
						isLoading={loading}
					/>
				);
			case 1:
				return (
					<MemoizedSelectUser
						searchUsers={searchUsers}
						onNext={handleUserNext}
						onPrev={handleUserPrev}
						isLoading={loading}
						users={users}
						pagination={pagination}
						clearCache={clearSearchCache}
					/>
				);
			case 2:
				if (!state.selectedRole) {
					setCurrentStep(0);
					return null;
				}

				// 如果是在选择用户后新建用户，则显示CreateByPhone，否则显示ConfirmAgent
				if (state.selectedUser?.isNewUser) {
					return (
						<MemoizedCreateByPhone
							selectedRole={state.selectedRole}
							onPrev={handleCreateUserPrev}
							onConfirm={handleCreateUserComplete}
							isLoading={loading}
							initialPhoneNumber={
								state.selectedUser.phoneNumber ||
								searchedPhoneNumber
							} // 使用已保存的手机号
						/>
					);
				}

				if (!state.selectedUser) {
					setCurrentStep(1);
					return null;
				}

				return (
					<MemoizedConfirmAgent
						selectedRole={state.selectedRole}
						selectedUser={state.selectedUser}
						onPrev={handleConfirmPrev}
						onConfirm={handleConfirmSubmit}
						isLoading={loading}
					/>
				);
			default:
				return null;
		}
	}, [
		currentStep,
		currentRole,
		loading,
		searchUsers,
		state.selectedRole,
		state.selectedUser,
		searchedPhoneNumber,
		handleRoleNext,
		handleUserNext,
		handleUserPrev,
		handleCreateUserPrev,
		handleCreateUserComplete,
		handleConfirmPrev,
		handleConfirmSubmit,
		users,
		pagination,
		clearSearchCache,
	]);

	// 渲染步骤指示器
	const stepIndicators = useMemo(
		() => (
			<div className="flex items-center gap-4">
				{STEPS.map((step, index) => {
					return (
						<div
							key={step.title}
							className={cn(
								"flex items-center gap-2",
								index !== STEPS.length - 1 &&
									"after:content-[''] after:w-8 after:h-[2px] after:bg-[#D4B485]/20",
							)}
						>
							<div
								className={cn(
									"flex h-8 w-8 shrink-0 items-center justify-center rounded-full",
									"transition-all duration-200",
									currentStep === index
										? "bg-[#D4B485] text-white ring-4 ring-[#D4B485]/20"
										: index < currentStep
											? "bg-[#D4B485]/20 text-[#D4B485]"
											: "bg-[#1E2023] text-[#D4B485]/40 ring-2 ring-[#D4B485]/20",
								)}
							>
								{index + 1}
							</div>
							<span className="text-sm text-[#D4B485]/60 hidden md:inline-block">
								{step.title}
							</span>
						</div>
					);
				})}
			</div>
		),
		[currentStep, state.selectedUser],
	);

	// 如果是超级个体，只显示推荐政策
	if (currentRole === AgentRole.SALES) {
		return (
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				className="w-full"
				key="referral-policy-only"
			>
				<motion.div
					initial={{ opacity: 0, y: 40 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ delay: 0.5 }}
					className="mt-16"
				>
					{/* 分隔线和标题 */}
					<div className="mb-8 relative">
						<div className="absolute inset-0 flex items-center">
							<div className="w-full border-t border-[#D4B485]/20" />
						</div>
						<div className="relative flex justify-center">
							<span
								className={cn(
									"px-4 bg-[#121416] text-xl font-semibold",
									zywhFont.className,
								)}
								style={{
									background: "#121416",
									WebkitBackgroundClip: "text",
									WebkitTextFillColor: "transparent",
									backgroundClip: "text",
									textShadow:
										"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.1)",
									filter: "contrast(1.1) brightness(1.05)",
								}}
							>
								<span
									style={{
										background:
											"linear-gradient(135deg, rgba(229,201,165,0.95) 0%, rgba(212,180,133,0.9) 50%, rgba(176,137,104,0.85) 100%)",
										WebkitBackgroundClip: "text",
										WebkitTextFillColor: "transparent",
										backgroundClip: "text",
									}}
								>
									推荐政策详情
								</span>
							</span>
						</div>
					</div>

					{/* 推荐政策组件 */}
					<MemoizedReferralPolicy />
				</motion.div>
			</motion.div>
		);
	}

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			className="w-full"
			// 添加key防止热重载时状态丢失
			key="agent-add-form"
		>
			{/* 标题和进度条区域 */}
			<motion.div
				initial={{ opacity: 0, x: -20 }}
				animate={{ opacity: 1, x: 0 }}
				transition={{ delay: 0.2 }}
				className="relative mt-8 mb-6"
			>
				<div className="flex items-center justify-between">
					{/* 标题区域 */}
					<div className="flex items-center gap-4">
						<div
							className={cn(
								"flex h-14 w-14 shrink-0 items-center justify-center",
								"rounded-xl bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
								"ring-1 ring-[#D4B485]/20",
								"shadow-[0_0_0_8px_rgba(212,180,133,0.03)]",
								"transform-gpu transition-transform duration-300",
								"hover:scale-105 hover:shadow-[0_0_0_10px_rgba(212,180,133,0.05)]",
							)}
						>
							<UserPlus className="h-7 w-7 text-[#D4B485]" />
						</div>
						<div className="space-y-1">
							<h2
								className={cn(
									"font-semibold text-3xl",
									zywhFont.className,
									"leading-none",
									"tracking-[0.05em]",
									"relative",
									"after:absolute after:inset-0",
									"after:bg-[radial-gradient(circle_at_50%_50%,rgba(212,180,133,0.15),transparent_70%)]",
									"after:blur-xl after:-z-10",
								)}
								style={{
									background:
										"linear-gradient(135deg, rgba(229,201,165,0.95) 0%, rgba(212,180,133,0.9) 50%, rgba(176,137,104,0.85) 100%)",
									WebkitBackgroundClip: "text",
									WebkitTextFillColor: "transparent",
									backgroundClip: "text",
									textShadow:
										"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.1)",
									filter: "contrast(1.1) brightness(1.05)",
								}}
							>
								新增代理商
								<span className="ml-3 font-normal text-base text-[#D4B485]/80 tracking-wide">
									三步完成开通
								</span>
							</h2>
							<p className="text-sm leading-relaxed text-white/40 transition-colors duration-300">
								为您的团队添加新的代理商，完成团队扩展
							</p>
						</div>
					</div>

					{/* 步骤指示器 */}
					{stepIndicators}
				</div>

				{/* 当前角色提示 */}
				<motion.div
					initial={{ opacity: 0, y: 10 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ delay: 0.4 }}
					className="mt-4 p-3 rounded-lg bg-[#D4B485]/5 border border-[#D4B485]/20 flex items-center text-sm"
				>
					<Info className="h-4 w-4 mr-2 text-[#D4B485]/60" />
					<span className="text-[#D4B485]/80">
						您当前是
						<span className="font-medium text-[#D4B485]">
							{" "}
							{currentRoleName}{" "}
						</span>
						角色，只能开通下级代理商角色
					</span>
				</motion.div>
			</motion.div>

			{/* 主要内容区域 */}
			<motion.div
				initial={{ opacity: 0, y: 40 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.3 }}
				className="mt-6"
			>
				<div
					className={cn(
						"rounded-xl",
						"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
						"border border-[#D4B485]/20",
						"p-8",
						"shadow-[0_8px_32px_-4px_rgba(0,0,0,0.3)]",
						"backdrop-blur-xl",
					)}
				>
					{stepContent}
				</div>
			</motion.div>

			{/* 推荐政策区域 */}
			<motion.div
				initial={{ opacity: 0, y: 40 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.5 }}
				className="mt-16"
			>
				{/* 分隔线和标题 */}
				<div className="mb-8 relative">
					<div className="absolute inset-0 flex items-center">
						<div className="w-full border-t border-[#D4B485]/20" />
					</div>
					<div className="relative flex justify-center">
						<span
							className={cn(
								"px-4 bg-[#121416] text-xl font-semibold",
								zywhFont.className,
							)}
							style={{
								background: "#121416",
								WebkitBackgroundClip: "text",
								WebkitTextFillColor: "transparent",
								backgroundClip: "text",
								textShadow:
									"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.1)",
								filter: "contrast(1.1) brightness(1.05)",
							}}
						>
							<span
								style={{
									background:
										"linear-gradient(135deg, rgba(229,201,165,0.95) 0%, rgba(212,180,133,0.9) 50%, rgba(176,137,104,0.85) 100%)",
									WebkitBackgroundClip: "text",
									WebkitTextFillColor: "transparent",
									backgroundClip: "text",
								}}
							>
								推荐政策详情
							</span>
						</span>
					</div>
				</div>

				{/* 推荐政策组件 */}
				<MemoizedReferralPolicy />
			</motion.div>
		</motion.div>
	);
}

// 导出使用memo包装的主组件
export const AgentAddForm = memo(AgentAddFormComponent);
