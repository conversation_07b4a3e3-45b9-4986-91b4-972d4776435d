import type { AgentR<PERSON> } from "@prisma/client";

// 角色名称映射
export const ROLE_LABELS: Record<AgentRole, string> = {
	ADMIN: "管理员",
	BRANCH: "分公司",
	DIRECTOR: "联席董事",
	PARTNER: "合伙人",
	SALES: "超级个体",
};

// 角色权重映射
export const ROLE_WEIGHTS: Record<AgentRole, number> = {
	ADMIN: 5,
	BRANCH: 4,
	DIRECTOR: 3,
	PARTNER: 2,
	SALES: 1,
};

// 代理商节点类型
export interface AgentNode {
	id: string;
	name: string;
	role: AgentRole;
	email: string;
	phone: string;
	status: string;
	teamSize: number;
	children?: AgentNode[];
}

// 代理商列表项类型
export interface AgentListItem {
	id: string;
	userId: string;
	name: string;
	role: AgentRole;
	email: string;
	phone: string;
	status: string;
	teamSize: number;
	performance: number;
	avatar: string | null;
	hasChildren: boolean;
	createdAt: string;
	parentId?: string | null;
	path?: string | null;
}

// 代理商列表项展示类型 (与 AgentListItem 保持一致)
export interface AgentItemData extends AgentListItem {}

// 查询参数类型
export interface AgentListQuery {
	page: number;
	pageSize: number;
	searchTerm?: string;
	role?: AgentRole | "ALL";
	status?: string;
	sortBy?: "createdAt" | "name" | "teamSize" | "performance" | "role";
	sortOrder?: "asc" | "desc";
	parentId?: string;
}

// 筛选选项类型
export interface AgentFilterOptions {
	searchTerm: string;
	role: AgentRole | "ALL";
	status: string;
}

// 团队成员筛选参数类型
export interface TeamMembersFilterParams {
	page: number;
	pageSize: number;
	searchTerm?: string;
	role?: AgentRole | "ALL";
	status?: string;
}
