import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";
import type { AgentListItem, AgentListQuery } from "../types";

// API响应类型
export interface ApiResponse<T> {
	data: T;
	message: string;
	success: boolean;
}

export function useAgentList() {
	const [loading, setLoading] = useState(false);
	const [agents, setAgents] = useState<AgentListItem[]>([]);
	const [total, setTotal] = useState(0);
	const [currentPage, setCurrentPage] = useState(1);
	const [totalPages, setTotalPages] = useState(0);

	const fetchAgents = useCallback(async (query: AgentListQuery) => {
		setLoading(true);

		try {
			const response = await apiClient.v1.agent.agents.list.$get({
				query,
			});

			const result = await response.json();

			if (response.ok && result.code === 0) {
				if (!result.data?.items) {
					toast.error("获取代理商列表失败: 响应数据格式不正确");
					return;
				}

				// 确保每个代理商数据都包含 userId
				const agentsWithUserId = result.data.items.map((item) => ({
					...item,
					userId: item.id, // 直接使用 id 作为 userId
				}));

				setAgents(agentsWithUserId);
				setTotal(result.data.total ?? 0);
				setCurrentPage(result.data.page ?? 1);
				setTotalPages(result.data.totalPages ?? 0);
			} else {
				const errorMsg = result.message || response.statusText;

				toast.error(`获取代理商列表失败: ${errorMsg}`);
			}
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : String(error);
			toast.error(`获取代理商列表失败: ${errorMessage}`);
		} finally {
			setLoading(false);
		}
	}, []);

	return {
		loading,
		agents,
		total,
		currentPage,
		totalPages,
		fetchAgents,
		setAgents,
	};
}
