import { useState } from "react";
import type { AgentNode } from "../types";

export function useAgentHierarchy() {
	const [loading, setLoading] = useState(false);
	const [hierarchy, setHierarchy] = useState<AgentNode | null>(null);

	const fetchHierarchy = async (rootId: string) => {
		setLoading(true);
		try {
			// TODO: Implement API call to fetch hierarchy data
			const response = await fetch(`/api/agents/${rootId}/hierarchy`);
			const data = await response.json();
			setHierarchy(data);
		} catch (_error) {
		} finally {
			setLoading(false);
		}
	};

	return {
		loading,
		hierarchy,
		fetchHierarchy,
	};
}
