import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";

// 禁用代理商的请求参数
export interface DisableAgentParams {
	id: string;
	reason?: string;
}

// API响应类型
interface ApiResponse {
	code: number;
	message: string;
	data?: {
		success: boolean;
		agentId: string;
		status: string;
	};
}

export function useAgentDisable() {
	const [loading, setLoading] = useState(false);

	const disableAgent = useCallback(async (params: DisableAgentParams) => {
		setLoading(true);

		try {
			const response =
				await apiClient.v1.agent.agents.update.disable.$post({
					json: params,
				});

			const result = (await response.json()) as ApiResponse;

			if (response.ok && result.code === 0) {
				if (!result.data?.success) {
					toast.error("禁用代理商失败: 响应数据格式不正确");
					return false;
				}

				return true;
			}
			const errorMsg = result.message;

			toast.error(`禁用代理商失败: ${errorMsg}`);
			return false;
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : String(error);

			toast.error(`禁用代理商失败: ${errorMessage}`);
			return false;
		} finally {
			setLoading(false);
		}
	}, []);

	return {
		loading,
		disableAgent,
	};
}
