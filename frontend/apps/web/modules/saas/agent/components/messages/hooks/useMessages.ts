import type {
	MessageStatus,
	MessageType,
} from "@repo/api/src/routes/v1/agent/messages/types";
import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";

// 获取消息列表参数
export interface GetMessagesParams {
	page?: number;
	limit?: number;
	type?: MessageType;
	status?: MessageStatus;
}

// 消息列表响应
export interface MessagesResponse {
	messages: Array<{
		id: string;
		userId: string;
		title: string;
		content: string;
		type: MessageType;
		routeLink?: string; // 跳转路由链接(可选)
		status: MessageStatus;
		readAt: string | null;
		createdAt: string;
		updatedAt: string;
	}>;
	total: number;
	page: number;
	limit: number;
}

/**
 * 消息列表hook
 * 用于获取消息中心的消息列表
 */
export function useMessages() {
	const [loading, setLoading] = useState(false);
	const [messages, setMessages] = useState<MessagesResponse["messages"]>([]);
	const [total, setTotal] = useState(0);
	const [page, setPage] = useState(1);
	const [limit, setLimit] = useState(10);

	/**
	 * 获取消息列表
	 */
	const fetchMessages = useCallback(
		async (params: GetMessagesParams = {}) => {
			setLoading(true);
			try {
				// logger.info("开始获取消息列表", { params });

				const response = await apiClient.v1.agent.messages.list.$get({
					query: params,
				});

				const result = (await response.json()) as MessagesResponse;

				// logger.info("消息列表响应:", result);

				if (response.ok) {
					setMessages(result.messages);
					setTotal(result.total);
					setPage(result.page);
					setLimit(result.limit);
					return result;
				}
				throw new Error("获取消息列表失败");
			} catch (error) {
				// logger.error("获取消息列表失败", { error });
				toast.error("获取消息列表失败", {
					description:
						error instanceof Error ? error.message : "请重试",
				});
				return {
					messages: [],
					total: 0,
					page: 1,
					limit: 10,
				};
			} finally {
				setLoading(false);
			}
		},
		[],
	);

	return {
		loading,
		messages,
		total,
		page,
		limit,
		fetchMessages,
	};
}
