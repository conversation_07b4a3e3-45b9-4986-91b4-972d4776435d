"use client";

import { But<PERSON> } from "@ui/components/button";
import { cn } from "@ui/lib";
import { AnimatePresence, motion } from "framer-motion";
import { Bell, ChevronRight, X } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

interface Message {
	id: string;
	title: string;
}

interface UnreadMessageToastProps {
	messages: Message[];
	totalCount: number;
	onClose: () => void;
}

export function UnreadMessageToast({
	messages,
	totalCount,
	onClose,
}: UnreadMessageToastProps) {
	const router = useRouter();
	const [visible, setVisible] = useState(true);

	// 自动关闭逻辑（10秒后自动关闭）
	useEffect(() => {
		const timer = setTimeout(() => {
			handleClose();
		}, 10000);

		return () => clearTimeout(timer);
	}, []);

	const handleClose = () => {
		setVisible(false);
		setTimeout(() => {
			onClose();
		}, 300); // 等动画结束后才移除组件
	};

	const handleViewMore = () => {
		router.push("/app/agent/messages");
		handleClose();
	};

	const handleKeyDown = (event: React.KeyboardEvent) => {
		if (event.key === "Enter" || event.key === " ") {
			router.push("/app/agent/messages");
		}
	};

	return (
		<AnimatePresence>
			{visible && (
				<motion.div
					initial={{ opacity: 0, scale: 0.9 }}
					animate={{ opacity: 1, scale: 1 }}
					exit={{ opacity: 0, scale: 0.9 }}
					transition={{ duration: 0.3 }}
					className={cn(
						"fixed inset-0 z-50 flex items-center justify-center",
						"bg-black/50 backdrop-blur-sm",
					)}
					onClick={handleClose}
				>
					<motion.div
						initial={{ opacity: 0, y: -20 }}
						animate={{ opacity: 1, y: 0 }}
						exit={{ opacity: 0, y: 20 }}
						transition={{ duration: 0.3, delay: 0.1 }}
						className={cn(
							"w-[450px] rounded-xl",
							"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
							"border border-[#D4B485]/30",
							"shadow-[0_0_30px_rgba(212,180,133,0.15)]",
							"overflow-hidden",
						)}
						onClick={(e) => e.stopPropagation()}
					>
						{/* 顶部标题栏 */}
						<div className="flex items-center justify-between p-4 bg-[#D4B485]/10 border-b border-[#D4B485]/20">
							<div className="flex items-center gap-2">
								<div className="p-1.5 rounded-full bg-[#D4B485]/15">
									<Bell className="h-5 w-5 text-[#D4B485]" />
								</div>
								<span className="font-medium text-[#D4B485] text-lg">
									新消息提醒
								</span>
								<span className="flex items-center justify-center h-6 min-w-6 rounded-full bg-red-500 text-white text-xs font-medium px-2">
									{totalCount}
								</span>
							</div>
							<button
								type="button"
								onClick={handleClose}
								onKeyDown={(e) =>
									e.key === "Enter" && handleClose()
								}
								className="rounded-full p-2 hover:bg-[#D4B485]/10 transition-colors"
							>
								<X className="h-5 w-5 text-[#D4B485]/60" />
							</button>
						</div>

						{/* 消息列表 */}
						<div className="p-4 space-y-3">
							{messages.slice(0, 3).map((message) => (
								<button
									key={message.id}
									className="p-3 rounded-lg bg-[#D4B485]/5 hover:bg-[#D4B485]/10 cursor-pointer transition-colors w-full text-left"
									onClick={() =>
										router.push("/app/agent/messages")
									}
									onKeyDown={handleKeyDown}
									type="button"
								>
									<p className="text-sm text-[#D4B485]/90 line-clamp-1">
										{message.title}
									</p>
								</button>
							))}
						</div>

						{/* 底部操作区 */}
						<div className="p-4 pt-2 flex gap-3">
							<Button
								type="button"
								onClick={handleClose}
								variant="ghost"
								className="flex-1 text-[#D4B485]/70 hover:bg-[#D4B485]/10 hover:text-[#D4B485] transition-colors"
							>
								关闭
							</Button>
							<Button
								type="button"
								onClick={handleViewMore}
								variant="outline"
								className="flex-1 border-[#D4B485]/20 text-[#D4B485] hover:bg-[#D4B485] hover:text-[#1A1C1E] transition-colors"
							>
								<span>查看所有消息</span>
								<ChevronRight className="h-4 w-4 ml-1" />
							</Button>
						</div>
					</motion.div>
				</motion.div>
			)}
		</AnimatePresence>
	);
}
