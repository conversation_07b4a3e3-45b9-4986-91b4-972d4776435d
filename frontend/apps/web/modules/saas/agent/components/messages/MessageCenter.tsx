"use client";

import {
	MessageStatus,
	MessageType,
} from "@repo/api/src/routes/v1/agent/messages/types";
import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Button } from "@ui/components/button";
import { Card } from "@ui/components/card";
import { Checkbox } from "@ui/components/checkbox";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { Input } from "@ui/components/input";
import { Skeleton } from "@ui/components/skeleton";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import {
	ArchiveIcon,
	Bell,
	Calendar,
	CheckCircle,
	CheckIcon,
	Download,
	MessageCircle,
	RefreshCwIcon,
	Search,
	Settings,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { useMessageBatchStatus, useMessageStatus, useMessages } from "./hooks";

// 消息类型图标组件
const MessageTypeIcon = ({ type }: { type: string }) => {
	const iconClassName = "h-5 w-5";
	const containerClassName = cn(
		"flex items-center justify-center",
		"w-8 h-8 rounded-full",
		"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
		"border border-[#D4B485]/20",
	);

	switch (type) {
		case MessageType.AGENT:
			return (
				<div className={containerClassName}>
					<CheckCircle
						className={cn(iconClassName, "text-emerald-500")}
					/>
				</div>
			);
		case MessageType.PERFORMANCE:
			return (
				<div className={containerClassName}>
					<Bell className={cn(iconClassName, "text-[#D4B485]")} />
				</div>
			);
		case MessageType.SYSTEM:
			return (
				<div className={containerClassName}>
					<Settings className={cn(iconClassName, "text-blue-500")} />
				</div>
			);
		case MessageType.ORDER:
			return (
				<div className={containerClassName}>
					<Download
						className={cn(iconClassName, "text-purple-500")}
					/>
				</div>
			);
		case MessageType.PLAN:
			return (
				<div className={containerClassName}>
					<Calendar
						className={cn(iconClassName, "text-orange-500")}
					/>
				</div>
			);
		case MessageType.QUOTA:
			return (
				<div className={containerClassName}>
					<CheckCircle
						className={cn(iconClassName, "text-green-500")}
					/>
				</div>
			);
		default:
			return (
				<div className={containerClassName}>
					<MessageCircle
						className={cn(iconClassName, "text-[#D4B485]/40")}
					/>
				</div>
			);
	}
};

// 消息列表项组件
const MessageItem = ({
	message,
	isSelected,
	onSelect,
	onStatusChange,
	router,
}: {
	message: any;
	isSelected: boolean;
	onSelect: (id: string, selected: boolean) => void;
	onStatusChange: (id: string, status: MessageStatus) => void;
	router: any;
}) => {
	const handleProcess = () => {
		// 点击"去处理"按钮时，自动将消息标记为已读状态
		if (message.status === MessageStatus.UNREAD) {
			onStatusChange(message.id, MessageStatus.READ);
		}

		if (message.routeLink) {
			router.push(message.routeLink);
		}
	};

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ delay: 0.1 }}
			className={cn(
				"rounded-lg p-6",
				"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
				"border border-[#D4B485]/20",
				"group",
				"hover:border-[#D4B485]/40",
				"transition-all duration-200",
				"relative overflow-hidden",
				message.status === MessageStatus.UNREAD &&
					"bg-[#D4B485]/5 border-[#D4B485]/30",
			)}
		>
			{/* 背景装饰 */}
			<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
			<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />

			<div className="relative flex items-start gap-4">
				{/* 选择框 */}
				<div className="flex items-center h-8">
					<Checkbox
						checked={isSelected}
						onCheckedChange={(checked) =>
							onSelect(message.id, !!checked)
						}
						className="border-[#D4B485]/40 data-[state=checked]:bg-[#D4B485] data-[state=checked]:text-[#1A1C1E]"
					/>
				</div>

				{/* 消息类型图标 */}
				<MessageTypeIcon type={message.type} />

				{/* 消息内容 */}
				<div className="flex-1 min-w-0">
					<div className="flex items-center gap-3">
						<h3
							className={cn(
								"font-medium",
								message.status === MessageStatus.UNREAD
									? "text-[#D4B485]"
									: "text-[#D4B485]/60",
							)}
						>
							{message.title}
						</h3>
						{message.status === MessageStatus.UNREAD && (
							<span className="inline-flex h-2 w-2 rounded-full bg-[#D4B485]" />
						)}
					</div>
					<p className="mt-1 text-sm text-[#D4B485]/40 line-clamp-2">
						{message.content}
					</p>
					<div className="mt-2 flex items-center justify-between">
						<span className="text-sm text-[#D4B485]/40">
							{new Date(message.createdAt).toLocaleString(
								"zh-CN",
								{
									year: "numeric",
									month: "2-digit",
									day: "2-digit",
									hour: "2-digit",
									minute: "2-digit",
								},
							)}
						</span>

						{/* 操作按钮 */}
						<div className="flex items-center gap-2">
							{message.status === MessageStatus.UNREAD ? (
								<Button
									variant="ghost"
									size="sm"
									className="text-[#D4B485]/60 hover:text-[#D4B485] hover:bg-[#D4B485]/10"
									onClick={() =>
										onStatusChange(
											message.id,
											MessageStatus.READ,
										)
									}
								>
									<CheckIcon className="h-4 w-4 mr-1" />
									标为已读
								</Button>
							) : null}

							{message.status !== MessageStatus.ARCHIVED && (
								<Button
									variant="ghost"
									size="sm"
									className="text-[#D4B485]/60 hover:text-[#D4B485] hover:bg-[#D4B485]/10"
									onClick={() =>
										onStatusChange(
											message.id,
											MessageStatus.ARCHIVED,
										)
									}
								>
									<ArchiveIcon className="h-4 w-4 mr-1" />
									归档
								</Button>
							)}

							{message.routeLink && (
								<Button
									variant="outline"
									size="sm"
									className="text-[#D4B485] hover:text-[#1A1C1E] hover:bg-[#D4B485] border-[#D4B485] font-medium transition-colors"
									onClick={handleProcess}
								>
									{message.status === MessageStatus.UNREAD
										? "去处理"
										: "去查看"}
								</Button>
							)}
						</div>
					</div>
				</div>
			</div>
		</motion.div>
	);
};

// 消息列表加载骨架屏
const MessageSkeleton = () => {
	return (
		<div className="rounded-lg p-6 bg-gradient-to-br from-[#1E2023] to-[#1A1C1E] border border-[#D4B485]/20">
			<div className="flex items-start gap-4">
				<Skeleton className="h-8 w-8 rounded-full bg-[#D4B485]/10" />
				<div className="flex-1">
					<Skeleton className="h-6 w-48 mb-2 bg-[#D4B485]/10" />
					<Skeleton className="h-4 w-full mb-2 bg-[#D4B485]/10" />
					<Skeleton className="h-4 w-3/4 mb-2 bg-[#D4B485]/10" />
					<div className="flex justify-between mt-2">
						<Skeleton className="h-4 w-24 bg-[#D4B485]/10" />
						<Skeleton className="h-8 w-24 bg-[#D4B485]/10" />
					</div>
				</div>
			</div>
		</div>
	);
};

export function MessageCenter() {
	const router = useRouter();
	const [searchTerm, setSearchTerm] = useState("");
	const [filterStatus, setFilterStatus] = useState<MessageStatus | "">("");

	const { loading, messages, total, fetchMessages } = useMessages();

	const { updateStatus } = useMessageStatus();
	const { batchUpdateStatus } = useMessageBatchStatus();

	const [selectedMessages, setSelectedMessages] = useState<string[]>([]);

	// 初始加载消息
	useEffect(() => {
		fetchMessages();
	}, [fetchMessages]);

	// 过滤消息
	const filteredMessages = messages.filter((message) => {
		const matchesSearch = searchTerm
			? message.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
				message.content.toLowerCase().includes(searchTerm.toLowerCase())
			: true;

		const matchesStatus = filterStatus
			? message.status === filterStatus
			: true;

		return matchesSearch && matchesStatus;
	});

	// 处理消息选择
	const handleSelectMessage = useCallback(
		(_id: string, _selected: boolean) => {
			// Implementation of handleSelectMessage
		},
		[],
	);

	// 处理全选
	const handleSelectAll = useCallback(
		(_selected: boolean) => {
			// Implementation of handleSelectAll
		},
		[filteredMessages],
	);

	// 处理单个消息状态更新
	const handleStatusChange = useCallback(
		async (id: string, status: MessageStatus) => {
			await updateStatus(id, status);
			fetchMessages();
		},
		[updateStatus, fetchMessages],
	);

	// 处理批量更新状态
	const handleBatchUpdate = useCallback(
		async (status: MessageStatus) => {
			if (selectedMessages.length === 0) {
				return;
			}

			await batchUpdateStatus(selectedMessages, status);
			setSelectedMessages([]);
			fetchMessages();
		},
		[selectedMessages, batchUpdateStatus, fetchMessages],
	);

	// 当前选中的消息数量
	const selectedCount = selectedMessages.length;
	// 是否全部选中
	const allSelected =
		filteredMessages.length > 0 &&
		selectedCount === filteredMessages.length;

	return (
		<div className="w-full space-y-6">
			{/* 标题和操作栏 */}
			<div className="flex items-center justify-between">
				<h1
					className={cn("text-2xl font-bold", zywhFont.className)}
					style={{
						background:
							"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
						WebkitBackgroundClip: "text",
						WebkitTextFillColor: "transparent",
						backgroundClip: "text",
						textShadow:
							"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
					}}
				>
					消息中心
				</h1>
				<div className="flex items-center gap-4">
					<div className="flex items-center gap-2">
						<span className="text-[#D4B485]/60">
							共 {total} 条消息
						</span>
					</div>
					<Button
						variant="outline"
						className={cn(
							"gap-2",
							"bg-[#1A1C1E]",
							"border-[#D4B485]/20",
							"text-[#D4B485]",
							"hover:bg-[#D4B485]/10",
						)}
						onClick={() => fetchMessages()}
					>
						<RefreshCwIcon className="h-4 w-4" />
						<span>刷新</span>
					</Button>
				</div>
			</div>

			{/* 搜索和筛选栏 */}
			<Card
				className={cn(
					"p-4",
					"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
					"border border-[#D4B485]/20",
					"group",
					"hover:border-[#D4B485]/40",
					"transition-all duration-500",
				)}
			>
				<div className="flex items-center gap-4">
					<div className="relative flex-1">
						<Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-[#D4B485]/40" />
						<Input
							placeholder="搜索消息..."
							className={cn(
								"pl-10",
								"bg-[#1A1C1E]",
								"border-[#D4B485]/20",
								"text-[#D4B485]",
								"placeholder:text-[#D4B485]/40",
							)}
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
						/>
					</div>

					<DropdownMenu>
						<DropdownMenuTrigger asChild>
							<Button
								variant="outline"
								className={cn(
									"bg-[#1A1C1E]",
									"border-[#D4B485]/20",
									"text-[#D4B485]",
									"hover:bg-[#D4B485]/10",
								)}
							>
								{filterStatus === MessageStatus.UNREAD
									? "未读"
									: filterStatus === MessageStatus.READ
										? "已读"
										: filterStatus ===
												MessageStatus.ARCHIVED
											? "已归档"
											: "全部状态"}
							</Button>
						</DropdownMenuTrigger>
						<DropdownMenuContent className="bg-[#1A1C1E] border-[#D4B485]/20">
							<DropdownMenuItem
								className="text-[#D4B485] hover:bg-[#D4B485]/10 cursor-pointer"
								onClick={() => setFilterStatus("")}
							>
								全部状态
							</DropdownMenuItem>
							<DropdownMenuItem
								className="text-[#D4B485] hover:bg-[#D4B485]/10 cursor-pointer"
								onClick={() =>
									setFilterStatus(MessageStatus.UNREAD)
								}
							>
								未读
							</DropdownMenuItem>
							<DropdownMenuItem
								className="text-[#D4B485] hover:bg-[#D4B485]/10 cursor-pointer"
								onClick={() =>
									setFilterStatus(MessageStatus.READ)
								}
							>
								已读
							</DropdownMenuItem>
							<DropdownMenuItem
								className="text-[#D4B485] hover:bg-[#D4B485]/10 cursor-pointer"
								onClick={() =>
									setFilterStatus(MessageStatus.ARCHIVED)
								}
							>
								已归档
							</DropdownMenuItem>
						</DropdownMenuContent>
					</DropdownMenu>
				</div>
			</Card>

			{/* 批量操作栏 */}
			{selectedCount > 0 && (
				<div className="flex items-center justify-between p-4 bg-[#1A1C1E] border border-[#D4B485]/20 rounded-lg">
					<div className="flex items-center gap-2">
						<Checkbox
							checked={allSelected}
							onCheckedChange={handleSelectAll}
							className="border-[#D4B485]/40 data-[state=checked]:bg-[#D4B485] data-[state=checked]:text-[#1A1C1E]"
						/>
						<span className="text-[#D4B485]">
							已选择 {selectedCount} 条消息
						</span>
					</div>
					<div className="flex items-center gap-2">
						<Button
							variant="outline"
							size="sm"
							className="text-[#D4B485] hover:bg-[#D4B485]/10 border-[#D4B485]/20"
							onClick={() =>
								handleBatchUpdate(MessageStatus.READ)
							}
						>
							<CheckIcon className="h-4 w-4 mr-1" />
							标为已读
						</Button>
						{/* 移除批量"标为未读"按钮，否则会将已读或已归档的消息标记为未读 */}
						<Button
							variant="outline"
							size="sm"
							className="text-[#D4B485] hover:bg-[#D4B485]/10 border-[#D4B485]/20"
							onClick={() =>
								handleBatchUpdate(MessageStatus.ARCHIVED)
							}
						>
							<ArchiveIcon className="h-4 w-4 mr-1" />
							归档
						</Button>
					</div>
				</div>
			)}

			{/* 消息列表 */}
			<div className="grid gap-4">
				{loading ? (
					// 加载中显示骨架屏
					<>
						<MessageSkeleton />
						<MessageSkeleton />
						<MessageSkeleton />
					</>
				) : filteredMessages.length > 0 ? (
					// 显示消息列表
					filteredMessages.map((message) => (
						<MessageItem
							key={message.id}
							message={message}
							isSelected={selectedMessages.includes(message.id)}
							onSelect={handleSelectMessage}
							onStatusChange={handleStatusChange}
							router={router}
						/>
					))
				) : (
					// 无消息时显示空状态
					<div className="flex flex-col items-center justify-center py-12 text-[#D4B485]/40">
						<MessageCircle className="h-12 w-12 mb-4" />
						<p className="text-lg font-medium">暂无消息</p>
						<p className="mt-2">
							{searchTerm || filterStatus
								? "没有符合筛选条件的消息"
								: "您的消息中心目前为空"}
						</p>
					</div>
				)}
			</div>

			{/* 分页 - 可以根据需要后续实现 */}
		</div>
	);
}
