"use client";
import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import { cn } from "@ui/lib";
import type { LucideIcon } from "lucide-react";
import { ChevronDownIcon, ChevronRightIcon } from "lucide-react";
import { memo } from "react";

interface SubItem {
	label: string;
	href: string;
	icon: LucideIcon;
	isActive: boolean;
}

interface MenuItem {
	label: string;
	href: string;
	icon: LucideIcon;
	isActive: boolean;
	subItems?: SubItem[];
	badge?: number;
}

interface MenuItemProps {
	item: MenuItem;
	isActive: boolean;
	onClick: (href: string) => void;
	toggleMenu: (label: string) => void;
	isCollapsed?: boolean;
	pathname?: string;
	DynamicComponent?: React.ComponentType | null;
	isOpen?: boolean;
}

export const AgentMenuItem = memo(function AgentMenuItem({
	item,
	isActive,
	onClick,
	toggleMenu,
	isCollapsed = false,
	pathname = "",
	DynamicComponent = null,
	isOpen = false,
}: MenuItemProps) {
	const isSubItemActive = item.subItems?.some(
		(subItem) => pathname === subItem.href,
	);
	const isMenuActive = isActive || isSubItemActive;

	const handleClick = (href: string) => {
		onClick(href);
	};

	const handleToggleMenu = (label: string) => {
		toggleMenu(label);
	};

	const Icon = item.icon;

	if (isCollapsed) {
		if (item.subItems) {
			return (
				<Popover>
					<PopoverTrigger asChild>
						<button
							type="button"
							className={cn(
								"flex items-center justify-center",
								"py-[0.75rem] px-[0.5rem]",
								"cursor-pointer",
								"mx-[0.5rem] my-[0.25rem]",
								"rounded-lg",
								"transition-all duration-300 ease-[cubic-bezier(0.4,0,0.2,1)]",
								"backdrop-blur-sm",
								"hover:scale-[1.02]",
								isMenuActive
									? "bg-[#1E2532] shadow-[0_0_20px_rgba(248,197,155,0.15)] ring-1 ring-[#F8C59B]/20"
									: "text-[#A1A1AA] hover:bg-[#1E2532]/90 hover:shadow-[0_0_15px_rgba(248,197,155,0.1)]",
								"hover:bg-[#F8C59B]/[0.03]",
								"hover:shadow-[inset_0_0_20px_rgba(248,197,155,0.05)]",
								"[&:hover_span]:[filter:contrast(1.2)_brightness(1.15)]",
							)}
						>
							<div className="relative">
								<Icon
									className={cn(
										"w-[1.5rem] h-[1.5rem]",
										"transition-all duration-300",
										isMenuActive
											? "text-[#F8C59B] drop-shadow-[0_0_8px_rgba(248,197,155,0.3)]"
											: "text-[#A1A1AA]",
									)}
								/>
								{item.badge !== undefined && item.badge > 0 && (
									<span className="absolute -top-1.5 -right-1.5 flex items-center justify-center h-4 min-w-4 rounded-full bg-red-500 text-[10px] text-white font-medium">
										{item.badge > 99 ? "99+" : item.badge}
									</span>
								)}
							</div>
						</button>
					</PopoverTrigger>
					<PopoverContent
						side="right"
						className={cn(
							"p-0 w-[220px]",
							"bg-[#1E2023]/95 backdrop-blur-xl",
							"border-[#D4B485]/20",
							"rounded-lg overflow-hidden",
							"animate-in fade-in-0 zoom-in-95 slide-in-from-right-2",
							"data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[state=closed]:slide-out-to-right-2",
						)}
						sideOffset={8}
					>
						<div className="py-2">
							<div
								className={cn(
									"px-5 py-2.5",
									"text-[16px]",
									"font-medium",
									"text-[#F8C59B]",
									"border-b border-[#D4B485]/10",
									"bg-gradient-to-r from-[#D4B485]/5 to-transparent",
								)}
							>
								{item.label}
							</div>
							{item.subItems && (
								<ul className="py-1.5">
									{item.subItems.map((subItem) => {
										const SubIcon = subItem.icon;
										return (
											<li key={subItem.href}>
												<button
													type="button"
													onClick={() =>
														handleClick(
															subItem.href,
														)
													}
													className={cn(
														"flex w-full items-center",
														"px-5 py-2.5",
														"text-[15px]",
														subItem.isActive
															? "text-[#F8C59B] bg-[#D4B485]/10"
															: "text-[#A1A1AA]",
														"hover:bg-[#D4B485]/5",
														"hover:text-[#F8C59B]",
														"transition-all duration-200",
														"group",
														"hover:translate-x-1",
													)}
												>
													<SubIcon
														className={cn(
															"w-[1.125rem] h-[1.125rem] mr-3 flex-shrink-0",
															"transition-transform duration-300",
															"group-hover:scale-110 group-hover:rotate-[8deg]",
															subItem.isActive
																? "text-[#F8C59B]"
																: "text-[#A1A1AA] group-hover:text-[#F8C59B]",
														)}
													/>
													<span
														className={cn(
															zywhFont.className,
															"tracking-[0.02em] truncate",
															"transition-all duration-300",
															"group-hover:translate-x-1",
														)}
														style={{
															background:
																"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
															WebkitBackgroundClip:
																"text",
															WebkitTextFillColor:
																"transparent",
															backgroundClip:
																"text",
															textShadow:
																"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.1)",
															filter: subItem.isActive
																? "contrast(1.2) brightness(1.15)"
																: "contrast(0.9) brightness(0.85)",
														}}
													>
														{subItem.label}
													</span>
												</button>
											</li>
										);
									})}
								</ul>
							)}
						</div>
					</PopoverContent>
				</Popover>
			);
		}

		return (
			<button
				type="button"
				className={cn(
					"flex items-center justify-center",
					"py-[0.75rem] px-[0.5rem]",
					"cursor-pointer",
					"mx-[0.5rem] my-[0.25rem]",
					"rounded-lg",
					"transition-all duration-300 ease-[cubic-bezier(0.4,0,0.2,1)]",
					"backdrop-blur-sm",
					"hover:scale-[1.02]",
					isMenuActive
						? "bg-[#1E2532] shadow-[0_0_20px_rgba(248,197,155,0.15)] ring-1 ring-[#F8C59B]/20"
						: "text-[#A1A1AA] hover:bg-[#1E2532]/90 hover:shadow-[0_0_15px_rgba(248,197,155,0.1)]",
					"hover:bg-[#F8C59B]/[0.03]",
					"hover:shadow-[inset_0_0_20px_rgba(248,197,155,0.05)]",
					"[&:hover_span]:[filter:contrast(1.2)_brightness(1.15)]",
				)}
				onClick={() => handleClick(item.href)}
			>
				<div className="relative">
					<Icon
						className={cn(
							"w-[1.5rem] h-[1.5rem]",
							"transition-all duration-300",
							isMenuActive
								? "text-[#F8C59B] drop-shadow-[0_0_8px_rgba(248,197,155,0.3)]"
								: "text-[#A1A1AA]",
						)}
					/>
					{item.badge !== undefined && item.badge > 0 && (
						<span className="absolute -top-1.5 -right-1.5 flex items-center justify-center h-4 min-w-4 rounded-full bg-red-500 text-[10px] text-white font-medium">
							{item.badge > 99 ? "99+" : item.badge}
						</span>
					)}
				</div>
			</button>
		);
	}

	return (
		<>
			{item.subItems ? (
				<>
					<button
						type="button"
						className={cn(
							"flex items-center justify-between w-full",
							"py-[0.75rem] px-[1rem]",
							"font-medium cursor-pointer",
							"mx-[0.5rem] my-[0.25rem]",
							"rounded-lg",
							"transition-all duration-500 ease-out",
							"backdrop-blur-sm",
							"group",
							"relative",
							"hover:translate-x-2",
							"before:absolute before:inset-0",
							"before:rounded-lg",
							"before:transition-all before:duration-500",
							"before:ease-out",
							"before:scale-[0.97]",
							"before:opacity-0",
							"before:bg-gradient-to-r",
							"before:from-transparent",
							"before:via-[#1E2532]",
							"before:to-transparent",
							"before:bg-clip-padding",
							"before:backdrop-filter",
							"before:backdrop-blur-[8px]",
							"before:border-transparent",
							"before:bg-opacity-50",
							"hover:before:scale-100 hover:before:opacity-100",
							"before:z-[-1]",
							"after:absolute after:inset-0",
							"after:rounded-lg",
							"after:transition-all after:duration-500",
							"after:ease-out",
							"after:scale-95",
							"after:bg-radial-gradient-ellipse",
							"after:from-[#F8C59B]/5",
							"after:via-transparent",
							"after:to-transparent",
							"hover:after:opacity-100",
							"after:z-[-2]",
							isOpen
								? isActive && !isSubItemActive
									? "bg-[#F8C59B]/[0.05] shadow-[inset_0_0_30px_rgba(248,197,155,0.08)] text-[#F8C59B]"
									: "text-[#F8C59B]/90"
								: isActive && !isSubItemActive
									? "bg-[#F8C59B]/[0.05] shadow-[inset_0_0_30px_rgba(248,197,155,0.08)] text-[#F8C59B]"
									: "text-[#A1A1AA] hover:text-[#F8C59B]",
							"hover:bg-[#F8C59B]/[0.03]",
							"hover:shadow-[inset_0_0_20px_rgba(248,197,155,0.05)]",
							"[&:hover_span]:[filter:contrast(1.2)_brightness(1.15)]",
						)}
						onClick={() => handleToggleMenu(item.label)}
					>
						<div className="flex items-center relative z-[1] transition-transform duration-500 ease-out group-hover:translate-x-1">
							<div className="relative">
								<Icon
									className={cn(
										"inline-block w-[1.25rem] h-[1.25rem] mr-[0.75rem]",
										"transition-all duration-500 ease-out",
										"transform-gpu",
										"group-hover:scale-110 group-hover:rotate-[8deg]",
										"hover:scale-125 hover:rotate-[8deg]",
										isActive || isOpen
											? "text-[#F8C59B] drop-shadow-[0_0_20px_rgba(248,197,155,0.5)]"
											: "text-[#A1A1AA] hover:text-[#F8C59B] hover:drop-shadow-[0_0_15px_rgba(248,197,155,0.4)]",
									)}
								/>
								{item.badge !== undefined && item.badge > 0 && (
									<span className="absolute -top-1.5 -right-0.5 flex items-center justify-center h-4 min-w-4 rounded-full bg-red-500 text-[10px] text-white font-medium">
										{item.badge > 99 ? "99+" : item.badge}
									</span>
								)}
							</div>
							<span className="font-medium tracking-wide text-[1rem] font-sans [-webkit-font-smoothing:antialiased] [-moz-osx-font-smoothing:grayscale] leading-[1.4] letter-spacing-[0.01em] whitespace-nowrap transition-transform duration-500 ease-out group-hover:translate-x-1">
								<span
									className={cn(
										zywhFont.className,
										"leading-none tracking-[0.12em] text-[17px] font-light",
									)}
									style={{
										background:
											"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
										WebkitBackgroundClip: "text",
										WebkitTextFillColor: "transparent",
										backgroundClip: "text",
										textShadow:
											"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.1)",
										filter:
											(isActive && !isSubItemActive) ||
											isOpen
												? "contrast(1.2) brightness(1.15)"
												: "contrast(0.9) brightness(0.85)",
									}}
								>
									{item.label}
								</span>
							</span>
						</div>
						<span className="transition-all duration-300">
							{isOpen ? (
								<ChevronDownIcon
									className={cn(
										"w-[1rem] h-[1rem]",
										"transform rotate-0",
										"transition-all duration-500",
										isActive
											? "text-[#F8C59B] drop-shadow-[0_0_20px_rgba(248,197,155,0.5)] scale-110"
											: "text-[#F8C59B]/60",
									)}
								/>
							) : (
								<ChevronRightIcon
									className={cn(
										"w-[1rem] h-[1rem]",
										"transform",
										"transition-all duration-500",
										"group-hover:translate-x-1",
										isActive
											? "text-[#F8C59B] drop-shadow-[0_0_20px_rgba(248,197,155,0.5)] scale-110"
											: "text-[#A1A1AA] group-hover:text-[#F8C59B]",
									)}
								/>
							)}
						</span>
					</button>
					<div
						className={cn(
							"overflow-hidden",
							"transition-all duration-500 ease-[cubic-bezier(0.4,0,0.2,1)]",
							isOpen ? "opacity-100 my-2" : "opacity-0 my-0",
							isOpen ? "max-h-[500px]" : "max-h-0",
							"rounded-md",
							"mx-2",
							"space-y-1.5",
							"relative",
							"before:absolute before:inset-0",
							"before:bg-gradient-to-b",
							"before:from-transparent",
							"before:via-[#1E2532]/10",
							"before:to-transparent",
							"before:opacity-50",
							"before:backdrop-blur-[4px]",
						)}
					>
						{item.subItems.map((subItem) => {
							const SubIcon = subItem.icon;
							return (
								<button
									key={subItem.href}
									type="button"
									onClick={() => handleClick(subItem.href)}
									className={cn(
										"flex items-center w-full",
										"py-[0.6rem] pl-[2rem] pr-[1rem]",
										"font-medium cursor-pointer",
										"rounded-lg",
										"transition-all duration-500 ease-out",
										"backdrop-blur-sm",
										"group",
										"relative",
										"hover:translate-x-2",
										"before:absolute before:inset-0",
										"before:rounded-lg",
										"before:transition-all before:duration-500",
										"before:ease-out",
										"before:scale-[0.97]",
										"before:opacity-0",
										"before:bg-gradient-to-r",
										"before:from-transparent",
										"before:via-[#1E2532]",
										"before:to-transparent",
										"before:bg-clip-padding",
										"before:backdrop-filter",
										"before:backdrop-blur-[8px]",
										"before:border-transparent",
										"before:bg-opacity-50",
										"hover:before:scale-100 hover:before:opacity-100",
										"before:z-[-1]",
										"after:absolute after:inset-0",
										"after:rounded-lg",
										"after:transition-all after:duration-500",
										"after:ease-out",
										"after:scale-95",
										"after:bg-radial-gradient-ellipse",
										"after:from-[#F8C59B]/5",
										"after:via-transparent",
										"after:to-transparent",
										"hover:after:opacity-100",
										"after:z-[-2]",
										subItem.isActive
											? "bg-[#F8C59B]/[0.05] shadow-[inset_0_0_30px_rgba(248,197,155,0.08)] text-[#F8C59B]"
											: "text-[#A1A1AA] hover:text-[#F8C59B]",
										"hover:bg-[#F8C59B]/[0.03]",
										"hover:shadow-[inset_0_0_20px_rgba(248,197,155,0.05)]",
										"[&:hover_span]:[filter:contrast(1.2)_brightness(1.15)]",
									)}
								>
									<div className="flex items-center relative z-[1]">
										<SubIcon
											className={cn(
												"inline-block w-[1.125rem] h-[1.125rem] mr-[0.75rem] flex-shrink-0",
												"transition-all duration-500 ease-out",
												"transform-gpu",
												"group-hover:scale-110 group-hover:rotate-[8deg]",
												"hover:scale-125 hover:rotate-[8deg]",
												subItem.isActive
													? "text-[#F8C59B] drop-shadow-[0_0_20px_rgba(248,197,155,0.5)]"
													: "text-[#A1A1AA] hover:text-[#F8C59B] hover:drop-shadow-[0_0_15px_rgba(248,197,155,0.4)]",
											)}
										/>
										<span className="truncate text-[0.9625rem] font-medium tracking-wide font-sans [-webkit-font-smoothing:antialiased] [-moz-osx-font-smoothing:grayscale] leading-[1.4] letter-spacing-[0.01em] whitespace-nowrap relative z-[1]">
											<span
												className={cn(
													zywhFont.className,
													"leading-none tracking-[0.12em] text-[16px] font-light",
												)}
												style={{
													background:
														"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
													WebkitBackgroundClip:
														"text",
													WebkitTextFillColor:
														"transparent",
													backgroundClip: "text",
													textShadow:
														"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.1)",
													filter: subItem.isActive
														? "contrast(1.2) brightness(1.15)"
														: "contrast(0.9) brightness(0.85)",
												}}
											>
												{subItem.label}
											</span>
										</span>
									</div>
								</button>
							);
						})}
					</div>
				</>
			) : (
				<button
					type="button"
					onClick={() => handleClick(item.href)}
					className={cn(
						"flex items-center justify-between w-full",
						"py-[0.75rem] px-[1rem]",
						"font-medium cursor-pointer",
						"mx-[0.5rem] my-[0.25rem]",
						"rounded-lg",
						"transition-all duration-500 ease-out",
						"backdrop-blur-sm",
						"group",
						"relative",
						"hover:translate-x-2",
						"before:absolute before:inset-0",
						"before:rounded-lg",
						"before:transition-all before:duration-500",
						"before:ease-out",
						"before:scale-[0.97]",
						"before:opacity-0",
						"before:bg-gradient-to-r",
						"before:from-transparent",
						"before:via-[#1E2532]",
						"before:to-transparent",
						"before:bg-clip-padding",
						"before:backdrop-filter",
						"before:backdrop-blur-[8px]",
						"before:border-transparent",
						"before:bg-opacity-50",
						"hover:before:scale-100 hover:before:opacity-100",
						"before:z-[-1]",
						"after:absolute after:inset-0",
						"after:rounded-lg",
						"after:transition-all after:duration-500",
						"after:ease-out",
						"after:scale-95",
						"after:bg-radial-gradient-ellipse",
						"after:from-[#F8C59B]/5",
						"after:via-transparent",
						"after:to-transparent",
						"hover:after:opacity-100",
						"after:z-[-2]",
						isActive
							? "bg-[#F8C59B]/[0.05] shadow-[inset_0_0_30px_rgba(248,197,155,0.08)] text-[#F8C59B]"
							: "text-[#A1A1AA] hover:text-[#F8C59B]",
						"hover:bg-[#F8C59B]/[0.03]",
						"hover:shadow-[inset_0_0_20px_rgba(248,197,155,0.05)]",
						"[&:hover_span]:[filter:contrast(1.2)_brightness(1.15)]",
					)}
				>
					<div className="flex items-center relative z-[1] transition-transform duration-500 ease-out group-hover:translate-x-1">
						<div className="relative">
							<Icon
								className={cn(
									"inline-block w-[1.25rem] h-[1.25rem] mr-[0.75rem]",
									"transition-all duration-500 ease-out",
									"transform-gpu",
									"group-hover:scale-110 group-hover:rotate-[8deg]",
									"hover:scale-125 hover:rotate-[8deg]",
									isActive
										? "text-[#F8C59B] drop-shadow-[0_0_8px_rgba(248,197,155,0.3)]"
										: "text-[#A1A1AA]",
								)}
							/>
							{item.badge !== undefined && item.badge > 0 && (
								<span className="absolute -top-1.5 -right-0.5 flex items-center justify-center h-4 min-w-4 rounded-full bg-red-500 text-[10px] text-white font-medium">
									{item.badge > 99 ? "99+" : item.badge}
								</span>
							)}
						</div>
						<span className="font-medium tracking-wide text-[1rem] font-sans [-webkit-font-smoothing:antialiased] [-moz-osx-font-smoothing:grayscale] leading-[1.4] letter-spacing-[0.01em] whitespace-nowrap transition-transform duration-500 ease-out group-hover:translate-x-1">
							<span
								className={cn(
									zywhFont.className,
									"leading-none tracking-[0.12em] text-[17px] font-light",
								)}
								style={{
									background:
										"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
									WebkitBackgroundClip: "text",
									WebkitTextFillColor: "transparent",
									backgroundClip: "text",
									textShadow:
										"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.1)",
									filter:
										(isActive && !isSubItemActive) || isOpen
											? "contrast(1.2) brightness(1.15)"
											: "contrast(0.9) brightness(0.85)",
								}}
							>
								{item.label}
							</span>
						</span>
					</div>
				</button>
			)}
			{DynamicComponent && typeof DynamicComponent === "function" && (
				<DynamicComponent />
			)}
		</>
	);
});
