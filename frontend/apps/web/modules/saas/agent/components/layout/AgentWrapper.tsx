"use client";

import { apiClient } from "@shared/lib/api-client";
import { cn } from "@ui/lib";
import { ArrowLeftIcon } from "lucide-react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import type { PropsWithChildren } from "react";
import {
	createContext,
	useCallback,
	useContext,
	useEffect,
	useId,
	useMemo,
	useRef,
	useState,
} from "react";
import { MessageNotification as AdminMessageNotification } from "../../admin/components/messages";
import { adminMenuItems, adminSystemLinks } from "../../config/routes/admin";
import {
	merchantMenuItems,
	merchantSystemLinks,
} from "../../config/routes/merchant";
import { salesMenuItems, salesSystemLinks } from "../../config/routes/sales";
import { AgentSystemType } from "../../config/types";
import { UnreadMessageToast } from "../messages/UnreadMessageToast";
import { AgentNav } from "./AgentNav";
import { AgentSidebar } from "./AgentSidebar";

// 未读消息接口响应类型
interface UnreadMessagesResponse {
	code: number;
	data?: {
		count: number;
		messages: Array<{
			id: string;
			title: string;
		}>;
	};
	error?: string;
	message?: string;
}

// 消息上下文类型
interface MessagesContextType {
	unreadCount: number;
	removeMessageFromUnread: (messageId: string) => void;
	removeMessagesFromUnread: (messageIds: string[]) => void;
	fetchUnreadMessages: () => Promise<void>;
}

// 创建消息上下文
export const MessagesContext = createContext<MessagesContextType>({
	unreadCount: 0,
	removeMessageFromUnread: () => {},
	removeMessagesFromUnread: () => {},
	fetchUnreadMessages: async () => {},
});

// 提供消息上下文的hook
export const useMessages = () => useContext(MessagesContext);

interface AgentWrapperProps extends PropsWithChildren {
	systemType: AgentSystemType;
}

const SYSTEM_CONFIGS = {
	[AgentSystemType.ADMIN]: {
		title: "管理后台",
		menuItems: adminMenuItems,
		systemLinks: adminSystemLinks,
	},
	[AgentSystemType.MERCHANT]: {
		title: "代理商系统",
		menuItems: merchantMenuItems,
		systemLinks: merchantSystemLinks,
	},
	[AgentSystemType.SALES]: {
		title: "代理商系统",
		menuItems: salesMenuItems,
		systemLinks: salesSystemLinks,
	},
} as const;

export function AgentWrapper({ children, systemType }: AgentWrapperProps) {
	const config = SYSTEM_CONFIGS[systemType];
	const pathname = usePathname();
	const _router = useRouter();
	const [isCollapsed, setIsCollapsed] = useState(false);
	const [isMounted, setIsMounted] = useState(false);
	const wrapperRef = useRef<HTMLDivElement>(null);
	const id = useId();

	// 未读消息相关状态
	const [unreadMessages, setUnreadMessages] = useState<
		Array<{ id: string; title: string }>
	>([]);
	const [unreadCount, setUnreadCount] = useState(0);
	const [showToast, setShowToast] = useState(false);
	// 使用ref存储已知消息ID，用于比较新消息
	const knownMessageIdsRef = useRef<Set<string>>(new Set());

	// 检查是否在验证页面
	const isVerifyPage = pathname === "/app/agent/verify";

	// 是否在消息中心页面
	const isMessagesPage = pathname === "/app/agent/messages";

	// 添加客户端挂载检测
	useEffect(() => {
		setIsMounted(true);
	}, []);

	// 处理鼠标移动，添加光效
	useEffect(() => {
		// 确保只在客户端执行
		if (!isMounted) {
			return;
		}

		let rafId = 0;
		let lastX = 0;
		let lastY = 0;

		const handleMouseMove = (e: MouseEvent) => {
			if (rafId) {
				return;
			}

			rafId = requestAnimationFrame(() => {
				const wrapper = wrapperRef.current;
				if (!wrapper) {
					return;
				}

				// 计算鼠标位置相对于包装器的坐标
				const rect = wrapper.getBoundingClientRect();
				const x = e.clientX - rect.left;
				const y = e.clientY - rect.top;

				// 只有当鼠标移动超过一定距离时才更新
				const distance = Math.sqrt((x - lastX) ** 2 + (y - lastY) ** 2);
				if (distance > 5) {
					(wrapper as HTMLElement).style.setProperty(
						"--mouse-x",
						`${x}px`,
					);
					(wrapper as HTMLElement).style.setProperty(
						"--mouse-y",
						`${y}px`,
					);
					lastX = x;
					lastY = y;
				}

				rafId = 0;
			});
		};

		document.addEventListener("mousemove", handleMouseMove);
		return () => {
			document.removeEventListener("mousemove", handleMouseMove);
			if (rafId) {
				cancelAnimationFrame(rafId);
			}
		};
	}, [isMounted]);

	// 从未读消息列表中移除单个消息
	const removeMessageFromUnread = useCallback((messageId: string) => {
		// 从本地缓存中移除已读消息ID
		if (knownMessageIdsRef.current.has(messageId)) {
			knownMessageIdsRef.current.delete(messageId);

			// 更新未读消息数量
			setUnreadCount((prev) => (prev > 0 ? prev - 1 : 0));

			// 更新未读消息列表
			setUnreadMessages((prev) =>
				prev.filter((msg) => msg.id !== messageId),
			);
		}
	}, []);

	// 从未读消息列表中移除多个消息
	const removeMessagesFromUnread = useCallback((messageIds: string[]) => {
		// 从本地缓存中批量移除已读消息ID
		let removedCount = 0;

		messageIds.forEach((id) => {
			if (knownMessageIdsRef.current.has(id)) {
				knownMessageIdsRef.current.delete(id);
				removedCount++;
			}
		});

		// 更新未读消息数量
		if (removedCount > 0) {
			setUnreadCount((prev) => Math.max(0, prev - removedCount));

			// 更新未读消息列表
			setUnreadMessages((prev) =>
				prev.filter((msg) => !messageIds.includes(msg.id)),
			);
		}
	}, []);

	// 获取未读消息
	const fetchUnreadMessages = useCallback(async () => {
		try {
			const response =
				await apiClient.v1.agent.messages.unread.count.$get();
			if (!response.ok) {
				return;
			}

			const data: UnreadMessagesResponse = await response.json();

			if (data.code === 200 && data.data) {
				const { count, messages } = data.data;
				setUnreadCount(count);

				// 检查是否有新消息
				const newMessages = messages.filter(
					(msg) => !knownMessageIdsRef.current.has(msg.id),
				);

				// 如果有新消息且不在消息中心页面，显示提示
				if (newMessages.length > 0) {
					setUnreadMessages(messages);
					setShowToast(true);

					// 更新已知消息ID
					messages.forEach((msg) =>
						knownMessageIdsRef.current.add(msg.id),
					);
				}
			}
		} catch (_error) {}
	}, [isMessagesPage]);

	// 组件挂载和路由变化时获取未读消息
	useEffect(() => {
		// 如果不是验证页面才获取未读消息
		if (!isVerifyPage) {
			fetchUnreadMessages();
		}
	}, [pathname, isVerifyPage, isMessagesPage, fetchUnreadMessages]);

	// 页面获得焦点时更新未读消息
	useEffect(() => {
		const handleFocus = () => {
			if (!isVerifyPage) {
				fetchUnreadMessages();
			}
		};

		window.addEventListener("focus", handleFocus);
		return () => {
			window.removeEventListener("focus", handleFocus);
		};
	}, [isVerifyPage, fetchUnreadMessages]);

	// 关闭提示
	const handleCloseToast = () => {
		setShowToast(false);
	};

	// 提供消息上下文值
	const messagesContextValue = useMemo(
		() => ({
			unreadCount,
			removeMessageFromUnread,
			removeMessagesFromUnread,
			fetchUnreadMessages,
		}),
		[
			unreadCount,
			removeMessageFromUnread,
			removeMessagesFromUnread,
			fetchUnreadMessages,
		],
	);

	// 生成面包屑数据
	const breadcrumbItems = useMemo(() => {
		const items = [];

		// 添加系统名称作为第一个面包屑
		items.push({
			label: config.title,
			href: `/${systemType.toLowerCase()}`,
		});

		// 查找当前路径对应的菜单项
		const findMenuItem = (menuItems: typeof config.menuItems) => {
			for (const item of menuItems) {
				if (pathname?.startsWith(item.href)) {
					items.push({
						label: item.label,
						href: item.href,
						icon: item.icon,
					});

					// 如果有子菜单，继续查找
					if (item.subItems) {
						const subItem = item.subItems.find((sub) =>
							pathname?.startsWith(sub.href),
						);
						if (subItem) {
							items.push({
								label: subItem.label,
								href: subItem.href,
								icon: subItem.icon,
							});
						}
					}
					break;
				}
			}
		};

		findMenuItem(config.menuItems);

		return items;
	}, [config.menuItems, config.title, pathname, systemType]);

	const handleToggleSidebar = () => {
		setIsCollapsed((prev) => !prev);
	};

	return (
		<MessagesContext.Provider value={messagesContextValue}>
			<div
				id={id}
				ref={wrapperRef}
				className={cn(
					"relative flex h-screen",
					"bg-gradient-to-r from-[#1E2023] to-[#1A1C1E]",
					"overflow-hidden", // 防止整体滚动
				)}
			>
				{/* 装饰背景 */}
				<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.02)_50%,transparent_75%)] bg-[length:500px_500px]" />
				<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.03),transparent_70%)]" />

				{/* 鼠标跟随光效 - 多层叠加 */}
				<div
					className="pointer-events-none absolute inset-0 transition-opacity duration-500"
					style={{
						background: `
							radial-gradient(1200px circle at var(--mouse-x) var(--mouse-y),
								rgba(212,180,133,0.015),
								transparent 45%
							),
							radial-gradient(800px circle at var(--mouse-x) var(--mouse-y),
								rgba(248,197,155,0.03),
								transparent 45%
							),
							radial-gradient(600px circle at var(--mouse-x) var(--mouse-y),
								rgba(229,201,165,0.02),
								transparent 45%
							),
							radial-gradient(400px circle at var(--mouse-x) var(--mouse-y),
								rgba(176,137,104,0.01),
								transparent 45%
							)
						`,
						opacity: 0.6,
					}}
				/>

				{/* 装饰性光晕 */}
				<div
					className="pointer-events-none absolute inset-0"
					style={{
						background: `
							radial-gradient(100% 100% at 50% 0%,
								rgba(212,180,133,0.015) 0%,
								rgba(248,197,155,0.01) 30%,
								transparent 70%
							),
							linear-gradient(90deg,
								rgba(212,180,133,0.005) 0%,
								rgba(248,197,155,0.01) 50%,
								rgba(212,180,133,0.005) 100%
							)
						`,
						opacity: 0.4,
					}}
				/>

				{/* 未读消息提示 */}
				{showToast && !isMessagesPage && (
					<UnreadMessageToast
						messages={unreadMessages}
						totalCount={unreadCount}
						onClose={handleCloseToast}
					/>
				)}

				{/* 管理员消息通知组件 - 仅在管理员系统显示 */}
				{systemType === AgentSystemType.ADMIN &&
					!isVerifyPage &&
					!isMessagesPage && <AdminMessageNotification />}

				{/* 如果是验证页面，就不显示左侧边栏 */}
				{!isVerifyPage && (
					<div className="fixed left-0 top-0 h-screen">
						<AgentSidebar
							menuItems={config.menuItems}
							isCollapsed={isCollapsed}
							unreadCount={unreadCount}
						/>
					</div>
				)}

				{/* 验证页面的返回按钮 */}
				{isVerifyPage && (
					<Link
						href="/auth"
						className={cn(
							"fixed left-8 top-8 z-50",
							"px-4 py-2 rounded-xl",
							"bg-[#2A2D31]/90 backdrop-blur-sm",
							"border-2 border-[#D4B485]/30",
							"text-[#D4B485]",
							"hover:bg-[#353940]/90 hover:text-[#F8D8B9]",
							"transition-all duration-300",
							"flex items-center gap-2",
							"shadow-[0_4px_16px_rgba(0,0,0,0.3)]",
							"hover:shadow-[0_6px_20px_rgba(212,180,133,0.2)]",
							"text-base font-medium",
						)}
					>
						<ArrowLeftIcon size={24} strokeWidth={2.5} />
						<span>返回登录</span>
					</Link>
				)}

				{/* 右侧内容区 */}
				<div
					className={cn(
						"flex-1",
						// 如果不是验证页面，才应用左边距
						!isVerifyPage && [
							"ml-[14rem]", // 根据侧边栏宽度调整
							isCollapsed && "ml-[4rem]",
						],
						"transition-all duration-300",
						"h-screen", // 确保高度占满
						"flex flex-col", // 使用 flex 布局
					)}
				>
					{/* 如果是验证页面，就不显示顶部导航 */}
					{!isVerifyPage && (
						<div
							className={cn(
								"flex-shrink-0", // 防止压缩
								"h-[4rem]", // 固定高度
								"bg-gradient-to-br from-[#1A1C1E]/80 via-[#1E2023]/80 to-[#23252A]/80",
								"border-b border-[#D4B485]/20",
								"backdrop-blur-xl",
								"shadow-[0_4px_24px_-4px_rgba(0,0,0,0.2)]",
								"z-50",
							)}
						>
							<AgentNav
								title={config.title}
								breadcrumbItems={breadcrumbItems}
								onToggleSidebar={handleToggleSidebar}
								isCollapsed={isCollapsed}
								unreadCount={unreadCount}
							/>
						</div>
					)}

					{/* 主要内容区域 - 可滚动 */}
					<main
						className={cn(
							"flex-1", // 占据剩余空间
							"overflow-y-scroll", // 改用 scroll
							"relative",
							"container max-w-6xl mx-auto",
							"py-6 px-4 pb-24", // 增加底部边距到 6rem (96px)
							"[&::-webkit-scrollbar]:hidden", // Webkit 浏览器隐藏滚动条
							"[scrollbar-width:none]", // Firefox 隐藏滚动条
							"-ms-overflow-style:none", // IE 和 Edge 隐藏滚动条
						)}
					>
						{children}
					</main>
				</div>
			</div>
		</MessagesContext.Provider>
	);
}
