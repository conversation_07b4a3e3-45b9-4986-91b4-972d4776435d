"use client";

import { useSession } from "@saas/auth/hooks/use-session";
import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { TooltipProvider } from "@ui/components/tooltip";
import { cn } from "@ui/lib";
import type { LucideIcon } from "lucide-react";
import { usePathname, useRouter } from "next/navigation";
import { memo, useCallback, useEffect, useMemo, useState } from "react";
import { AgentLogo } from "./AgentLogo";
import { AgentMenuItem } from "./AgentMenuItem";
import { AgentUserCard } from "./AgentUserCard";

interface MenuItemData {
	label: string;
	href: string;
	icon: LucideIcon;
	isActive?: boolean;
	subItems?: {
		label: string;
		href: string;
		icon: LucideIcon;
		isActive?: boolean;
	}[];
	isCategory?: boolean; // 标记是否为分类标题
}

interface SidebarProps {
	onNavigate?: (path: string) => void;
	currentPath?: string;
	isCollapsed?: boolean;
	menuItems: MenuItemData[];
	unreadCount?: number;
}

const _CategoryTitle = ({ children }: { children: React.ReactNode }) => {
	return (
		<div className="px-4 py-2 text-sm font-medium">
			<span className="bg-gradient-to-r from-amber-500 via-amber-300 to-amber-500 bg-clip-text text-transparent">
				{children}
			</span>
		</div>
	);
};

export const AgentSidebar = memo(function AgentSidebar({
	onNavigate = () => {},
	currentPath = "",
	isCollapsed: propIsCollapsed = false,
	menuItems = [],
	unreadCount,
}: SidebarProps) {
	const router = useRouter();
	const pathname = usePathname();
	useSession();
	const [openMenus, setOpenMenus] = useState<string[]>([]);
	const [activePath, setActivePath] = useState(currentPath);

	const isActiveMenuItem = useCallback(
		(item: MenuItemData) => {
			// 分类标题不应该被标记为活动状态
			if (item.isCategory) {
				return false;
			}

			if (activePath === item.href) {
				return true;
			}
			if (item.subItems) {
				return item.subItems.some(
					(subItem) => activePath === subItem.href,
				);
			}
			return false;
		},
		[activePath],
	);

	const toggleMenu = useCallback(
		(label: string) => {
			if (!propIsCollapsed) {
				setOpenMenus((prev) =>
					prev.includes(label)
						? prev.filter((item) => item !== label)
						: [label],
				);
			}
		},
		[propIsCollapsed],
	);

	const handleItemClick = useCallback(
		(href: string) => {
			// 跳过分类标题
			if (!href) {
				return;
			}

			// 找到被点击的菜单项
			const clickedItem = menuItems.find(
				(item) => !item.isCategory && item.href === href,
			);
			const parentItem = menuItems.find(
				(item) =>
					!item.isCategory &&
					item.subItems?.some((subItem) => subItem.href === href),
			);

			// 如果点击的是主菜单项
			if (clickedItem?.subItems) {
				toggleMenu(clickedItem.label);
				return; // 有子菜单的主菜单项点击时只切换展开状态，不导航
			}

			// 如果点击的是子菜单项
			if (parentItem) {
				setOpenMenus([parentItem.label]); // 保持父菜单展开
			} else {
				setOpenMenus([]); // 点击没有子菜单的项时，关闭所有菜单
			}

			// 更新活动路径并导航
			setActivePath(href);
			onNavigate(href);
			router.push(href);
		},
		[menuItems, onNavigate, router, toggleMenu],
	);

	// 初始化时根据当前路径打开相应的菜单
	useEffect(() => {
		const parentMenuItem = menuItems.find(
			(item) =>
				!item.isCategory &&
				item.subItems?.some((subItem) => pathname === subItem.href),
		);
		if (parentMenuItem) {
			setOpenMenus([parentMenuItem.label]);
		}
		setActivePath(pathname || "");
	}, [pathname, menuItems]);

	// 处理菜单项，为消息中心添加未读消息计数
	const processedMenuItems = useMemo(() => {
		return menuItems.map((item) => {
			// 如果是消息中心菜单项，添加badge属性
			if (!item.isCategory && item.href === "/app/agent/messages") {
				return {
					...item,
					badge: unreadCount || 0,
				};
			}
			return item;
		});
	}, [menuItems, unreadCount]);

	return (
		<TooltipProvider delayDuration={0}>
			<aside
				className={cn(
					"relative flex flex-col h-screen",
					"bg-gradient-to-br from-[#1A1C1E] via-[#1E2023] to-[#23252A]",
					"border-r border-[#D4B485]/20",
					"shadow-[4px_0_24px_-4px_rgba(0,0,0,0.3)]",
					"backdrop-blur-xl",
					"transition-all duration-300 ease-out",
					"overflow-x-hidden",
					propIsCollapsed ? "w-[4rem]" : "w-[14rem]",
				)}
			>
				{/* 装饰背景 */}
				<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
				<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />
				<div className="absolute inset-0 bg-[linear-gradient(to_bottom,rgba(255,255,255,0.03)_0%,transparent_100%)]" />

				<div
					className={cn(
						"relative flex justify-center items-center h-[7rem] flex-shrink-0 pt-2 pb-4",
					)}
				>
					<AgentLogo
						withLabel={!propIsCollapsed}
						sidebarOpen={!propIsCollapsed}
					/>
				</div>

				<nav
					className={cn(
						"relative flex-1 min-h-0",
						"flex flex-col",
						"overflow-y-auto overflow-x-hidden",
						"pb-[0.25rem]",
						"[&::-webkit-scrollbar]:hidden",
						"[scrollbar-width:none]",
						"-ms-overflow-style:none",
					)}
				>
					{processedMenuItems.map((item, index) => (
						<div
							key={item.href || `category-${index}`}
							className="relative flex flex-col"
						>
							{item.isCategory ? (
								<div
									className={cn(
										"relative",
										"px-4 py-2.5 mt-5 mb-2",
										"overflow-hidden",
										propIsCollapsed
											? "opacity-0 h-0 overflow-hidden"
											: "opacity-100",
									)}
								>
									{/* 背景装饰 */}
									<div className="absolute inset-0 bg-gradient-to-r from-[#D4B485]/10 via-[#D4B485]/5 to-transparent opacity-80" />
									<div className="absolute bottom-0 left-0 w-full h-[1px] bg-gradient-to-r from-[#F8C59B]/30 via-[#D4B485]/20 to-transparent" />
									<div className="absolute top-1/2 left-0 w-[3px] h-1/2 -translate-y-1/2 bg-[#F8C59B]/30 rounded-r" />

									{/* 标题文本 */}
									<span
										className={cn(
											zywhFont.className,
											"text-[14px] font-medium uppercase tracking-[0.15em]",
											"relative z-10",
										)}
										style={{
											background:
												"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
											WebkitBackgroundClip: "text",
											WebkitTextFillColor: "transparent",
											backgroundClip: "text",
											textShadow:
												"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.1)",
											filter: "contrast(1.1) brightness(1.1)",
										}}
									>
										{item.label}
									</span>
								</div>
							) : (
								<AgentMenuItem
									item={{
										...item,
										isActive: isActiveMenuItem(item),
										subItems: item.subItems?.map(
											(subItem) => ({
												...subItem,
												isActive:
													activePath === subItem.href,
											}),
										),
									}}
									isActive={isActiveMenuItem(item)}
									onClick={handleItemClick}
									isCollapsed={propIsCollapsed}
									isOpen={openMenus.includes(item.label)}
									toggleMenu={toggleMenu}
									pathname={activePath}
									DynamicComponent={null}
								/>
							)}
						</div>
					))}
				</nav>

				<div
					className={cn(
						"relative flex-shrink-0",
						"py-1.5",
						"transition-all duration-300 ease-out",
						"overflow-x-hidden",
						"before:absolute before:inset-x-4 before:top-0",
						"before:h-px",
						"before:bg-gradient-to-r",
						"before:from-transparent",
						"before:via-[#D4B485]/20",
						"before:to-transparent",
					)}
				>
					<AgentUserCard isCollapsed={propIsCollapsed} />
				</div>
			</aside>
		</TooltipProvider>
	);
});
