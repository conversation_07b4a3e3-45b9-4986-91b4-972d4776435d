"use client";

import { useSession } from "@saas/auth/hooks/use-session";
import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { cn } from "@ui/lib";
import type { PropsWithChildren } from "react";

export function AgentLayout({ children }: PropsWithChildren) {
	useSession();

	return (
		<div
			className={cn(
				"flex min-h-screen flex-col",
				"bg-[#1D1F23]",
				"relative",
			)}
		>
			{/* 装饰背景 */}
			<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.02)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
			<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.03),transparent_70%)]" />

			<header
				className={cn(
					"relative border-b",
					"bg-gradient-to-br from-[#1A1C1E]/80 via-[#1E2023]/80 to-[#23252A]/80",
					"border-[#D4B485]/20",
					"backdrop-blur-xl",
					"shadow-[0_4px_24px_-4px_rgba(0,0,0,0.2)]",
					"px-6 py-4",
				)}
			>
				<h1
					className={cn(
						"text-xl font-semibold",
						zywhFont.className,
						"leading-none",
						"tracking-[0.05em]",
					)}
					style={{
						background:
							"linear-gradient(135deg, rgba(229,201,165,0.95) 0%, rgba(212,180,133,0.9) 50%, rgba(176,137,104,0.85) 100%)",
						WebkitBackgroundClip: "text",
						WebkitTextFillColor: "transparent",
						backgroundClip: "text",
						textShadow:
							"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
						filter: "contrast(1.1) brightness(1.05)",
					}}
				>
					代理商系统
				</h1>
			</header>
			<main
				className={cn(
					"relative flex-1 p-6",
					"container max-w-6xl mx-auto",
				)}
			>
				{children}
			</main>
		</div>
	);
}
