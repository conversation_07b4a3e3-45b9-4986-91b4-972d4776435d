"use client";

import { useSession } from "@saas/auth/hooks/use-session";
import { cn } from "@ui/lib";
import Image from "next/image";
import { AgentSystemMenu } from "./AgentSystemMenu";
import { AgentUserMenu } from "./AgentUserMenu";

// 角色名称映射
const roleNameMap = {
	SALES: "超级个体",
	PARTNER: "合伙人",
	DIRECTOR: "联席董事",
	BRANCH: "分公司",
	ADMIN: "管理员",
} as const;

interface AgentUserCardProps {
	isCollapsed?: boolean;
}

export function AgentUserCard({ isCollapsed = false }: AgentUserCardProps) {
	const { user } = useSession();

	if (!user) {
		return null;
	}

	const userCard = (
		<div
			className={cn(
				"relative p-3 mx-2",
				"bg-[#1E2023]",
				"rounded-lg",
				"group",
				"cursor-pointer",
				"hover:bg-[#1E2023]/80",
				"transition-colors duration-200",
			)}
		>
			<div className="relative flex items-center gap-3">
				<div className="flex-shrink-0">
					<div className="w-8 h-8 rounded-full overflow-hidden bg-[#1E2023] border border-[#D4B485]/20">
						{user.image ? (
							<Image
								src={user.image}
								alt={user.name || "用户头像"}
								width={32}
								height={32}
								className="w-full h-full object-cover"
							/>
						) : (
							<div className="w-full h-full flex items-center justify-center text-[#D4B485]">
								{user.name?.[0] || "U"}
							</div>
						)}
					</div>
				</div>

				<div className="flex-1 min-w-0">
					<div
						className={cn(
							"font-light text-[17px] truncate",
							"tracking-[0.12em]",
						)}
						style={{
							background:
								"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
							WebkitBackgroundClip: "text",
							WebkitTextFillColor: "transparent",
							backgroundClip: "text",
							textShadow:
								"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
							filter: "contrast(1.1) brightness(1.05)",
						}}
					>
						{user.name || "未命名用户"}
						<span className="ml-3 text-[15px] text-[#D4B485]/60">
							(
							{roleNameMap[
								user.agentRole as keyof typeof roleNameMap
							] || "普通用户"}
							)
						</span>
					</div>
					<div className="text-[14px] text-[#D4B485]/60 truncate mt-1">
						{user.email || "未设置邮箱"}
					</div>
				</div>
			</div>
		</div>
	);

	if (isCollapsed) {
		return (
			<div className="flex flex-col gap-2 pb-5">
				<AgentSystemMenu isCollapsed={true} />
				<div className="flex-1" />
				<AgentUserMenu
					trigger={
						<div className="w-8 h-8 mx-auto rounded-full overflow-hidden bg-[#1E2023] border border-[#D4B485]/20">
							{user.image ? (
								<Image
									src={user.image}
									alt={user.name || "用户头像"}
									width={32}
									height={32}
									className="w-full h-full object-cover"
								/>
							) : (
								<div className="w-full h-full flex items-center justify-center text-[#D4B485]">
									{user.name?.[0] || "U"}
								</div>
							)}
						</div>
					}
				/>
			</div>
		);
	}

	return (
		<div className="flex flex-col space-y-3">
			{/* 菜单项 */}
			<AgentSystemMenu isCollapsed={false} />

			{/* 分割线 */}
			<div className="px-3">
				<div className="h-px bg-gradient-to-r from-transparent via-[#D4B485]/20 to-transparent" />
			</div>

			{/* 用户卡片 */}
			<AgentUserMenu trigger={userCard} />

			{/* 版权信息 */}
			<div className="px-3 pb-2">
				<div className="text-[11px] text-[#D4B485]/40 text-center">
					© 2025 9000AI Inc.
				</div>
			</div>
		</div>
	);
}
