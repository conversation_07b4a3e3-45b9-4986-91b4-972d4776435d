"use client";

import { authClient } from "@repo/auth/client";
import { config } from "@repo/config";
import { useSession } from "@saas/auth/hooks/use-session";
import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { clearCache } from "@shared/lib/cache";
import { useQueryClient } from "@tanstack/react-query";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { cn } from "@ui/lib";
import {
	Building2,
	Calendar,
	Crown,
	DollarSign,
	History,
	LogOut,
	Settings,
	Store,
	UserCheck,
	Users,
	Zap,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";

interface AgentUserMenuProps {
	trigger?: React.ReactNode;
	unreadCount?: number;
}

interface AgentStatistics {
	customerCount: number;
	monthNewCustomers: number;
	totalSales: number;
	monthSales: number;
	agentCount: number;
	monthNewAgents: number;
}

export function AgentUserMenu({ trigger }: AgentUserMenuProps) {
	const router = useRouter();
	const queryClient = useQueryClient();
	const session = useSession();
	const { user } = session;
	const [stats, _setStats] = useState<AgentStatistics>({
		customerCount: 0,
		monthNewCustomers: 0,
		totalSales: 0,
		monthSales: 0,
		agentCount: 0,
		monthNewAgents: 0,
	});

	// 格式化数字为易读格式（k和w）
	const formatNumber = (num: number) => {
		if (num >= 10000) {
			return `${(num / 10000).toFixed(1)}w`;
		}
		if (num >= 1000) {
			return `${(num / 1000).toFixed(1)}k`;
		}
		return num.toString();
	};

	// 如果没有用户信息则不渲染
	if (!user || !user.agentId) {
		return null;
	}

	// 从session中获取用户和代理商信息
	const {
		name: userName = "未命名用户",
		computingPower = 0,
		agentRole = "SALES",
		agentTeamSize = 0,
		agentTeamQuota = 0,
		agentUsedTeamQuota = 0,
		agentTotalSales = 0,
		agentMonthSales = 0,
	} = user;

	// 计算剩余名额
	const remainingQuota = Math.max(
		0,
		(agentTeamQuota || 0) - (agentUsedTeamQuota || 0),
	);

	// 转换代理商角色为中文
	const getRoleText = (role: string | null) => {
		if (!role) {
			return "未知角色";
		}
		const roleMap: Record<string, string> = {
			ADMIN: "系统管理员",
			BRANCH: "分公司",
			DIRECTOR: "联席董事",
			PARTNER: "合伙人",
			SALES: "超级个体",
		};
		return roleMap[role] || "未知角色";
	};

	// 根据角色获取显示的指标数据
	const getMetricsData = () => {
		switch (agentRole) {
			case "SALES":
				return [
					{
						icon: UserCheck,
						value: formatNumber(stats.customerCount || 0),
						label: "客户数量",
					},
					{
						icon: Zap,
						value: formatNumber(computingPower),
						label: "算力值",
					},
					{
						icon: DollarSign,
						value: ((agentMonthSales || 0) / 100).toFixed(0),
						label: "月销售额",
					},
				];
			case "BRANCH":
			case "DIRECTOR":
			case "PARTNER":
				return [
					{
						icon: Calendar,
						value: remainingQuota.toString(),
						label: "剩余名额",
					},

					{
						icon: Users,
						value: (agentTeamSize || 0).toString(),
						label: "团队规模",
					},
					{
						icon: UserCheck,
						value: formatNumber(stats.customerCount || 0),
						label: "客户数量",
					},
					{
						icon: DollarSign,
						value: ((agentMonthSales || 0) / 100).toFixed(0),
						label: "月销售额",
					},
				];
			case "ADMIN":
				return [
					{
						icon: Building2,
						value: formatNumber(stats.agentCount || 0),
						label: "代理商数",
					},
					{
						icon: Store,
						value: formatNumber(stats.customerCount || 0),
						label: "客户总数",
					},
					{
						icon: DollarSign,
						value: ((stats.totalSales || 0) / 100).toFixed(0),
						label: "总销售额",
					},
				];
			default:
				return [];
		}
	};

	// 根据角色获取卡片信息
	const getCardInfo = () => {
		switch (agentRole) {
			case "SALES":
				return [
					{
						icon: UserCheck,
						label: "本月新增客户",
						value: `${stats.monthNewCustomers || 0}个`,
					},
					{
						icon: Zap,
						label: "剩余算力值",
						value: formatNumber(computingPower),
					},
					{
						icon: DollarSign,
						label: "本月销售额",
						value: `¥${((agentMonthSales || 0) / 100).toFixed(2)}`,
					},
				];
			case "BRANCH":
			case "DIRECTOR":
			case "PARTNER":
				return [
					{
						icon: Calendar,
						label: "剩余名额",
						value: `${remainingQuota}个`,
					},
					{
						icon: Users,
						label: "团队规模",
						value: `${agentTeamSize || 0}人`,
					},
					{
						icon: UserCheck,
						label: "客户总数",
						value: `${stats.customerCount || 0}个`,
					},
					{
						icon: Zap,
						label: "剩余算力值",
						value: formatNumber(computingPower),
					},
					{
						icon: DollarSign,
						label: "本月销售额",
						value: `¥${((agentMonthSales || 0) / 100).toFixed(2)}`,
					},
					{
						icon: DollarSign,
						label: "总销售额",
						value: `¥${((agentTotalSales || 0) / 100).toFixed(2)}`,
					},
				];
			case "ADMIN":
				return [
					{
						icon: Building2,
						label: "本月新增代理",
						value: `${stats.monthNewAgents || 0}个`,
					},
					{
						icon: Store,
						label: "本月新增客户",
						value: `${stats.monthNewCustomers || 0}个`,
					},
					{
						icon: DollarSign,
						label: "平台总销售额",
						value: `¥${((stats.totalSales || 0) / 100).toFixed(2)}`,
					},
				];
			default:
				return [];
		}
	};

	const handleLogout = async () => {
		await authClient.signOut({
			fetchOptions: {
				onSuccess: async () => {
					await clearCache();
					queryClient.clear();
					router.push(config.auth.redirectAfterLogout);
				},
			},
		});
	};

	return (
		<DropdownMenu modal={false}>
			<DropdownMenuTrigger asChild>{trigger}</DropdownMenuTrigger>
			<DropdownMenuContent
				align="end"
				className={cn(
					"w-[320px]",
					(agentRole === "BRANCH" ||
						agentRole === "DIRECTOR" ||
						agentRole === "PARTNER") &&
						"w-[380px]",
					"bg-[#1E2023]/95",
					"backdrop-blur-xl",
					"border border-[#D4B485]/20",
					"shadow-[0_4px_24px_-4px_rgba(0,0,0,0.3),0_0_40px_rgba(212,180,133,0.05)]",
					"rounded-xl",
				)}
			>
				{/* 用户信息区域 */}
				<DropdownMenuLabel
					className={cn(
						"p-5",
						"border-b border-[#D4B485]/20",
						"flex items-center gap-4",
					)}
				>
					<div className="w-12 h-12 rounded-full bg-gradient-to-br from-[#D4B485] to-[#B08968] flex items-center justify-center">
						<span className="text-white text-lg font-bold">
							{userName[0]}
						</span>
					</div>
					<div className="flex flex-col gap-1.5">
						<span
							className={cn(
								"text-xl font-medium",
								zywhFont.className,
							)}
							style={{
								background:
									"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
								WebkitBackgroundClip: "text",
								WebkitTextFillColor: "transparent",
								backgroundClip: "text",
								textShadow:
									"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
								filter: "contrast(1.1) brightness(1.05)",
							}}
						>
							{userName}
						</span>
						<span className="text-sm text-[#D4B485]/60">
							{getRoleText(agentRole)}
						</span>
					</div>
				</DropdownMenuLabel>

				{/* 代理商信息卡片 */}
				<div className="p-5 border-b border-[#D4B485]/20">
					<div
						className={cn(
							"relative p-4",
							"rounded-lg",
							"bg-gradient-to-br from-[#1A1C1E] via-[#1E2023] to-[#23252A]",
							"border border-[#D4B485]/20",
							"shadow-[0_4px_16px_-6px_rgba(212,180,133,0.15)]",
							"overflow-hidden",
							"transition-all duration-300 hover:shadow-[0_6px_20px_-6px_rgba(212,180,133,0.25)] hover:scale-[1.01]",
						)}
					>
						{/* 装饰背景 */}
						<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:200px_200px]" />
						<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_0%,rgba(212,180,133,0.1),transparent_70%)]" />

						{/* 内容区域 */}
						<div className="relative">
							<div className="flex items-center gap-3 mb-4">
								<div
									className={cn(
										"p-2 rounded-md",
										"bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
										"border border-[#D4B485]/20",
										"shadow-[0_2px_8px_-2px_rgba(212,180,133,0.3)]",
									)}
								>
									<Crown className="w-5 h-5 text-[#D4B485]" />
								</div>
								<span
									className={cn(
										"text-base font-medium",
										zywhFont.className,
									)}
									style={{
										background:
											"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
										WebkitBackgroundClip: "text",
										WebkitTextFillColor: "transparent",
										backgroundClip: "text",
										textShadow:
											"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
										filter: "contrast(1.1) brightness(1.05)",
									}}
								>
									{agentRole === "ADMIN"
										? "平台数据"
										: "代理商信息"}
								</span>
							</div>
							<div
								className={cn(
									"space-y-3",
									(agentRole === "BRANCH" ||
										agentRole === "DIRECTOR" ||
										agentRole === "PARTNER") &&
										"grid grid-cols-2 gap-3 space-y-0",
								)}
							>
								{getCardInfo().map((item) => (
									<div
										key={item.label}
										className="flex items-center justify-between"
									>
										<div className="flex items-center gap-2">
											<item.icon className="w-4 h-4 text-[#D4B485]/60" />
											<span className="text-sm text-[#D4B485]/60">
												{item.label}
											</span>
										</div>
										<span className="text-sm font-medium text-[#D4B485]">
											{item.value}
										</span>
									</div>
								))}
							</div>
						</div>
					</div>
				</div>

				{/* 统计数据展示 */}
				<div className="grid grid-cols-3 gap-4 p-5 border-b border-[#D4B485]/20">
					{getMetricsData().map((metric) => (
						<div
							key={metric.label}
							className="flex flex-col items-center gap-2 group"
						>
							<div className="flex items-center gap-2">
								<metric.icon className="w-5 h-5 text-[#D4B485] transition-transform duration-300 group-hover:scale-110" />
								<span className="text-xl font-medium text-[#D4B485] transition-all duration-300 group-hover:text-[#D4B485]/90">
									{metric.value}
								</span>
							</div>
							<span className="text-sm text-[#D4B485]/60">
								{metric.label}
							</span>
						</div>
					))}
				</div>

				{/* 功能菜单 */}
				<div className="p-2">
					<DropdownMenuItem
						className={cn(
							"flex items-center gap-3 px-5 py-3 text-[#D4B485]/90",
							"hover:text-[#D4B485] hover:bg-[#D4B485]/10",
							"transition-all duration-200",
							"rounded-md",
						)}
						onClick={() => router.push("/app/agent/setting")}
					>
						<Settings className="w-5 h-5 transition-transform duration-200 hover:scale-110" />
						<span className="text-base">账号设置</span>
					</DropdownMenuItem>
					<DropdownMenuItem
						className={cn(
							"flex items-center gap-3 px-5 py-3 text-[#D4B485]/90",
							"hover:text-[#D4B485] hover:bg-[#D4B485]/10",
							"transition-all duration-200",
							"rounded-md",
						)}
						onClick={() => router.push("/app/agent/history")}
					>
						<History className="w-5 h-5 transition-transform duration-200 hover:scale-110" />
						<span className="text-base">历史创建任务</span>
					</DropdownMenuItem>
					<DropdownMenuSeparator className="bg-[#D4B485]/20 my-1" />
					<DropdownMenuItem
						className={cn(
							"flex items-center gap-3 px-5 py-3 text-[#D4B485]/90",
							"hover:text-[#D4B485] hover:bg-[#D4B485]/10",
							"transition-all duration-200",
							"rounded-md",
						)}
						onClick={handleLogout}
					>
						<LogOut className="w-5 h-5 transition-transform duration-200 hover:scale-110" />
						<span className="text-base">退出登录</span>
					</DropdownMenuItem>
				</div>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}
