"use client";

import { useSession } from "@saas/auth/hooks/use-session";
import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { cn } from "@ui/lib";
import { Menu } from "lucide-react";
import Image from "next/image";
import { useEffect, useState } from "react";
import { AgentBreadcrumb } from "./AgentBreadcrumb";
import { AgentUserMenu } from "./AgentUserMenu";

export interface NavBarProps {
	title: string;
	breadcrumbItems: Array<{
		label: string;
		href: string;
		icon?: React.ReactNode;
	}>;
	onToggleSidebar: () => void;
	isCollapsed: boolean;
	unreadCount?: number;
}

export function AgentNav({
	title = "代理商系统",
	breadcrumbItems = [],
	onToggleSidebar,
	isCollapsed = false,
	unreadCount = 0,
}: NavBarProps) {
	const { user } = useSession();
	const [_showTip, setShowTip] = useState(false);

	// 监听窗口大小变化
	useEffect(() => {
		const checkWindowSize = () => {
			setShowTip(window.innerWidth < 1280);
		};

		checkWindowSize();
		window.addEventListener("resize", checkWindowSize);
		return () => window.removeEventListener("resize", checkWindowSize);
	}, []);

	const userTrigger = user && (
		<div
			className={cn(
				"relative h-full",
				"py-[0.375rem]",
				"text-[0.9375rem]",
				"transition-transform duration-200",
				"hover:scale-[1.02]",
				"flex items-center",
			)}
		>
			<div className="flex items-center gap-2">
				<div
					className={cn(
						"w-8 h-8 rounded-full overflow-hidden",
						"bg-gradient-to-br from-[#1A1C1E] via-[#1E2023] to-[#23252A]",
						"border border-[#D4B485]/20",
						"shadow-[0_4px_12px_-2px_rgba(212,180,133,0.12)]",
					)}
				>
					{user.avatar ? (
						<Image
							src={user.avatar}
							alt={user.name || "用户头像"}
							width={32}
							height={32}
							className="w-full h-full object-cover"
						/>
					) : (
						<div
							className={cn(
								"w-full h-full",
								"flex items-center justify-center",
								"text-[#D4B485]",
								"bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
							)}
						>
							{user.name?.[0] || "U"}
						</div>
					)}
				</div>
				<span
					className={cn(
						"text-[#D4B485]/90 hover:text-[#D4B485]",
						"transition-colors duration-300",
						"font-medium",
					)}
				>
					{user.name || "用户"}
				</span>
			</div>
		</div>
	);

	return (
		<header
			className={cn(
				"relative w-full h-[4rem]",
				"bg-gradient-to-br from-[#1A1C1E] via-[#1E2023] to-[#23252A]",
				"border-b border-[#D4B485]/20",
				"shadow-[0_4px_24px_-4px_rgba(0,0,0,0.2)]",
				"backdrop-blur-xl",
				"z-50",
			)}
		>
			{/* 装饰背景 */}
			<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
			<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />

			<div
				className={cn(
					"relative h-full w-full flex justify-between items-center",
					"px-6 py-3",
				)}
			>
				<div className="flex items-center space-x-4">
					<button
						type="button"
						onClick={onToggleSidebar}
						className={cn(
							"flex items-center justify-center",
							"w-10 h-10",
							"rounded-lg",
							"transition-all duration-300 ease-out",
							"bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
							"hover:from-[#D4B485]/15 hover:to-[#D4B485]/10",
							"border border-[#D4B485]/20",
							"hover:border-[#D4B485]/30",
							"shadow-[0_4px_12px_-2px_rgba(212,180,133,0.12)]",
							"hover:shadow-[0_4px_16px_-2px_rgba(212,180,133,0.2)]",
							"group",
							isCollapsed && "bg-[#D4B485]/20",
						)}
					>
						<Menu
							className={cn(
								"w-5 h-5",
								"transition-all duration-300 ease-out",
								"text-[#D4B485]/70 group-hover:text-[#D4B485]",
								"transform-gpu group-hover:scale-110 group-hover:rotate-[8deg]",
								isCollapsed && "text-[#D4B485]",
							)}
						/>
					</button>
					{breadcrumbItems.length > 0 ? (
						<AgentBreadcrumb items={breadcrumbItems} />
					) : (
						<div
							className={cn(
								"text-lg font-medium",
								zywhFont.className,
								"leading-none",
								"tracking-[0.05em]",
							)}
							style={{
								background:
									"linear-gradient(135deg, rgba(229,201,165,0.95) 0%, rgba(212,180,133,0.9) 50%, rgba(176,137,104,0.85) 100%)",
								WebkitBackgroundClip: "text",
								WebkitTextFillColor: "transparent",
								backgroundClip: "text",
								textShadow:
									"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
								filter: "contrast(1.1) brightness(1.05)",
							}}
						>
							{title}
						</div>
					)}
				</div>
				<div className="flex-1" />
				<div
					className={cn(
						"flex items-center h-full",
						"space-x-[1.25rem]",
						"transition-all duration-200",
						"flex-1 justify-end",
					)}
				>
					{user && (
						<AgentUserMenu
							trigger={userTrigger}
							unreadCount={unreadCount}
						/>
					)}
				</div>
			</div>
		</header>
	);
}
