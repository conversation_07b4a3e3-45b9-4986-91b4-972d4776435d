"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { cn } from "@ui/lib";
import { ChevronRight } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useMemo } from "react";

interface BreadcrumbItem {
	label: string;
	href: string;
}

interface AgentBreadcrumbProps {
	items?: BreadcrumbItem[];
	className?: string;
}

export function AgentBreadcrumb({
	items = [],
	className,
}: AgentBreadcrumbProps) {
	const pathname = usePathname();

	const breadcrumbItems = useMemo(() => {
		return items.filter((item) => pathname?.startsWith(item.href));
	}, [items, pathname]);

	if (!breadcrumbItems.length) {
		return null;
	}

	return (
		<nav
			className={cn(
				"flex items-center",
				"text-base font-medium",
				zywhFont.className,
				"leading-none tracking-[0.02em]",
				className,
			)}
		>
			{breadcrumbItems.map((item, index) => {
				const isLast = index === breadcrumbItems.length - 1;

				return (
					<div key={item.href} className="flex items-center">
						{index > 0 && (
							<ChevronRight
								className={cn(
									"mx-1.5 h-3.5 w-3.5",
									"text-[#D4B485]/40",
								)}
							/>
						)}
						<Link
							href={item.href}
							className={cn(
								"transition-all duration-300",
								"hover:scale-[1.02]",
								"relative group",
								isLast
									? "pointer-events-none"
									: "hover:opacity-80",
							)}
						>
							<span
								style={{
									background:
										"linear-gradient(135deg, rgba(229,201,165,0.95) 0%, rgba(212,180,133,0.9) 50%, rgba(176,137,104,0.85) 100%)",
									WebkitBackgroundClip: "text",
									WebkitTextFillColor: "transparent",
									backgroundClip: "text",
									textShadow:
										"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
									filter: isLast
										? "contrast(1.2) brightness(1.15)"
										: "contrast(0.9) brightness(0.85)",
								}}
							>
								{item.label}
							</span>
						</Link>
					</div>
				);
			})}
		</nav>
	);
}
