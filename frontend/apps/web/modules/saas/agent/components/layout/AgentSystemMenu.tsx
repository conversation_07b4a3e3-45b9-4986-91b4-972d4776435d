"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Button } from "@ui/components/button";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import { cn } from "@ui/lib";
import {
	Bot,
	type HelpCircle,
	MessageCircle,
	type Settings,
} from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useState } from "react";

interface SystemMenuItem {
	icon:
		| typeof Settings
		| typeof HelpCircle
		| typeof MessageCircle
		| typeof Bot;
	label: string;
	href?: string;
	onClick?: () => void;
}

interface AgentSystemMenuProps {
	isCollapsed?: boolean;
}

export function AgentSystemMenu({ isCollapsed = false }: AgentSystemMenuProps) {
	const router = useRouter();
	const [showQRCode, setShowQRCode] = useState(false);

	const systemMenuItems: SystemMenuItem[] = [
		{
			icon: MessageCircle,
			label: "人工客服",
			onClick: () => setShowQRCode(true),
		},
		{
			icon: Bot,
			label: "前往数字人系统",
			href: "/app/ecosystem/avatar/dashboard",
		},
	];

	if (isCollapsed) {
		return (
			<>
				{systemMenuItems.map((item) => {
					const Icon = item.icon;
					return (
						<Button
							key={item.label}
							variant="ghost"
							className={cn(
								"flex items-center justify-center",
								"py-[0.75rem] px-[0.5rem]",
								"cursor-pointer",
								"mx-[0.5rem] my-[0.25rem]",
								"rounded-lg",
								"transition-all duration-300 ease-[cubic-bezier(0.4,0,0.2,1)]",
								"backdrop-blur-sm",
								"hover:scale-[1.02]",
								"text-[#A1A1AA] hover:bg-[#1E2532]/90 hover:shadow-[0_0_15px_rgba(248,197,155,0.1)]",
								"hover:bg-[#F8C59B]/[0.03]",
								"hover:shadow-[inset_0_0_20px_rgba(248,197,155,0.05)]",
								"[&:hover_span]:[filter:contrast(1.2)_brightness(1.15)]",
							)}
							onClick={() => {
								if (item.href) {
									router.push(item.href);
								} else if (item.onClick) {
									item.onClick();
								}
							}}
						>
							<Icon
								className={cn(
									"w-[1.5rem] h-[1.5rem]",
									"transition-all duration-300",
									"transform-gpu",
									"group-hover:scale-110 group-hover:rotate-[8deg]",
									"hover:scale-125 hover:rotate-[8deg]",
									"text-[#A1A1AA] hover:text-[#F8C59B] hover:drop-shadow-[0_0_15px_rgba(248,197,155,0.4)]",
								)}
							/>
						</Button>
					);
				})}

				<Dialog open={showQRCode} onOpenChange={setShowQRCode}>
					<DialogContent className="bg-[#1E2023] border-[#D4B485]/20">
						<DialogHeader>
							<DialogTitle
								className={cn(
									"text-center",
									zywhFont.className,
									"text-[20px] tracking-[0.12em] font-light",
								)}
								style={{
									background:
										"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
									WebkitBackgroundClip: "text",
									WebkitTextFillColor: "transparent",
									backgroundClip: "text",
									textShadow:
										"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.1)",
								}}
							>
								扫码添加客服
							</DialogTitle>
						</DialogHeader>
						<div className="flex justify-center items-center p-4">
							<Image
								src="https://eco.9000aigc.com/static/QRCode/%E5%AE%A2%E6%9C%8D%E4%BA%8C%E7%BB%B4%E7%A0%81.png"
								alt="客服微信二维码"
								width={256}
								height={256}
								className="w-64 h-64 rounded-lg"
							/>
						</div>
					</DialogContent>
				</Dialog>
			</>
		);
	}

	return (
		<div className="space-y-2">
			{systemMenuItems.map((item) => {
				const Icon = item.icon;
				return (
					<Button
						key={item.label}
						variant="ghost"
						className="w-full justify-start text-[#D4B485]/70 hover:text-[#D4B485] hover:bg-[#D4B485]/10 py-2.5"
						size="md"
						onClick={() => {
							if (item.href) {
								router.push(item.href);
							} else if (item.onClick) {
								item.onClick();
							}
						}}
					>
						<Icon className="mr-3 h-5 w-5" />
						<span
							className={cn(
								"text-[17px]",
								zywhFont.className,
								"leading-none tracking-[0.12em] font-light",
							)}
							style={{
								background:
									"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
								WebkitBackgroundClip: "text",
								WebkitTextFillColor: "transparent",
								backgroundClip: "text",
								textShadow:
									"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.1)",
								filter: "contrast(1.1) brightness(1.05)",
							}}
						>
							{item.label}
						</span>
					</Button>
				);
			})}

			<Dialog open={showQRCode} onOpenChange={setShowQRCode}>
				<DialogContent className="bg-[#1E2023] border-[#D4B485]/20">
					<DialogHeader>
						<DialogTitle
							className={cn(
								"text-center",
								zywhFont.className,
								"text-[20px] tracking-[0.12em] font-light",
							)}
							style={{
								background:
									"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
								WebkitBackgroundClip: "text",
								WebkitTextFillColor: "transparent",
								backgroundClip: "text",
								textShadow:
									"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.1)",
							}}
						>
							扫码添加客服
						</DialogTitle>
					</DialogHeader>
					<div className="flex justify-center items-center p-4">
						<Image
							src="https://eco.9000aigc.com/static/QRCode/%E5%AE%A2%E6%9C%8D%E4%BA%8C%E7%BB%B4%E7%A0%81.png"
							alt="客服微信二维码"
							width={256}
							height={256}
							className="w-64 h-64 rounded-lg"
						/>
					</div>
				</DialogContent>
			</Dialog>
		</div>
	);
}
