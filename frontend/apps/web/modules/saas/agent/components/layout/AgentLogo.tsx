"use client";

import {
	aqbFont,
	qxygFont,
	zywhFont,
} from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { cn } from "@ui/lib";

interface LogoProps {
	withLabel?: boolean;
	className?: string;
	sidebarOpen?: boolean;
}

export function AgentLogo({
	withLabel = true,
	className,
	sidebarOpen = true,
}: LogoProps) {
	return (
		<span
			className={cn(
				"flex items-center font-semibold text-foreground leading-none gap-3",
				className,
			)}
		>
			{withLabel ? (
				<div
					className={`mt-3 mb-3 flex flex-col items-center justify-center transition-all duration-300 ${sidebarOpen ? "opacity-100" : "opacity-0"}`}
				>
					<div className="relative text-center">
						<span
							className={`text-[2.4em] font-extrabold tracking-wide ${zywhFont.className}`}
							style={{
								letterSpacing: "0.01em",
								background:
									"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
								WebkitBackgroundClip: "text",
								WebkitTextFillColor: "transparent",
								backgroundClip: "text",
								textShadow:
									"0 2px 4px rgba(0,0,0,0.15), 0 0 25px rgba(212,180,133,0.25)",
								filter: "contrast(1.15) brightness(1.1)",
								width: "fit-content",
								display: "inline-block",
								marginRight: "-4px",
							}}
						>
							<span style={{ fontStyle: "italic" }}>9000</span>
							<span
								className={`text-[1.4em] font-extrabold tracking-wide ${aqbFont.className}`}
								style={{
									background:
										"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
									WebkitBackgroundClip: "text",
									WebkitTextFillColor: "transparent",
									backgroundClip: "text",
									textShadow:
										"0 2px 4px rgba(0,0,0,0.15), 0 0 25px rgba(212,180,133,0.25)",
									filter: "contrast(1.15) brightness(1.1)",
								}}
							>
								AI
							</span>
						</span>
					</div>
					<span
						className={`text-2xl tracking-[0.1em] -mt-[1px] text-center ${qxygFont.className}`}
						style={{
							background:
								"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
							WebkitBackgroundClip: "text",
							WebkitTextFillColor: "transparent",
							backgroundClip: "text",
							textShadow:
								"0 2px 4px rgba(0,0,0,0.15), 0 0 25px rgba(212,180,133,0.25)",
							filter: "contrast(1.15) brightness(1.1)",
							width: "fit-content",
							display: "inline-block",
						}}
					>
						代理招商系统
					</span>
				</div>
			) : (
				<span
					className={cn(
						"text-[2.4em] font-extrabold tracking-wide",
						"transition-all duration-500 ease-out",
						"hover:scale-110 hover:rotate-[8deg]",
						"transform-gpu",
						aqbFont.className,
					)}
					style={{
						letterSpacing: "0.01em",
						background:
							"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
						WebkitBackgroundClip: "text",
						WebkitTextFillColor: "transparent",
						backgroundClip: "text",
						textShadow:
							"0 2px 4px rgba(0,0,0,0.15), 0 0 25px rgba(212,180,133,0.25)",
						filter: "contrast(1.15) brightness(1.1)",
						width: "fit-content",
						display: "inline-block",
						paddingRight: "4px",
					}}
				>
					AI
				</span>
			)}
		</span>
	);
}
