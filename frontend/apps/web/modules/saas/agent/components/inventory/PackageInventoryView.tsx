"use client";

import type {
	PlanType as ApiPlanType,
	BillingCycle,
	PlanLevel,
	PlanType,
} from "@prisma/client"; // 引入后端定义的类型
import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { apiClient } from "@shared/lib/api-client"; // 假设 apiClient 路径正确
import { Badge } from "@ui/components/badge";
import { Card } from "@ui/components/card";
import { Input } from "@ui/components/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import {
	ArchiveIcon,
	BriefcaseIcon,
	CheckCircleIcon,
	CircleDollarSignIcon,
	ClockIcon,
	Loader2Icon,
	PackageIcon,
	ZapIcon,
} from "lucide-react";
import type React from "react";
import { useEffect, useMemo, useState } from "react";
import { toast } from "sonner";

// 定义套餐类型 (与后端类型保持一致或进行映射)
type PackageType = ApiPlanType | "ALL"; // 增加 "ALL" 用于筛选

// 定义套餐状态 (前端展示用)
type PackageStatus = "ACTIVE" | "EMPTY_STOCK" | "ALL"; // 简化为有效和库存为空

// 套餐库存项中包含的套餐详情
export interface InventoryPlanDetail {
	id: string;
	name: string;
	level: PlanLevel;
	type: PlanType;
	code: string;
	computingPower: number;
	validityDays: number;
	description?: string;
	billingCycle: BillingCycle;
	currentPrice?: number; // 新增：套餐当前单价 (单位：分)，可选，需API支持
}

// 单个套餐库存项
export interface InventoryItem {
	inventoryId: string; // AgentPlanInventory 的 id
	plan: InventoryPlanDetail; // 关联的套餐信息
	quantityInStock: number; // 库存数量
	lastUpdatedAt: string; // ISO date string
	// 可以根据需要添加 AgentPlanInventory 表中的其他字段，如 remark
	remark?: string | null;
}

// 将后端API的 PlanType 映射到前端显示名称
const PLAN_TYPE_DISPLAY_NAMES: Record<ApiPlanType, string> = {
	AVATAR: "数字人",
	IMAGE: "图文",
	VIDEO: "视频",
	TEXT: "文案",
};

const BILLING_CYCLE_DISPLAY_NAMES: Record<BillingCycle, string> = {
	ONCE: "体验",
	MONTHLY: "月度",
	QUARTERLY: "季度",
	YEARLY: "年度",
};

const formatPower = (power: number): string => {
	return power.toLocaleString();
};

const formatDate = (dateString: string): string => {
	try {
		return new Date(dateString).toLocaleDateString("zh-CN", {
			year: "numeric",
			month: "2-digit",
			day: "2-digit",
		});
	} catch (_error) {
		return "日期无效";
	}
};

// 前端根据库存判断套餐状态
const determineFrontendPackageStatus = (
	item: InventoryItem,
): "ACTIVE" | "EMPTY_STOCK" => {
	if (item.quantityInStock === 0) {
		return "EMPTY_STOCK";
	}
	return "ACTIVE";
};

const PackageStatusBadge: React.FC<{ status: "ACTIVE" | "EMPTY_STOCK" }> = ({
	status,
}) => {
	let icon: JSX.Element | string;
	let text: string;
	let colorClass: string;
	let bgColorClass: string;

	switch (status) {
		case "ACTIVE":
			icon = <CheckCircleIcon className="h-3 w-3" />;
			text = "有库存";
			colorClass = "text-green-400";
			bgColorClass = "bg-green-500/10";
			break;
		case "EMPTY_STOCK":
			icon = <ArchiveIcon className="h-3 w-3" />;
			text = "库存为空";
			colorClass = "text-gray-400";
			bgColorClass = "bg-gray-500/10";
			break;
		default:
			// Should not happen with strict typing, but as a fallback
			icon = <PackageIcon className="h-3 w-3" />;
			text = "未知";
			colorClass = "text-gray-400";
			bgColorClass = "bg-gray-500/10";
	}

	return (
		<Badge
			className={cn(
				"flex items-center gap-1 border-none px-2 py-1 text-xs font-medium",
				colorClass,
				bgColorClass,
			)}
		>
			{icon}
			{text}
		</Badge>
	);
};

const PackageCard: React.FC<{ item: InventoryItem }> = ({ item }) => {
	const { plan, quantityInStock, lastUpdatedAt } = item;
	const frontendStatus = determineFrontendPackageStatus(item);

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.3 }}
			className={cn(
				"group relative overflow-hidden rounded-lg border transition-all duration-300 h-full flex flex-col",
				"bg-gradient-to-br from-[#1A1C1E] via-[#1E2023] to-[#23252A]",
				"border-[#D4B485]/20 hover:border-[#D4B485]/40 hover:shadow-lg hover:shadow-[#D4B485]/10",
				frontendStatus === "ACTIVE" &&
					"border-[#D4B485] hover:border-[#E5C9A5]",
				frontendStatus === "EMPTY_STOCK" &&
					"opacity-70 border-gray-500/30 hover:border-gray-500/50", // Adjusted for EMPTY_STOCK
			)}
		>
			<div
				className="pointer-events-none absolute inset-0 opacity-0 transition-opacity duration-500 group-hover:opacity-100"
				style={{
					background:
						"radial-gradient(600px circle at var(--mouse-x) var(--mouse-y), rgba(212,180,133,0.05), transparent 40%)",
				}}
			/>
			<div className="relative p-5 flex flex-col flex-grow">
				<div className="flex justify-between items-start mb-3">
					<h3
						className={cn(
							"text-lg font-semibold",
							zywhFont.className,
							"tracking-wide",
						)}
						style={{
							background:
								"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 50%, #D4B485 100%)",
							WebkitBackgroundClip: "text",
							WebkitTextFillColor: "transparent",
						}}
					>
						{plan.name}
					</h3>
					<PackageStatusBadge status={frontendStatus} />
				</div>

				{plan.description && (
					<p className="text-xs text-[#D4B485]/60 mb-3 leading-relaxed line-clamp-2 group-hover:line-clamp-none transition-all">
						{plan.description}
					</p>
				)}

				<div className="grid grid-cols-2 gap-x-4 gap-y-2 text-xs mb-4 text-[#D4B485]/70">
					<div className="flex items-center gap-1.5">
						<BriefcaseIcon className="h-3.5 w-3.5 text-[#D4B485]/50" />
						<span>
							类型:{" "}
							<span className="font-medium text-[#D4B485]/90">
								{PLAN_TYPE_DISPLAY_NAMES[plan.type]}
							</span>
						</span>
					</div>
					<div className="flex items-center gap-1.5">
						<ZapIcon className="h-3.5 w-3.5 text-[#D4B485]/50" />
						<span>
							算力:{" "}
							<span className="font-medium text-[#D4B485]/90">
								{formatPower(plan.computingPower)} 点
							</span>
						</span>
					</div>
					{plan.currentPrice && plan.currentPrice > 0 ? (
						<div className="flex items-center gap-1.5">
							<CircleDollarSignIcon className="h-3.5 w-3.5 text-[#D4B485]/50" />
							<span>
								单价:{" "}
								<span className="font-medium text-[#D4B485]/90">
									¥{(plan.currentPrice / 100).toFixed(2)}
								</span>
							</span>
						</div>
					) : (
						<div /> // Empty div to maintain grid structure if price is not available
					)}
					<div className="flex items-center gap-1.5">
						<ClockIcon className="h-3.5 w-3.5 text-[#D4B485]/50" />
						<span>
							计费周期:{" "}
							<span className="font-medium text-[#D4B485]/90">
								{BILLING_CYCLE_DISPLAY_NAMES[plan.billingCycle]}
							</span>
						</span>
					</div>
				</div>

				<div className="mt-auto border-t border-[#D4B485]/10 pt-3 flex justify-between items-center">
					<div className="flex items-center gap-1.5 text-base text-[#D4B485]/90">
						<ArchiveIcon className="h-4 w-4 text-[#D4B485]/70" />
						<span>
							库存:{" "}
							<span className="font-semibold">
								{quantityInStock} 件
							</span>
						</span>
					</div>
					<div className="text-xs text-[#D4B485]/70">
						{/* Reminder: lastUpdatedAt is used here. Confirm if it truly represents purchase date. */}
						上次购买于:{" "}
						<span className="font-medium text-[#D4B485]/90">
							{formatDate(lastUpdatedAt)}
						</span>
					</div>
				</div>
			</div>
		</motion.div>
	);
};

export function PackageInventoryView() {
	const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [_totalItems, setTotalItems] = useState(0);
	const [currentPage, _setCurrentPage] = useState(1);
	const [pageSize, _setPageSize] = useState(9); // 每页9个，方便3列布局

	const [filterType, setFilterType] = useState<PackageType>("ALL");
	const [filterStatus, setFilterStatus] = useState<PackageStatus>("ALL"); // 前端筛选状态
	const [searchTerm, setSearchTerm] = useState("");

	useEffect(() => {
		const fetchInventory = async () => {
			setIsLoading(true);
			try {
				const params: Record<string, string | number> = {
					page: currentPage,
					pageSize: pageSize,
				};
				if (filterType !== "ALL") {
					params.planType = filterType;
				}

				// Map frontend filterStatus to backend API status query
				if (filterStatus === "ACTIVE") {
					params.status = "AVAILABLE";
				} else if (filterStatus === "EMPTY_STOCK") {
					params.status = "OUT_OF_STOCK";
				} else if (filterStatus === "ALL") {
					// If "ALL" is selected, do not send status param or send a backend equivalent for all
					// Assuming backend handles missing status as 'all' or has an 'ALL' enum value
					// params.status = "ALL"; // Uncomment if backend supports this explicitly
				}

				if (searchTerm) {
					params.searchName = searchTerm;
				}

				const res = await apiClient.v1.agent.inventory.$get({
					query: params as any,
				});
				const result = await res.json();

				if (result.code === 200 && "data" in result && result.data) {
					setInventoryItems(result.data.items);
					setTotalItems(result.data.total);
				} else {
					toast.error("获取套餐库存失败", {
						description: (result as any).message || "请稍后再试",
					});
				}
			} catch (error) {
				toast.error("获取套餐库存失败", {
					description:
						error instanceof Error ? error.message : "网络请求错误",
				});
			} finally {
				setIsLoading(false);
			}
		};

		fetchInventory();
	}, [currentPage, pageSize, filterType, filterStatus, searchTerm]);

	const packageTypesForFilter: { value: PackageType; label: string }[] = [
		{ value: "ALL", label: "全部分类" },
		...(Object.keys(PLAN_TYPE_DISPLAY_NAMES) as ApiPlanType[]).map(
			(value) => ({
				value,
				label: PLAN_TYPE_DISPLAY_NAMES[value],
			}),
		),
	];

	const packageStatusesForFilter: { value: PackageStatus; label: string }[] =
		[
			{ value: "ALL", label: "全部状态" },
			{ value: "ACTIVE", label: "有库存" },
			{ value: "EMPTY_STOCK", label: "库存为空" },
		];

	useEffect(() => {
		const handleMouseMove = (e: MouseEvent) => {
			const cards = document.getElementsByClassName("group");
			for (const card of Array.from(cards)) {
				const rect = card.getBoundingClientRect();
				const x = e.clientX - rect.left;
				const y = e.clientY - rect.top;
				(card as HTMLElement).style.setProperty("--mouse-x", `${x}px`);
				(card as HTMLElement).style.setProperty("--mouse-y", `${y}px`);
			}
		};
		document.addEventListener("mousemove", handleMouseMove);
		return () => document.removeEventListener("mousemove", handleMouseMove);
	}, []);

	// 处理实际显示的列表，应用前端的状态筛选逻辑
	const displayedItems = useMemo(() => {
		// Frontend filtering is now simpler as API call handles status filtering for ACTIVE/EMPTY_STOCK
		// If API doesn't filter precisely or if ALL status is handled client-side for some reason:
		if (filterStatus === "ALL") {
			return inventoryItems;
		}
		// This client-side filter might be redundant if API does it perfectly
		return inventoryItems.filter(
			(item) => determineFrontendPackageStatus(item) === filterStatus,
		);
	}, [inventoryItems, filterStatus]);

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			className="w-full p-4 md:p-6"
		>
			<motion.div
				initial={{ opacity: 0, x: -20 }}
				animate={{ opacity: 1, x: 0 }}
				transition={{ delay: 0.1 }}
				className="mb-6 md:mb-8"
			>
				<div className="flex items-center gap-3 md:gap-4">
					<div
						className={cn(
							"flex h-12 w-12 md:h-14 md:w-14 shrink-0 items-center justify-center",
							"rounded-xl bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
							"ring-1 ring-[#D4B485]/20",
							"shadow-[0_0_0_6px_rgba(212,180,133,0.03)] md:shadow-[0_0_0_8px_rgba(212,180,133,0.03)]",
						)}
					>
						<ArchiveIcon className="h-6 w-6 md:h-7 md:w-7 text-[#D4B485]" />
					</div>
					<div>
						<h1
							className={cn(
								"font-semibold text-2xl md:text-3xl",
								zywhFont.className,
								"leading-tight tracking-wide",
							)}
							style={{
								background:
									"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 50%, #D4B485 100%)",
								WebkitBackgroundClip: "text",
								WebkitTextFillColor: "transparent",
							}}
						>
							我的套餐仓库
						</h1>
						<p className="text-xs md:text-sm text-[#D4B485]/60 mt-1">
							管理和查看您已购买的所有算力套餐
						</p>
					</div>
				</div>
			</motion.div>

			<Card
				className={cn(
					"p-4 mb-6 md:mb-8",
					"bg-gradient-to-br from-[#1A1C1E]/80 via-[#1E2023]/90 to-[#23252A]",
					"border border-[#D4B485]/20 backdrop-blur-sm",
				)}
			>
				<div className="flex flex-col sm:flex-row gap-4 items-center">
					<div className="w-full sm:w-auto sm:flex-1">
						<Input
							placeholder="按套餐名称搜索..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className={cn(
								"w-full bg-[#1A1C1E] border-[#D4B485]/20 text-[#D4B485]/80 focus:ring-[#D4B485]/40 placeholder:text-[#D4B485]/40",
							)}
						/>
					</div>
					<div className="w-full sm:w-auto sm:min-w-[180px]">
						<Select
							value={filterType}
							onValueChange={(value) =>
								setFilterType(value as PackageType)
							}
						>
							<SelectTrigger
								className={cn(
									"w-full bg-[#1A1C1E] border-[#D4B485]/20 text-[#D4B485]/80 focus:ring-[#D4B485]/40 placeholder:text-[#D4B485]/40",
								)}
							>
								<SelectValue placeholder="全部分类" />
							</SelectTrigger>
							<SelectContent
								className={cn(
									"bg-[#1A1C1E] border-[#D4B485]/20 text-[#D4B485]/80",
								)}
							>
								{packageTypesForFilter.map((type) => (
									<SelectItem
										key={type.value}
										value={type.value}
										className="hover:bg-[#D4B485]/10 focus:bg-[#D4B485]/10"
									>
										{type.label}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>
					<div className="w-full sm:w-auto sm:min-w-[180px]">
						<Select
							value={filterStatus}
							onValueChange={(value) =>
								setFilterStatus(value as PackageStatus)
							}
						>
							<SelectTrigger
								className={cn(
									"w-full bg-[#1A1C1E] border-[#D4B485]/20 text-[#D4B485]/80 focus:ring-[#D4B485]/40 placeholder:text-[#D4B485]/40",
								)}
							>
								<SelectValue placeholder="全部状态" />
							</SelectTrigger>
							<SelectContent
								className={cn(
									"bg-[#1A1C1E] border-[#D4B485]/20 text-[#D4B485]/80",
								)}
							>
								{packageStatusesForFilter.map((status) => (
									<SelectItem
										key={status.value}
										value={status.value}
										className="hover:bg-[#D4B485]/10 focus:bg-[#D4B485]/10"
									>
										{status.label}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>
				</div>
			</Card>

			{isLoading ? (
				<div className="flex flex-col items-center justify-center text-center py-12 min-h-[300px] rounded-lg bg-[#1A1C1E]/50 border border-[#D4B485]/10">
					<Loader2Icon className="h-12 w-12 text-[#D4B485]/50 animate-spin mb-4" />
					<p className="text-lg font-medium text-[#D4B485]/70">
						正在加载套餐库存...
					</p>
					<p className="text-sm text-[#D4B485]/50">
						请稍候，精彩马上呈现！
					</p>
				</div>
			) : displayedItems.length > 0 ? (
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
					{displayedItems.map((item) => (
						<PackageCard key={item.inventoryId} item={item} />
					))}
				</div>
			) : (
				<motion.div
					initial={{ opacity: 0, y: 10 }}
					animate={{ opacity: 1, y: 0 }}
					className="flex flex-col items-center justify-center text-center py-12 min-h-[300px] rounded-lg bg-[#1A1C1E]/50 border border-[#D4B485]/10"
				>
					<PackageIcon className="h-16 w-16 text-[#D4B485]/30 mb-4" />
					<p className="text-lg font-medium text-[#D4B485]/70 mb-1">
						没有找到符合条件的套餐
					</p>
					<p className="text-sm text-[#D4B485]/50">
						请尝试调整筛选条件或检查您的购买记录。
					</p>
				</motion.div>
			)}
			{/* TODO: Pagination controls if totalItems > pageSize */}
		</motion.div>
	);
}
