"use client";

import { TabsContent } from "@ui/components/tabs";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { ActivationList } from "./activations/ActivationList";
import { AgentOrderList } from "./agent-orders/AgentOrderList";

export function ContentSection() {
	return (
		<motion.div
			initial={{ opacity: 0, y: 40 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.3, ease: "easeOut" }}
			className="mt-6"
		>
			<div
				className={cn(
					"relative",
					"rounded-2xl",
					"bg-gradient-to-br from-[#1A1C1E]/90 via-[#1E2023]/95 to-[#23252A]/90",
					"border border-[#D4B485]/10",
					"p-8",
					"shadow-[0_4px_16px_-4px_rgba(212,180,133,0.08)]",
					"backdrop-blur-lg",
					"overflow-hidden",
					"transition-all duration-300",
					"hover:border-[#D4B485]/20",
					"hover:shadow-[0_6px_20px_-6px_rgba(212,180,133,0.12)]",
				)}
			>
				{/* 背景装饰 */}
				<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.02)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite] pointer-events-none opacity-50" />
				<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.03),transparent_70%)] pointer-events-none opacity-50" />

				{/* 内容区域 */}
				<div className="relative z-10">
					<TabsContent value="agent-orders">
						<motion.div
							key="agent-orders"
							initial={{ opacity: 0, x: -20 }}
							animate={{ opacity: 1, x: 0 }}
							exit={{ opacity: 0, x: 20 }}
							transition={{ duration: 0.2 }}
						>
							<AgentOrderList />
						</motion.div>
					</TabsContent>

					<TabsContent value="activations">
						<motion.div
							key="activations"
							initial={{ opacity: 0, x: -20 }}
							animate={{ opacity: 1, x: 0 }}
							exit={{ opacity: 0, x: 20 }}
							transition={{ duration: 0.2 }}
						>
							<ActivationList />
						</motion.div>
					</TabsContent>
				</div>
			</div>
		</motion.div>
	);
}
