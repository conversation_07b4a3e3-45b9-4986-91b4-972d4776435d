"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { cn } from "@ui/lib";
import { ListChecksIcon, TrendingUpIcon } from "lucide-react";
import { formatAmount } from "./lib/power";
import type { SelectedPlanItem } from "./PurchaseView";

// Define a mapping for PlanType to its display name
// This should ideally be consistent with PLAN_TYPES_CONFIG in PurchaseView.tsx
// For simplicity here, we'll define it directly. For a larger app, consider sharing this config.
const PLAN_TYPE_DISPLAY_NAMES: Record<
	SelectedPlanItem["plan"]["type"],
	string
> = {
	AVATAR: "数字人",
	IMAGE: "图文",
	VIDEO: "视频",
	TEXT: "文案",
};

// MARKET_PRICE_PER_HOUR is what the agent SELLS 1 hour of video service for to end-users.
// This value is used to calculate potential profit for the agent.
// We should confirm if this is a fixed value or varies per plan type.
// For now, using the existing constant from the original PriceDetail.
const _MARKET_PRICE_PER_HOUR = 790; // Original value from old PriceDetail.tsx, represents end-user price.

interface PriceDetailProps {
	selectedPlanItems: SelectedPlanItem[];
	totalAgentPriceInCents: number; // Total cost for the agent in cents
}

export function PriceDetail({
	selectedPlanItems,
	totalAgentPriceInCents,
}: PriceDetailProps) {
	const _totalSelectedPower = selectedPlanItems.reduce(
		(sum, item) => sum + item.plan.computingPower * item.quantity,
		0,
	);

	// Calculate total equivalent hours based on combined computing power
	// const totalEquivalentHours = computingPowerToHours(totalSelectedPower); // No longer needed for new profit calculation

	// Agent's total cost in Yuan
	const agentTotalCostInYuan = totalAgentPriceInCents / 100;

	// New profit calculation: (user discount price - agent price) * quantity for each plan, then sum up
	// Prices are in cents, so divide by 100 at the end for Yuan
	const estimatedTotalProfitInCents = selectedPlanItems.reduce(
		(sum, item) => {
			const profitPerUnit =
				item.plan.discountPrice - item.plan.agentPrice;
			return sum + profitPerUnit * item.quantity;
		},
		0,
	);
	const estimatedTotalProfitInYuan = estimatedTotalProfitInCents / 100;

	return (
		<div className="space-y-4">
			{/* Currently Selected Plans - Moved to the top as per general fee structure */}
			{selectedPlanItems.length > 0 && (
				<div className="pb-4 border-b border-[#D4B485]/10">
					<h4
						className={cn(
							"font-medium text-base mb-3 flex items-center gap-2",
							zywhFont.className,
							"leading-none tracking-[0.05em]",
						)}
						style={{
							background:
								"linear-gradient(135deg, rgba(229,201,165,0.95) 0%, rgba(212,180,133,0.9) 50%, rgba(176,137,104,0.85) 100%)",
							WebkitBackgroundClip: "text",
							WebkitTextFillColor: "transparent",
						}}
					>
						<ListChecksIcon className="h-4 w-4 text-[#D4B485]/80" />
						当前已选套餐
					</h4>
					<div className="space-y-1.5 text-sm max-h-32 overflow-y-auto pr-2">
						{selectedPlanItems.map((item) => {
							const planTypeDisplayName =
								PLAN_TYPE_DISPLAY_NAMES[item.plan.type] ||
								"未知类型";
							return (
								<div
									key={item.plan.id}
									className="flex justify-between items-center text-white/80"
								>
									<span
										className="truncate"
										title={`【${planTypeDisplayName}】${item.plan.name}`}
									>
										<span className="text-white/50 mr-1">{`【${planTypeDisplayName}】`}</span>
										{item.plan.name}
									</span>
									<span className="text-white/60 ml-2 whitespace-nowrap">
										x {item.quantity}
									</span>
								</div>
							);
						})}
					</div>
				</div>
			)}

			{/* Estimated Profit Card - only show if there are selected items */}
			{selectedPlanItems.length > 0 && (
				<div
					className={cn(
						"relative overflow-hidden rounded-lg p-3",
						"bg-gradient-to-br from-[#1A1C1E] via-[#1E2023] to-[#23252A]",
						"border border-[#D4B485]/20",
						"shadow-[0_8px_32px_-4px_rgba(0,0,0,0.3)]",
						"group",
					)}
				>
					<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
					<div className="relative flex items-center justify-between">
						<div className="flex items-center gap-2">
							<div
								className={cn(
									"flex h-8 w-8 items-center justify-center rounded-full",
									"bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
									"ring-1 ring-[#D4B485]/20",
									"shadow-[0_4px_12px_-2px_rgba(212,180,133,0.12)]",
								)}
							>
								<TrendingUpIcon className="h-4 w-4 text-[#D4B485]" />
							</div>
							<span
								className={cn(
									"font-medium text-base",
									zywhFont.className,
									"leading-none tracking-[0.05em]",
								)}
								style={{
									background:
										"linear-gradient(135deg, rgba(229,201,165,0.95) 0%, rgba(212,180,133,0.9) 50%, rgba(176,137,104,0.85) 100%)",
									WebkitBackgroundClip: "text",
									WebkitTextFillColor: "transparent",
								}}
							>
								预计转售利润
							</span>
						</div>
						<div
							className={cn(
								"font-bold text-lg tabular-nums",
								zywhFont.className,
								"leading-none tracking-[0.05em]",
							)}
							style={{
								background:
									"linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.85) 50%, rgba(229,201,165,0.8) 100%)",
								WebkitBackgroundClip: "text",
								WebkitTextFillColor: "transparent",
							}}
						>
							+¥
							{formatAmount(
								estimatedTotalProfitInYuan < 0
									? 0
									: estimatedTotalProfitInYuan,
							) || 0}
						</div>
					</div>
				</div>
			)}

			{/* Total Price (Agent Cost) - only show if there are selected items */}
			{selectedPlanItems.length > 0 && (
				<div
					className={cn(
						"flex items-center justify-between",
						"border-t border-[#D4B485]/20 pt-4 mt-4", // Added mt-4 for spacing if profit is shown
					)}
				>
					<span
						className={cn(
							"font-medium text-base",
							zywhFont.className,
							"leading-none tracking-[0.05em]",
						)}
						style={{
							background:
								"linear-gradient(135deg, rgba(229,201,165,0.95) 0%, rgba(212,180,133,0.9) 50%, rgba(176,137,104,0.85) 100%)",
							WebkitBackgroundClip: "text",
							WebkitTextFillColor: "transparent",
						}}
					>
						支付总额
					</span>
					<div
						className={cn(
							"font-bold text-2xl tabular-nums tracking-tight",
							zywhFont.className,
							"leading-none tracking-[0.05em]",
						)}
						style={{
							background:
								"linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.85) 50%, rgba(229,201,165,0.8) 100%)",
							WebkitBackgroundClip: "text",
							WebkitTextFillColor: "transparent",
						}}
					>
						¥{formatAmount(agentTotalCostInYuan) || 0}
					</div>
				</div>
			)}
		</div>
	);
}
