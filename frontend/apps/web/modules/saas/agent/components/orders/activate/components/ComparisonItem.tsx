import { cn } from "@ui/lib";
import { AnimatedNumber } from "../../../dashboard/components/AnimatedNumber";

interface ComparisonItemProps {
	label: string;
	value: number;
	unit: string;
	isAfter?: boolean;
}

export function ComparisonItem({
	label,
	value,
	unit,
	isAfter = false,
}: ComparisonItemProps) {
	return (
		<div
			className="flex items-center justify-between rounded-lg bg-[#1E2023]/30 p-3 group relative cursor-default
			before:absolute before:inset-0
			before:rounded-lg
			before:transition-all before:duration-500
			before:ease-out
			before:scale-[0.97]
			before:opacity-0
			before:bg-gradient-to-r
			before:from-transparent
			before:via-[#1E2532]
			before:to-transparent
			before:bg-clip-padding
			before:backdrop-filter
			before:backdrop-blur-[8px]
			before:border-transparent
			before:bg-opacity-50
			hover:before:scale-100 hover:before:opacity-100
			before:z-[-1]
			after:absolute after:inset-0
			after:rounded-lg
			after:transition-all after:duration-500
			after:ease-out
			after:scale-95
			after:bg-radial-gradient-ellipse
			after:from-[#F8C59B]/5
			after:via-transparent
			after:to-transparent
			hover:after:opacity-100
			after:z-[-2]
			hover:bg-[#F8C59B]/[0.03]
			hover:shadow-[inset_0_0_20px_rgba(248,197,155,0.05)]"
		>
			<span className="text-sm text-[#D4B485]/60 relative z-[1]">
				{label}
			</span>
			{isAfter ? (
				<span
					className={cn(
						"font-medium text-emerald-500 flex items-center justify-end",
						"min-w-[60px]",
						label.includes("算力") && "min-w-[100px]",
						label.includes("时长") && "min-w-[80px]",
						"relative z-[1]",
					)}
				>
					<AnimatedNumber value={value} duration={2} />
					<span className="ml-1">{unit}</span>
				</span>
			) : (
				<span className="font-medium text-[#D4B485] relative z-[1]">
					{value}
					{unit}
				</span>
			)}
		</div>
	);
}
