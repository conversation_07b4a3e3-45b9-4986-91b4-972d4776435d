"use client";

import { <PERSON><PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@ui/components/table";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { Loader2Icon, RefreshCcwIcon, SearchIcon } from "lucide-react";
import { useEffect, useState } from "react";
import { useAgentOrders } from "../../hooks/useAgentOrders";

type OrderStatus =
	| "PENDING"
	| "PAID"
	| "COMPLETED"
	| "FAILED"
	| "CANCELLED"
	| "REFUNDED";

const ORDER_STATUS_MAP = {
	PENDING: { label: "待支付", color: "bg-yellow-500/20 text-yellow-500" },
	PAID: { label: "已支付", color: "bg-blue-500/20 text-blue-500" },
	COMPLETED: { label: "已完成", color: "bg-emerald-500/20 text-emerald-500" },
	FAILED: { label: "失败", color: "bg-red-500/20 text-red-500" },
	CANCELLED: { label: "已取消", color: "bg-gray-500/20 text-gray-500" },
	REFUNDED: { label: "已退款", color: "bg-purple-500/20 text-purple-500" },
} as const;

const STATUS_OPTIONS = [
	{ label: "全部状态", value: "ALL" },
	{ label: "待支付", value: "PENDING" },
	{ label: "已支付", value: "PAID" },
	{ label: "已完成", value: "COMPLETED" },
	{ label: "已失败", value: "FAILED" },
	{ label: "已取消", value: "CANCELLED" },
	{ label: "已退款", value: "REFUNDED" },
] as const;

const PAYMENT_METHOD_MAP = {
	WECHAT_NATIVE: "微信扫码支付",
	WECHAT_JSAPI: "微信JSAPI支付",
	WECHAT_H5: "微信H5支付",
	WECHAT_APP: "微信APP支付",
	ALIPAY_PAGE: "支付宝网页支付",
	ALIPAY_WAP: "支付宝手机网站支付",
} as const;

export function AgentOrderList() {
	const { orders, total, loading, fetchOrders } = useAgentOrders();
	const [currentPage, setCurrentPage] = useState(1);
	const [searchTerm, setSearchTerm] = useState("");
	const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
	const [status, setStatus] = useState<OrderStatus | "ALL">("ALL");
	const pageSize = 10;

	// 使用防抖处理搜索词
	useEffect(() => {
		const timer = setTimeout(() => {
			setDebouncedSearchTerm(searchTerm);
		}, 500); // 500ms 的防抖延迟

		return () => clearTimeout(timer);
	}, [searchTerm]);

	// 监听防抖后的搜索词和状态变化
	useEffect(() => {
		fetchOrders({
			page: currentPage,
			pageSize,
			...(status !== "ALL" && { status: status as OrderStatus }),
			...(debouncedSearchTerm && { keyword: debouncedSearchTerm }),
		});
	}, [fetchOrders, currentPage, debouncedSearchTerm, status]);

	// 刷新数据
	const handleRefresh = () => {
		setCurrentPage(1);
		fetchOrders({
			page: 1,
			pageSize,
			...(status !== "ALL" && { status: status as OrderStatus }),
			...(debouncedSearchTerm && { keyword: debouncedSearchTerm }),
		});
	};

	// 处理搜索按钮点击
	const handleSearch = () => {
		setCurrentPage(1);
		setDebouncedSearchTerm(searchTerm);
	};

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			className="space-y-4"
		>
			{/* 搜索和筛选区域 - 始终显示 */}
			<div className="flex items-center gap-4">
				<div className="relative flex-1">
					<SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-[#D4B485]/40" />
					<Input
						type="search"
						placeholder="输入订单号搜索"
						value={searchTerm}
						onChange={(e) => setSearchTerm(e.target.value)}
						onKeyDown={(e) => {
							if (e.key === "Enter") {
								handleSearch();
							}
						}}
						className={cn(
							"pl-9",
							"bg-[#1E2023]/50 border-[#D4B485]/20",
							"text-[#D4B485] placeholder:text-[#D4B485]/40",
							"focus:border-[#D4B485]/40 focus:ring-[#D4B485]/20",
						)}
					/>
				</div>
				<Select
					value={status}
					onValueChange={(value) => {
						setStatus(value as OrderStatus | "ALL");
						setCurrentPage(1);
					}}
				>
					<SelectTrigger
						className={cn(
							"w-[160px]",
							"bg-[#1E2023]/50 border-[#D4B485]/20",
							"text-[#D4B485]",
							"focus:border-[#D4B485]/40 focus:ring-[#D4B485]/20",
						)}
					>
						<SelectValue placeholder="选择状态" />
					</SelectTrigger>
					<SelectContent>
						{STATUS_OPTIONS.map((option) => (
							<SelectItem
								key={option.value}
								value={option.value}
								className={cn(
									"text-[#D4B485]",
									"focus:bg-[#D4B485]/10",
									"focus:text-[#E5C9A5]",
								)}
							>
								{option.label}
							</SelectItem>
						))}
					</SelectContent>
				</Select>
				<Button
					className={cn(
						"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
						"text-white",
						"border-none",
						"shadow-[0_8px_16px_-4px_rgba(212,180,133,0.3)]",
						"hover:shadow-[0_12px_20px_-4px_rgba(212,180,133,0.4)]",
						"transition-all duration-200",
					)}
					onClick={handleSearch}
				>
					搜索
				</Button>
				<Button
					variant="outline"
					size="icon"
					className={cn(
						"bg-[#1E2023]/50 border-[#D4B485]/20",
						"text-[#D4B485] hover:text-[#E5C9A5]",
						"hover:bg-[#D4B485]/10",
						"transition duration-200",
						"w-10 h-10",
						"flex items-center justify-center",
					)}
					onClick={handleRefresh}
				>
					<RefreshCcwIcon className="h-4 w-4" />
				</Button>
			</div>

			{/* 表格区域 - 根据状态显示不同内容 */}
			<div
				className={cn(
					"rounded-lg overflow-hidden",
					"bg-gradient-to-br from-[#1A1C1E]/80 via-[#1E2023]/90 to-[#23252A]",
					"ring-1 ring-[#D4B485]/10",
					"shadow-[0_4px_16px_-4px_rgba(0,0,0,0.3)]",
					"backdrop-blur-lg",
				)}
			>
				{loading ? (
					<div className="flex flex-col items-center justify-center py-12">
						<Loader2Icon className="h-8 w-8 animate-spin text-[#D4B485]/60" />
						<span className="mt-4 text-[#D4B485]/60">
							加载中...
						</span>
					</div>
				) : !orders.length ? (
					<div className="flex flex-col items-center justify-center py-12">
						<div
							className={cn(
								"flex h-16 w-16 items-center justify-center rounded-full",
								"bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
								"ring-1 ring-[#D4B485]/20",
								"shadow-[0_4px_12px_-2px_rgba(212,180,133,0.12)]",
							)}
						>
							<span className="text-2xl">📋</span>
						</div>
						<span className="mt-4 text-[#D4B485]/60">
							暂无套餐购买记录
						</span>
					</div>
				) : (
					<Table>
						<TableHeader>
							<TableRow className="hover:bg-[#D4B485]/5 border-b border-[#D4B485]/20">
								<TableHead className="text-[#D4B485]/60 font-medium">
									订单号
								</TableHead>
								<TableHead className="text-[#D4B485]/60 font-medium">
									购买套餐
								</TableHead>
								<TableHead className="text-[#D4B485]/60 font-medium">
									支付金额
								</TableHead>
								<TableHead className="text-[#D4B485]/60 font-medium">
									支付方式
								</TableHead>
								<TableHead className="text-[#D4B485]/60 font-medium">
									订单状态
								</TableHead>
								<TableHead className="text-[#D4B485]/60 font-medium">
									创建时间
								</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{orders.map((order, index) => (
								<TableRow
									key={order.id}
									className={cn(
										"transition-colors duration-200",
										"hover:bg-[#D4B485]/5",
										"border-b border-[#D4B485]/10",
										"last:border-0",
										index % 2 === 0 &&
											"bg-[#D4B485]/[0.02]",
									)}
								>
									<TableCell className="font-medium">
										<div className="flex items-center gap-2">
											<div
												className={cn(
													"w-1 h-4 rounded-full",
													ORDER_STATUS_MAP[
														order.status as keyof typeof ORDER_STATUS_MAP
													].color
														.replace("text-", "bg-")
														.replace("/20", "/40"),
												)}
											/>
											<span className="text-[#D4B485]">
												{order.orderNo}
											</span>
										</div>
									</TableCell>
									<TableCell>
										<div className="flex items-baseline gap-1">
											<span className="text-[#D4B485]/80 font-medium">
												{order.quantity.toLocaleString()}
											</span>
											<span className="text-[#D4B485]/40 text-sm">
												个
											</span>
										</div>
									</TableCell>
									<TableCell>
										<div className="flex items-baseline gap-1">
											<span className="text-[#D4B485]/80 font-medium">
												¥
												{(
													order.amount / 100
												).toLocaleString()}
											</span>
										</div>
									</TableCell>
									<TableCell className="text-[#D4B485]/60">
										{order.paymentMethod
											? PAYMENT_METHOD_MAP[
													order.paymentMethod as keyof typeof PAYMENT_METHOD_MAP
												]
											: "-"}
									</TableCell>
									<TableCell>
										<div
											className={cn(
												"inline-flex items-center rounded-full px-2.5 py-0.5",
												"text-xs font-semibold",
												"transition-colors duration-200",
												"shadow-[0_2px_4px_rgba(0,0,0,0.1)]",
												"border border-current/20",
												ORDER_STATUS_MAP[
													order.status as keyof typeof ORDER_STATUS_MAP
												].color,
											)}
										>
											{
												ORDER_STATUS_MAP[
													order.status as keyof typeof ORDER_STATUS_MAP
												].label
											}
										</div>
									</TableCell>
									<TableCell className="text-[#D4B485]/60">
										{new Date(
											order.createdAt,
										).toLocaleDateString()}
									</TableCell>
								</TableRow>
							))}
						</TableBody>
					</Table>
				)}
			</div>

			{/* 分页器 - 只在有数据时显示 */}
			{!loading && orders.length > 0 && (
				<div className="flex justify-center mt-6">
					<div
						className={cn(
							"flex items-center gap-2 rounded-lg px-4 py-2",
							"bg-[#1A1C1E]/80",
							"ring-1 ring-[#D4B485]/10",
							"shadow-[0_2px_8px_-2px_rgba(0,0,0,0.3)]",
						)}
					>
						<button
							type="button"
							className={cn(
								"px-3 py-1 rounded-md",
								"text-sm font-medium",
								"transition-colors duration-200",
								"disabled:opacity-50 disabled:cursor-not-allowed",
								currentPage === 1
									? "text-[#D4B485]/40 bg-[#D4B485]/5"
									: "text-[#D4B485]/80 hover:bg-[#D4B485]/10",
							)}
							disabled={currentPage === 1}
							onClick={() => setCurrentPage((p) => p - 1)}
						>
							上一页
						</button>
						<span className="text-[#D4B485]/40 px-2">
							第 {currentPage} 页 / 共{" "}
							{Math.ceil(total / pageSize)} 页
						</span>
						<button
							type="button"
							className={cn(
								"px-3 py-1 rounded-md",
								"text-sm font-medium",
								"transition-colors duration-200",
								"disabled:opacity-50 disabled:cursor-not-allowed",
								currentPage === Math.ceil(total / pageSize)
									? "text-[#D4B485]/40 bg-[#D4B485]/5"
									: "text-[#D4B485]/80 hover:bg-[#D4B485]/10",
							)}
							disabled={
								currentPage === Math.ceil(total / pageSize)
							}
							onClick={() => setCurrentPage((p) => p + 1)}
						>
							下一页
						</button>
					</div>
				</div>
			)}
		</motion.div>
	);
}
