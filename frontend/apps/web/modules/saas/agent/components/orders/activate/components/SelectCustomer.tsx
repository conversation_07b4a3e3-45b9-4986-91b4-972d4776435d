"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { SearchIcon, UserIcon } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { ConsumerPagination } from "../../../../components/consumers/components/ConsumersPagination";
import type { Consumer } from "../../../../types/consumer";
import { ConsumerBindingStatus, useConsumers } from "../hooks";

interface Package {
	id: string;
	name: string;
	level: string;
	computingPower: number;
	validityDays: number;
	price: number;
	description?: string;
	features: Array<{
		type: string;
		enabled: boolean;
		description?: string;
	}>;
}

interface OrderInfo {
	orderId: string;
	customer: Consumer;
	package: Package;
	createdAt: string;
}

interface FormData {
	customer: Consumer | null;
	package: Package | null;
	orderInfo: OrderInfo | null;
}

interface SelectCustomerProps {
	onNext: (data: { customer: Consumer }) => void;
	formData: FormData;
}

// 防抖 hook
function useDebounce<T>(value: T, delay: number): T {
	const [debouncedValue, setDebouncedValue] = useState<T>(value);

	useEffect(() => {
		const timer = setTimeout(() => {
			setDebouncedValue(value);
		}, delay);

		return () => {
			clearTimeout(timer);
		};
	}, [value, delay]);

	return debouncedValue;
}

// 绑定状态配置
const BINDING_STATUS_CONFIG = {
	self: {
		label: "我的客户",
		color: "text-emerald-400",
		bgColor: "bg-emerald-400/10",
		borderColor: "border-emerald-400/20",
		tooltip: "此客户已绑定您的代理账号",
	},
	none: {
		label: "未绑定",
		color: "text-blue-400",
		bgColor: "bg-blue-400/10",
		borderColor: "border-blue-400/20",
		tooltip: "此客户尚未绑定任何代理，选择后将自动绑定",
	},
	other: {
		label: "其他代理",
		color: "text-red-400",
		bgColor: "bg-red-400/10",
		borderColor: "border-red-400/20",
		tooltip: "此客户已绑定其他代理，无法开通套餐",
	},
	agent: {
		label: "代理商",
		color: "text-red-400",
		bgColor: "bg-red-500/10",
		borderColor: "border-red-500/30",
		tooltip: "无法给代理商开通套餐",
	},
};

export function SelectCustomer({ onNext, formData }: SelectCustomerProps) {
	const [searchTerm, setSearchTerm] = useState("");
	const [selectedCustomer, setSelectedCustomer] = useState(
		formData.customer || null,
	);
	const [currentPage, setCurrentPage] = useState(1);
	const pageSize = 9;

	const { consumers, total, loading, fetchConsumers } = useConsumers();

	// 使用防抖处理搜索关键词
	const debouncedSearchTerm = useDebounce(searchTerm, 300);

	// 判断是否是手机号码格式 - 改为useCallback以避免依赖项警告
	const isPhoneNumber = useCallback((str: string) => {
		return /^1[3-9]\d{9}$/.test(str);
	}, []);

	// 获取消费者列表
	useEffect(() => {
		fetchConsumers({
			page: currentPage,
			pageSize,
			keyword: debouncedSearchTerm || undefined,
			status: "ACTIVE",
			searchAll:
				!!debouncedSearchTerm && isPhoneNumber(debouncedSearchTerm), // 只有是手机号时才搜索全部
		});
	}, [fetchConsumers, currentPage, debouncedSearchTerm, isPhoneNumber]);

	// 处理选择消费者
	const handleSelectCustomer = (customer: Consumer) => {
		if (
			customer.bindingStatus === ConsumerBindingStatus.OTHER ||
			customer.bindingStatus === ConsumerBindingStatus.AGENT
		) {
			return; // 已绑定其他代理的客户或代理商不能选择
		}
		setSelectedCustomer(customer);
	};

	// 处理下一步
	const handleNext = () => {
		if (selectedCustomer) {
			onNext({ customer: selectedCustomer });
		}
	};

	// 处理页码变化
	const handlePageChange = (page: number) => {
		setCurrentPage(page);
	};

	return (
		<div className="space-y-8">
			{/* 大标题 */}
			<div className="space-y-2 text-center">
				<h3
					className={cn("text-2xl font-semibold", zywhFont.className)}
					style={{
						background:
							"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
						WebkitBackgroundClip: "text",
						WebkitTextFillColor: "transparent",
						backgroundClip: "text",
						textShadow:
							"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
					}}
				>
					选择开通客户
				</h3>
				<p className="text-[#D4B485]/60">
					从下方列表中选择需要开通套餐的客户账户
				</p>
			</div>

			{/* 搜索区域 */}
			<div className="flex items-center gap-4">
				<div className="relative flex-1">
					<SearchIcon className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-[#D4B485]/40" />
					<Input
						type="search"
						placeholder="输入手机号搜索未绑定客户，或查找我的客户..."
						value={searchTerm}
						onChange={(e) => setSearchTerm(e.target.value)}
						className={cn(
							"h-12 pl-10",
							"bg-[#1E2023]/50 border-[#D4B485]/20",
							"text-[#E5C9A5] placeholder:text-[#D4B485]/40",
							"focus:border-[#D4B485]/40 focus:ring-[#D4B485]/20",
							"text-base",
						)}
					/>
					{loading && (
						<div className="absolute right-3 top-1/2 -translate-y-1/2 text-[#D4B485]/40 text-sm">
							搜索中...
						</div>
					)}
				</div>
			</div>

			{/* 客户列表 */}
			<div className="grid grid-cols-3 gap-4">
				{loading ? (
					// 加载状态
					<div className="col-span-3 flex items-center justify-center py-12">
						<div className="text-[#D4B485]/60">加载中...</div>
					</div>
				) : consumers.length === 0 ? (
					// 空状态
					<div className="col-span-3 flex items-center justify-center py-12">
						<div className="text-[#D4B485]/60">暂无客户数据</div>
					</div>
				) : (
					// 客户列表
					consumers.map((customer) => (
						<motion.div
							key={customer.id}
							initial={{ opacity: 0, y: 20 }}
							animate={{ opacity: 1, y: 0 }}
							className={cn(
								"rounded-lg p-4",
								"cursor-pointer",
								"transition-all duration-300",
								"relative overflow-hidden",
								(customer.bindingStatus ===
									ConsumerBindingStatus.OTHER ||
									customer.bindingStatus ===
										ConsumerBindingStatus.AGENT) &&
									"opacity-50 cursor-not-allowed",
								selectedCustomer?.id === customer.id
									? [
											"bg-[#D4B485]/10",
											"border-[#D4B485]/40",
											"ring-[1px]",
											"ring-[#D4B485]/40",
											"ring-offset-2",
											"ring-offset-[#1A1C1E]",
											"shadow-[0_0_20px_rgba(212,180,133,0.2)]",
											"scale-[1.02]",
										].join(" ")
									: [
											"bg-[#1E2023]/50",
											"border-[#D4B485]/20",
											"hover:border-[#D4B485]/30",
											"hover:scale-[1.01]",
											"hover:shadow-[0_8px_16px_-4px_rgba(212,180,133,0.15)]",
										].join(" "),
								"border",
								// 第一层装饰 - 内部光效
								"before:absolute before:inset-0",
								"before:rounded-lg",
								"before:transition-all before:duration-500",
								"before:ease-out",
								"before:scale-[0.97]",
								"before:opacity-0",
								"before:bg-gradient-to-r",
								"before:from-transparent",
								"before:via-[#1E2532]",
								"before:to-transparent",
								"before:bg-clip-padding",
								"before:backdrop-filter",
								"before:backdrop-blur-[8px]",
								"before:border-transparent",
								"before:bg-opacity-50",
								"hover:before:scale-100 hover:before:opacity-100",
								"before:z-[-1]",
								// 第二层装饰 - 外部光晕
								"after:absolute after:inset-[-1px]",
								"after:rounded-lg",
								"after:transition-all after:duration-500",
								"after:ease-out",
								"after:scale-[1.02]",
								"after:bg-gradient-to-r",
								"after:from-transparent",
								"after:via-[#D4B485]/5",
								"after:to-transparent",
								"after:opacity-0",
								"hover:after:opacity-100",
								"after:z-[-2]",
								// 第三层装饰 - 内部阴影
								"shadow-[inset_0_0_0_1px_rgba(212,180,133,0.05)]",
								"hover:shadow-[inset_0_0_0_1px_rgba(212,180,133,0.1)]",
							)}
							onClick={() => handleSelectCustomer(customer)}
						>
							<div className="flex items-start gap-3 relative z-[1]">
								<div
									className={cn(
										"flex h-10 w-10 shrink-0 items-center justify-center rounded-full",
										"bg-gradient-to-br from-[#D4B485]/15 to-[#D4B485]/10",
										"ring-1 ring-[#D4B485]/30",
										"shadow-[0_4px_12px_-2px_rgba(212,180,133,0.2)]",
										"transition-all duration-300",
										selectedCustomer?.id === customer.id
											? "from-[#D4B485]/20 to-[#D4B485]/15 ring-[#D4B485]/40"
											: "hover:from-[#D4B485]/20 hover:to-[#D4B485]/15",
									)}
								>
									<UserIcon className="h-5 w-5 text-[#D4B485]" />
								</div>
								<div className="min-w-0 flex-1">
									{/* 头部区域 - 用户名和绑定状态 */}
									<div className="flex items-center justify-between gap-2">
										<div
											className={cn(
												"text-base font-medium truncate",
												zywhFont.className,
											)}
											style={{
												background:
													"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
												WebkitBackgroundClip: "text",
												WebkitTextFillColor:
													"transparent",
												backgroundClip: "text",
												textShadow:
													"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
											}}
										>
											{customer.name}
										</div>
										{/* 绑定状态标签 */}
										<div
											className={cn(
												"px-2 py-0.5 rounded-full text-xs border shrink-0 group relative",
												BINDING_STATUS_CONFIG[
													customer.bindingStatus
												].bgColor,
												BINDING_STATUS_CONFIG[
													customer.bindingStatus
												].borderColor,
												BINDING_STATUS_CONFIG[
													customer.bindingStatus
												].color,
											)}
										>
											{
												BINDING_STATUS_CONFIG[
													customer.bindingStatus
												].label
											}
											{/* 悬浮提示 */}
											{(customer.bindingStatus ===
												ConsumerBindingStatus.AGENT ||
												customer.bindingStatus ===
													ConsumerBindingStatus.OTHER) && (
												<div className="absolute top-full mt-1 right-0 w-48 p-2 bg-[#1E2023] border border-red-500/30 rounded text-xs text-white opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-opacity z-[100] shadow-lg">
													{
														BINDING_STATUS_CONFIG[
															customer
																.bindingStatus
														].tooltip
													}
												</div>
											)}
										</div>
									</div>

									{/* 中部区域 - 用户信息 */}
									<div className="mt-1.5">
										<div className="text-xs text-[#D4B485]/40">
											{customer.phone}
										</div>
									</div>

									{/* 底部区域 - 套餐信息 */}
									<div className="mt-2 flex items-center justify-between">
										<div className="flex flex-col">
											<div className="text-xs text-[#D4B485]/60 font-medium">
												{customer.currentPlan?.name ||
													"未开通套餐"}
											</div>
											{customer.currentPlan?.expireAt && (
												<div className="text-xs text-[#D4B485]/40 mt-0.5">
													到期:{" "}
													{new Date(
														customer.currentPlan
															.expireAt,
													).toLocaleDateString()}
												</div>
											)}
										</div>
									</div>
								</div>
							</div>
						</motion.div>
					))
				)}
			</div>

			{/* 底部分页和按钮 */}
			<div className="flex items-center justify-between pt-4">
				<div className="flex-1" />
				<ConsumerPagination
					total={total}
					page={currentPage}
					pageSize={pageSize}
					onPageChange={handlePageChange}
					className="flex-1"
				/>
				<div className="flex-1 flex justify-end">
					<Button
						className={cn(
							"px-8",
							"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
							"text-white",
							"border-none",
							"shadow-[0_8px_16px_-4px_rgba(212,180,133,0.3)]",
							"hover:shadow-[0_12px_20px_-4px_rgba(212,180,133,0.4)]",
							"transition-all duration-200",
							"disabled:opacity-50 disabled:cursor-not-allowed",
						)}
						onClick={handleNext}
						disabled={!selectedCustomer}
					>
						下一步
					</Button>
				</div>
			</div>
		</div>
	);
}
