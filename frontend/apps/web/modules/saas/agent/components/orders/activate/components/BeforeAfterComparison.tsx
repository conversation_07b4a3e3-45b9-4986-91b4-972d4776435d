import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { cn } from "@ui/lib";
import { ArrowRightIcon } from "lucide-react";
import { ComparisonItem } from "./ComparisonItem";

interface Package {
	id: string;
	name: string;
	power: number;
	hours: number;
	price: number;
	features: string[];
	description: string;
	popular?: boolean;
}

interface BeforeAfterComparisonProps {
	customerName: string;
	selectedPackage: Package;
}

export function BeforeAfterComparison({
	customerName,
	selectedPackage,
}: BeforeAfterComparisonProps) {
	const comparisonItems = [
		{ label: "可用算力", value: selectedPackage.power, unit: "点" },
		{ label: "可用时长", value: selectedPackage.hours, unit: "小时" },
		{ label: "克隆形象数量", value: 5, unit: "个" },
		{ label: "克隆音色数量", value: 3, unit: "个" },
		{ label: "授权发布账号", value: 2, unit: "个" },
	];

	return (
		<div className="space-y-4">
			<h3
				className={cn("text-lg font-semibold", zywhFont.className)}
				style={{
					background:
						"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
					WebkitBackgroundClip: "text",
					WebkitTextFillColor: "transparent",
					backgroundClip: "text",
					textShadow:
						"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
				}}
			>
				{customerName} 的开通前后对比
			</h3>
			<div
				className={cn(
					"rounded-lg p-4",
					"bg-[#1E2023]/50 border-[#D4B485]/20",
					"border",
					"h-full",
					"relative overflow-hidden",
					"transition-all duration-300",
					// 第一层装饰 - 内部光效
					"before:absolute before:inset-0",
					"before:rounded-lg",
					"before:transition-all before:duration-500",
					"before:ease-out",
					"before:scale-[0.97]",
					"before:opacity-0",
					"before:bg-gradient-to-r",
					"before:from-transparent",
					"before:via-[#1E2532]",
					"before:to-transparent",
					"before:bg-clip-padding",
					"before:backdrop-filter",
					"before:backdrop-blur-[8px]",
					"before:border-transparent",
					"before:bg-opacity-50",
					"hover:before:scale-100 hover:before:opacity-100",
					"before:z-[-1]",
					// 第二层装饰 - 外部光晕
					"after:absolute after:inset-[-1px]",
					"after:rounded-lg",
					"after:transition-all after:duration-500",
					"after:ease-out",
					"after:scale-[1.02]",
					"after:bg-gradient-to-r",
					"after:from-transparent",
					"after:via-[#D4B485]/5",
					"after:to-transparent",
					"after:opacity-0",
					"hover:after:opacity-100",
					"after:z-[-2]",
					// 第三层装饰 - 内部阴影
					"shadow-[inset_0_0_0_1px_rgba(212,180,133,0.05)]",
					"hover:shadow-[inset_0_0_0_1px_rgba(212,180,133,0.1)]",
					"hover:bg-[#1E2023]/60",
				)}
			>
				<div className="flex items-center gap-4 relative z-[1]">
					<div className="flex-1 space-y-4">
						<div className="text-center text-sm text-[#D4B485]/60">
							开通前
						</div>
						<div className="space-y-3">
							{comparisonItems.map((item) => (
								<ComparisonItem
									key={item.label}
									label={item.label}
									value={0}
									unit={item.unit}
								/>
							))}
						</div>
					</div>

					<div className="flex h-20 w-16 items-center justify-center">
						<ArrowRightIcon className="h-8 w-8 text-[#D4B485]/40" />
					</div>

					<div className="flex-1 space-y-4">
						<div className="text-center text-sm text-[#D4B485]/60">
							开通后
						</div>
						<div className="space-y-3">
							{comparisonItems.map((item) => (
								<ComparisonItem
									key={item.label}
									label={item.label}
									value={item.value}
									unit={item.unit}
									isAfter
								/>
							))}
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
