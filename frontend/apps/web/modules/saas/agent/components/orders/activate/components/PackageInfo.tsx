import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { cn } from "@ui/lib";
import { CheckIcon, ZapIcon } from "lucide-react";
import { AnimatedNumber } from "../../../dashboard/components/AnimatedNumber";

interface Package {
	id: string;
	name: string;
	power: number;
	hours: number;
	price: number;
	features: string[];
	description: string;
	popular?: boolean;
}

interface PackageInfoProps {
	selectedPackage: Package;
}

export function PackageInfo({ selectedPackage }: PackageInfoProps) {
	return (
		<div className="space-y-4">
			<h3
				className={cn("text-lg font-semibold", zywhFont.className)}
				style={{
					background:
						"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
					WebkitBackgroundClip: "text",
					WebkitTextFillColor: "transparent",
					backgroundClip: "text",
					textShadow:
						"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
				}}
			>
				套餐信息
			</h3>
			<div
				className={cn(
					"rounded-lg p-6",
					"bg-[#1E2023]/50 border-[#D4B485]/20",
					"border",
					"h-full",
					"relative overflow-hidden",
					"transition-all duration-300",
					// 第一层装饰 - 内部光效
					"before:absolute before:inset-0",
					"before:rounded-lg",
					"before:transition-all before:duration-500",
					"before:ease-out",
					"before:scale-[0.97]",
					"before:opacity-0",
					"before:bg-gradient-to-r",
					"before:from-transparent",
					"before:via-[#1E2532]",
					"before:to-transparent",
					"before:bg-clip-padding",
					"before:backdrop-filter",
					"before:backdrop-blur-[8px]",
					"before:border-transparent",
					"before:bg-opacity-50",
					"hover:before:scale-100 hover:before:opacity-100",
					"before:z-[-1]",
					// 第二层装饰 - 外部光晕
					"after:absolute after:inset-[-1px]",
					"after:rounded-lg",
					"after:transition-all after:duration-500",
					"after:ease-out",
					"after:scale-[1.02]",
					"after:bg-gradient-to-r",
					"after:from-transparent",
					"after:via-[#D4B485]/5",
					"after:to-transparent",
					"after:opacity-0",
					"hover:after:opacity-100",
					"after:z-[-2]",
					// 第三层装饰 - 内部阴影
					"shadow-[inset_0_0_0_1px_rgba(212,180,133,0.05)]",
					"hover:shadow-[inset_0_0_0_1px_rgba(212,180,133,0.1)]",
					"hover:bg-[#1E2023]/60",
				)}
			>
				<div className="flex h-full flex-col relative z-[1]">
					<div className="space-y-6">
						<div className="flex items-start justify-between">
							<div>
								<div
									className={cn(
										"text-xl font-semibold",
										zywhFont.className,
									)}
									style={{
										background:
											"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
										WebkitBackgroundClip: "text",
										WebkitTextFillColor: "transparent",
										backgroundClip: "text",
										textShadow:
											"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
									}}
								>
									{selectedPackage.name}
								</div>
								<div className="mt-1 text-sm text-[#D4B485]/60">
									{selectedPackage.description}
								</div>
							</div>
							<div className="text-right">
								<div
									className={cn(
										"text-3xl font-bold",
										zywhFont.className,
									)}
									style={{
										background:
											"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
										WebkitBackgroundClip: "text",
										WebkitTextFillColor: "transparent",
										backgroundClip: "text",
										textShadow:
											"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
									}}
								>
									¥
									<AnimatedNumber
										value={selectedPackage.price}
										duration={2}
									/>
								</div>
								<div className="mt-1 text-sm text-[#D4B485]/40">
									/套餐
								</div>
							</div>
						</div>

						<div className="space-y-2">
							{selectedPackage.features.map((feature) => (
								<div
									key={feature}
									className="flex items-center gap-2 text-sm text-[#D4B485]/60"
								>
									<CheckIcon className="h-4 w-4 text-emerald-500" />
									<span>{feature}</span>
								</div>
							))}
						</div>
					</div>

					{/* 分隔线 */}
					<div className="my-6 border-t border-[#D4B485]/10" />

					{/* 算力消耗 */}
					<div className="space-y-3">
						<div className="flex items-center gap-2 text-sm text-[#D4B485]/60">
							<ZapIcon className="h-4 w-4" />
							<span>
								本次开通将消耗您账户
								<span className="mx-1 text-[#D4B485]">
									<AnimatedNumber
										value={selectedPackage.power}
										duration={2}
									/>
									点
								</span>
								算力
							</span>
							<span className="text-[#D4B485]/40">
								(
								<AnimatedNumber
									value={selectedPackage.hours}
									duration={2}
								/>
								小时)
							</span>
						</div>

						{/* 当前额度 - 这里用模拟数据，实际应该从 props 或 API 获取 */}
						<div className="flex items-center gap-2 text-sm">
							<div className="flex items-center gap-2 rounded-lg bg-[#1E2023]/30 px-3 py-2">
								<span className="text-[#D4B485]/60">
									当前可用额度：
								</span>
								<span className="font-medium text-[#D4B485]">
									<AnimatedNumber
										value={50000}
										duration={2}
									/>
									点
								</span>
							</div>
							<div className="text-red-400">
								（额度不足，请先充值）
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
