"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Card } from "@ui/components/card";
import { cn } from "@ui/lib";
import { FileTextIcon, PlayCircleIcon, VideoIcon } from "lucide-react";
import { POWER_FEATURES } from "./constants";

export function PurchaseInfo() {
	return (
		<Card
			className={cn(
				"p-6",
				"bg-gradient-to-br from-[#1A1C1E] via-[#1E2023] to-[#23252A]",
				"border border-[#D4B485]/20",
				"backdrop-blur-xl",
				"shadow-[0_8px_32px_-4px_rgba(0,0,0,0.3)]",
				"hover:border-[#D4B485]/30",
				"transition-all duration-300",
				"relative overflow-hidden",
			)}
		>
			{/* 背景装饰 */}
			<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
			<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />
			<div className="absolute inset-0 bg-[linear-gradient(to_bottom,rgba(255,255,255,0.03)_0%,transparent_100%)]" />

			<div className="relative">
				<h3
					className={cn(
						"flex items-center gap-2 font-semibold text-lg",
						zywhFont.className,
						"leading-none",
						"tracking-[0.05em]",
					)}
					style={{
						background:
							"linear-gradient(135deg, rgba(229,201,165,0.95) 0%, rgba(212,180,133,0.9) 50%, rgba(176,137,104,0.85) 100%)",
						WebkitBackgroundClip: "text",
						WebkitTextFillColor: "transparent",
						backgroundClip: "text",
						textShadow:
							"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
						filter: "contrast(1.1) brightness(1.05)",
					}}
				>
					<FileTextIcon className="h-5 w-5 text-[#D4B485]" />
					算力说明
				</h3>
				<div className="mt-6 space-y-6">
					{POWER_FEATURES.map((feature, index: number) => (
						<div
							key={`feature-${feature.title}`}
							className="group flex gap-4"
						>
							<div
								className={cn(
									"flex h-9 w-9 shrink-0 items-center justify-center rounded-full",
									"bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
									"ring-1 ring-[#D4B485]/20",
									"shadow-[0_4px_12px_-2px_rgba(212,180,133,0.12)]",
									"group-hover:from-[#D4B485]/15 group-hover:to-[#D4B485]/10",
									"group-hover:ring-[#D4B485]/30",
									"transition-all duration-200",
								)}
							>
								{index === 0 ? (
									<VideoIcon className="h-5 w-5 text-[#D4B485]" />
								) : index === 1 ? (
									<FileTextIcon className="h-5 w-5 text-[#D4B485]" />
								) : (
									<PlayCircleIcon className="h-5 w-5 text-[#D4B485]" />
								)}
							</div>
							<div>
								<div
									className={cn(
										"font-medium",
										zywhFont.className,
										"leading-none",
										"tracking-[0.05em]",
									)}
									style={{
										background:
											"linear-gradient(135deg, #E5C9A5 0%, #D4B485 50%, #B08968 100%)",
										WebkitBackgroundClip: "text",
										WebkitTextFillColor: "transparent",
										backgroundClip: "text",
										textShadow:
											"0 0 20px rgba(212,180,133,0.25)",
									}}
								>
									{feature.title}
								</div>
								<div className="mt-2 text-sm leading-relaxed text-white/60 group-hover:text-white/80 transition-colors duration-300">
									{feature.description}
								</div>
							</div>
						</div>
					))}
				</div>
			</div>
		</Card>
	);
}
