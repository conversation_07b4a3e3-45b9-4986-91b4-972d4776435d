import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";

// Matches Prisma's PlanType enum if possible, otherwise use string for flexibility
// For now, using string as PlanType from Prisma isn't directly imported here.
type PlanType = "AVATAR" | "VIDEO" | "TEXT" | "IMAGE" | "ALL";

interface PlanFeature {
	type: string;
	enabled: boolean;
	description?: string;
	code?: string; // Added feature code, optional for now
}

interface Plan {
	id: string;
	name: string;
	level: string; // Consider using Prisma's PlanLevel if available
	computingPower: number;
	validityDays: number;
	price: number; // Original price from plan.actualPrice
	discountPrice: number; // Agent's cost from calculateAgentPrice(plan.actualPrice) in API
	priority: number;
	description?: string;
	features: PlanFeature[];
	inventoryQuantity: number; // Added inventory quantity
	type: PlanType; // Added plan's type
	code: string; // Added plan's code
}

interface ApiSuccessResponse<T> {
	code: number;
	message?: string;
	data: T;
}

interface ApiErrorResponse {
	code: number;
	error: string;
	message: string;
}

type ApiResponse<T> = ApiSuccessResponse<T> | ApiErrorResponse;

interface PlansResponseData {
	total: number; // Total plans matching the type, from API
	items: Plan[];
}

export interface FetchPlansParams {
	type?: PlanType; // Type is now the primary filter
}

export function usePlans() {
	const [plans, setPlans] = useState<Plan[]>([]);
	const [total, setTotal] = useState(0); // Total plans of the current type
	const [loading, setLoading] = useState(false);

	const fetchPlans = useCallback(async (params: FetchPlansParams) => {
		try {
			setLoading(true);
			const { type = "ALL" } = params; // Default to "ALL" if no type is provided
			// logger.info("开始获取套餐列表 (activate hook)", { type });

			// API endpoint expects type in query
			const res = await apiClient.v1.agent.activate.plans.$get({
				query: { type },
			});
			const result = (await res.json()) as ApiResponse<PlansResponseData>;

			// logger.info("套餐列表响应 (activate hook):", result);

			if ("error" in result || result.code !== 200 || !result.data) {
				const errorMessage =
					"message" in result && result.message
						? result.message
						: "获取套餐列表失败";
				throw new Error(errorMessage);
			}

			setPlans(result.data.items);
			setTotal(result.data.total);

			return {
				items: result.data.items,
				total: result.data.total,
			};
		} catch (error) {
			// logger.error("获取套餐列表失败 (activate hook)", { error });
			toast.error("获取套餐列表失败", {
				description: error instanceof Error ? error.message : "请重试",
			});
			// Ensure state is reset on error to avoid displaying stale data
			setPlans([]);
			setTotal(0);
			return {
				items: [],
				total: 0,
			};
		} finally {
			setLoading(false);
		}
	}, []);

	return {
		plans,
		total,
		loading,
		fetchPlans,
	};
}
