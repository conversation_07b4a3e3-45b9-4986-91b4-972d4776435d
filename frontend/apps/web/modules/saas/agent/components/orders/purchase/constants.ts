import { calculatePowerUnitPrice, hoursToComputingPower } from "./lib/power";

// 每小时价格(元)
export const HOURLY_PRICE = 490;

// 每点算力的价格
export const POWER_UNIT_PRICE = calculatePowerUnitPrice(HOURLY_PRICE);

// 最小购买步长（算力）
export const POWER_STEP = hoursToComputingPower(1); // 1小时 = 6000算力

// 算力包配置
export const POWER_PACKAGES = [
	{
		hours: 2,
		power: hoursToComputingPower(2), // 2小时 = 12000算力
		price: 2 * HOURLY_PRICE, // 2 * 490
		label: "基础包",
		description: "2小时数字人视频",
		hourlyPrice: HOURLY_PRICE,
		details: "适合个人创作者快速入门的经济之选",
	},
	{
		hours: 10,
		power: hoursToComputingPower(10), // 10小时 = 60000算力
		price: 10 * HOURLY_PRICE, // 10 * 490
		label: "增强包",
		description: "10小时数字人视频",
		hourlyPrice: HOURLY_PRICE,
		details: "专业创作者的最佳之选，性价比超高",
	},
	{
		hours: 30,
		power: hoursToComputingPower(30), // 30小时 = 180000算力
		price: 30 * HOURLY_PRICE, // 30 * 490
		label: "企业包",
		description: "30小时数字人视频",
		hourlyPrice: HOURLY_PRICE,
		details: "为企业级团队打造的专业解决方案",
	},
	{
		hours: 80,
		power: hoursToComputingPower(80), // 80小时 = 480000算力
		price: 80 * HOURLY_PRICE, // 80 * 490
		label: "尊享包",
		description: "80小时数字人视频",
		hourlyPrice: HOURLY_PRICE,
		details: "企业数字人创作的旗舰级解决方案",
	},
] as const;

// 算力功能说明
export const POWER_FEATURES = [
	{
		title: "算力与时长换算",
		description: "1 小时数字人视频 = 6000 算力",
	},
	{
		title: "算力多用途",
		description: "算力可用于兑换数字人、图文裂变等多种产品套餐",
	},
	{
		title: "灵活囤货兑换",
		description: "支持提前囤货，需要时随时兑换开通任意数字人套餐",
	},
] as const;

// 购买建议
export const PURCHASE_TIPS = [
	"建议根据月使用量选择合适的时长",
	"支持购买多个套餐组合使用",
	"所有套餐统一单价490元/小时",
] as const;

// 安全保障
export const SECURITY_FEATURES = [
	{
		title: "正品算力保障",
		description: "官方直充，安全可靠",
	},
	{
		title: "即买即用",
		description: "支付成功后立即到账",
	},
	{
		title: "7×24小时服务",
		description: "专业技术支持",
	},
] as const;
