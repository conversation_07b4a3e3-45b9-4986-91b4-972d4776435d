// import { logger } from "@repo/logs";
import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { apiClient } from "@shared/lib/api-client";
import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";
import { AnimatePresence, motion } from "framer-motion";
import {
	AlertCircleIcon,
	CheckCircleIcon,
	CopyIcon,
	Loader2Icon,
	SmartphoneIcon,
	XIcon,
} from "lucide-react";
import { QRCodeSVG as QRCode } from "qrcode.react";
import { useCallback, useEffect, useState } from "react";
import { toast } from "sonner";
import { v4 as uuidv4 } from "uuid";
import { computingPowerToHours, formatAmount } from "./lib/power";
import type { SelectedPlanItem } from "./PurchaseView";

// 定义响应类型
interface OrderResponse {
	code: number;
	message?: string;
	data: {
		id: string;
		orderNo: string;
		amount: number;
		quantity: number;
		qrcodeUrl: string;
	};
}

interface OrderStatusResponse {
	code: number;
	message?: string;
	data?: {
		id: string;
		orderNo: string;
		quantity: number;
		amount: number;
		status:
			| "PENDING"
			| "PAID"
			| "COMPLETED"
			| "FAILED"
			| "CANCELLED"
			| "REFUNDED";
		paymentStatus: "PENDING" | "FAILED" | "REFUNDED" | "SUCCESS";
		paidAt?: string;
		completedAt?: string;
	};
}

interface PaymentModalProps {
	show: boolean;
	selectedPlanItems: SelectedPlanItem[];
	onClose: () => void;
	onAmountChange: (amount: string) => void;
	onPaymentSuccess?: () => void;
}

// 微信图标组件
function WechatIcon({ className = "w-5 h-5" }: { className?: string }) {
	return (
		<svg className={className} viewBox="0 0 24 24" fill="currentColor">
			<title>微信图标</title>
			<path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.295.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098 10.16 10.16 0 0 0 2.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 3.882-1.98 5.853-1.838-.576-3.583-4.196-6.348-8.596-6.348zM5.785 5.991c.642 0 1.162.529 1.162 1.18a1.17 1.17 0 0 1-1.162 1.178A1.17 1.17 0 0 1 4.623 7.17c0-.651.52-1.18 1.162-1.18zm5.813 0c.642 0 1.162.529 1.162 1.18a1.17 1.17 0 0 1-1.162 1.178 1.17 1.17 0 0 1-1.162-1.178c0-.651.52-1.18 1.162-1.18zm5.34 2.867c-1.797-.052-3.746.512-5.28 1.786-1.72 1.428-2.687 3.72-1.78 6.22.942 2.453 3.666 4.229 6.884 4.229.826 0 1.622-.12 2.361-.336a.722.722 0 0 1 .598.082l1.584.926a.272.272 0 0 0 .14.047c.134 0 .24-.111.24-.247 0-.06-.023-.12-.038-.177l-.327-1.233a.49.49 0 0 1 .176-.554C23.36 18.654 24.4 16.95 24.4 15c0-3.37-3.298-6.07-7.462-6.142zm-2.825 3.926c.535 0 .969.44.969.982a.976.976 0 0 1-.969.983.976.976 0 0 1-.969-.983c0-.542.434-.982.97-.982zm4.844 0c.535 0 .969.44.969.982a.976.976 0 0 1-.969.983.976.976 0 0 1-.969-.983c0-.542.434-.982.969-.982z" />
		</svg>
	);
}

// 二维码加载骨架屏
function QRCodeSkeleton() {
	return (
		<motion.div
			initial={{ opacity: 0 }}
			animate={{ opacity: 1 }}
			exit={{ opacity: 0 }}
			className={cn(
				"relative w-[240px] h-[240px] overflow-hidden rounded-2xl",
				"bg-gradient-to-br from-[#1A1C1E]/80 via-[#1E2023]/90 to-[#23252A]",
				"ring-1 ring-[#D4B485]/30",
				"shadow-[0_8px_32px_-4px_rgba(212,180,133,0.2)]",
				"backdrop-blur-xl",
				"group",
			)}
		>
			{/* 发光效果 */}
			<div className="absolute inset-0 bg-gradient-to-br from-[#D4B485]/5 via-transparent to-transparent" />
			<div className="absolute inset-0 bg-gradient-to-tl from-[#1AAD19]/5 via-transparent to-transparent" />

			{/* 动画背景 */}
			<motion.div
				className="absolute inset-0 bg-gradient-to-r from-transparent via-[#D4B485]/10 to-transparent"
				animate={{
					x: ["-200%", "200%"],
				}}
				transition={{
					repeat: Number.POSITIVE_INFINITY,
					duration: 2,
					ease: "linear",
				}}
			/>

			<div className="absolute inset-0 flex flex-col items-center justify-center gap-6">
				<motion.div
					animate={{
						scale: [1, 1.1, 1],
						opacity: [0.6, 1, 0.6],
					}}
					transition={{
						repeat: Number.POSITIVE_INFINITY,
						duration: 2.5,
						ease: "easeInOut",
					}}
					className="relative"
				>
					<div className="absolute inset-0 blur-2xl bg-[#1AAD19]/20 rounded-full" />
					<WechatIcon className="h-16 w-16 text-[#1AAD19] relative z-10" />
				</motion.div>
				<motion.div
					className="flex flex-col items-center gap-3"
					animate={{
						opacity: [0.6, 1, 0.6],
					}}
					transition={{
						repeat: Number.POSITIVE_INFINITY,
						duration: 2,
						ease: "easeInOut",
					}}
				>
					<div className="flex items-center gap-2 text-[#D4B485]/80">
						<Loader2Icon className="h-4 w-4 animate-spin" />
						<span className="text-sm font-medium">
							获取支付二维码...
						</span>
					</div>
					<div className="text-xs text-[#D4B485]/40">请稍候片刻</div>
				</motion.div>
			</div>
		</motion.div>
	);
}

// 二维码展示组件
function QRCodeDisplay({ value }: { value: string }) {
	return (
		<motion.div
			initial={{ opacity: 0, scale: 0.95 }}
			animate={{ opacity: 1, scale: 1 }}
			exit={{ opacity: 0, scale: 0.95 }}
			transition={{ duration: 0.4, ease: "easeOut" }}
			className={cn(
				"relative w-[240px] overflow-hidden rounded-2xl p-6",
				"bg-gradient-to-br from-white/95 to-white/90",
				"ring-1 ring-[#D4B485]/30",
				"shadow-[0_8px_32px_-4px_rgba(212,180,133,0.2)]",
				"backdrop-blur-xl",
				"group",
			)}
		>
			{/* 发光效果 */}
			<div className="absolute inset-0 bg-gradient-to-br from-[#D4B485]/10 via-transparent to-transparent" />
			<div className="absolute inset-0 bg-gradient-to-tl from-[#1AAD19]/5 via-transparent to-transparent" />

			<motion.div
				initial={{ opacity: 0, scale: 0.95 }}
				animate={{ opacity: 1, scale: 1 }}
				transition={{ delay: 0.2, duration: 0.4 }}
				className="relative z-10"
			>
				<QRCode
					value={value}
					size={200}
					level="M"
					className="w-full h-full"
				/>
			</motion.div>
		</motion.div>
	);
}

const MAX_INTERNAL_RETRIES = 2;
const RETRY_DELAY_MS = 1500; // 每次重试的固定延迟，也可以设计成指数退避

export function PaymentModal({
	show,
	selectedPlanItems,
	onClose,
	onAmountChange,
	onPaymentSuccess,
}: PaymentModalProps) {
	// 状态管理
	const [orderId, setOrderId] = useState<string>("");
	const [_idempotencyKey, setIdempotencyKey] = useState<string | null>(null);
	const [isLoadingOrder, setIsLoadingOrder] = useState<boolean>(false);
	const [codeUrl, setCodeUrl] = useState<string>("");
	const [amount, setAmount] = useState<string>("0");
	const [status, setStatus] = useState<"pending" | "success" | "failed">(
		"pending",
	);
	const [countdown, setCountdown] = useState(300);
	const [isExpired, setIsExpired] = useState(false);
	const [_showSuccess, setShowSuccess] = useState(false);

	// Calculate total quantity and power from selectedPlanItems for display
	const totalQuantityForDisplay = selectedPlanItems.reduce(
		(sum, item) => sum + item.quantity,
		0,
	);
	const totalPowerForDisplay = selectedPlanItems.reduce(
		(sum, item) => sum + item.plan.computingPower * item.quantity,
		0,
	);

	// 创建订单
	const createOrder = useCallback(
		async (currentIdempotencyKey: string | null) => {
			if (!currentIdempotencyKey) {
				// logger.error("创建订单失败: 幂等性键为空");
				toast.error("创建订单操作无效", {
					description: "缺少关键请求参数，请重试或联系支持。",
				});
				setStatus("failed");
				return;
			}
			setIsLoadingOrder(true);
			setCodeUrl("");

			let attempts = 0;
			let lastError: Error | null = null;

			while (attempts <= MAX_INTERNAL_RETRIES) {
				try {
					// logger.info(`开始创建订单 (尝试 ${attempts + 1}/${MAX_INTERNAL_RETRIES + 1})`, {
					// 	selectedPlanItems: selectedPlanItems.map(item => ({ planId: item.plan.id, name: item.plan.name, quantity: item.quantity })),
					// 	idempotencyKey: currentIdempotencyKey,
					// });

					const plansForApi = selectedPlanItems.map((item) => ({
						planId: item.plan.id,
						quantity: item.quantity,
					}));

					if (plansForApi.length === 0) {
						toast.error("请至少选择一个套餐");
						setStatus("failed");
						setIsLoadingOrder(false);
						return; // 直接返回，不进行重试，因为这是用户输入问题
					}

					// logger.info("创建订单请求:", {
					// 	plans: plansForApi,
					// 	idempotencyKey: currentIdempotencyKey,
					// });

					const response =
						await apiClient.v1.agent.orders.create.$post(
							{ plans: plansForApi },
							{
								headers: {
									"Idempotency-Key": currentIdempotencyKey,
								},
							},
						);

					const result = (await response.json()) as OrderResponse;
					// logger.info("订单创建成功", { result });

					if (response.ok && result.data?.qrcodeUrl) {
						const { id: newOrderId, amount: orderAmount } =
							result.data;

						setOrderId(newOrderId);
						const formattedAmount = (orderAmount / 100).toFixed(2);
						setAmount(formattedAmount);
						onAmountChange(formattedAmount);
						setStatus("pending");
						setShowSuccess(false);
						setCountdown(300);
						setIsExpired(false);

						// logger.info("订单创建成功或已存在且获取成功", {
						// 	orderId: newOrderId,
						// 	amount: formattedAmount,
						// });

						if (
							response.status === 200 &&
							result.message ===
								"Idempotent request processed successfully, returning cached response"
						) {
							toast.success("订单已存在", {
								description: "已为您加载之前的支付二维码。",
							});
						} else if (
							response.status === 201 ||
							(response.status === 200 &&
								!result.message?.includes("cached response"))
						) {
							toast.success("订单创建成功", {
								description: "请扫码支付",
							});
						}
						setIsLoadingOrder(false);
						return; // 成功，退出循环和函数
					}

					// 所有内部重试都失败后执行
					setStatus("failed");
					toast.error("创建订单失败", {
						description:
							lastError?.message ||
							"多次尝试后仍然无法获取支付信息，请稍后重试或联系支持。",
					});
					setIsLoadingOrder(false);
					return;
				} catch (error) {
					lastError =
						error instanceof Error
							? error
							: new Error(String(error));
					// logger.error(`创建订单尝试 ${attempts + 1} 失败`, { error: lastError, idempotencyKey: currentIdempotencyKey });

					if (
						error instanceof Response &&
						error.status >= 400 &&
						error.status < 500 &&
						error.status !== 409
					) {
						setStatus("failed");
						toast.error("创建订单请求无效", {
							description: `错误码: ${error.status}. ${lastError.message}`,
						});
						setIsLoadingOrder(false);
						return; // 不再重试
					}

					if (attempts < MAX_INTERNAL_RETRIES) {
						toast.info(
							`获取支付信息遇到问题，正在自动重试... (${attempts + 1}/${MAX_INTERNAL_RETRIES + 1})`,
						);
						await new Promise((resolve) =>
							setTimeout(
								resolve,
								RETRY_DELAY_MS * (attempts + 1),
							),
						); // 简单递增延迟
					}
					attempts++;
				}
			}

			// 所有内部重试都失败后执行
			setStatus("failed");
			toast.error("创建订单失败", {
				description:
					lastError?.message ||
					"多次尝试后仍然无法获取支付信息，请稍后重试或联系支持。",
			});
			setIsLoadingOrder(false);
		},
		[selectedPlanItems, onAmountChange],
	);

	// 查询订单状态
	const queryOrderStatus = useCallback(async () => {
		if (!orderId) {
			return;
		}

		try {
			const res = await apiClient.v1.agent.orders.detail[":id"].$get({
				param: { id: orderId },
			});
			const data: OrderStatusResponse = await res.json();

			// logger.info("订单状态查询响应:", data);

			if (!data || data.code !== 200 || !data.data) {
				return false;
			}

			const { paymentStatus } = data.data;

			if (paymentStatus === "SUCCESS") {
				setStatus("success");
				setShowSuccess(true);
				return true;
			}

			return false;
		} catch (_error) {
			// logger.error("查询订单失败", { error });
			return false;
		}
	}, [orderId]);

	// 处理重试
	const handleRetry = useCallback(async () => {
		toast.info("正在重新获取支付二维码...", { description: "请稍候片刻" });
		const newKey = uuidv4();
		setIdempotencyKey(newKey);

		setOrderId("");
		setAmount("0");
		setStatus("pending");
		setIsExpired(false);
		setCountdown(300);
		setShowSuccess(false);

		await createOrder(newKey);
	}, [createOrder]);

	// 初始化订单
	useEffect(() => {
		if (show) {
			const newKey = uuidv4();
			setIdempotencyKey(newKey);

			setOrderId("");
			setAmount("0");
			setStatus("pending");
			setIsExpired(false);
			setCountdown(300);
			setShowSuccess(false);

			createOrder(newKey);
		} else {
			setIdempotencyKey(null);
		}
	}, [show, createOrder]);

	// 轮询订单状态
	useEffect(() => {
		if (!show || status !== "pending" || !orderId || isLoadingOrder) {
			return;
		}

		const timer = setInterval(async () => {
			const isDone = await queryOrderStatus();
			if (isDone) {
				clearInterval(timer);
			}
		}, 3000);

		return () => {
			clearInterval(timer);
		};
	}, [show, status, orderId, queryOrderStatus, isLoadingOrder]);

	// 处理倒计时
	useEffect(() => {
		if (!show || status !== "pending" || isLoadingOrder) {
			return;
		}

		const timer = setInterval(() => {
			setCountdown((prev) => {
				if (prev <= 1) {
					clearInterval(timer);
					setIsExpired(true);
					return 0;
				}
				return prev - 1;
			});
		}, 1000);

		return () => {
			clearInterval(timer);
		};
	}, [show, status, isLoadingOrder]);

	// 复制订单号
	const copyOrderId = useCallback(() => {
		navigator.clipboard.writeText(orderId);
		toast("订单号已复制", {
			description: orderId,
		});
	}, [orderId]);

	// 格式化倒计时
	const formatTime = (seconds: number) => {
		const mins = Math.floor(seconds / 60);
		const secs = seconds % 60;
		return `${mins}:${secs.toString().padStart(2, "0")}`;
	};

	// 处理关闭
	const handleClose = useCallback(() => {
		setOrderId("");
		setCodeUrl("");
		setAmount("0");
		setStatus("pending");
		setCountdown(300);
		setIsExpired(false);
		setShowSuccess(false);
		setIdempotencyKey(null);
		setIsLoadingOrder(false);

		if (status === "success") {
			toast.success("支付完成", {
				description: "感谢您的购买！",
			});
		}

		onClose();
	}, [status, onClose]);

	// 处理支付成功后的完成操作
	const handleComplete = useCallback(() => {
		toast.success("支付完成", {
			description: "感谢您的购买！",
		});

		setOrderId("");
		setCodeUrl("");
		setAmount("0");
		setStatus("pending");
		setCountdown(300);
		setIsExpired(false);
		setShowSuccess(false);
		setIdempotencyKey(null);
		setIsLoadingOrder(false);
		onPaymentSuccess?.();
		onClose();
	}, [onClose, onPaymentSuccess]);

	return (
		<AnimatePresence>
			{show && (
				<motion.div
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					exit={{ opacity: 0 }}
					className="fixed inset-0 z-50 flex items-center justify-center bg-[#1E2023]/80 p-4 backdrop-blur-sm"
				>
					<motion.div
						initial={{ opacity: 0, scale: 0.95 }}
						animate={{ opacity: 1, scale: 1 }}
						exit={{ opacity: 0, scale: 0.95 }}
						className={cn(
							"relative w-full max-w-lg overflow-hidden rounded-lg",
							"bg-gradient-to-br from-[#1A1C1E] via-[#1E2023] to-[#23252A]",
							"border border-[#D4B485]/20",
							"p-6",
							"shadow-[0_8px_32px_-4px_rgba(212,180,133,0.15)]",
							"group",
							"hover:border-[#D4B485]/30",
							"transition-all duration-500",
							"before:absolute before:inset-0",
							"before:rounded-lg",
							"before:bg-gradient-to-r",
							"before:from-[#E5C9A5]/5 before:via-[#D4B485]/5 before:to-[#B39065]/5",
							"before:opacity-0",
							"hover:before:opacity-100",
							"before:transition-opacity before:duration-500",
						)}
					>
						{/* 关闭按钮 */}
						<button
							type="button"
							onClick={handleClose}
							className={cn(
								"absolute right-6 top-6 z-10",
								"flex h-8 w-8 items-center justify-center rounded-full",
								"bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
								"ring-1 ring-[#D4B485]/20",
								"shadow-[0_4px_12px_-2px_rgba(212,180,133,0.12)]",
								"group-hover:from-[#D4B485]/15 group-hover:to-[#D4B485]/10",
								"group-hover:ring-[#D4B485]/30",
								"transition-all duration-200",
								"hover:scale-110",
							)}
						>
							<XIcon className="h-4 w-4 text-[#D4B485]" />
						</button>

						{/* 标题 */}
						<div className="mb-8 text-center select-none">
							<h2
								className={cn(
									"text-2xl md:text-3xl font-semibold",
									zywhFont.className,
									"leading-none",
									"tracking-[0.08em]",
									"relative",
									"after:absolute after:inset-0",
									"after:bg-[radial-gradient(circle_at_50%_50%,rgba(212,180,133,0.15),transparent_70%)]",
									"after:blur-xl after:-z-10",
									"select-none",
								)}
								style={{
									background:
										"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
									WebkitBackgroundClip: "text",
									WebkitTextFillColor: "transparent",
									backgroundClip: "text",
									textShadow:
										"0 2px 4px rgba(0,0,0,0.15), 0 0 25px rgba(212,180,133,0.25)",
									filter: "contrast(1.15) brightness(1.1)",
									userSelect: "none",
								}}
							>
								微信扫码支付
							</h2>
							<p className="mt-3 text-base text-[#D4B485]/60 select-none">
								请使用微信扫描二维码完成支付
							</p>
						</div>

						{/* 内容区域 */}
						<div className="space-y-6">
							{/* 订单信息 */}
							<div
								className={cn(
									"space-y-4 rounded-lg p-4",
									"bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
									"ring-1 ring-[#D4B485]/20",
									"shadow-[0_4px_12px_-2px_rgba(212,180,133,0.12)]",
									"group-hover:from-[#D4B485]/15 group-hover:to-[#D4B485]/10",
									"group-hover:ring-[#D4B485]/30",
									"transition-all duration-200",
								)}
							>
								<div className="flex items-center justify-between">
									<span className="text-[#D4B485]/60">
										订单金额
									</span>
									<span className="font-medium text-[#D4B485]">
										¥{formatAmount(Number(amount))}
									</span>
								</div>
								<div className="flex items-center justify-between">
									<span className="text-[#D4B485]/60">
										购买额度
									</span>
									<span className="font-medium text-[#D4B485]">
										{totalQuantityForDisplay} 套餐 (总计{" "}
										{totalPowerForDisplay} 算力,{" "}
										{computingPowerToHours(
											totalPowerForDisplay,
										)}
										小时)
									</span>
								</div>
								<div className="flex items-center justify-between">
									<span className="text-[#D4B485]/60">
										订单号
									</span>
									<div className="flex items-center gap-2">
										<span className="font-medium text-[#D4B485] select-all">
											{orderId}
										</span>
										<button
											type="button"
											onClick={copyOrderId}
											className={cn(
												"flex h-6 w-6 items-center justify-center rounded-full",
												"bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
												"ring-1 ring-[#D4B485]/20",
												"text-[#D4B485]/60 hover:text-[#D4B485]",
												"transition-all duration-200",
												"hover:scale-110",
												"active:scale-95",
												"z-50",
											)}
										>
											<CopyIcon className="h-3 w-3" />
										</button>
									</div>
								</div>
							</div>

							{/* 二维码区域 */}
							{status === "pending" && !isExpired && (
								<div className="flex flex-col items-center justify-center">
									<div className="relative">
										{isLoadingOrder || !codeUrl ? (
											<QRCodeSkeleton />
										) : (
											<>
												<QRCodeDisplay
													value={codeUrl}
												/>
												<div className="mt-6 flex flex-col items-center gap-3">
													<div className="flex items-center gap-2.5 text-[#1AAD19] bg-[#1AAD19]/5 px-4 py-2 rounded-full">
														<SmartphoneIcon className="h-4 w-4" />
														<span className="text-sm font-medium">
															打开微信，扫描上方二维码
														</span>
													</div>
													<div className="text-sm text-[#D4B485]/60 flex items-center gap-1.5">
														<span>
															二维码有效期：
														</span>
														<span className="font-medium text-[#D4B485] bg-[#D4B485]/5 px-2 py-0.5 rounded">
															{formatTime(
																countdown,
															)}
														</span>
													</div>
												</div>
											</>
										)}
									</div>
								</div>
							)}

							{/* 过期提示 */}
							{status === "pending" && isExpired && (
								<motion.div
									initial={{ opacity: 0, y: 10 }}
									animate={{ opacity: 1, y: 0 }}
									exit={{ opacity: 0, y: -10 }}
									className="flex flex-col items-center justify-center py-8"
								>
									<div className="relative">
										<div className="absolute inset-0 blur-2xl bg-[#D4B485]/10 rounded-full" />
										<AlertCircleIcon className="h-16 w-16 text-[#D4B485]/60 relative z-10" />
									</div>
									<p className="mt-6 text-center text-[#D4B485]/60 font-medium">
										二维码已过期，请点击重新获取
									</p>
									<Button
										onClick={handleRetry}
										disabled={isLoadingOrder}
										className={cn(
											"mt-6 h-11 px-8",
											"bg-gradient-to-r from-[#D4B485]/15 to-[#D4B485]/10",
											"text-[#D4B485] hover:text-[#D4B485]",
											"border border-[#D4B485]/20 hover:border-[#D4B485]/30",
											"shadow-[0_8px_16px_-4px_rgba(212,180,133,0.15)]",
											"transition-all duration-300",
											"hover:shadow-[0_12px_20px_-4px_rgba(212,180,133,0.2)]",
											"hover:scale-105",
											"disabled:opacity-50 disabled:cursor-not-allowed",
										)}
									>
										{isLoadingOrder ? (
											<Loader2Icon className="mr-2 h-4 w-4 animate-spin" />
										) : null}
										重新获取
									</Button>
								</motion.div>
							)}

							{/* 支付成功 */}
							{status === "success" && (
								<motion.div
									initial={{ opacity: 0, scale: 0.9 }}
									animate={{ opacity: 1, scale: 1 }}
									exit={{ opacity: 0, scale: 0.9 }}
									transition={{
										duration: 0.5,
										ease: "easeOut",
									}}
									className="flex flex-col items-center justify-center py-8 select-none"
								>
									<motion.div
										initial={{ scale: 0 }}
										animate={{ scale: 1 }}
										transition={{
											type: "spring",
											stiffness: 200,
											damping: 15,
										}}
										className="relative"
									>
										<div className="absolute inset-0 blur-2xl bg-[#1AAD19]/20 rounded-full" />
										<CheckCircleIcon className="h-16 w-16 text-[#1AAD19] relative z-10" />
									</motion.div>
									<motion.p
										initial={{ opacity: 0, y: 10 }}
										animate={{ opacity: 1, y: 0 }}
										transition={{ delay: 0.2 }}
										className="mt-6 text-center font-medium bg-gradient-to-r from-[#1AAD19] to-[#1AAD19]/80 bg-clip-text text-transparent text-xl"
									>
										支付成功
									</motion.p>
									<motion.div
										initial={{ opacity: 0, y: 10 }}
										animate={{ opacity: 1, y: 0 }}
										transition={{ delay: 0.3 }}
										className="relative z-50"
									>
										<Button
											type="button"
											onClick={handleComplete}
											className={cn(
												"mt-6 h-11 px-8",
												"bg-gradient-to-r from-[#1AAD19] to-[#1AAD19]/90",
												"text-white hover:text-white/90",
												"border-none",
												"shadow-[0_8px_16px_-4px_rgba(26,173,25,0.3)]",
												"transition-all duration-300",
												"hover:shadow-[0_12px_20px_-4px_rgba(26,173,25,0.4)]",
												"hover:scale-105",
												"active:scale-95",
												"cursor-pointer",
												"select-none",
												"z-50",
											)}
										>
											完成
										</Button>
									</motion.div>
								</motion.div>
							)}

							{/* 支付失败 */}
							{status === "failed" && (
								<motion.div
									initial={{ opacity: 0, y: 10 }}
									animate={{ opacity: 1, y: 0 }}
									exit={{ opacity: 0, y: -10 }}
									className="flex flex-col items-center justify-center py-8"
								>
									<div className="relative">
										<div className="absolute inset-0 blur-2xl bg-rose-500/20 rounded-full" />
										<AlertCircleIcon className="h-16 w-16 text-rose-500 relative z-10" />
									</div>
									<p className="mt-6 text-center text-[#D4B485]/60 font-medium">
										支付失败，请重试
									</p>
									<Button
										onClick={handleRetry}
										disabled={isLoadingOrder}
										className={cn(
											"mt-6 h-11 px-8",
											"bg-gradient-to-r from-rose-500/15 to-rose-500/10",
											"text-rose-500 hover:text-rose-500",
											"border border-rose-500/20 hover:border-rose-500/30",
											"shadow-[0_8px_16px_-4px_rgba(244,63,94,0.15)]",
											"transition-all duration-300",
											"hover:shadow-[0_12px_20px_-4px_rgba(244,63,94,0.2)]",
											"hover:scale-105",
											"disabled:opacity-50 disabled:cursor-not-allowed",
											"active:scale-95",
										)}
									>
										{isLoadingOrder ? (
											<Loader2Icon className="mr-2 h-4 w-4 animate-spin" />
										) : null}
										重新支付
									</Button>
								</motion.div>
							)}
						</div>
					</motion.div>
				</motion.div>
			)}
		</AnimatePresence>
	);
}
