"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";
import { CheckIcon, ZapIcon } from "lucide-react";
import { useState } from "react";
import type { Consumer } from "../../../../types/consumer";
import { useActivate } from "../hooks";

interface Package {
	id: string;
	name: string;
	level: string;
	computingPower: number;
	validityDays: number;
	price: number;
	discountPrice: number;
	priority: number;
	description?: string;
	features: Array<{
		type: string;
		enabled: boolean;
		description?: string;
	}>;
	inventoryQuantity: number;
	type: "AVATAR" | "VIDEO" | "TEXT" | "IMAGE" | "ALL";
	code: string;
}

interface OrderInfo {
	orderId: string;
	customer: Consumer;
	package: Package;
	createdAt: string;
}

interface FormData {
	customer: Consumer | null;
	package: Package | null;
	orderInfo: OrderInfo | null;
}

interface ConfirmOrderProps {
	onPrev: () => void;
	formData: FormData;
	onActivationSuccess: (activationInfo: {
		orderId: string;
		customer: Consumer;
		package: Package;
		createdAt: string;
	}) => void;
}

export function ConfirmOrder({
	onPrev,
	formData,
	onActivationSuccess,
}: ConfirmOrderProps) {
	const { activatePlan } = useActivate();
	const [isSubmitting, setIsSubmitting] = useState(false);

	// 处理提交
	const handleSubmit = async () => {
		if (!formData.customer || !formData.package) {
			return;
		}

		setIsSubmitting(true);
		try {
			const result = await activatePlan({
				consumerId: formData.customer.id,
				planId: formData.package.id,
			});

			if (result) {
				onActivationSuccess({
					orderId: result.id,
					customer: formData.customer,
					package: formData.package,
					createdAt: result.startAt,
				});
			}
		} finally {
			setIsSubmitting(false);
		}
	};

	return (
		<div className="space-y-8">
			{/* 大标题 */}
			<div className="space-y-2 text-center">
				<h3
					className={cn("text-2xl font-semibold", zywhFont.className)}
					style={{
						background:
							"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
						WebkitBackgroundClip: "text",
						WebkitTextFillColor: "transparent",
						backgroundClip: "text",
						textShadow:
							"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
					}}
				>
					确认开通信息
				</h3>
				<p className="text-[#D4B485]/60">
					请确认以下开通信息，确认无误后点击提交
				</p>
			</div>

			{/* 客户信息 */}
			<div className="space-y-4">
				<div className="text-base font-medium text-[#D4B485]">
					客户信息
				</div>
				<div
					className={cn(
						"rounded-lg p-4",
						"bg-[#1E2023]/50",
						"border border-[#D4B485]/20",
						"shadow-[inset_0_0_0_1px_rgba(212,180,133,0.05)]",
					)}
				>
					<div className="space-y-2">
						<div className="flex items-center justify-between">
							<div
								className={cn(
									"text-lg font-medium",
									zywhFont.className,
								)}
								style={{
									background:
										"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
									WebkitBackgroundClip: "text",
									WebkitTextFillColor: "transparent",
									backgroundClip: "text",
									textShadow:
										"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
								}}
							>
								{formData.customer?.name}
							</div>
							<div className="text-sm text-[#D4B485]/60">
								{formData.customer?.currentPlan?.name ||
									"未开通"}
							</div>
						</div>
						<div className="space-y-1">
							<div className="text-sm text-[#D4B485]/60">
								{formData.customer?.currentPlan?.expireAt
									? `到期时间：${new Date(
											formData.customer.currentPlan
												.expireAt,
										).toLocaleDateString()}`
									: "暂无套餐"}
							</div>
							<div className="text-sm text-[#D4B485]/40">
								{formData.customer?.phone}
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* 套餐信息 */}
			<div className="space-y-4">
				<div className="text-base font-medium text-[#D4B485]">
					套餐信息
				</div>
				<div
					className={cn(
						"rounded-lg p-4",
						"bg-[#1E2023]/50",
						"border border-[#D4B485]/20",
						"shadow-[inset_0_0_0_1px_rgba(212,180,133,0.05)]",
					)}
				>
					<div className="space-y-4">
						<div className="space-y-1">
							<div
								className={cn(
									"text-lg font-medium",
									zywhFont.className,
								)}
								style={{
									background:
										"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
									WebkitBackgroundClip: "text",
									WebkitTextFillColor: "transparent",
									backgroundClip: "text",
									textShadow:
										"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
								}}
							>
								{formData.package?.name}
							</div>
							<div className="text-sm text-[#D4B485]/60">
								{formData.package?.description}
							</div>
						</div>

						<div className="space-y-2">
							<div className="flex items-baseline gap-1">
								<div
									className={cn(
										"text-2xl font-bold",
										zywhFont.className,
									)}
									style={{
										background:
											"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
										WebkitBackgroundClip: "text",
										WebkitTextFillColor: "transparent",
										backgroundClip: "text",
										textShadow:
											"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
									}}
								>
									¥
									{formData.package?.price
										? (
												formData.package.price / 100
											).toLocaleString()
										: 0}
								</div>
								<div className="text-sm text-[#D4B485]/40">
									/套餐
								</div>
							</div>
							<div className="flex items-center gap-2 text-sm text-[#D4B485]/60">
								<ZapIcon className="h-4 w-4" />
								<span>
									{formData.package?.computingPower.toLocaleString()}
									点算力
								</span>
								<span className="text-[#D4B485]/40">
									({formData.package?.validityDays}天)
								</span>
							</div>
						</div>

						<div className="space-y-2">
							{formData.package?.features.map((feature) => (
								<div
									key={`${formData.package?.id}-${feature.type}`}
									className="flex items-center gap-2 text-sm text-[#D4B485]/60"
								>
									<CheckIcon className="h-4 w-4 text-emerald-500" />
									<span>{feature.description}</span>
								</div>
							))}
						</div>
					</div>
				</div>
			</div>

			{/* 底部按钮 */}
			<div className="flex justify-between pt-4">
				<Button
					variant="outline"
					className={cn(
						"px-8",
						"bg-[#1E2023]/50 border-[#D4B485]/20",
						"text-[#D4B485] hover:text-[#E5C9A5]",
						"hover:bg-[#D4B485]/10",
						"transition duration-200",
					)}
					onClick={onPrev}
					disabled={isSubmitting}
				>
					上一步
				</Button>
				<Button
					className={cn(
						"px-8",
						"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
						"text-white",
						"border-none",
						"shadow-[0_8px_16px_-4px_rgba(212,180,133,0.3)]",
						"hover:shadow-[0_12px_20px_-4px_rgba(212,180,133,0.4)]",
						"transition-all duration-200",
						"disabled:opacity-50 disabled:cursor-not-allowed",
					)}
					onClick={handleSubmit}
					disabled={
						isSubmitting || !formData.customer || !formData.package
					}
				>
					{isSubmitting ? "提交中..." : "确认提交"}
				</Button>
			</div>
		</div>
	);
}
