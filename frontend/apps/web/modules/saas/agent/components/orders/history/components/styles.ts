import { cn } from "@ui/lib";

// 动画变体
export const container = {
	hidden: { opacity: 0 },
	show: {
		opacity: 1,
		transition: {
			staggerChildren: 0.1,
			delayChildren: 0.2,
		},
	},
};

export const item = {
	hidden: { opacity: 0, y: 20 },
	show: { opacity: 1, y: 0 },
};

export const fadeIn = {
	initial: { opacity: 0 },
	animate: { opacity: 1 },
	exit: { opacity: 0 },
	transition: { duration: 0.2 },
};

export const slideIn = {
	initial: { opacity: 0, x: -20 },
	animate: { opacity: 1, x: 0 },
	exit: { opacity: 0, x: 20 },
	transition: { duration: 0.2 },
};

// 共享样式
export const cardStyle = cn(
	"relative",
	"rounded-2xl",
	"bg-gradient-to-br from-[#1A1C1E] via-[#1E2023] to-[#23252A]",
	"border border-[#D4B485]/20",
	"p-8",
	"shadow-[0_4px_12px_-2px_rgba(212,180,133,0.12)]",
	"backdrop-blur-xl",
	"overflow-hidden",
	"transition-all duration-300",
	"hover:border-[#D4B485]/30",
	"hover:shadow-[0_8px_16px_-4px_rgba(212,180,133,0.15)]",
	// 背景装饰
	"before:absolute before:inset-0",
	"before:bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)]",
	"before:bg-[length:500px_500px]",
	"before:animate-[gradient_20s_linear_infinite]",
	"after:absolute after:inset-0",
	"after:bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]",
);

export const titleGradient = {
	background:
		"linear-gradient(135deg, rgba(229,201,165,0.95) 0%, rgba(212,180,133,0.9) 50%, rgba(176,137,104,0.85) 100%)",
	WebkitBackgroundClip: "text",
	WebkitTextFillColor: "transparent",
	backgroundClip: "text",
	textShadow: "0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
	filter: "contrast(1.1) brightness(1.05)",
};
