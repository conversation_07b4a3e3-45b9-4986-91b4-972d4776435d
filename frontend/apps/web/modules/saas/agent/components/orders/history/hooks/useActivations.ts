import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";

interface Activation {
	id: string;
	consumerId: string;
	consumerName: string;
	planId: string;
	planName: string;
	computingPowerCost: number;
	startAt: string;
	expireAt: string;
	status: string;
	createdAt: string;
}

interface ApiSuccessResponse<T> {
	code: number;
	message?: string;
	data: T;
}

interface ApiErrorResponse {
	code: number;
	error: string;
	message: string;
}

type ApiResponse<T> = ApiSuccessResponse<T> | ApiErrorResponse;

interface ActivationsResponseData {
	total: number;
	items: Activation[];
}

export function useActivations() {
	const [activations, setActivations] = useState<Activation[]>([]);
	const [total, setTotal] = useState(0);
	const [loading, setLoading] = useState(false);

	const fetchActivations = useCallback(
		async (params: {
			page?: number;
			pageSize?: number;
			status?: "ACTIVE" | "EXPIRED" | "DISABLED";
		}) => {
			try {
				setLoading(true);
				// logger.info("开始获取激活记录列表", { params });

				const searchParams = new URLSearchParams();
				for (const [key, value] of Object.entries(params)) {
					if (value !== undefined) {
						searchParams.append(key, value.toString());
					}
				}

				const res = await apiClient.v1.agent.activate.list.$get({
					query: params,
				});
				const result =
					(await res.json()) as ApiResponse<ActivationsResponseData>;

				// logger.info("激活记录列表响应:", result);

				if ("error" in result || !result.data) {
					throw new Error(result.message || "获取激活记录列表失败");
				}

				setActivations(result.data.items);
				setTotal(result.data.total);

				return {
					items: result.data.items,
					total: result.data.total,
				};
			} catch (error) {
				// logger.error("获取激活记录列表失败", { error });
				toast.error("获取激活记录列表失败", {
					description:
						error instanceof Error ? error.message : "请重试",
				});
				return {
					items: [],
					total: 0,
				};
			} finally {
				setLoading(false);
			}
		},
		[],
	);

	return {
		activations,
		total,
		loading,
		fetchActivations,
	};
}
