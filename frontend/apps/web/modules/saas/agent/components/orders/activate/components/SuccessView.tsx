"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { CheckIcon, ZapIcon } from "lucide-react";
import { useEffect } from "react";

interface Package {
	id: string;
	name: string;
	level: string;
	computingPower: number;
	validityDays: number;
	price: number;
	description?: string;
	features: Array<{
		type: string;
		enabled: boolean;
		description?: string;
	}>;
}

interface SuccessViewProps {
	selectedPackage: Package | null;
	onBackToList: () => void;
}

export function SuccessView({
	selectedPackage,
	onBackToList,
}: SuccessViewProps) {
	// 播放成功音效
	useEffect(() => {
		const audio = new Audio("/sounds/success.mp3");
		audio.play().catch(() => {
			// 忽略自动播放策略导致的错误
		});
	}, []);

	if (!selectedPackage) {
		return null;
	}

	return (
		<div className="space-y-8">
			{/* 大标题 */}
			<div className="space-y-2 text-center">
				<h3
					className={cn("text-2xl font-semibold", zywhFont.className)}
					style={{
						background:
							"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
						WebkitBackgroundClip: "text",
						WebkitTextFillColor: "transparent",
						backgroundClip: "text",
						textShadow:
							"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
					}}
				>
					开通成功
				</h3>
				<p className="text-[#D4B485]/60">
					套餐已成功开通，可以开始使用了
				</p>
			</div>

			{/* 成功图标 */}
			<div className="flex justify-center">
				<motion.div
					initial={{ scale: 0.5, opacity: 0 }}
					animate={{ scale: 1, opacity: 1 }}
					transition={{
						type: "spring",
						stiffness: 200,
						damping: 20,
					}}
					className={cn(
						"flex h-24 w-24 items-center justify-center rounded-full",
						"bg-gradient-to-br from-[#D4B485]/15 to-[#D4B485]/10",
						"ring-1 ring-[#D4B485]/30",
						"shadow-[0_4px_12px_-2px_rgba(212,180,133,0.2)]",
					)}
				>
					<CheckIcon className="h-12 w-12 text-[#D4B485]" />
				</motion.div>
			</div>

			{/* 套餐信息 */}
			<div className="space-y-4">
				<div className="text-base font-medium text-[#D4B485]">
					开通套餐
				</div>
				<div
					className={cn(
						"rounded-lg p-4",
						"bg-[#1E2023]/50",
						"border border-[#D4B485]/20",
						"shadow-[inset_0_0_0_1px_rgba(212,180,133,0.05)]",
					)}
				>
					<div className="space-y-4">
						<div className="space-y-1">
							<div
								className={cn(
									"text-lg font-medium",
									zywhFont.className,
								)}
								style={{
									background:
										"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
									WebkitBackgroundClip: "text",
									WebkitTextFillColor: "transparent",
									backgroundClip: "text",
									textShadow:
										"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
								}}
							>
								{selectedPackage.name}
							</div>
							<div className="text-sm text-[#D4B485]/60">
								{selectedPackage.description}
							</div>
						</div>

						<div className="space-y-2">
							<div className="flex items-baseline gap-1">
								<div
									className={cn(
										"text-2xl font-bold",
										zywhFont.className,
									)}
									style={{
										background:
											"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
										WebkitBackgroundClip: "text",
										WebkitTextFillColor: "transparent",
										backgroundClip: "text",
										textShadow:
											"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
									}}
								>
									¥
									{selectedPackage.price
										? (
												selectedPackage.price / 100
											).toLocaleString()
										: 0}
								</div>
								<div className="text-sm text-[#D4B485]/40">
									/套餐
								</div>
							</div>
							<div className="flex items-center gap-2 text-sm text-[#D4B485]/60">
								<ZapIcon className="h-4 w-4" />
								<span>
									{selectedPackage.computingPower.toLocaleString()}
									点算力
								</span>
								<span className="text-[#D4B485]/40">
									({selectedPackage.validityDays}天)
								</span>
							</div>
						</div>

						<div className="space-y-2">
							{selectedPackage.features.map((feature) => (
								<div
									key={`${selectedPackage.id}-${feature.type}`}
									className="flex items-center gap-2 text-sm text-[#D4B485]/60"
								>
									<CheckIcon className="h-4 w-4 text-emerald-500" />
									<span>{feature.description}</span>
								</div>
							))}
						</div>
					</div>
				</div>
			</div>

			{/* 底部按钮 */}
			<div className="flex justify-center pt-4">
				<Button
					className={cn(
						"px-8",
						"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
						"text-white",
						"border-none",
						"shadow-[0_8px_16px_-4px_rgba(212,180,133,0.3)]",
						"hover:shadow-[0_12px_20px_-4px_rgba(212,180,133,0.4)]",
						"transition-all duration-200",
					)}
					onClick={onBackToList}
				>
					返回列表
				</Button>
			</div>
		</div>
	);
}
