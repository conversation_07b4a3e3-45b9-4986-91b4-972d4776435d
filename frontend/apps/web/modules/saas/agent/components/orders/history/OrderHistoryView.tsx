"use client";

import { Tabs } from "@ui/components/tabs";
import { AnimatePresence, motion } from "framer-motion";
import { useState } from "react";
import { ContentSection } from "./components/ContentSection";
import { TabNavigation } from "./components/TabNavigation";
import { TitleSection } from "./components/TitleSection";

const TAB_INFO = {
	"agent-orders": {
		title: "套餐购买记录",
		subtitle: "查看套餐购买订单",
		description: "包含所有套餐购买订单记录，支持按订单状态筛选和分页查看",
	},
	activations: {
		title: "套餐激活记录",
		subtitle: "查看套餐激活记录",
		description: "包含所有套餐激活记录，支持按激活状态筛选和分页查看",
	},
} as const;

export type TabType = keyof typeof TAB_INFO;

const container = {
	hidden: { opacity: 0 },
	show: {
		opacity: 1,
		transition: {
			staggerChildren: 0.1,
			delayChildren: 0.2,
		},
	},
};

const item = {
	hidden: { opacity: 0, y: 20 },
	show: { opacity: 1, y: 0 },
};

export function OrderHistoryView() {
	const [activeTab, setActiveTab] = useState<TabType>("agent-orders");

	return (
		<motion.div
			variants={container}
			initial="hidden"
			animate="show"
			className="w-full"
		>
			<Tabs
				value={activeTab}
				onValueChange={(value) => setActiveTab(value as TabType)}
				className="w-full"
			>
				{/* 标题区域 */}
				<motion.div variants={item} className="relative mt-8 mb-6">
					<div className="flex items-center justify-between">
						<TitleSection
							title={TAB_INFO[activeTab].title}
							subtitle={TAB_INFO[activeTab].subtitle}
							description={TAB_INFO[activeTab].description}
						/>
						<TabNavigation />
					</div>
				</motion.div>

				{/* 主要内容区域 */}
				<AnimatePresence mode="wait">
					<ContentSection />
				</AnimatePresence>
			</Tabs>
		</motion.div>
	);
}
