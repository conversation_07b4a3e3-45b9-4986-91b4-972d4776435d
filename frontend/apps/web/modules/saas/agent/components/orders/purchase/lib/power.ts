/**
 * 算力计算相关工具函数
 */

// 1小时 = 6000算力
export const HOUR_TO_POWER_RATIO = 6000;

/**
 * 将小时数转换为算力值
 * @param hours 小时数
 * @returns 算力值
 */
export function hoursToComputingPower(hours: number): number {
	return hours * HOUR_TO_POWER_RATIO;
}

/**
 * 将算力值转换为小时数
 * @param power 算力值
 * @returns 小时数
 */
export function computingPowerToHours(power: number): number {
	return power / HOUR_TO_POWER_RATIO;
}

/**
 * 计算每点算力的价格
 * @param hourlyPrice 每小时价格(元)
 * @returns 每点算力价格(元)
 */
export function calculatePowerUnitPrice(hourlyPrice: number): number {
	return hourlyPrice / HOUR_TO_POWER_RATIO;
}

/**
 * 格式化金额显示
 * @param amount 金额(元)
 * @returns 格式化后的金额字符串（元）
 */
export function formatAmount(amount: number): string {
	// 使用 toLocaleString 添加千位分隔符
	return amount.toLocaleString("zh-CN", {
		minimumFractionDigits: 0,
		maximumFractionDigits: 0,
	});
}

/**
 * 计算预估价格
 * @param power 算力值
 * @param hourlyPrice 每小时价格(元)
 * @returns 预估价格(元)
 */
export function calculateEstimatedPrice(
	power: number,
	hourlyPrice: number,
): number {
	const hours = computingPowerToHours(power);
	return hours * hourlyPrice;
}

/**
 * 计算利润
 * @param power 算力值
 * @param costPrice 成本价(每小时/元)
 * @param marketPrice 市场价(每小时/元)
 * @returns 利润(元)
 */
export function calculateProfit(
	power: number,
	costPrice: number,
	marketPrice: number,
): number {
	const hours = computingPowerToHours(power);
	return hours * (marketPrice - costPrice);
}
