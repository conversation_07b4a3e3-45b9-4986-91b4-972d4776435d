"use client";

import type {
	<PERSON>ing<PERSON>ycle,
	PlanFeature as FetchedPlanFeature,
	PlanLevel,
	PlanType as PrismaPlanType,
} from "@prisma/client";
import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { apiClient } from "@shared/lib/api-client";
import { Button } from "@ui/components/button";
import { Card } from "@ui/components/card";
import { Ta<PERSON>, TabsList, TabsTrigger } from "@ui/components/tabs";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { CreditCardIcon, ZapIcon } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { toast } from "sonner";
import { PaymentModal } from "./PaymentModal";
import { PowerPackage } from "./PlanPackage";
import { PriceDetail } from "./PriceDetail";
import { PurchaseInfo } from "./PurchaseInfo";

// import { logger } from "@repo/logs";

// Use PrismaPlanType for consistency, ensure it matches API's enum values
type PlanType = PrismaPlanType; // "AVATAR" | "VIDEO" | "TEXT" | "IMAGE" based on prisma schema

// Interface for individual plan items returned by the API
export interface ApiPlanItem {
	id: string;
	name: string;
	level: PlanLevel;
	computingPower: number;
	validityDays: number;
	price: number; // Original price in cents (from db plan.actualPrice)
	discountPrice: number; // Current user discount price in cents (from db plan.currentPrice)
	agentDiscount?: number; // Agent discount percentage
	agentPrice: number; // Calculated agent price in cents
	description?: string | null; // Mapped from plan.remark
	billingCycle: BillingCycle;
	features: FetchedPlanFeature[];
	code: string; // From Plan model
	type: PlanType; // From Plan model
}

// Interface for the data part of the successful API response
interface PlanListResponseData {
	total: number;
	items: ApiPlanItem[];
	billingCycleOptions: { value: string; label: string; discount?: boolean }[];
}

// Interface for the successful API response structure
interface ApiSuccessResponse {
	code: 200;
	data: PlanListResponseData;
	message?: string;
}

// Interface for the error API response structure
interface ApiErrorResponse {
	code: number; // Typically non-200
	error?: string;
	message?: string;
}

const PLAN_TYPES_CONFIG: { value: PlanType; label: string }[] = [
	{ value: "AVATAR", label: "数字人套餐" },
	{ value: "IMAGE", label: "图文套餐" },
	{ value: "VIDEO", label: "视频套餐" },
	{ value: "TEXT", label: "文案套餐" },
];

export interface SelectedPlanItem {
	plan: ApiPlanItem; // Use ApiPlanItem here
	quantity: number;
}

export function PurchaseView() {
	const [selectedPlanType, setSelectedPlanType] =
		useState<PlanType>("AVATAR");
	const [fetchedPlans, setFetchedPlans] = useState<ApiPlanItem[]>([]); // Use ApiPlanItem[]
	const [isLoadingPlans, setIsLoadingPlans] = useState(false);
	const [selectedPlanItems, setSelectedPlanItems] = useState<
		SelectedPlanItem[]
	>([]);

	const [showQRCode, setShowQRCode] = useState(false);
	// orderAmount state is not strictly needed if PriceDetail directly uses totalAmountInCents / 100
	// but it was used by PaymentModal's onAmountChange. We'll keep it for now if needed.

	useEffect(() => {
		const fetchPlansByType = async () => {
			setIsLoadingPlans(true);
			setFetchedPlans([]);
			setSelectedPlanItems([]);
			try {
				// logger.info(`[PurchaseView] Fetching plans for type: ${selectedPlanType}`);
				const res = await apiClient.v1.agent.plans.getPlans.$get({
					query: { type: selectedPlanType },
				});
				const responseData: ApiSuccessResponse | ApiErrorResponse =
					await res.json();

				if (
					responseData.code === 200 &&
					"data" in responseData &&
					responseData.data
				) {
					// Explicitly cast responseData.data to PlanListResponseData to resolve type inference issue
					const TSLintSucksSometimes =
						responseData.data as PlanListResponseData;
					// logger.info(`[PurchaseView] Successfully fetched ${TSLintSucksSometimes.items.length} plans.`);
					setFetchedPlans(TSLintSucksSometimes.items);
				} else {
					const errorDesc =
						"message" in responseData && responseData.message
							? responseData.message
							: "无法加载套餐数据";
					toast.error("获取套餐列表失败", {
						description: errorDesc,
					});
					// logger.error("[PurchaseView] Failed to fetch plans", { error: errorDesc });
				}
			} catch (_error) {
				toast.error("获取套餐列表失败", {
					description: "请检查您的网络连接或稍后再试",
				});
				// logger.error("[PurchaseView] Error fetching plans", { error });
			} finally {
				setIsLoadingPlans(false);
			}
		};

		fetchPlansByType();
	}, [selectedPlanType]);

	const totalSelectedPower = selectedPlanItems.reduce(
		(sum, item) => sum + item.plan.computingPower * item.quantity,
		0,
	);

	const totalAmountInCents = selectedPlanItems.reduce(
		(sum, item) => sum + item.plan.agentPrice * item.quantity,
		0,
	);

	// This state might be redundant if PriceDetail calculates display amount itself.
	// Kept for now as PaymentModal onAmountChange was wired to it.
	const [_orderAmountForDisplay, setOrderAmountForDisplay] = useState("0");
	useEffect(() => {
		setOrderAmountForDisplay((totalAmountInCents / 100).toFixed(2));
	}, [totalAmountInCents]);

	const handleSelectedPlansChange = useCallback(
		(items: SelectedPlanItem[]) => {
			setSelectedPlanItems(items);
		},
		[],
	);

	const handlePaymentComplete = useCallback(() => {
		// Called by PaymentModal or onClose logic
		setSelectedPlanItems([]);
		setShowQRCode(false);
		toast.success("支付成功", {
			description: "套餐已购买成功，算力已添加到您的账户",
		});
	}, []);

	const handleClosePayment = useCallback(() => {
		if (showQRCode) {
			setShowQRCode(false);
			// If payment was successful, PaymentModal should inform PurchaseView (e.g. via onPaymentComplete)
			// For now, assuming PaymentModal handles its own success toast and state reset on successful close.
			// If user just closes modal without paying, selected items remain.
		}
	}, [showQRCode]);

	// Callback for PaymentModal to signal successful payment, leading to UI reset
	const onPaymentSuccess = useCallback(() => {
		handlePaymentComplete();
	}, [handlePaymentComplete]);

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			className="w-full"
		>
			{/* 标题区域 */}
			<motion.div
				initial={{ opacity: 0, x: -20 }}
				animate={{ opacity: 1, x: 0 }}
				transition={{ delay: 0.2 }}
				className="relative mt-8 mb-6 space-y-1"
			>
				<div className="flex items-center gap-4">
					<div
						className={cn(
							"flex h-14 w-14 shrink-0 items-center justify-center",
							"rounded-xl bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
							"ring-1 ring-[#D4B485]/20",
							"shadow-[0_0_0_8px_rgba(212,180,133,0.03)]",
							"transform-gpu transition-transform duration-300",
							"hover:scale-105 hover:shadow-[0_0_0_10px_rgba(212,180,133,0.05)]",
						)}
					>
						<ZapIcon className="h-7 w-7 text-[#D4B485]" />
					</div>
					<div className="space-y-1">
						<h2
							className={cn(
								"font-semibold text-3xl",
								zywhFont.className,
								"leading-none",
								"tracking-[0.05em]",
								"relative",
								"after:absolute after:inset-0",
								"after:bg-[radial-gradient(circle_at_50%_50%,rgba(212,180,133,0.15),transparent_70%)]",
								"after:blur-xl after:-z-10",
							)}
							style={{
								background:
									"linear-gradient(135deg, rgba(229,201,165,0.95) 0%, rgba(212,180,133,0.9) 50%, rgba(176,137,104,0.85) 100%)",
								WebkitBackgroundClip: "text",
								WebkitTextFillColor: "transparent",
								backgroundClip: "text",
								textShadow:
									"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.1)",
								filter: "contrast(1.1) brightness(1.05)",
							}}
						>
							购买套餐
							<span className="ml-3 font-normal text-base text-[#D4B485]/80 tracking-wide">
								按需购买，灵活兑换
							</span>
						</h2>
						<p className="text-sm leading-relaxed text-white/40 group-hover:text-white/60 transition-colors duration-300">
							选择适合您业务的套餐，购买后可灵活使用或兑换成集团其他产品服务
						</p>
					</div>
				</div>
			</motion.div>

			{/* 主要内容区域 */}
			<motion.div
				initial={{ opacity: 0, y: 40 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.3 }}
				className="mt-6"
			>
				<Tabs
					defaultValue="AVATAR"
					className="w-full mb-6"
					onValueChange={(value) =>
						setSelectedPlanType(value as PlanType)
					}
				>
					<TabsList className="grid w-full grid-cols-4 bg-[#1A1C1E] border border-[#D4B485]/20 p-1 h-auto">
						{PLAN_TYPES_CONFIG.map((type) => (
							<TabsTrigger
								key={type.value}
								value={type.value}
								className={cn(
									"py-2.5 text-sm font-medium leading-5 text-[#D4B485]/70 rounded-md",
									"focus:outline-none focus-visible:ring-2 focus-visible:ring-[#D4B485] focus-visible:ring-opacity-75",
									"data-[state=active]:bg-[#D4B485]/10 data-[state=active]:text-[#E5C9A5] data-[state=active]:shadow-md",
									"hover:bg-[#D4B485]/5 hover:text-[#D4B485]",
								)}
							>
								{type.label}
							</TabsTrigger>
						))}
					</TabsList>
				</Tabs>

				<div className="grid h-full w-full grid-cols-1 md:grid-cols-3 gap-6">
					{" "}
					{/* Changed to md:grid-cols-3 for responsiveness */}
					{/* 左侧购买区域 */}
					<div className="md:col-span-2 flex h-full flex-col">
						{" "}
						{/* Changed to md:col-span-2 */}
						<Card
							className={cn(
								"flex h-full flex-col p-6",
								"bg-gradient-to-br from-[#1A1C1E] via-[#1E2023] to-[#23252A]",
								"border border-[#D4B485]/20",
								"backdrop-blur-xl",
								"shadow-[0_8px_32px_-4px_rgba(0,0,0,0.3)]",
								"hover:border-[#D4B485]/30",
								"transition-all duration-300",
								"relative overflow-hidden",
								"min-h-[400px]",
							)}
						>
							<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
							<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />
							<div className="absolute inset-0 bg-[linear-gradient(to_bottom,rgba(255,255,255,0.03)_0%,transparent_100%)]" />

							<div className="relative">
								{isLoadingPlans ? (
									<div className="flex items-center justify-center h-full min-h-[300px]">
										<p className="text-[#D4B485]/70">
											正在加载套餐...
										</p>
									</div>
								) : fetchedPlans.length === 0 ? (
									<div className="flex items-center justify-center h-full min-h-[300px]">
										<p className="text-[#D4B485]/70">
											该类型下暂无可用套餐
										</p>
									</div>
								) : (
									<PowerPackage
										plans={fetchedPlans}
										selectedPlanItems={selectedPlanItems}
										onChange={handleSelectedPlansChange}
									/>
								)}
							</div>
						</Card>
					</div>
					{/* 右侧费用明细和购买按钮 */}
					<div className="md:col-span-1 flex flex-col gap-6">
						{" "}
						{/* Changed to md:col-span-1 */}
						<Card
							className={cn(
								"flex flex-1 flex-col p-6",
								"bg-gradient-to-br from-[#1A1C1E] via-[#1E2023] to-[#23252A]",
								"border border-[#D4B485]/20",
								"backdrop-blur-xl",
								"shadow-[0_8px_32px_-4px_rgba(0,0,0,0.3)]",
								"hover:border-[#D4B485]/30",
								"transition-all duration-300",
								"relative overflow-hidden",
							)}
						>
							<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
							<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />
							<div className="absolute inset-0 bg-[linear-gradient(to_bottom,rgba(255,255,255,0.03)_0%,transparent_100%)]" />

							<div className="relative">
								<h3
									className={cn(
										"mb-4 font-semibold text-lg",
										zywhFont.className,
										"leading-none",
										"tracking-[0.05em]",
										"flex items-center gap-2",
									)}
									style={{
										background:
											"linear-gradient(135deg, rgba(229,201,165,0.95) 0%, rgba(212,180,133,0.9) 50%, rgba(176,137,104,0.85) 100%)",
										WebkitBackgroundClip: "text",
										WebkitTextFillColor: "transparent",
										backgroundClip: "text",
										textShadow:
											"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.1)",
										filter: "contrast(1.1) brightness(1.05)",
									}}
								>
									<div
										className={cn(
											"flex h-8 w-8 items-center justify-center rounded-full",
											"bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
											"ring-1 ring-[#D4B485]/20",
											"shadow-[0_4px_12px_-2px_rgba(212,180,133,0.12)]",
											"group-hover:from-[#D4B485]/15 group-hover:to-[#D4B485]/10",
											"group-hover:ring-[#D4B485]/30",
											"transition-all duration-200",
										)}
									>
										<CreditCardIcon className="h-4 w-4 text-[#D4B485]" />
									</div>
									费用明细
								</h3>
								<div className="flex-1 rounded-lg border border-[#D4B485]/20 bg-[#D4B485]/5 p-6">
									<div className="space-y-6">
										<PriceDetail
											selectedPlanItems={
												selectedPlanItems
											}
											totalAgentPriceInCents={
												totalAmountInCents
											}
										/>

										{!showQRCode && (
											<Button
												className={cn(
													"mt-6 h-12 w-full font-medium text-lg",
													"bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
													"text-[#D4B485] hover:text-white",
													"border border-[#D4B485]/20 hover:border-[#D4B485]/30",
													"shadow-[0_8px_16px_-4px_rgba(212,180,133,0.1)]",
													"transition-all duration-200",
												)}
												onClick={() =>
													setShowQRCode(true)
												}
												disabled={
													totalSelectedPower === 0
												}
											>
												{totalSelectedPower === 0
													? "请选择套餐"
													: "立即购买"}
											</Button>
										)}
									</div>
								</div>
							</div>
						</Card>
					</div>
				</div>

				{/* PurchaseInfo (算力说明) moved below the two columns */}
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ delay: 0.4 }}
					className="mt-6"
				>
					<PurchaseInfo />
				</motion.div>

				<PaymentModal
					show={showQRCode}
					selectedPlanItems={selectedPlanItems}
					onClose={handleClosePayment}
					onAmountChange={(finalAmountYuan) => {
						// This can be used to update orderAmountForDisplay if PaymentModal changes it.
						// For now, orderAmountForDisplay is primarily driven by totalAmountInCents.
						setOrderAmountForDisplay(finalAmountYuan);
					}}
					onPaymentSuccess={onPaymentSuccess} // Pass the success callback
				/>
			</motion.div>
		</motion.div>
	);
}
