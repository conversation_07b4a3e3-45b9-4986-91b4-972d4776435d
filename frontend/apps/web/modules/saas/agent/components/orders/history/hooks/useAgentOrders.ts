import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";

interface AgentOrder {
	id: string;
	orderNo: string;
	quantity: number;
	amount: number;
	status: string;
	paymentStatus: string;
	paymentMethod?: string;
	createdAt: string;
}

interface ApiSuccessResponse<T> {
	code: number;
	message?: string;
	data: T;
}

interface ApiErrorResponse {
	code: number;
	error: string;
	message: string;
}

type ApiResponse<T> = ApiSuccessResponse<T> | ApiErrorResponse;

interface OrdersResponseData {
	total: number;
	items: AgentOrder[];
}

export function useAgentOrders() {
	const [orders, setOrders] = useState<AgentOrder[]>([]);
	const [total, setTotal] = useState(0);
	const [loading, setLoading] = useState(false);

	const fetchOrders = useCallback(
		async (params: {
			page?: number;
			pageSize?: number;
			status?:
				| "PENDING"
				| "PAID"
				| "COMPLETED"
				| "FAILED"
				| "CANCELLED"
				| "REFUNDED";
		}) => {
			try {
				setLoading(true);
				// logger.info("开始获取套餐购买记录列表", { params });

				const searchParams = new URLSearchParams();
				for (const [key, value] of Object.entries(params)) {
					if (value !== undefined) {
						searchParams.append(key, value.toString());
					}
				}

				const res = await apiClient.v1.agent.orders.list.$get({
					query: params,
				});
				const result =
					(await res.json()) as ApiResponse<OrdersResponseData>;

				// logger.info("套餐购买记录列表响应:", result);

				if ("error" in result || !result.data) {
					throw new Error(
						result.message || "获取套餐购买记录列表失败",
					);
				}

				setOrders(result.data.items);
				setTotal(result.data.total);

				return {
					items: result.data.items,
					total: result.data.total,
				};
			} catch (error) {
				// logger.error("获取套餐购买记录列表失败", { error });
				toast.error("获取套餐购买记录列表失败", {
					description:
						error instanceof Error ? error.message : "请重试",
				});
				return {
					items: [],
					total: 0,
				};
			} finally {
				setLoading(false);
			}
		},
		[],
	);

	return {
		orders,
		total,
		loading,
		fetchOrders,
	};
}
