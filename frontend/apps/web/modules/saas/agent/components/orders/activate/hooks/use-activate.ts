import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";

interface ActivateResponse {
	id: string;
	consumer: {
		id: string;
		name: string;
	};
	plan: {
		id: string;
		name: string;
		level: string;
	};
	computingPowerCost: number;
	startAt: string;
	expireAt: string;
	status: string;
}

interface ApiSuccessResponse<T> {
	code: number;
	message?: string;
	data: T;
}

interface ApiErrorResponse {
	code: number;
	error: string;
	message: string;
}

type ApiResponse<T> = ApiSuccessResponse<T> | ApiErrorResponse;

export function useActivate() {
	const [loading, setLoading] = useState(false);
	const [activationRecord, setActivationRecord] =
		useState<ActivateResponse | null>(null);

	const activatePlan = useCallback(
		async (params: {
			consumerId: string;
			planId: string;
			startAt?: string;
		}) => {
			try {
				setLoading(true);
				// logger.info("开始开通套餐", { params });

				const res = await apiClient.v1.agent.activate.activate.$post({
					json: params,
				});
				const result =
					(await res.json()) as ApiResponse<ActivateResponse>;

				// logger.info("套餐开通响应:", result);

				if ("error" in result || !result.data) {
					throw new Error(result.message || "套餐开通失败");
				}

				setActivationRecord(result.data);
				toast.success("套餐开通成功", {
					description: `已为 ${result.data.consumer.name} 开通 ${result.data.plan.name}`,
				});

				return result.data;
			} catch (error) {
				// logger.error("套餐开通失败", { error });
				toast.error("套餐开通失败", {
					description:
						error instanceof Error ? error.message : "请重试",
				});
				return null;
			} finally {
				setLoading(false);
			}
		},
		[],
	);

	return {
		loading,
		activationRecord,
		activatePlan,
	};
}
