"use client";

import { useSession } from "@saas/auth/hooks/use-session";
import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Button } from "@ui/components/button";
import { Tabs, TabsList, TabsTrigger } from "@ui/components/tabs";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { CheckIcon, PackageSearchIcon, ZapIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import { type FetchPlansParams, usePlans } from "../hooks";
import { InventoryAlertModal } from "./InventoryAlertModal";

// Define PlanType to match the one in usePlans or a shared location
// This should align with Prisma's PlanType if possible for strong typing.
// For now, matching the string literals used in usePlans and PurchaseView.
type PlanType = "AVATAR" | "VIDEO" | "TEXT" | "IMAGE" | "ALL";

// Configuration for plan type tabs - similar to PurchaseView
const PLAN_TYPES_CONFIG: { value: PlanType; label: string }[] = [
	{ value: "AVATAR", label: "数字人套餐" },
	{ value: "IMAGE", label: "图文套餐" },
	{ value: "VIDEO", label: "视频套餐" },
	{ value: "TEXT", label: "文案套餐" },
];

interface Customer {
	id: string;
	name: string;
	phone: string;
	status: string;
	currentPlan?: {
		id: string;
		name: string;
		level: string;
		expireAt?: string;
	};
	createdAt: string;
}

interface Package {
	id: string;
	name: string;
	level: string;
	computingPower: number;
	validityDays: number;
	price: number;
	discountPrice: number;
	priority: number;
	description?: string;
	features: Array<{
		type: string;
		enabled: boolean;
		description?: string;
		code?: string;
	}>;
	inventoryQuantity: number;
	type: PlanType;
	code: string;
}

interface OrderInfo {
	orderId: string;
	customer: Customer;
	package: Package;
	createdAt: string;
}

interface FormData {
	customer: Customer | null;
	package: Package | null;
	orderInfo: OrderInfo | null;
}

interface SelectPackageProps {
	onNext: (data: { package: Package }) => void;
	onPrev: () => void;
	formData: FormData;
}

export function SelectPackage({
	onNext,
	onPrev,
	formData,
}: SelectPackageProps) {
	const { plans, loading, fetchPlans } = usePlans();
	const [selectedPackage, setSelectedPackage] = useState<Package | null>(
		formData.package || null,
	);
	useSession();
	// const computingPower = user?.computingPower || 0; // No longer needed if the display is removed
	const [selectedPlanType, setSelectedPlanType] =
		useState<PlanType>("AVATAR"); // Default to AVATAR
	const [showInventoryAlert, setShowInventoryAlert] = useState(false); // State for modal visibility
	const router = useRouter(); // Initialize router

	// Fetch plans when selectedPlanType changes
	useEffect(() => {
		const params: FetchPlansParams = { type: selectedPlanType };
		fetchPlans(params);
	}, [selectedPlanType, fetchPlans]);

	// 按优先级排序的套餐
	const sortedPlans = useMemo(() => {
		return [...(plans || [])].sort((a, b) => a.priority - b.priority);
	}, [plans]);

	// 处理选择套餐
	const handleSelectPackage = (pkg: Package) => {
		setSelectedPackage(pkg);
	};

	// 处理下一步 - NOW INCLUDES INVENTORY CHECK
	const handleNext = () => {
		if (selectedPackage) {
			if (selectedPackage.inventoryQuantity > 0) {
				onNext({ package: selectedPackage });
			} else {
				setShowInventoryAlert(true); // Show modal if no inventory
			}
		}
	};

	// Handler for closing the alert modal
	const handleCloseAlert = () => {
		setShowInventoryAlert(false);
	};

	// Handler for confirming to purchase the plan from the modal
	const handleConfirmPurchaseFromModal = () => {
		setShowInventoryAlert(false);
		if (selectedPackage) {
			router.push(
				`/app/agent/orders/purchase?planCode=${selectedPackage.code}&type=${selectedPackage.type}`,
			);
		}
	};

	// Determine the label for the selected plan type to pass to the modal
	const selectedPlanTypeLabel = useMemo(() => {
		if (selectedPackage) {
			const config = PLAN_TYPES_CONFIG.find(
				(pt) => pt.value === selectedPackage.type,
			);
			return config ? config.label : selectedPackage.type; // Fallback to type value if no label found
		}
		return null;
	}, [selectedPackage]);

	return (
		<div className="space-y-8 relative">
			{/* 右上角算力卡片 - REMOVED
			<div className="absolute right-0 top-0 bg-gradient-to-br from-[#1E2023]/80 to-[#1E2023]/95 rounded-lg p-3 border border-[#D4B485]/20 shadow-lg">
				<div className="flex items-center gap-2">
					<ZapIcon className="h-5 w-5 text-amber-500" />
					<div>
						<div className="text-xs text-[#D4B485]/60">
							我的算力
						</div>
						<div className="text-base font-semibold text-amber-500">
							{computingPower.toLocaleString()} 点
						</div>
					</div>
				</div>
			</div>
			*/}

			{/* 大标题 */}
			<div className="space-y-2 text-center mb-8">
				<h3
					className={cn("text-2xl font-semibold", zywhFont.className)}
					style={{
						background:
							"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
						WebkitBackgroundClip: "text",
						WebkitTextFillColor: "transparent",
						backgroundClip: "text",
						textShadow:
							"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
					}}
				>
					选择开通套餐
				</h3>
				<p className="text-[#D4B485]/60">
					从下方列表中选择需要开通的套餐类型
				</p>
			</div>

			{/* 套餐类型 Tabs */}
			<Tabs
				defaultValue={selectedPlanType}
				className="w-full mb-8"
				onValueChange={(value) =>
					setSelectedPlanType(value as PlanType)
				}
			>
				<TabsList className="grid w-full grid-cols-4 bg-[#1A1C1E] border border-[#D4B485]/20 p-1 h-auto">
					{PLAN_TYPES_CONFIG.map((typeOpt) => (
						<TabsTrigger
							key={typeOpt.value}
							value={typeOpt.value}
							className={cn(
								"py-2.5 text-sm font-medium leading-5 text-[#D4B485]/70 rounded-md",
								"focus:outline-none focus-visible:ring-2 focus-visible:ring-[#D4B485] focus-visible:ring-opacity-75",
								"data-[state=active]:bg-[#D4B485]/10 data-[state=active]:text-[#E5C9A5] data-[state=active]:shadow-md",
								"hover:bg-[#D4B485]/5 hover:text-[#D4B485]",
							)}
						>
							{typeOpt.label}
						</TabsTrigger>
					))}
				</TabsList>
			</Tabs>

			{/* 套餐列表 */}
			<div className="grid grid-cols-3 gap-6">
				{loading ? (
					// 加载状态
					<div className="col-span-3 flex items-center justify-center py-12">
						<div className="text-[#D4B485]/60">加载中...</div>
					</div>
				) : sortedPlans.length === 0 ? (
					// 空状态
					<div className="col-span-3 flex items-center justify-center py-12">
						<div className="text-[#D4B485]/60">
							该类型下暂无可用套餐
						</div>
					</div>
				) : (
					// 套餐列表
					sortedPlans.map((pkg) => (
						<motion.div
							key={pkg.id}
							initial={{ opacity: 0, y: 20 }}
							animate={{ opacity: 1, y: 0 }}
							className={cn(
								"rounded-xl p-6",
								"cursor-pointer",
								"transition-all duration-300",
								"relative overflow-hidden",
								selectedPackage?.id === pkg.id
									? [
											"bg-[#D4B485]/10",
											"border-[#D4B485]/40",
											"ring-[1px]",
											"ring-[#D4B485]/40",
											"ring-offset-2",
											"ring-offset-[#1A1C1E]",
											"shadow-[0_0_20px_rgba(212,180,133,0.2)]",
											"scale-[1.02]",
										].join(" ")
									: [
											"bg-[#1E2023]/50",
											"border-[#D4B485]/20",
											"hover:border-[#D4B485]/30",
											"hover:scale-[1.01]",
											"hover:shadow-[0_8px_16px_-4px_rgba(212,180,133,0.15)]",
										].join(" "),
								"border",
								// 第一层装饰 - 内部光效
								"before:absolute before:inset-0",
								"before:rounded-xl",
								"before:transition-all before:duration-500",
								"before:ease-out",
								"before:scale-[0.97]",
								"before:opacity-0",
								"before:bg-gradient-to-r",
								"before:from-transparent",
								"before:via-[#1E2532]",
								"before:to-transparent",
								"before:bg-clip-padding",
								"before:backdrop-filter",
								"before:backdrop-blur-[8px]",
								"before:border-transparent",
								"before:bg-opacity-50",
								"hover:before:scale-100 hover:before:opacity-100",
								"before:z-[-1]",
								// 第二层装饰 - 外部光晕
								"after:absolute after:inset-[-1px]",
								"after:rounded-xl",
								"after:transition-all after:duration-500",
								"after:ease-out",
								"after:scale-[1.02]",
								"after:bg-gradient-to-r",
								"after:from-transparent",
								"after:via-[#D4B485]/5",
								"after:to-transparent",
								"after:opacity-0",
								"hover:after:opacity-100",
								"after:z-[-2]",
								// 第三层装饰 - 内部阴影
								"shadow-[inset_0_0_0_1px_rgba(212,180,133,0.05)]",
								"hover:shadow-[inset_0_0_0_1px_rgba(212,180,133,0.1)]",
							)}
							onClick={() => handleSelectPackage(pkg)}
						>
							{/* 套餐内容 */}
							<div className="space-y-4 relative z-[1]">
								{/* 套餐名称和描述 */}
								<div className="space-y-1">
									<div
										className={cn(
											"text-xl font-semibold",
											zywhFont.className,
											"relative",
											"after:absolute after:inset-0",
											"after:bg-gradient-to-r",
											"after:from-[#D4B485]/0",
											"after:via-[#D4B485]/10",
											"after:to-[#D4B485]/0",
											"after:opacity-0",
											"group-hover:after:opacity-100",
											"after:transition-opacity after:duration-300",
										)}
										style={{
											background:
												"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
											WebkitBackgroundClip: "text",
											WebkitTextFillColor: "transparent",
											backgroundClip: "text",
											textShadow:
												"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
										}}
									>
										{pkg.name}
									</div>
									<div className="text-sm text-[#D4B485]/60">
										{pkg.description}
									</div>
								</div>

								{/* 价格信息 */}
								<div className="space-y-2">
									{/* 原价与代理商成本价显示 */}
									<div className="flex flex-col">
										{/* 原价 */}
										<div className="flex items-center gap-1 mb-1">
											<span className="text-sm text-[#D4B485]/50">
												原价
											</span>
											<span className="text-base font-medium text-[#D4B485]/70 line-through relative">
												¥{pkg.price / 100}
											</span>
										</div>

										{/* 代理商成本价 */}
										<div className="flex items-baseline gap-2">
											<div
												className={cn(
													"text-3xl font-bold",
													zywhFont.className,
												)}
												style={{
													background:
														"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
													WebkitBackgroundClip:
														"text",
													WebkitTextFillColor:
														"transparent",
													backgroundClip: "text",
													textShadow:
														"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
												}}
											>
												¥{pkg.discountPrice / 100}
											</div>

											<div className="text-sm text-[#D4B485]/40">
												/套餐
											</div>

											<div className="px-1.5 py-0.5 bg-gradient-to-r from-sky-500/20 to-sky-600/20 rounded border border-sky-500/30 text-xs text-sky-400">
												代理成本
											</div>
										</div>
									</div>
								</div>

								{/* 功能列表 */}
								<div className="space-y-2">
									{pkg.features.map((feature) => (
										<div
											key={`${pkg.id}-${feature.type}`}
											className="flex items-center gap-2 text-sm text-[#D4B485]/60"
										>
											<CheckIcon className="h-4 w-4 text-emerald-500" />
											<span>{feature.description}</span>
										</div>
									))}
								</div>

								{/* 分隔线 */}
								<div className="border-t border-[#D4B485]/10 my-2" />

								{/* 算力信息 */}
								<div className="flex items-center gap-2 text-sm">
									<ZapIcon className="h-4 w-4 text-amber-500" />
									<span className="text-amber-500 font-medium">
										{pkg.computingPower.toLocaleString()}
										点算力
									</span>
									<span className="text-[#D4B485]/40">
										({pkg.validityDays}天有效期)
									</span>
								</div>

								{/* 套餐库存显示 */}
								<div className="flex items-center gap-2 text-sm mt-2 pt-2 border-t border-[#D4B485]/10">
									<PackageSearchIcon
										className={cn(
											"h-4 w-4",
											pkg.inventoryQuantity > 0
												? "text-green-500"
												: "text-red-500",
										)}
									/>
									<span
										className={cn(
											pkg.inventoryQuantity > 0
												? "text-green-400/80"
												: "text-red-400/80",
											"font-medium",
										)}
									>
										代理库存:{" "}
										{pkg.inventoryQuantity > 0
											? `${pkg.inventoryQuantity.toLocaleString()} 件`
											: "暂无库存"}
									</span>
								</div>
							</div>
						</motion.div>
					))
				)}
			</div>

			{/* 底部按钮 */}
			<div className="flex justify-between pt-4">
				<Button
					variant="outline"
					className={cn(
						"border-[#D4B485]/20 text-[#D4B485]",
						"hover:bg-[#D4B485]/5 hover:border-[#D4B485]/30",
					)}
					onClick={onPrev}
				>
					上一步
				</Button>
				<Button
					className={cn(
						"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
						"text-white",
						"border-none",
						"shadow-[0_8px_16px_-4px_rgba(212,180,133,0.3)]",
						"hover:shadow-[0_12px_20px_-4px_rgba(212,180,133,0.4)]",
						"transition-all duration-200",
						"disabled:opacity-50 disabled:cursor-not-allowed",
					)}
					onClick={handleNext}
					disabled={!selectedPackage}
				>
					下一步
				</Button>
			</div>

			{/* Inventory Alert Modal */}
			<InventoryAlertModal
				isOpen={showInventoryAlert}
				onClose={handleCloseAlert}
				onConfirmPurchase={handleConfirmPurchaseFromModal}
				planName={selectedPackage?.name}
				planTypeLabel={selectedPlanTypeLabel}
			/>
		</div>
	);
}
