"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { TabsList, TabsTrigger } from "@ui/components/tabs";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";

export function TabNavigation() {
	return (
		<motion.div
			initial={{ opacity: 0, scale: 0.95 }}
			animate={{ opacity: 1, scale: 1 }}
			transition={{ duration: 0.2 }}
			className="flex-shrink-0"
		>
			<TabsList
				className={cn(
					"relative",
					"bg-gradient-to-br from-[#1A1C1E] via-[#1E2023] to-[#23252A]",
					"border border-[#D4B485]/20",
					"rounded-2xl",
					"p-1.5",
					"shadow-[0_4px_12px_-2px_rgba(212,180,133,0.12)]",
					"backdrop-blur-xl",
					"overflow-hidden",
					"transition-all duration-300",
					"hover:border-[#D4B485]/30",
					"hover:shadow-[0_8px_16px_-4px_rgba(212,180,133,0.15)]",
					// 背景装饰
					"before:absolute before:inset-0",
					"before:bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)]",
					"before:bg-[length:250px_250px]",
					"before:opacity-50",
					"before:animate-[gradient_20s_linear_infinite]",
					// 内部光效
					"after:absolute after:inset-0",
					"after:bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]",
				)}
			>
				<div className="grid w-full grid-cols-2 gap-1.5 relative z-10">
					<TabsTrigger
						value="agent-orders"
						className={cn(
							"relative",
							"rounded-xl",
							"text-sm font-medium",
							zywhFont.className,
							"transition-all duration-300",
							"overflow-hidden",
							"min-w-[120px] px-6 py-2.5",
							// 默认状态
							"bg-[#1E2023]/50",
							"text-[#D4B485]/60",
							"hover:text-[#D4B485]/80",
							"hover:bg-[#D4B485]/5",
							// 激活状态
							"data-[state=active]:bg-gradient-to-r",
							"data-[state=active]:from-[#D4B485]/20",
							"data-[state=active]:to-[#D4B485]/10",
							"data-[state=active]:text-[#E5C9A5]",
							"data-[state=active]:shadow-[0_4px_12px_-2px_rgba(212,180,133,0.2)]",
							// 内部光效
							"before:absolute before:inset-px",
							"before:rounded-[10px]",
							"before:bg-gradient-to-b before:from-white/5 before:to-transparent",
							"before:opacity-0",
							"data-[state=active]:before:opacity-100",
							"before:transition-opacity before:duration-300",
							// 外部光晕
							"after:absolute after:inset-0",
							"after:bg-[radial-gradient(circle_at_50%_0%,rgba(212,180,133,0.1),transparent_70%)]",
							"after:opacity-0",
							"data-[state=active]:after:opacity-100",
							"after:transition-opacity after:duration-300",
							// 悬浮效果
							"hover:scale-[1.02]",
							"active:scale-[0.98]",
							"transform-gpu",
						)}
					>
						套餐购买记录
					</TabsTrigger>
					<TabsTrigger
						value="activations"
						className={cn(
							"relative",
							"rounded-xl",
							"text-sm font-medium",
							zywhFont.className,
							"transition-all duration-300",
							"overflow-hidden",
							"min-w-[120px] px-6 py-2.5",
							// 默认状态
							"bg-[#1E2023]/50",
							"text-[#D4B485]/60",
							"hover:text-[#D4B485]/80",
							"hover:bg-[#D4B485]/5",
							// 激活状态
							"data-[state=active]:bg-gradient-to-r",
							"data-[state=active]:from-[#D4B485]/20",
							"data-[state=active]:to-[#D4B485]/10",
							"data-[state=active]:text-[#E5C9A5]",
							"data-[state=active]:shadow-[0_4px_12px_-2px_rgba(212,180,133,0.2)]",
							// 内部光效
							"before:absolute before:inset-px",
							"before:rounded-[10px]",
							"before:bg-gradient-to-b before:from-white/5 before:to-transparent",
							"before:opacity-0",
							"data-[state=active]:before:opacity-100",
							"before:transition-opacity before:duration-300",
							// 外部光晕
							"after:absolute after:inset-0",
							"after:bg-[radial-gradient(circle_at_50%_0%,rgba(212,180,133,0.1),transparent_70%)]",
							"after:opacity-0",
							"data-[state=active]:after:opacity-100",
							"after:transition-opacity after:duration-300",
							// 悬浮效果
							"hover:scale-[1.02]",
							"active:scale-[0.98]",
							"transform-gpu",
						)}
					>
						套餐激活记录
					</TabsTrigger>
				</div>
			</TabsList>
		</motion.div>
	);
}
