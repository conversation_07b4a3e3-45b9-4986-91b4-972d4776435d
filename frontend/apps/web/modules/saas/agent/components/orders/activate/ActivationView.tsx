"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { ZapIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import type { Consumer } from "../../../types/consumer";
import { ActivationProgress } from "./components/ActivationProgress";
import { ConfirmOrder } from "./components/ConfirmOrder";
import { SelectCustomer } from "./components/SelectCustomer";
import { SelectPackage } from "./components/SelectPackage";
import { SuccessView } from "./components/SuccessView";

// 激活步骤
const STEPS = [
	{
		title: "选择客户",
		description: "从客户列表中选择开通账户",
	},
	{
		title: "选择套餐",
		description: "选择要激活的套餐类型",
	},
	{
		title: "确认订单",
		description: "确认账单信息和套餐详情",
	},
];

// 表单数据类型
interface Package {
	id: string;
	name: string;
	level: string;
	computingPower: number;
	validityDays: number;
	price: number;
	discountPrice: number; // 代理折扣价
	priority: number; // 展示优先级，数字越小优先级越高
	description?: string;
	features: Array<{
		type: string;
		enabled: boolean;
		description?: string;
	}>;
	inventoryQuantity: number; // 库存数量
	type: "AVATAR" | "VIDEO" | "TEXT" | "IMAGE" | "ALL"; // 套餐类型
	code: string; // 套餐代码
}

interface OrderInfo {
	orderId: string;
	customer: Consumer;
	package: Package;
	createdAt: string;
}

interface FormData {
	customer: Consumer | null;
	package: Package | null;
	orderInfo: OrderInfo | null;
}

interface StepData {
	customer?: Consumer;
	package?: Package;
	orderInfo?: OrderInfo;
}

export function ActivationView() {
	const [currentStep, setCurrentStep] = useState(0);
	const [formData, setFormData] = useState<FormData>({
		customer: null,
		package: null,
		orderInfo: null,
	});
	const router = useRouter();

	// 处理下一步
	const handleNext = (data: StepData) => {
		setFormData((prev) => ({ ...prev, ...data }));
		setCurrentStep((prev) => prev + 1);
	};

	// 处理上一步
	const handlePrev = () => {
		setCurrentStep((prev) => prev - 1);
	};

	// 处理开通成功
	const handleActivationSuccess = (activationInfo: {
		orderId: string;
		customer: Consumer;
		package: Package;
		createdAt: string;
	}) => {
		setFormData((prev) => ({
			...prev,
			orderInfo: {
				orderId: activationInfo.orderId,
				customer: activationInfo.customer,
				package: activationInfo.package,
				createdAt: activationInfo.createdAt,
			},
		}));
	};

	// 渲染当前步骤内容
	const renderStepContent = () => {
		switch (currentStep) {
			case 0:
				return (
					<SelectCustomer onNext={handleNext} formData={formData} />
				);
			case 1:
				return (
					<SelectPackage
						onNext={handleNext}
						onPrev={handlePrev}
						formData={formData}
					/>
				);
			case 2:
				if (!formData.customer || !formData.package) {
					setCurrentStep(0);
					return null;
				}
				return (
					<motion.div
						key="confirm-order"
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						exit={{ opacity: 0, y: -20 }}
					>
						{formData.orderInfo ? (
							<SuccessView
								selectedPackage={formData.package}
								onBackToList={() => {
									router.push("/app/agent/orders/history");
								}}
							/>
						) : (
							<ConfirmOrder
								onPrev={handlePrev}
								formData={formData}
								onActivationSuccess={handleActivationSuccess}
							/>
						)}
					</motion.div>
				);
			default:
				return null;
		}
	};

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			className="w-full"
		>
			{/* 标题和进度条区域 */}
			<motion.div
				initial={{ opacity: 0, x: -20 }}
				animate={{ opacity: 1, x: 0 }}
				transition={{ delay: 0.2 }}
				className="relative mt-8 mb-6"
			>
				<div className="flex items-center justify-between">
					{/* 标题区域 */}
					<div className="flex items-center gap-4">
						<div
							className={cn(
								"flex h-14 w-14 shrink-0 items-center justify-center",
								"rounded-xl bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
								"ring-1 ring-[#D4B485]/20",
								"shadow-[0_0_0_8px_rgba(212,180,133,0.03)]",
								"transform-gpu transition-transform duration-300",
								"hover:scale-105 hover:shadow-[0_0_0_10px_rgba(212,180,133,0.05)]",
							)}
						>
							<ZapIcon className="h-7 w-7 text-[#D4B485]" />
						</div>
						<div className="space-y-1">
							<h2
								className={cn(
									"font-semibold text-3xl",
									zywhFont.className,
									"leading-none",
									"tracking-[0.05em]",
									"relative",
									"after:absolute after:inset-0",
									"after:bg-[radial-gradient(circle_at_50%_50%,rgba(212,180,133,0.15),transparent_70%)]",
									"after:blur-xl after:-z-10",
								)}
								style={{
									background:
										"linear-gradient(135deg, rgba(229,201,165,0.95) 0%, rgba(212,180,133,0.9) 50%, rgba(176,137,104,0.85) 100%)",
									WebkitBackgroundClip: "text",
									WebkitTextFillColor: "transparent",
									backgroundClip: "text",
									textShadow:
										"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.1)",
									filter: "contrast(1.1) brightness(1.05)",
								}}
							>
								套餐激活
								<span className="ml-3 font-normal text-base text-[#D4B485]/80 tracking-wide">
									三步开通，即刻生效
								</span>
							</h2>
							<p className="text-sm leading-relaxed text-white/40 group-hover:text-white/60 transition-colors duration-300">
								为客户开通数字人系统、图文裂变系统等产品套餐，支持灵活配置
							</p>
						</div>
					</div>

					{/* 进度条 */}
					<div className="flex-1 max-w-[600px] px-8">
						<ActivationProgress
							steps={STEPS}
							currentStep={currentStep}
						/>
					</div>
				</div>
			</motion.div>

			{/* 主要内容区域 */}
			<motion.div
				initial={{ opacity: 0, y: 40 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.3 }}
				className="mt-6"
			>
				<div
					className={cn(
						"rounded-xl",
						"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
						"border border-[#D4B485]/20",
						"p-8",
						"shadow-[0_8px_32px_-4px_rgba(0,0,0,0.3)]",
						"backdrop-blur-xl",
					)}
				>
					{renderStepContent()}
				</div>
			</motion.div>
		</motion.div>
	);
}
