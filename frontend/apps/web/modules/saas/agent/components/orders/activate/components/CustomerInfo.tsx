import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { cn } from "@ui/lib";
import { UserIcon } from "lucide-react";

interface Customer {
	id: string;
	name: string;
	email: string;
	phone: string;
	company: string;
	type: string;
}

interface CustomerInfoProps {
	customer: Customer;
}

export function CustomerInfo({ customer }: CustomerInfoProps) {
	return (
		<div className="space-y-4">
			<h3
				className={cn("text-lg font-semibold", zywhFont.className)}
				style={{
					background:
						"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
					WebkitBackgroundClip: "text",
					WebkitTextFillColor: "transparent",
					backgroundClip: "text",
					textShadow:
						"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
				}}
			>
				客户信息
			</h3>
			<div
				className={cn(
					"rounded-lg p-4",
					"bg-[#1E2023]/50 border-[#D4B485]/20",
					"border",
					"h-full",
					"relative overflow-hidden",
					"transition-all duration-300",
					// 第一层装饰 - 内部光效
					"before:absolute before:inset-0",
					"before:rounded-lg",
					"before:transition-all before:duration-500",
					"before:ease-out",
					"before:scale-[0.97]",
					"before:opacity-0",
					"before:bg-gradient-to-r",
					"before:from-transparent",
					"before:via-[#1E2532]",
					"before:to-transparent",
					"before:bg-clip-padding",
					"before:backdrop-filter",
					"before:backdrop-blur-[8px]",
					"before:border-transparent",
					"before:bg-opacity-50",
					"hover:before:scale-100 hover:before:opacity-100",
					"before:z-[-1]",
					// 第二层装饰 - 外部光晕
					"after:absolute after:inset-[-1px]",
					"after:rounded-lg",
					"after:transition-all after:duration-500",
					"after:ease-out",
					"after:scale-[1.02]",
					"after:bg-gradient-to-r",
					"after:from-transparent",
					"after:via-[#D4B485]/5",
					"after:to-transparent",
					"after:opacity-0",
					"hover:after:opacity-100",
					"after:z-[-2]",
					// 第三层装饰 - 内部阴影
					"shadow-[inset_0_0_0_1px_rgba(212,180,133,0.05)]",
					"hover:shadow-[inset_0_0_0_1px_rgba(212,180,133,0.1)]",
					"hover:bg-[#1E2023]/60",
				)}
			>
				<div className="flex items-center gap-4 relative z-[1]">
					<div
						className={cn(
							"flex h-12 w-12 items-center justify-center rounded-full",
							"bg-gradient-to-br from-[#D4B485]/15 to-[#D4B485]/10",
							"ring-1 ring-[#D4B485]/30",
							"shadow-[0_4px_12px_-2px_rgba(212,180,133,0.2)]",
							"transition-all duration-300",
							"group-hover:from-[#D4B485]/20 group-hover:to-[#D4B485]/15",
							"group-hover:ring-[#D4B485]/40",
						)}
					>
						<UserIcon className="h-6 w-6 text-[#D4B485]" />
					</div>
					<div className="flex-1">
						<div className="flex items-center justify-between">
							<div
								className={cn(
									"text-lg font-medium",
									zywhFont.className,
								)}
								style={{
									background:
										"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
									WebkitBackgroundClip: "text",
									WebkitTextFillColor: "transparent",
									backgroundClip: "text",
									textShadow:
										"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
								}}
							>
								{customer.name}
							</div>
							<div className="text-sm text-[#D4B485]/60">
								{customer.type}
							</div>
						</div>
						<div className="mt-1 space-y-1">
							<div className="text-sm text-[#D4B485]/60">
								{customer.company}
							</div>
							<div className="flex items-center gap-4 text-sm text-[#D4B485]/40">
								<span>{customer.email}</span>
								<span>{customer.phone}</span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
