"use client";

import { <PERSON><PERSON> } from "@ui/components/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import { cn } from "@ui/lib";
import { AlertTriangleIcon } from "lucide-react";

interface InventoryAlertModalProps {
	isOpen: boolean;
	onClose: () => void;
	onConfirmPurchase: () => void;
	planName?: string | null;
	planTypeLabel?: string | null;
}

export function InventoryAlertModal({
	isOpen,
	onClose,
	onConfirmPurchase,
	planName,
	planTypeLabel,
}: InventoryAlertModalProps) {
	if (!isOpen) {
		return null;
	}

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="sm:max-w-[425px] bg-[#1E2023] border-[#D4B485]/20">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2 text-[#D4B485]">
						<AlertTriangleIcon className="h-5 w-5 text-amber-500" />
						库存不足提示
					</DialogTitle>
					<DialogDescription className="text-[#D4B485]/70 pt-2">
						您选择的套餐{" "}
						{planTypeLabel && (
							<span className="font-semibold text-[#E5C9A5]">
								{planTypeLabel} -
							</span>
						)}
						<span className="font-semibold text-[#E5C9A5]">
							{" "}
							{planName || "当前套餐"}
						</span>{" "}
						目前代理库存为 0。
						<br />
						您可以前往购买该套餐补充库存，或选择其他有库存的套餐进行开通。
					</DialogDescription>
				</DialogHeader>
				<DialogFooter className="pt-4 flex items-center">
					<Button
						variant="outline"
						onClick={onClose}
						className={cn(
							"border-[#D4B485]/30 text-[#D4B485] hover:bg-[#D4B485]/10 hover:text-[#E5C9A5]",
						)}
					>
						选择其他套餐
					</Button>
					<div className="flex-grow" />
					<Button
						onClick={onConfirmPurchase}
						className={cn(
							"bg-gradient-to-r from-[#D4B485] to-[#B08968] text-white",
							"hover:opacity-90",
						)}
					>
						前往购买套餐
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
