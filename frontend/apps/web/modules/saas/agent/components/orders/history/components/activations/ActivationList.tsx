"use client";

import { <PERSON><PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@ui/components/table";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { Loader2Icon, RefreshCcwIcon, SearchIcon } from "lucide-react";
import { useEffect, useState } from "react";
import { useActivations } from "../../hooks/useActivations";

type ActivationStatus = "ACTIVE" | "EXPIRED" | "DISABLED";

const ACTIVATION_STATUS_MAP = {
	ACTIVE: { label: "生效中", color: "bg-emerald-500/20 text-emerald-500" },
	EXPIRED: { label: "已过期", color: "bg-yellow-500/20 text-yellow-500" },
	DISABLED: { label: "已禁用", color: "bg-red-500/20 text-red-500" },
} as const;

const STATUS_OPTIONS = [
	{ label: "全部状态", value: "ALL" },
	{ label: "生效中", value: "ACTIVE" },
	{ label: "已过期", value: "EXPIRED" },
	{ label: "已禁用", value: "DISABLED" },
] as const;

export function ActivationList() {
	const { activations, total, loading, fetchActivations } = useActivations();
	const [currentPage, setCurrentPage] = useState(1);
	const [searchTerm, setSearchTerm] = useState("");
	const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
	const [status, setStatus] = useState<ActivationStatus | "ALL">("ALL");
	const pageSize = 10;

	// 使用防抖处理搜索词
	useEffect(() => {
		const timer = setTimeout(() => {
			setDebouncedSearchTerm(searchTerm);
		}, 500); // 500ms 的防抖延迟

		return () => clearTimeout(timer);
	}, [searchTerm]);

	// 监听防抖后的搜索词和状态变化
	useEffect(() => {
		fetchActivations({
			page: currentPage,
			pageSize,
			...(status !== "ALL" && { status: status as ActivationStatus }),
			...(debouncedSearchTerm && { keyword: debouncedSearchTerm }),
		});
	}, [fetchActivations, currentPage, debouncedSearchTerm, status]);

	// 刷新数据
	const handleRefresh = () => {
		setCurrentPage(1);
		fetchActivations({
			page: 1,
			pageSize,
			...(status !== "ALL" && { status: status as ActivationStatus }),
			...(debouncedSearchTerm && { keyword: debouncedSearchTerm }),
		});
	};

	// 处理搜索按钮点击
	const handleSearch = () => {
		setCurrentPage(1);
		setDebouncedSearchTerm(searchTerm);
	};

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			className="space-y-4"
		>
			{/* 搜索和筛选区域 - 始终显示 */}
			<div className="flex items-center gap-4">
				<div className="relative flex-1">
					<SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-[#D4B485]/40" />
					<Input
						type="search"
						placeholder="输入客户名称搜索"
						value={searchTerm}
						onChange={(e) => setSearchTerm(e.target.value)}
						onKeyDown={(e) => {
							if (e.key === "Enter") {
								handleSearch();
							}
						}}
						className={cn(
							"pl-9",
							"bg-[#1E2023]/50 border-[#D4B485]/20",
							"text-[#D4B485] placeholder:text-[#D4B485]/40",
							"focus:border-[#D4B485]/40 focus:ring-[#D4B485]/20",
						)}
					/>
				</div>
				<Select
					value={status}
					onValueChange={(value) => {
						setStatus(value as ActivationStatus | "ALL");
						setCurrentPage(1);
					}}
				>
					<SelectTrigger
						className={cn(
							"w-[160px]",
							"bg-[#1E2023]/50 border-[#D4B485]/20",
							"text-[#D4B485]",
							"focus:border-[#D4B485]/40 focus:ring-[#D4B485]/20",
						)}
					>
						<SelectValue placeholder="选择状态" />
					</SelectTrigger>
					<SelectContent>
						{STATUS_OPTIONS.map((option) => (
							<SelectItem
								key={option.value}
								value={option.value}
								className={cn(
									"text-[#D4B485]",
									"focus:bg-[#D4B485]/10",
									"focus:text-[#E5C9A5]",
								)}
							>
								{option.label}
							</SelectItem>
						))}
					</SelectContent>
				</Select>
				<Button
					className={cn(
						"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
						"text-white",
						"border-none",
						"shadow-[0_8px_16px_-4px_rgba(212,180,133,0.3)]",
						"hover:shadow-[0_12px_20px_-4px_rgba(212,180,133,0.4)]",
						"transition-all duration-200",
					)}
					onClick={handleSearch}
				>
					搜索
				</Button>
				<Button
					variant="outline"
					size="icon"
					className={cn(
						"bg-[#1E2023]/50 border-[#D4B485]/20",
						"text-[#D4B485] hover:text-[#E5C9A5]",
						"hover:bg-[#D4B485]/10",
						"transition duration-200",
						"w-10 h-10",
						"flex items-center justify-center",
					)}
					onClick={handleRefresh}
				>
					<RefreshCcwIcon className="h-4 w-4" />
				</Button>
			</div>

			{/* 表格区域 - 根据状态显示不同内容 */}
			<div
				className={cn(
					"rounded-lg overflow-hidden",
					"bg-gradient-to-br from-[#1A1C1E]/80 via-[#1E2023]/90 to-[#23252A]",
					"ring-1 ring-[#D4B485]/10",
					"shadow-[0_4px_16px_-4px_rgba(0,0,0,0.3)]",
					"backdrop-blur-lg",
				)}
			>
				{loading ? (
					<div className="flex flex-col items-center justify-center py-12">
						<Loader2Icon className="h-8 w-8 animate-spin text-[#D4B485]/60" />
						<span className="mt-4 text-[#D4B485]/60">
							加载中...
						</span>
					</div>
				) : !activations.length ? (
					<div className="flex flex-col items-center justify-center py-12">
						<div
							className={cn(
								"flex h-16 w-16 items-center justify-center rounded-full",
								"bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
								"ring-1 ring-[#D4B485]/20",
								"shadow-[0_4px_12px_-2px_rgba(212,180,133,0.12)]",
							)}
						>
							<span className="text-2xl">📋</span>
						</div>
						<span className="mt-4 text-[#D4B485]/60">
							暂无套餐激活记录
						</span>
					</div>
				) : (
					<Table>
						<TableHeader>
							<TableRow className="hover:bg-[#D4B485]/5 border-b border-[#D4B485]/20">
								<TableHead className="text-[#D4B485]/60 font-medium">
									客户名称
								</TableHead>
								<TableHead className="text-[#D4B485]/60 font-medium">
									套餐名称
								</TableHead>
								<TableHead className="text-[#D4B485]/60 font-medium">
									消耗算力
								</TableHead>
								<TableHead className="text-[#D4B485]/60 font-medium">
									开始时间
								</TableHead>
								<TableHead className="text-[#D4B485]/60 font-medium">
									到期时间
								</TableHead>
								<TableHead className="text-[#D4B485]/60 font-medium">
									状态
								</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{activations.map((activation, index) => (
								<TableRow
									key={activation.id}
									className={cn(
										"transition-colors duration-200",
										"hover:bg-[#D4B485]/5",
										"border-b border-[#D4B485]/10",
										"last:border-0",
										index % 2 === 0 &&
											"bg-[#D4B485]/[0.02]",
									)}
								>
									<TableCell className="font-medium">
										<div className="flex items-center gap-2">
											<div
												className={cn(
													"w-1 h-4 rounded-full",
													ACTIVATION_STATUS_MAP[
														activation.status as keyof typeof ACTIVATION_STATUS_MAP
													].color
														.replace("text-", "bg-")
														.replace("/20", "/40"),
												)}
											/>
											<span className="text-[#D4B485]">
												{activation.consumerName}
											</span>
										</div>
									</TableCell>
									<TableCell className="text-[#D4B485]/80">
										{activation.planName}
									</TableCell>
									<TableCell>
										<div className="flex items-baseline gap-1">
											<span className="text-[#D4B485]/80 font-medium">
												{typeof activation.computingPowerCost ===
												"number"
													? activation.computingPowerCost.toLocaleString()
													: "0"}
											</span>
											<span className="text-[#D4B485]/40 text-sm">
												点
											</span>
										</div>
									</TableCell>
									<TableCell className="text-[#D4B485]/60">
										{new Date(
											activation.startAt,
										).toLocaleDateString()}
									</TableCell>
									<TableCell className="text-[#D4B485]/60">
										{new Date(
											activation.expireAt,
										).toLocaleDateString()}
									</TableCell>
									<TableCell>
										<div
											className={cn(
												"inline-flex items-center rounded-full px-2.5 py-0.5",
												"text-xs font-semibold",
												"transition-colors duration-200",
												"shadow-[0_2px_4px_rgba(0,0,0,0.1)]",
												"border border-current/20",
												ACTIVATION_STATUS_MAP[
													activation.status as keyof typeof ACTIVATION_STATUS_MAP
												].color,
											)}
										>
											{
												ACTIVATION_STATUS_MAP[
													activation.status as keyof typeof ACTIVATION_STATUS_MAP
												].label
											}
										</div>
									</TableCell>
								</TableRow>
							))}
						</TableBody>
					</Table>
				)}
			</div>

			{/* 分页器 - 只在有数据时显示 */}
			{!loading && activations.length > 0 && (
				<div className="flex justify-center mt-6">
					<div
						className={cn(
							"flex items-center gap-2 rounded-lg px-4 py-2",
							"bg-[#1A1C1E]/80",
							"ring-1 ring-[#D4B485]/10",
							"shadow-[0_2px_8px_-2px_rgba(0,0,0,0.3)]",
						)}
					>
						<button
							type="button"
							className={cn(
								"px-3 py-1 rounded-md",
								"text-sm font-medium",
								"transition-colors duration-200",
								"disabled:opacity-50 disabled:cursor-not-allowed",
								currentPage === 1
									? "text-[#D4B485]/40 bg-[#D4B485]/5"
									: "text-[#D4B485]/80 hover:bg-[#D4B485]/10",
							)}
							disabled={currentPage === 1}
							onClick={() => setCurrentPage((p) => p - 1)}
						>
							上一页
						</button>
						<span className="text-[#D4B485]/40 px-2">
							第 {currentPage} 页 / 共{" "}
							{Math.ceil(total / pageSize)} 页
						</span>
						<button
							type="button"
							className={cn(
								"px-3 py-1 rounded-md",
								"text-sm font-medium",
								"transition-colors duration-200",
								"disabled:opacity-50 disabled:cursor-not-allowed",
								currentPage === Math.ceil(total / pageSize)
									? "text-[#D4B485]/40 bg-[#D4B485]/5"
									: "text-[#D4B485]/80 hover:bg-[#D4B485]/10",
							)}
							disabled={
								currentPage === Math.ceil(total / pageSize)
							}
							onClick={() => setCurrentPage((p) => p + 1)}
						>
							下一页
						</button>
					</div>
				</div>
			)}
		</motion.div>
	);
}
