"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { ZapIcon } from "lucide-react";

interface TitleSectionProps {
	title: string;
	subtitle: string;
	description: string;
}

export function TitleSection({
	title,
	subtitle,
	description,
}: TitleSectionProps) {
	return (
		<motion.div
			className="flex items-center gap-4"
			initial={{ opacity: 0, x: -20 }}
			animate={{ opacity: 1, x: 0 }}
			transition={{ duration: 0.3, ease: "easeOut" }}
		>
			<motion.div
				whileHover={{ scale: 1.05 }}
				whileTap={{ scale: 0.95 }}
				transition={{ type: "spring", stiffness: 400, damping: 17 }}
				className={cn(
					"flex h-14 w-14 shrink-0 items-center justify-center",
					"rounded-xl bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
					"ring-1 ring-[#D4B485]/20",
					"shadow-[0_4px_12px_-2px_rgba(212,180,133,0.2)]",
					"group-hover:from-[#D4B485]/15 group-hover:to-[#D4B485]/10",
					"group-hover:ring-[#D4B485]/30",
					"transition-all duration-200",
					"transform-gpu",
				)}
			>
				<ZapIcon className="h-7 w-7 text-[#D4B485]" />
			</motion.div>
			<div className="space-y-1">
				<motion.h2
					initial={{ opacity: 0, y: -10 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ delay: 0.1, duration: 0.2 }}
					className={cn(
						"font-semibold text-3xl",
						zywhFont.className,
						"leading-none",
						"tracking-[0.05em]",
					)}
					style={{
						background:
							"linear-gradient(135deg, rgba(229,201,165,0.95) 0%, rgba(212,180,133,0.9) 50%, rgba(176,137,104,0.85) 100%)",
						WebkitBackgroundClip: "text",
						WebkitTextFillColor: "transparent",
						backgroundClip: "text",
						textShadow:
							"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
						filter: "contrast(1.1) brightness(1.05)",
					}}
				>
					{title}
					<motion.span
						initial={{ opacity: 0, x: -10 }}
						animate={{ opacity: 1, x: 0 }}
						transition={{ delay: 0.2, duration: 0.2 }}
						className="ml-3 font-normal text-base text-[#D4B485]/80 tracking-wide"
					>
						{subtitle}
					</motion.span>
				</motion.h2>
				<motion.p
					initial={{ opacity: 0, y: 10 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ delay: 0.3, duration: 0.2 }}
					className="text-sm leading-relaxed text-[#D4B485]/60"
				>
					{description}
				</motion.p>
			</div>
		</motion.div>
	);
}
