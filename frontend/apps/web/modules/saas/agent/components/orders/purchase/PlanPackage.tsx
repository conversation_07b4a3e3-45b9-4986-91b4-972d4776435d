"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import {
	CheckCircleIcon,
	MinusIcon,
	PlusIcon,
	TagIcon,
	ZapIcon,
} from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { formatAmount } from "./lib/power";
import type { ApiPlanItem, SelectedPlanItem } from "./PurchaseView";

interface PowerPackageProps {
	plans: ApiPlanItem[];
	selectedPlanItems: SelectedPlanItem[];
	onChange: (items: SelectedPlanItem[]) => void;
}

const container = {
	hidden: { opacity: 0 },
	show: {
		opacity: 1,
		transition: {
			staggerChildren: 0.07,
		},
	},
};

const item = {
	hidden: { opacity: 0, y: 20 },
	show: { opacity: 1, y: 0 },
};

export function PowerPackage({
	plans,
	selectedPlanItems,
	onChange,
}: PowerPackageProps) {
	const [isMounted, setIsMounted] = useState(false);

	useEffect(() => {
		setIsMounted(true);
	}, []);

	const quantitiesMap = useMemo(() => {
		return selectedPlanItems.reduce(
			(acc, curItem) => {
				acc[curItem.plan.id] = curItem.quantity;
				return acc;
			},
			{} as Record<string, number>,
		);
	}, [selectedPlanItems]);

	useEffect(() => {
		if (!isMounted) {
			return;
		}

		const handleMouseMove = (e: MouseEvent) => {
			const cards = document.getElementsByClassName("power-card");
			for (const card of Array.from(cards)) {
				const rect = card.getBoundingClientRect();
				const x = e.clientX - rect.left;
				const y = e.clientY - rect.top;
				(card as HTMLElement).style.setProperty("--mouse-x", `${x}px`);
				(card as HTMLElement).style.setProperty("--mouse-y", `${y}px`);
			}
		};

		document.addEventListener("mousemove", handleMouseMove);
		return () => document.removeEventListener("mousemove", handleMouseMove);
	}, [isMounted]);

	const handleQuantityChange = (plan: ApiPlanItem, delta: number) => {
		const currentQuantity = quantitiesMap[plan.id] || 0;
		const newQuantity = Math.max(0, currentQuantity + delta);

		const newSelectedItems = [...selectedPlanItems];
		const existingItemIndex = newSelectedItems.findIndex(
			(it) => it.plan.id === plan.id,
		);

		if (newQuantity === 0) {
			if (existingItemIndex !== -1) {
				newSelectedItems.splice(existingItemIndex, 1);
			}
		} else {
			if (existingItemIndex !== -1) {
				newSelectedItems[existingItemIndex] = {
					...newSelectedItems[existingItemIndex],
					quantity: newQuantity,
				};
			} else {
				newSelectedItems.push({ plan, quantity: newQuantity });
			}
		}
		onChange(newSelectedItems);
	};

	return (
		<motion.div
			variants={container}
			initial="hidden"
			animate="show"
			className="space-y-6"
		>
			<div className="flex items-center justify-between">
				<h3
					className={cn(
						"font-semibold text-lg",
						zywhFont.className,
						"leading-none",
						"tracking-[0.05em]",
						"flex items-center gap-2",
					)}
					style={{
						background:
							"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
						WebkitBackgroundClip: "text",
						WebkitTextFillColor: "transparent",
						backgroundClip: "text",
						textShadow:
							"0 2px 4px rgba(0,0,0,0.15), 0 0 25px rgba(212,180,133,0.25)",
						filter: "contrast(1.15) brightness(1.1)",
					}}
				>
					<div
						className={cn(
							"flex h-8 w-8 items-center justify-center rounded-full",
							"bg-gradient-to-br from-[#D4B485]/15 to-[#D4B485]/10",
							"ring-1 ring-[#D4B485]/30",
							"shadow-[0_4px_12px_-2px_rgba(212,180,133,0.2)]",
						)}
					>
						<ZapIcon className="h-4 w-4 text-[#D4B485]" />
					</div>
					选择套餐规格
				</h3>
			</div>

			<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
				{plans.map((plan) => {
					const quantity = quantitiesMap[plan.id] || 0;
					const agentPriceInYuan = plan.agentPrice / 100;
					const totalPriceForThisPlan = agentPriceInYuan * quantity;
					const originalPriceInYuan = plan.price / 100;
					const savedAmount =
						(originalPriceInYuan - agentPriceInYuan) * quantity;

					return (
						<motion.div
							key={plan.id}
							variants={item}
							whileHover={{
								scale: 1.02,
								transition: {
									type: "spring",
									stiffness: 400,
									damping: 17,
								},
							}}
							whileTap={{ scale: 0.98 }}
						>
							<div
								className={cn(
									"power-card group relative overflow-hidden rounded-lg border-2 transition-all duration-300 h-full flex flex-col",
									"bg-gradient-to-br from-[#1A1C1E] via-[#1E2023] to-[#23252A]",
									"hover:border-[#D4B485] hover:shadow-lg hover:shadow-[#D4B485]/20",
									quantity > 0 &&
										"border-[#D4B485] bg-[#D4B485]/5",
									!quantity && "border-[#D4B485]/20",
								)}
							>
								<div
									className="pointer-events-none absolute inset-0 opacity-0 transition-opacity duration-500 group-hover:opacity-100"
									style={{
										background:
											"radial-gradient(600px circle at var(--mouse-x) var(--mouse-y), rgba(255,255,255,0.07), transparent 40%)",
									}}
								/>
								<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />

								<div className="relative flex flex-col p-6 flex-grow">
									<motion.div
										initial={{ opacity: 0, y: 10 }}
										animate={{ opacity: 1, y: 0 }}
										transition={{ delay: 0.1 }}
										className="space-y-2 mb-4"
									>
										<div className="flex items-center justify-between">
											<h4
												className={cn(
													"font-semibold text-2xl",
													zywhFont.className,
													"leading-none tracking-[0.05em]",
												)}
												style={{
													background:
														"linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.85) 50%, rgba(229,201,165,0.8) 100%)",
													WebkitBackgroundClip:
														"text",
													WebkitTextFillColor:
														"transparent",
												}}
											>
												{plan.name}
											</h4>
											<div className="flex flex-col items-end h-[2em]">
												{plan.discountPrice >
													plan.agentPrice && (
													<Badge
														className={cn(
															"line-through text-[#D4B485]/70 border border-[#D4B485]/30 bg-transparent",
															"text-xs px-2 py-0.5 font-normal",
														)}
													>
														¥
														{formatAmount(
															plan.discountPrice /
																100,
														)}
													</Badge>
												)}
											</div>
										</div>
										{plan.description && (
											<p className="text-[#D4B485]/70 text-sm leading-relaxed opacity-80 group-hover:opacity-100 transition-opacity duration-300">
												{plan.description}
											</p>
										)}
									</motion.div>

									{plan.features &&
										plan.features.length > 0 && (
											<div className="mb-4 space-y-2 text-xs text-[#D4B485]/60">
												<p className="font-medium text-[#D4B485]/80">
													套餐包含:
												</p>
												<ul className="list-none space-y-1 pl-0">
													{plan.features
														.filter(
															(f) => f.enabled,
														)
														.map((feature) => (
															<li
																key={feature.id}
																className="flex items-center"
															>
																<CheckCircleIcon className="h-3.5 w-3.5 mr-1.5 text-green-500/70 shrink-0" />
																{feature.description ||
																	feature.featureCode}
																{feature.dailyLimit
																	? ` (每日: ${feature.dailyLimit})`
																	: ""}
																{feature.monthlyLimit
																	? ` (每月: ${feature.monthlyLimit})`
																	: ""}
															</li>
														))}
												</ul>
											</div>
										)}

									<div className="mt-auto space-y-4">
										<div className="flex items-center justify-between">
											<motion.div
												initial={{ opacity: 0, x: -20 }}
												animate={{ opacity: 1, x: 0 }}
												transition={{ delay: 0.2 }}
												className="flex items-center gap-2"
											>
												<div
													className={cn(
														"flex h-7 w-7 items-center justify-center rounded-full",
														"bg-gradient-to-br from-white/5 to-white/2 ring-1 ring-white/10 text-white/80 group-hover:text-white",
													)}
												>
													<ZapIcon className="h-3.5 w-3.5" />
												</div>
												<div className="space-y-0.5">
													<span className="block text-[#D4B485] text-xs group-hover:text-[#E5C9A5]">
														算力值
													</span>
													<span className="block font-medium text-sm text-[#D4B485]/80 tabular-nums">
														{plan.computingPower.toLocaleString()}{" "}
														点
													</span>
												</div>
											</motion.div>

											<motion.div
												initial={{ opacity: 0, x: 20 }}
												animate={{ opacity: 1, x: 0 }}
												transition={{ delay: 0.3 }}
												className="flex items-center gap-2"
											>
												<Button
													type="button"
													variant="outline"
													size="icon"
													className={cn(
														"h-7 w-7 border-[#D4B485]/20 text-[#D4B485] hover:bg-[#D4B485]/10",
														quantity > 0 &&
															"border-[#D4B485]/40",
													)}
													onClick={() =>
														handleQuantityChange(
															plan,
															-1,
														)
													}
													disabled={quantity === 0}
												>
													<MinusIcon className="h-3 w-3" />
												</Button>
												<span className="min-w-[2ch] text-center font-medium text-sm text-[#D4B485]/80 tabular-nums">
													{quantity}
												</span>
												<Button
													type="button"
													variant="outline"
													size="icon"
													className={cn(
														"h-7 w-7 border-[#D4B485]/20 text-[#D4B485] hover:bg-[#D4B485]/10",
														quantity > 0 &&
															"border-[#D4B485]/40",
													)}
													onClick={() =>
														handleQuantityChange(
															plan,
															1,
														)
													}
												>
													<PlusIcon className="h-3 w-3" />
												</Button>
											</motion.div>
										</div>

										<motion.div
											initial={{ opacity: 0, y: 20 }}
											animate={{ opacity: 1, y: 0 }}
											transition={{ delay: 0.4 }}
											className="flex items-end justify-between pt-2 border-t border-[#D4B485]/10"
										>
											<div className="space-y-0.5">
												<div className="text-[#D4B485]/80 text-xs flex items-center">
													<TagIcon className="h-3 w-3 mr-1 text-[#D4B485]/60" />
													代理价{" "}
													{savedAmount > 0
														? `(省 ${formatAmount(savedAmount)}元)`
														: ""}
												</div>
												<div className="font-medium text-sm text-[#D4B485]/80 tabular-nums">
													¥
													{formatAmount(
														agentPriceInYuan,
													)}{" "}
													/ 个
												</div>
											</div>
											<div
												className={cn(
													"font-semibold text-xl text-[#E5C9A5] tabular-nums tracking-tight",
													zywhFont.className,
												)}
												style={{
													background:
														"linear-gradient(135deg, rgba(229,201,165,0.95) 0%, rgba(212,180,133,0.9) 50%, rgba(176,137,104,0.85) 100%)",
													WebkitBackgroundClip:
														"text",
													WebkitTextFillColor:
														"transparent",
												}}
											>
												¥
												{formatAmount(
													totalPriceForThisPlan,
												)}
											</div>
										</motion.div>
									</div>
								</div>
							</div>
						</motion.div>
					);
				})}
			</div>
		</motion.div>
	);
}
