import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";

// 消费者绑定状态枚举
export enum ConsumerBindingStatus {
	SELF = "self", // 已绑定当前代理
	NONE = "none", // 未绑定任何代理
	OTHER = "other", // 已绑定其他代理
	AGENT = "agent", // 该用户是代理商
}

interface Consumer {
	id: string;
	name: string;
	phone: string;
	status: string;
	currentPlan?: {
		id: string;
		name: string;
		level: string;
		expireAt?: string;
	};
	createdAt: string;
	bindingStatus: ConsumerBindingStatus; // 与当前代理的绑定状态
}

interface ApiSuccessResponse<T> {
	code: number;
	message?: string;
	data: T;
}

interface ApiErrorResponse {
	code: number;
	error: string;
	message: string;
}

type ApiResponse<T> = ApiSuccessResponse<T> | ApiErrorResponse;

interface ConsumersResponseData {
	total: number;
	items: Consumer[];
}

export function useConsumers() {
	const [consumers, setConsumers] = useState<Consumer[]>([]);
	const [total, setTotal] = useState(0);
	const [loading, setLoading] = useState(false);

	const fetchConsumers = useCallback(
		async (params: {
			page?: number;
			pageSize?: number;
			keyword?: string;
			status?: "ACTIVE" | "DISABLED" | "PENDING";
			planLevel?:
				| "TRIAL"
				| "BASIC"
				| "ENHANCED"
				| "ENTERPRISE"
				| "PREMIUM";
			planExpireBefore?: string;
			planExpireAfter?: string;
			searchAll?: boolean;
			bindingStatus?: ConsumerBindingStatus;
		}) => {
			try {
				setLoading(true);
				// logger.info("开始获取消费者列表", { params });

				const searchParams = new URLSearchParams();
				for (const [key, value] of Object.entries(params)) {
					if (value !== undefined) {
						searchParams.append(key, value.toString());
					}
				}

				const res = await apiClient.v1.agent.activate.consumers.$get({
					query: params,
				});
				const result =
					(await res.json()) as ApiResponse<ConsumersResponseData>;

				// logger.info("消费者列表响应:", result);

				if ("error" in result || !result.data) {
					throw new Error(result.message || "获取消费者列表失败");
				}

				setConsumers(result.data.items);
				setTotal(result.data.total);

				return {
					items: result.data.items,
					total: result.data.total,
				};
			} catch (error) {
				// logger.error("获取消费者列表失败", { error });
				toast.error("获取消费者列表失败", {
					description:
						error instanceof Error ? error.message : "请重试",
				});
				return {
					items: [],
					total: 0,
				};
			} finally {
				setLoading(false);
			}
		},
		[],
	);

	return {
		consumers,
		total,
		loading,
		fetchConsumers,
	};
}
