"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Card } from "@ui/components/card";
import { Skeleton } from "@ui/components/skeleton";
import { cn } from "@ui/lib";
import { BadgePercent, Target, Ticket, Users } from "lucide-react";
import { roleNameMap } from "./AllocationModal";

export interface QuotaStats {
	agentId: string;
	agentName: string;
	agentRole: string;
	teamQuota: number;
	usedTeamQuota: number;
	remainingQuota: number;
	totalAllocated: number;
	monthAllocated: number;
}

export interface StatsCardProps {
	stats?: QuotaStats | null;
	loading?: boolean;
	className?: string;
}

export function StatsCard({
	stats,
	loading = false,
	className,
}: StatsCardProps) {
	return (
		<Card
			className={cn(
				"p-6 relative overflow-hidden border-[#D4B485]/20",
				className,
			)}
		>
			{/* 背景装饰 */}
			<div className="absolute inset-0 bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]" />
			<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
			<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />

			{/* 标题区域 */}
			<div className="relative mb-6 flex items-center justify-between">
				<div>
					<h3
						className={cn(
							"text-lg font-medium text-[#D4B485]",
							zywhFont.className,
						)}
					>
						名额统计
					</h3>
					{stats && (
						<div className="mt-1 flex items-center gap-2">
							<span className="text-sm text-[#D4B485]/60">
								{stats.agentName}
							</span>
							<div
								className={cn(
									"rounded-full px-2 py-0.5 text-xs",
									"bg-[#D4B485]/5 text-[#D4B485]",
									"ring-1 ring-[#D4B485]/20",
								)}
							>
								{roleNameMap[stats.agentRole]}
							</div>
						</div>
					)}
				</div>
				<div className="flex items-center gap-2">
					<div className="text-sm text-[#D4B485]/60">代理商ID:</div>
					<div className="text-sm font-mono text-[#D4B485]">
						{stats?.agentId || "-"}
					</div>
				</div>
			</div>

			{/* 统计数据 */}
			<div className="relative grid grid-cols-1 md:grid-cols-4 gap-6">
				{/* 总名额 */}
				<StatItem
					label="总名额"
					value={loading ? null : stats?.teamQuota || 0}
					icon={Ticket}
					iconColor="bg-blue-500/10 text-blue-500"
					description="代理商可分配的总名额数量"
				/>

				{/* 已使用名额 */}
				<StatItem
					label="已使用名额"
					value={loading ? null : stats?.usedTeamQuota || 0}
					icon={Users}
					iconColor="bg-amber-500/10 text-amber-500"
					description="已分配给下级代理商的名额"
				/>

				{/* 剩余名额 */}
				<StatItem
					label="剩余名额"
					value={loading ? null : stats?.remainingQuota || 0}
					icon={BadgePercent}
					iconColor="bg-emerald-500/10 text-emerald-500"
					description="当前可用于分配的名额数量"
					highlight={true}
				/>

				{/* 本月分配 */}
				<StatItem
					label="本月已分配"
					value={loading ? null : stats?.monthAllocated || 0}
					icon={Target}
					iconColor="bg-purple-500/10 text-purple-500"
					description="本月累计分配的名额数量"
				/>
			</div>
		</Card>
	);
}

interface StatItemProps {
	label: string;
	value: number | null;
	icon: React.FC<React.SVGProps<SVGSVGElement>>;
	iconColor: string;
	description: string;
	highlight?: boolean;
}

function StatItem({
	label,
	value,
	icon: Icon,
	iconColor,
	description,
	highlight,
}: StatItemProps) {
	return (
		<div
			className={cn(
				"rounded-lg p-4",
				"bg-[#1E2023]/50",
				"border border-[#D4B485]/20",
				"transition-all duration-200",
				"hover:border-[#D4B485]/40",
				"hover:shadow-[0_0_20px_rgba(212,180,133,0.1)]",
				highlight && "ring-1 ring-[#D4B485]/40",
			)}
		>
			<div className="flex items-center gap-3">
				<div className={cn("rounded-lg p-2", iconColor)}>
					<Icon className="h-5 w-5" />
				</div>
				<div>
					<div className="text-sm text-[#D4B485]/60">{label}</div>
					{value === null ? (
						<Skeleton className="h-7 w-24 mt-1 bg-[#1E2023]/60" />
					) : (
						<div
							className={cn(
								"text-xl font-medium mt-0.5",
								highlight
									? "text-[#D4B485]"
									: "text-[#D4B485]/80",
							)}
						>
							{value.toLocaleString()}
						</div>
					)}
				</div>
			</div>
			<div className="mt-2 text-xs text-[#D4B485]/40">{description}</div>
		</div>
	);
}
