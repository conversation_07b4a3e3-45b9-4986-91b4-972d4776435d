"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Button } from "@ui/components/button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import { cn } from "@ui/lib";
import { Calculator, Ticket, Users } from "lucide-react";
import { useEffect, useId, useState } from "react";
import type { AllocateQuotaParams } from "../hooks/use-allocate-quota";
import type { DirectAgent } from "../hooks/use-direct-agents";

// 角色名称映射
export const roleNameMap: Record<string, string> = {
	ADMIN: "管理员",
	BRANCH: "分公司",
	DIRECTOR: "联席董事",
	PARTNER: "合伙人",
	SALES: "超级个体",
};

// 状态映射配置
const statusConfig = {
	ACTIVE: {
		label: "已激活",
		color: "bg-emerald-500/10 text-emerald-500 ring-emerald-500/30",
	},
	DISABLED: {
		label: "已禁用",
		color: "bg-red-500/10 text-red-500 ring-red-500/30",
	},
	PENDING: {
		label: "待审核",
		color: "bg-amber-500/10 text-amber-500 ring-amber-500/30",
	},
};

interface AllocationModalProps {
	agent: DirectAgent | null;
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onAllocate: (params: AllocateQuotaParams) => Promise<void>;
	remainingQuota: number;
	isAllocating?: boolean;
}

export function AllocationModal({
	agent,
	open,
	onOpenChange,
	onAllocate,
	remainingQuota,
	isAllocating = false,
}: AllocationModalProps) {
	const [quantity, setQuantity] = useState<number>(1);
	const [reason, setReason] = useState<string>("");
	const [error, setError] = useState<string | null>(null);
	const id = useId();

	// 当代理商变化时重置表单
	useEffect(() => {
		if (agent) {
			setQuantity(1);
			setReason("");
			setError(null);
		}
	}, [agent]);

	// 处理数量变化
	const handleQuantityChange = (value: string) => {
		const numValue = Number(value);

		// 检查是否为有效数字
		if (!Number.isInteger(numValue) || numValue < 1) {
			setQuantity(1);
			setError("分配数量必须为大于0的整数");
			return;
		}

		// 检查是否超过剩余名额
		if (numValue > remainingQuota) {
			setQuantity(remainingQuota);
			setError(`您最多可分配 ${remainingQuota} 个名额`);
			return;
		}

		setQuantity(numValue);
		setError(null);
	};

	// 检查是否可以提交
	const canSubmit =
		!error &&
		quantity >= 1 &&
		quantity <= remainingQuota &&
		!isAllocating &&
		agent?.status === "ACTIVE";

	// 处理提交
	const handleSubmit = async () => {
		if (!agent || !canSubmit) {
			return;
		}

		await onAllocate({
			agentId: agent.id,
			quantity,
			reason: reason.trim() || undefined,
		});
	};

	if (!agent) {
		return null;
	}

	// 获取状态配置
	const status = statusConfig[agent.status as keyof typeof statusConfig] || {
		label: "未知状态",
		color: "bg-gray-500/10 text-gray-500 ring-gray-500/30",
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="sm:max-w-[500px] bg-[#1A1C1E] border-[#D4B485]/20">
				<DialogHeader>
					<DialogTitle
						className={cn("text-[#D4B485]", zywhFont.className)}
					>
						分配名额
					</DialogTitle>
					<DialogDescription className="text-[#D4B485]/60">
						为代理商 {agent.name} 分配团队名额
					</DialogDescription>
				</DialogHeader>

				<div className="space-y-6 py-4">
					{/* 代理商信息卡片 */}
					<div className="rounded-lg bg-[#1E2023]/50 p-4 space-y-4">
						<div className="flex items-center gap-3">
							<div className="rounded-lg bg-[#D4B485]/10 p-2">
								<Users className="h-5 w-5 text-[#D4B485]" />
							</div>
							<div>
								<div className="text-base font-medium text-[#D4B485]">
									{agent.name}
								</div>
								<div className="text-sm text-[#D4B485]/60">
									{roleNameMap[agent.role] || agent.role}
								</div>
							</div>
							{/* 添加状态标签 */}
							<div className="ml-auto">
								<span
									className={cn(
										"rounded-full px-2 py-0.5 text-xs",
										"ring-1",
										status.color,
									)}
								>
									{status.label}
								</span>
							</div>
						</div>
						<div className="grid grid-cols-2 gap-4">
							<div>
								<div className="text-sm text-[#D4B485]/60">
									当前名额
								</div>
								<div className="mt-1 text-sm text-[#D4B485]">
									{agent.teamQuota.toLocaleString()} 个
								</div>
							</div>
							<div>
								<div className="text-sm text-[#D4B485]/60">
									已用名额
								</div>
								<div className="mt-1 text-sm text-[#D4B485]">
									{agent.usedTeamQuota.toLocaleString()} 个
								</div>
							</div>
						</div>
					</div>

					{/* 状态警告提示 */}
					{agent.status !== "ACTIVE" && (
						<div className="mt-4 rounded-md bg-red-500/10 p-3 border border-red-500/20">
							<p className="text-sm text-red-500">
								{agent.status === "DISABLED"
									? "该代理商已被禁用，无法进行名额分配"
									: agent.status === "PENDING"
										? "该代理商正在审核中，暂时无法进行名额分配"
										: "该代理商状态异常，无法进行名额分配"}
							</p>
						</div>
					)}

					{/* 分配数量 */}
					<div className="space-y-4">
						<div className="flex items-center justify-between">
							<Label
								htmlFor={`${id}-quantity`}
								className="text-[#D4B485]"
							>
								分配数量
							</Label>
							<div className="text-sm text-[#D4B485]/60">
								可分配: {remainingQuota.toLocaleString()} 个
							</div>
						</div>
						<div className="relative">
							<Ticket className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-[#D4B485]/40" />
							<Input
								id={`${id}-quantity`}
								type="number"
								min={1}
								max={remainingQuota}
								value={quantity}
								onChange={(e) =>
									handleQuantityChange(e.target.value)
								}
								className={cn(
									"pl-9",
									"bg-[#1E2023]/50",
									"border-[#D4B485]/20",
									"text-[#D4B485]",
									"focus:border-[#D4B485]/40",
									"focus:ring-[#D4B485]/20",
									error &&
										"border-red-500/50 focus:border-red-500/50",
								)}
							/>
						</div>
						{error && (
							<div className="text-xs text-red-500">{error}</div>
						)}
					</div>

					{/* 分配原因 */}
					<div className="space-y-4">
						<Label
							htmlFor={`${id}-reason`}
							className="text-[#D4B485]"
						>
							分配原因 (可选)
						</Label>
						<div className="relative">
							<Calculator className="absolute left-3 top-3 h-4 w-4 text-[#D4B485]/40" />
							<Textarea
								id={`${id}-reason`}
								placeholder="例如：新团队拓展奖励"
								value={reason}
								onChange={(e) => setReason(e.target.value)}
								className={cn(
									"pl-9",
									"bg-[#1E2023]/50",
									"border-[#D4B485]/20",
									"text-[#D4B485]",
									"focus:border-[#D4B485]/40",
									"focus:ring-[#D4B485]/20",
									"placeholder:text-[#D4B485]/40",
									"resize-none",
									"h-20",
								)}
							/>
						</div>
					</div>
				</div>

				<DialogFooter>
					<Button
						type="button"
						variant="outline"
						onClick={() => onOpenChange(false)}
						className="border-[#D4B485]/20 text-[#D4B485] hover:bg-[#D4B485]/10"
						disabled={isAllocating}
					>
						取消
					</Button>
					<Button
						type="button"
						onClick={handleSubmit}
						className={cn(
							"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
							"text-white",
							"border-none",
							"shadow-[0_8px_16px_-4px_rgba(212,180,133,0.3)]",
							"hover:shadow-[0_12px_20px_-4px_rgba(212,180,133,0.4)]",
							"transition-all duration-200",
							!canSubmit && "opacity-50 cursor-not-allowed",
						)}
						disabled={!canSubmit}
					>
						{isAllocating ? "分配中..." : "确认分配"}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
