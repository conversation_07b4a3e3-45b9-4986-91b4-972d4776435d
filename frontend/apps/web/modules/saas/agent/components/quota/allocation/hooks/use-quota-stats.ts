import type {
	ApiErrorResponse,
	ApiResponse,
} from "@repo/api/src/routes/v1/agent/quota/types";
import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";

export interface QuotaStats {
	agentId: string;
	agentName: string;
	agentRole: string;
	teamQuota: number;
	usedTeamQuota: number;
	remainingQuota: number;
	totalAllocated: number;
	monthAllocated: number;
}

// 类型守卫函数，判断是否为错误响应
function isErrorResponse<T>(
	response: ApiResponse<T>,
): response is ApiErrorResponse {
	return "error" in response;
}

export function useQuotaStats() {
	const [loading, setLoading] = useState(false);
	const [stats, setStats] = useState<QuotaStats | null>(null);

	const fetchQuotaStats = useCallback(async () => {
		setLoading(true);

		try {
			// logger.info("[Merchant][QuotaStats] 发送请求", {
			// 	endpoint: "/api/v1/agent/merchant/quota/stats",
			// });

			const response = await apiClient.v1.agent.quota.stats.$get();

			// logger.info("[Merchant][QuotaStats] 收到响应", {
			// 	status: response.status,
			// 	statusText: response.statusText,
			// });

			// 解析响应内容
			const responseText = await response.text();

			let result: ApiResponse<QuotaStats>;
			try {
				result = JSON.parse(responseText);
			} catch (_parseError) {
				// logger.error("[Merchant][QuotaStats] 响应解析失败", {
				// 	parseError:
				// 		parseError instanceof Error
				// 			? parseError.message
				// 			: String(parseError),
				// 	responseText: responseText.substring(0, 200),
				// });
				throw new Error("响应解析失败");
			}

			if (response.ok && result.code === 200) {
				if (isErrorResponse(result)) {
					// logger.error("[Merchant][QuotaStats] 响应数据格式不正确", {
					// 	result,
					// 	expectedFields: "data",
					// });
					toast.error("获取名额统计失败: 响应数据格式不正确");
					return;
				}

				const statsData = result.data;
				if (!statsData) {
					// logger.error("[Merchant][QuotaStats] 响应数据为空");
					toast.error("获取名额统计失败: 响应数据为空");
					return;
				}

				// logger.info("[Merchant][QuotaStats] 获取名额统计成功", {
				// 	stats: {
				// 		agentId: statsData.agentId,
				// 		agentName: statsData.agentName,
				// 		teamQuota: statsData.teamQuota,
				// 		usedTeamQuota: statsData.usedTeamQuota,
				// 		remainingQuota: statsData.remainingQuota,
				// 	},
				// });

				setStats(statsData);
			} else {
				const _errorCode = result.code || response.status;
				const errorMsg = isErrorResponse(result)
					? result.message
					: response.statusText;

				// logger.error("[Merchant][QuotaStats] 获取名额统计失败", {
				// 	code: errorCode,
				// 	message: errorMsg,
				// 	result,
				// });

				toast.error(`获取名额统计失败: ${errorMsg}`);
			}
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : String(error);
			const _errorStack =
				error instanceof Error ? error.stack : undefined;

			// logger.error("[Merchant][QuotaStats] 获取名额统计异常", {
			// 	error: errorMessage,
			// 	stack: errorStack,
			// });

			toast.error(`获取名额统计失败: ${errorMessage}`);
		} finally {
			setLoading(false);
		}
	}, []);

	return {
		loading,
		stats,
		fetchQuotaStats,
	};
}
