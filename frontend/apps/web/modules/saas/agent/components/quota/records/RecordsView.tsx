"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";
import { ArrowLeft, FileText } from "lucide-react";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { RecordsFilter } from "./components/RecordsFilter";
import { RecordsList } from "./components/RecordsList";
import type { RecordsQuery } from "./hooks/use-records";
import { useRecords } from "./hooks/use-records";

export interface RecordsViewProps {
	className?: string;
}

export function RecordsView({ className }: RecordsViewProps) {
	const router = useRouter();
	const [query, setQuery] = useState<RecordsQuery>({
		page: 1,
		pageSize: 10,
	});

	const { loading, records, total, fetchRecords } = useRecords();

	// 处理筛选
	const handleFilter = useCallback((filterQuery: RecordsQuery) => {
		setQuery(filterQuery);
	}, []);

	// 处理分页
	const handlePageChange = useCallback((page: number) => {
		setQuery((prev) => ({ ...prev, page }));
	}, []);

	// 处理返回
	const handleGoBack = useCallback(() => {
		router.push("/app/agent/quota/allocation");
	}, [router]);

	// 初始加载和查询变化时获取数据
	useEffect(() => {
		fetchRecords(query);
	}, [fetchRecords, query]);

	return (
		<div className={cn("space-y-8", className)}>
			{/* 标题区域 */}
			<div className="flex items-center justify-between">
				<div className="flex items-center gap-4">
					<Button
						className={cn(
							"flex items-center gap-2 mr-4",
							"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
							"text-white font-medium",
							"rounded-md px-4 py-2",
							"border-none",
							"shadow-[0_8px_16px_-4px_rgba(212,180,133,0.3)]",
							"hover:shadow-[0_12px_20px_-4px_rgba(212,180,133,0.4)]",
							"transition-all duration-200",
						)}
						onClick={handleGoBack}
					>
						<ArrowLeft className="h-4 w-4" />
						<span>返回</span>
					</Button>
					<div
						className={cn(
							"flex h-14 w-14 shrink-0 items-center justify-center",
							"rounded-xl bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
							"ring-1 ring-[#D4B485]/20",
							"shadow-[0_0_0_8px_rgba(212,180,133,0.03)]",
							"transform-gpu transition-transform duration-300",
							"hover:scale-105 hover:shadow-[0_0_0_10px_rgba(212,180,133,0.05)]",
						)}
					>
						<FileText className="h-7 w-7 text-[#D4B485]" />
					</div>
					<div className="space-y-1">
						<h1
							className={cn(
								"text-2xl font-bold",
								zywhFont.className,
							)}
							style={{
								background:
									"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
								WebkitBackgroundClip: "text",
								WebkitTextFillColor: "transparent",
								backgroundClip: "text",
								textShadow:
									"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
							}}
						>
							分配记录
						</h1>
						<p className="text-sm text-[#D4B485]/60">
							查看名额分配历史记录，追踪分配情况
						</p>
					</div>
				</div>
			</div>

			{/* 筛选区域 */}
			<RecordsFilter onFilter={handleFilter} defaultValues={query} />

			{/* 记录列表 */}
			<RecordsList
				loading={loading}
				records={records}
				total={total}
				page={query.page}
				pageSize={query.pageSize}
				onPageChange={handlePageChange}
			/>
		</div>
	);
}
