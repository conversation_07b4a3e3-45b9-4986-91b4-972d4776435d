import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";

// 分配名额请求参数
export interface AllocateQuotaParams {
	agentId: string;
	quantity: number;
	reason?: string;
}

// 分配名额响应
export interface AllocateQuotaResult {
	id: string;
	fromAgentId: string;
	fromAgentName: string;
	fromAgentRole: string;
	toAgentId: string;
	toAgentName: string;
	toAgentRole: string;
	quantity: number;
	status: string;
	createdAt: string;
}

// API响应类型
interface ApiResponse<T> {
	code: number;
	message?: string;
	data?: T;
	error?: string;
}

export function useAllocateQuota() {
	const [loading, setLoading] = useState(false);
	const [result, setResult] = useState<AllocateQuotaResult | null>(null);

	const allocateQuota = useCallback(async (params: AllocateQuotaParams) => {
		setLoading(true);
		// logger.info("[Merchant][AllocateQuota] 开始分配名额", {
		// 	params,
		// 	timestamp: new Date().toLocaleString(),
		// });

		try {
			// logger.info("[Merchant][AllocateQuota] 发送请求", {
			// 	endpoint: "/api/v1/agent/merchant/quota/allocate",
			// 	params,
			// });

			const response = await apiClient.v1.agent.quota.allocate.$post({
				json: {
					toAgentId: params.agentId,
					quantity: params.quantity,
					reason: params.reason,
				},
			});

			// logger.info("[Merchant][AllocateQuota] 收到响应", {
			// 	status: response.status,
			// 	statusText: response.statusText,
			// });

			// 解析响应内容
			const responseText = await response.text();
			// logger.info("[Merchant][AllocateQuota] 响应内容", {
			// 	responseText:
			// 		responseText.substring(0, 500) +
			// 		(responseText.length > 500 ? "..." : ""),
			// });

			let result: ApiResponse<AllocateQuotaResult>;
			try {
				result = JSON.parse(responseText);
			} catch (_parseError) {
				// logger.error("[Merchant][AllocateQuota] 响应解析失败", {
				// 	parseError:
				// 		parseError instanceof Error
				// 			? parseError.message
				// 			: String(parseError),
				// 	responseText: responseText.substring(0, 200),
				// });
				throw new Error("响应解析失败");
			}

			if (response.ok && result.code === 200) {
				if (!result.data) {
					// logger.error(
					// 	"[Merchant][AllocateQuota] 响应数据格式不正确",
					// 	{
					// 		result,
					// 		expectedFields: "data对象",
					// 	},
					// );
					toast.error("分配名额失败: 响应数据格式不正确");
					return null;
				}

				// logger.info("[Merchant][AllocateQuota] 分配名额成功", {
				// 	result: result.data,
				// });

				setResult(result.data);
				toast.success(
					`成功分配 ${params.quantity} 个名额给 ${result.data.toAgentName}`,
				);
				return result.data;
			}
			const _errorCode = result.code || response.status;
			const errorMsg =
				result.message || result.error || response.statusText;

			// logger.error("[Merchant][AllocateQuota] 分配名额失败", {
			// 	code: errorCode,
			// 	message: errorMsg,
			// 	result,
			// });

			toast.error(`分配名额失败: ${errorMsg}`);
			return null;
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : String(error);
			const _errorStack =
				error instanceof Error ? error.stack : undefined;

			// logger.error("[Merchant][AllocateQuota] 分配名额异常", {
			// 	error: errorMessage,
			// 	stack: errorStack,
			// 	params,
			// });

			toast.error(`分配名额失败: ${errorMessage}`);
			return null;
		} finally {
			setLoading(false);
		}
	}, []);

	return {
		loading,
		result,
		allocateQuota,
	};
}
