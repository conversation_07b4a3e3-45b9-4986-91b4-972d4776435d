"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";
import { FileText, Ticket } from "lucide-react";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { AllocationList } from "./components/AllocationList";
import { StatsCard } from "./components/StatsCard";
import { useQuotaStats } from "./hooks/use-quota-stats";

export interface AllocationViewProps {
	className?: string;
}

export function AllocationView({ className }: AllocationViewProps) {
	const router = useRouter();
	const { loading: statsLoading, stats, fetchQuotaStats } = useQuotaStats();
	const [quotaUpdated, setQuotaUpdated] = useState(0);

	useEffect(() => {
		fetchQuotaStats();
	}, [fetchQuotaStats, quotaUpdated]);

	// 处理名额分配成功后的刷新
	const handleQuotaAllocated = useCallback(() => {
		// 触发名额统计更新
		setQuotaUpdated((prev) => prev + 1);
	}, []);

	// 跳转到分配记录页面
	const handleViewRecords = useCallback(() => {
		router.push("/app/agent/quota/records");
	}, [router]);

	return (
		<div className={cn("space-y-8", className)}>
			{/* 标题区域 */}
			<div className="flex items-center justify-between">
				<div className="flex items-center gap-4">
					<div
						className={cn(
							"flex h-14 w-14 shrink-0 items-center justify-center",
							"rounded-xl bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
							"ring-1 ring-[#D4B485]/20",
							"shadow-[0_0_0_8px_rgba(212,180,133,0.03)]",
							"transform-gpu transition-transform duration-300",
							"hover:scale-105 hover:shadow-[0_0_0_10px_rgba(212,180,133,0.05)]",
						)}
					>
						<Ticket className="h-7 w-7 text-[#D4B485]" />
					</div>
					<div className="space-y-1">
						<h1
							className={cn(
								"text-2xl font-bold",
								zywhFont.className,
							)}
							style={{
								background:
									"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
								WebkitBackgroundClip: "text",
								WebkitTextFillColor: "transparent",
								backgroundClip: "text",
								textShadow:
									"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
							}}
						>
							名额分配
						</h1>
						<p className="text-sm text-[#D4B485]/60">
							管理直属代理商名额，协助他们拓展团队规模
						</p>
					</div>
				</div>

				{/* 查看分配记录按钮 */}
				<Button
					className={cn(
						"flex items-center gap-2 px-4 py-2 rounded-md",
						"bg-gradient-to-r from-[#D4B485] to-[#B08968]",
						"text-white font-medium",
						"border-none",
						"shadow-[0_8px_16px_-4px_rgba(212,180,133,0.3)]",
						"hover:shadow-[0_12px_20px_-4px_rgba(212,180,133,0.4)]",
						"transition-all duration-200",
					)}
					onClick={handleViewRecords}
				>
					<FileText className="h-4 w-4" />
					<span>查看分配记录</span>
				</Button>
			</div>

			{/* 名额统计卡片 */}
			<StatsCard stats={stats} loading={statsLoading} />

			{/* 代理商列表 */}
			<AllocationList
				remainingQuota={stats?.remainingQuota || 0}
				onQuotaAllocated={handleQuotaAllocated}
			/>
		</div>
	);
}
