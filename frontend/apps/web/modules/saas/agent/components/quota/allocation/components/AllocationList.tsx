"use client";
import { cn } from "@ui/lib";
import { useEffect, useState } from "react";
import type { AllocateQuotaParams } from "../hooks/use-allocate-quota";
import { useAllocateQuota } from "../hooks/use-allocate-quota";
import type { DirectAgent } from "../hooks/use-direct-agents";
import { useDirectAgents } from "../hooks/use-direct-agents";
import { AgentCard } from "./AgentCard";
import { AllocationModal } from "./AllocationModal";

// 角色名称映射
const _roleNameMap: Record<string, string> = {
	ADMIN: "管理员",
	BRANCH: "分公司",
	DIRECTOR: "联席董事",
	PARTNER: "合伙人",
	SALES: "超级个体",
};

export interface AllocationListProps {
	remainingQuota: number;
	className?: string;
	onQuotaAllocated?: () => void;
}

export function AllocationList({
	remainingQuota,
	className,
	onQuotaAllocated,
}: AllocationListProps) {
	const [selectedAgent, setSelectedAgent] = useState<DirectAgent | null>(
		null,
	);
	const [modalOpen, setModalOpen] = useState(false);
	const [forceUpdate, setForceUpdate] = useState(0);

	const {
		loading: agentsLoading,
		agents,
		total,
		page,
		pageSize,
		setPage,
		fetchAgents,
	} = useDirectAgents();

	const { loading: allocating, allocateQuota } = useAllocateQuota();

	useEffect(() => {
		fetchAgents({
			page: 1,
			pageSize: 5,
		});
	}, [fetchAgents, forceUpdate]);

	// 处理分页
	const handlePageChange = (newPage: number) => {
		setPage(newPage);
		fetchAgents({
			page: newPage,
			pageSize,
		});
	};

	// 处理分配名额
	const handleAllocateQuota = async (params: AllocateQuotaParams) => {
		const result = await allocateQuota(params);
		if (result) {
			setModalOpen(false);
			setForceUpdate((prev) => prev + 1);
			onQuotaAllocated?.();
		}
	};

	// 获取分页数字
	const getPageNumbers = () => {
		const totalPages = Math.ceil(total / pageSize);
		const currentPage = page;
		const delta = 2;
		const range = [];
		const rangeWithDots = [];
		let l = 0;

		for (
			let i = Math.max(1, currentPage - delta);
			i <= Math.min(totalPages, currentPage + delta);
			i++
		) {
			range.push(i);
		}

		if (range[0] > 1) {
			rangeWithDots.push(1);
			if (range[0] > 2) {
				rangeWithDots.push("...");
			}
		}

		for (const i of range) {
			if (l) {
				if (i - l === 2) {
					rangeWithDots.push(l + 1);
				} else if (i - l !== 1) {
					rangeWithDots.push("...");
				}
			}
			rangeWithDots.push(i);
			l = i;
		}

		if (l && l < totalPages) {
			if (l < totalPages - 1) {
				rangeWithDots.push("...");
			}
			rangeWithDots.push(totalPages);
		}

		return rangeWithDots;
	};

	return (
		<div className={cn("space-y-4", className)}>
			{/* 表格区域 */}
			<div
				className={cn(
					"rounded-lg overflow-hidden",
					"bg-gradient-to-br from-[#1A1C1E]/80 via-[#1E2023]/90 to-[#23252A]",
					"ring-1 ring-[#D4B485]/10",
					"shadow-[0_4px_16px_-4px_rgba(0,0,0,0.3)]",
					"backdrop-blur-lg",
				)}
			>
				{agentsLoading ? (
					<div className="flex flex-col items-center justify-center py-12">
						<div className="h-8 w-8 animate-spin text-[#D4B485]/60">
							<svg
								xmlns="http://www.w3.org/2000/svg"
								width="24"
								height="24"
								viewBox="0 0 24 24"
								fill="none"
								stroke="currentColor"
								strokeWidth="2"
								strokeLinecap="round"
								strokeLinejoin="round"
							>
								<title>Loading...</title>
								<line x1="12" y1="2" x2="12" y2="6" />
								<line x1="12" y1="18" x2="12" y2="22" />
								<line x1="4.93" y1="4.93" x2="7.76" y2="7.76" />
								<line
									x1="16.24"
									y1="16.24"
									x2="19.07"
									y2="19.07"
								/>
								<line x1="2" y1="12" x2="6" y2="12" />
								<line x1="18" y1="12" x2="22" y2="12" />
								<line
									x1="4.93"
									y1="19.07"
									x2="7.76"
									y2="16.24"
								/>
								<line
									x1="16.24"
									y1="7.76"
									x2="19.07"
									y2="4.93"
								/>
							</svg>
						</div>
						<span className="mt-4 text-[#D4B485]/60">
							加载中...
						</span>
					</div>
				) : !agents.length ? (
					<div className="flex flex-col items-center justify-center py-12">
						<div
							className={cn(
								"flex h-16 w-16 items-center justify-center rounded-full",
								"bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
								"ring-1 ring-[#D4B485]/20",
								"shadow-[0_4px_12px_-2px_rgba(212,180,133,0.12)]",
							)}
						>
							<span className="text-2xl">👥</span>
						</div>
						<span className="mt-4 text-[#D4B485]/60">
							暂无直属代理商
						</span>
					</div>
				) : (
					<div>
						{/* 列标题栏 */}
						<div
							className={cn(
								"flex items-center gap-6 p-4",
								"border-b border-[#D4B485]/10",
								"bg-[#1E2023]/80",
							)}
						>
							<div className="flex-1">
								<div className="text-sm font-medium text-[#D4B485]/60">
									代理商信息
								</div>
							</div>
							<div className="flex-1">
								<div className="text-sm font-medium text-[#D4B485]/60">
									名额信息
								</div>
							</div>
							<div className="flex-1">
								<div className="text-sm font-medium text-[#D4B485]/60">
									团队规模
								</div>
							</div>
							<div className="flex-1">
								<div className="text-sm font-medium text-[#D4B485]/60">
									状态
								</div>
							</div>
							<div className="flex-1">
								<div className="text-sm font-medium text-[#D4B485]/60">
									加入时间
								</div>
							</div>
							<div className="w-24 text-left">
								<div className="text-sm font-medium text-[#D4B485]/60">
									操作
								</div>
							</div>
						</div>

						{/* 代理商列表 */}
						<div className="divide-y divide-[#D4B485]/10">
							{agents.map((agent) => (
								<AgentCard
									key={agent.id}
									agent={agent}
									onSelect={(agent) => {
										setSelectedAgent(agent);
										setModalOpen(true);
									}}
								/>
							))}
						</div>
					</div>
				)}
			</div>

			{/* 分页器和总记录数 */}
			{!agentsLoading && agents.length > 0 && (
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-2">
						<button
							type="button"
							className={cn(
								"px-3 py-1 rounded-md",
								"text-sm font-medium",
								"transition-colors duration-200",
								"disabled:opacity-50 disabled:cursor-not-allowed",
								page === 1
									? "text-[#D4B485]/40 bg-[#D4B485]/5"
									: "text-[#D4B485]/80 hover:bg-[#D4B485]/10",
							)}
							disabled={page === 1}
							onClick={() => handlePageChange(page - 1)}
						>
							上一页
						</button>
						{getPageNumbers().map((pageNumber) => (
							<button
								key={
									typeof pageNumber === "string"
										? "dots"
										: `page-${pageNumber}`
								}
								type="button"
								className={cn(
									"px-3 py-1 rounded-md",
									"text-sm font-medium",
									"transition-colors duration-200",
									typeof pageNumber === "string"
										? "text-[#D4B485]/40 cursor-default"
										: pageNumber === page
											? "text-[#D4B485] bg-[#D4B485]/10"
											: "text-[#D4B485]/80 hover:bg-[#D4B485]/10",
								)}
								disabled={typeof pageNumber === "string"}
								onClick={() =>
									typeof pageNumber === "number" &&
									handlePageChange(pageNumber)
								}
							>
								{pageNumber}
							</button>
						))}
						<button
							type="button"
							className={cn(
								"px-3 py-1 rounded-md",
								"text-sm font-medium",
								"transition-colors duration-200",
								"disabled:opacity-50 disabled:cursor-not-allowed",
								page === Math.ceil(total / pageSize)
									? "text-[#D4B485]/40 bg-[#D4B485]/5"
									: "text-[#D4B485]/80 hover:bg-[#D4B485]/10",
							)}
							disabled={page === Math.ceil(total / pageSize)}
							onClick={() => handlePageChange(page + 1)}
						>
							下一页
						</button>
					</div>
					<div className="text-sm text-[#D4B485]/60">
						共 {total} 个代理商
					</div>
				</div>
			)}

			{/* 分配对话框 */}
			<AllocationModal
				agent={selectedAgent}
				open={modalOpen}
				onOpenChange={setModalOpen}
				onAllocate={handleAllocateQuota}
				remainingQuota={remainingQuota}
				isAllocating={allocating}
			/>
		</div>
	);
}
