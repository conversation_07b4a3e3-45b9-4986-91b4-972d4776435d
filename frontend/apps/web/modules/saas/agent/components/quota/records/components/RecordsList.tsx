"use client";

import type { <PERSON><PERSON><PERSON> } from "@prisma/client";
import { <PERSON><PERSON> } from "@ui/components/button";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@ui/components/table";
import { cn } from "@ui/lib";
import { format } from "date-fns";
import { ArrowRight } from "lucide-react";
import { useState } from "react";
import {
	AgentRoleColors,
	AgentRoleLabels,
	OrderStatusColors,
} from "../config/records-config";
import type { QuotaRecord } from "../hooks/use-records";
import { RecordDetailDialog } from "./RecordDetailDialog";

interface RecordsListProps {
	loading: boolean;
	records: QuotaRecord[];
	total: number;
	page: number;
	pageSize: number;
	onPageChange: (page: number) => void;
}

export function RecordsList({
	loading,
	records,
	total,
	page,
	pageSize,
	onPageChange,
}: RecordsListProps) {
	const [selectedRecord, setSelectedRecord] = useState<QuotaRecord | null>(
		null,
	);
	const [detailOpen, setDetailOpen] = useState(false);

	return (
		<div className="space-y-4">
			{/* 表格区域 */}
			<div
				className={cn(
					"rounded-lg overflow-hidden",
					"bg-gradient-to-br from-[#1A1C1E]/80 via-[#1E2023]/90 to-[#23252A]",
					"ring-1 ring-[#D4B485]/10",
					"shadow-[0_4px_16px_-4px_rgba(0,0,0,0.3)]",
					"backdrop-blur-lg",
				)}
			>
				{loading ? (
					<div className="flex flex-col items-center justify-center py-12">
						<div className="h-8 w-8 animate-spin text-[#D4B485]/60">
							<svg
								xmlns="http://www.w3.org/2000/svg"
								width="24"
								height="24"
								viewBox="0 0 24 24"
								fill="none"
								stroke="currentColor"
								strokeWidth="2"
								strokeLinecap="round"
								strokeLinejoin="round"
							>
								<title>Loading...</title>
								<line x1="12" y1="2" x2="12" y2="6" />
								<line x1="12" y1="18" x2="12" y2="22" />
								<line x1="4.93" y1="4.93" x2="7.76" y2="7.76" />
								<line
									x1="16.24"
									y1="16.24"
									x2="19.07"
									y2="19.07"
								/>
								<line x1="2" y1="12" x2="6" y2="12" />
								<line x1="18" y1="12" x2="22" y2="12" />
								<line
									x1="4.93"
									y1="19.07"
									x2="7.76"
									y2="16.24"
								/>
								<line
									x1="16.24"
									y1="7.76"
									x2="19.07"
									y2="4.93"
								/>
							</svg>
						</div>
						<span className="mt-4 text-[#D4B485]/60">
							加载中...
						</span>
					</div>
				) : !records.length ? (
					<div className="flex flex-col items-center justify-center py-12">
						<div
							className={cn(
								"flex h-16 w-16 items-center justify-center rounded-full",
								"bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
								"ring-1 ring-[#D4B485]/20",
								"shadow-[0_4px_12px_-2px_rgba(212,180,133,0.12)]",
							)}
						>
							<span className="text-2xl">📋</span>
						</div>
						<span className="mt-4 text-[#D4B485]/60">
							暂无名额分配记录
						</span>
					</div>
				) : (
					<Table>
						<TableHeader>
							<TableRow className="hover:bg-[#D4B485]/5 border-b border-[#D4B485]/20">
								<TableHead className="text-[#D4B485]/60 font-medium">
									记录ID
								</TableHead>
								<TableHead className="text-[#D4B485]/60 font-medium">
									代理商信息
								</TableHead>
								<TableHead className="text-[#D4B485]/60 font-medium">
									分配数量
								</TableHead>
								<TableHead className="text-[#D4B485]/60 font-medium">
									创建时间
								</TableHead>
								<TableHead className="text-[#D4B485]/60 font-medium text-right">
									操作
								</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{records.map((record, index) => (
								<TableRow
									key={`${record.id}-${index}`}
									className={cn(
										"transition-colors duration-200",
										"hover:bg-[#D4B485]/5",
										"border-b border-[#D4B485]/10",
										"last:border-0",
										index % 2 === 0 &&
											"bg-[#D4B485]/[0.02]",
									)}
								>
									<TableCell className="font-medium">
										<div className="flex items-center gap-2">
											<div
												className={cn(
													"w-1 h-4 rounded-full",
													OrderStatusColors[
														record.status ||
															"COMPLETED"
													]
														?.replace(
															"text-",
															"bg-",
														)
														?.replace(
															"/20",
															"/40",
														) ||
														"bg-emerald-500/40",
												)}
											/>
											<span className="text-[#D4B485] font-mono text-sm">
												{record.id}
											</span>
										</div>
									</TableCell>
									<TableCell>
										<div className="flex items-center gap-4">
											{/* 来源代理商 */}
											<div className="flex-1">
												<div className="flex items-center gap-2">
													<div className="text-[#D4B485]/60 text-sm">
														从
													</div>
													<div className="text-[#D4B485]">
														{record.fromAgentName}
													</div>
													<div
														className={cn(
															"rounded-full px-2 py-0.5 text-xs",
															"border border-current/20",
															AgentRoleColors[
																record.fromAgentRole as AgentRole
															] ||
																"text-gray-500",
														)}
													>
														{AgentRoleLabels[
															record.fromAgentRole as AgentRole
														] ||
															record.fromAgentRole}
													</div>
												</div>
												{/* <div className="text-xs text-[#D4B485]/40 mt-0.5">
													{record.fromAgentId}
												</div> */}
											</div>
											{/* 箭头 */}
											<div className="text-[#D4B485]/20">
												<ArrowRight className="h-4 w-4" />
											</div>
											{/* 目标代理商 */}
											<div className="flex-1">
												<div className="flex items-center gap-2">
													<div className="text-[#D4B485]/60 text-sm">
														至
													</div>
													<div className="text-[#D4B485]">
														{record.toAgentName}
													</div>
													<div
														className={cn(
															"rounded-full px-2 py-0.5 text-xs",
															"border border-current/20",
															AgentRoleColors[
																record.toAgentRole as AgentRole
															] ||
																"text-gray-500",
														)}
													>
														{AgentRoleLabels[
															record.toAgentRole as AgentRole
														] || record.toAgentRole}
													</div>
												</div>
												{/* <div className="text-xs text-[#D4B485]/40 mt-0.5">
													{record.toAgentId}
												</div> */}
											</div>
										</div>
									</TableCell>
									<TableCell>
										<div className="flex flex-col">
											<div className="flex items-baseline gap-1">
												<span className="text-[#D4B485] font-medium">
													{record.quantity.toLocaleString()}
												</span>
												<span className="text-[#D4B485]/60 text-sm">
													个
												</span>
											</div>
											{record.unitPrice && (
												<div className="text-xs text-[#D4B485]/40 mt-0.5">
													¥
													{record.unitPrice.toLocaleString()}
													/个
												</div>
											)}
										</div>
									</TableCell>
									<TableCell>
										<div className="flex flex-col">
											<div className="text-[#D4B485]/80">
												{format(
													new Date(record.createdAt),
													"yyyy-MM-dd HH:mm:ss",
												)}
											</div>
											{record.completedAt && (
												<div className="text-xs text-[#D4B485]/40 mt-0.5">
													完成于:{" "}
													{format(
														new Date(
															record.completedAt,
														),
														"HH:mm:ss",
													)}
												</div>
											)}
										</div>
									</TableCell>
									<TableCell className="text-right">
										<Button
											variant="ghost"
											size="sm"
											className={cn(
												"text-[#D4B485]/60 hover:text-[#D4B485]",
												"hover:bg-[#D4B485]/10",
											)}
											onClick={() => {
												setSelectedRecord(record);
												setDetailOpen(true);
											}}
										>
											查看详情
										</Button>
									</TableCell>
								</TableRow>
							))}
						</TableBody>
					</Table>
				)}
			</div>

			{/* 分页器和总记录数 */}
			{!loading && records.length > 0 && (
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-2">
						<button
							type="button"
							className={cn(
								"px-3 py-1 rounded-md",
								"text-sm font-medium",
								"transition-colors duration-200",
								"disabled:opacity-50 disabled:cursor-not-allowed",
								page === 1
									? "text-[#D4B485]/40 bg-[#D4B485]/5"
									: "text-[#D4B485]/80 hover:bg-[#D4B485]/10",
							)}
							disabled={page === 1}
							onClick={() => onPageChange(page - 1)}
						>
							上一页
						</button>
						<span className="text-[#D4B485]/40 px-2">
							第 {page} 页 / 共 {Math.ceil(total / pageSize)} 页
						</span>
						<button
							type="button"
							className={cn(
								"px-3 py-1 rounded-md",
								"text-sm font-medium",
								"transition-colors duration-200",
								"disabled:opacity-50 disabled:cursor-not-allowed",
								page === Math.ceil(total / pageSize)
									? "text-[#D4B485]/40 bg-[#D4B485]/5"
									: "text-[#D4B485]/80 hover:bg-[#D4B485]/10",
							)}
							disabled={page === Math.ceil(total / pageSize)}
							onClick={() => onPageChange(page + 1)}
						>
							下一页
						</button>
					</div>
					<div className="text-sm text-[#D4B485]/60">
						共 {total} 条记录
					</div>
				</div>
			)}

			{/* 详情对话框 */}
			<RecordDetailDialog
				record={selectedRecord}
				open={detailOpen}
				onOpenChange={setDetailOpen}
			/>
		</div>
	);
}
