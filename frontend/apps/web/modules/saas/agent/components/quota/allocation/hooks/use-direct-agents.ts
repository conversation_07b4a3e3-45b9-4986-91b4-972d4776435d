import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";

// 直属代理商类型
export interface DirectAgent {
	id: string;
	name: string;
	role: string;
	email: string;
	phone: string;
	teamQuota: number;
	usedTeamQuota: number;
	remainingQuota: number;
	teamSize: number;
	totalReceived: number;
	monthReceived: number;
	createdAt: string;
	status: string; // 代理商状态: ACTIVE | DISABLED | PENDING
}

// API响应类型
interface ApiResponse<T> {
	code: number;
	message?: string;
	data?: T;
	error?: string;
}

// 查询参数
interface AgentsQuery {
	page: number;
	pageSize: number;
	role?: string;
	keyword?: string;
	sortBy?: string;
	sortOrder?: "asc" | "desc";
}

// 响应数据
interface AgentsResponse {
	total: number;
	items: DirectAgent[];
}

export function useDirectAgents() {
	const [loading, setLoading] = useState(false);
	const [agents, setAgents] = useState<DirectAgent[]>([]);
	const [total, setTotal] = useState(0);
	const [page, setPage] = useState(1);
	const [pageSize, setPageSize] = useState(5);

	const fetchAgents = useCallback(async (query: AgentsQuery) => {
		setLoading(true);
		try {
			// 清空缓存添加时间戳，确保获取最新数据
			const timestamp = Date.now();
			const response = await apiClient.v1.agent.quota[
				"direct-agents"
			].$get({
				query: {
					...query,
					_t: timestamp, // 添加时间戳防止缓存
				},
			});

			const result =
				(await response.json()) as ApiResponse<AgentsResponse>;

			if (result.code === 200 && result.data) {
				// 创建新数组确保状态更新
				setAgents([...result.data.items]);
				setTotal(result.data.total);
				setPage(query.page);
				setPageSize(query.pageSize);

				// 调试日志
				// logger.info("[Merchant] 成功更新代理商列表数据", {
				// 	count: result.data.items.length,
				// 	firstAgent: result.data.items[0]?.id,
				// });
			} else {
				toast.error("获取代理商列表失败");
				// logger.error("[Merchant] 获取代理商列表失败", {
				// 	error: result.message || result.error,
				// });
			}
		} catch (_error) {
			toast.error("获取代理商列表失败");
			// logger.error("[Merchant] 获取代理商列表失败", {
			// 	error: error instanceof Error ? error.message : "未知错误",
			// });
		} finally {
			setLoading(false);
		}
	}, []);

	return {
		loading,
		agents,
		total,
		page,
		pageSize,
		setPage,
		setPageSize,
		fetchAgents,
	};
}
