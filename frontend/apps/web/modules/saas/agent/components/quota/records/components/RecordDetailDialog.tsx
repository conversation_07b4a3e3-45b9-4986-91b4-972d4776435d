"use client";

import type { <PERSON><PERSON><PERSON> } from "@prisma/client";
import {
	<PERSON><PERSON>,
	Dialog<PERSON>ontent,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import { cn } from "@ui/lib";
import { format } from "date-fns";
import { ArrowBigDown } from "lucide-react";
import {
	AgentRoleColors,
	AgentRoleLabels,
	OrderStatusColors,
	OrderStatusLabels,
} from "../config/records-config";
import type { QuotaRecord } from "../hooks/use-records";

interface RecordDetailDialogProps {
	record: QuotaRecord | null;
	open: boolean;
	onOpenChange: (open: boolean) => void;
}

export function RecordDetailDialog({
	record,
	open,
	onOpenChange,
}: RecordDetailDialogProps) {
	if (!record) {
		return null;
	}

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent
				className={cn(
					"bg-gradient-to-br from-[#1A1C1E]/80 via-[#1E2023]/90 to-[#23252A]",
					"ring-1 ring-[#D4B485]/10",
					"shadow-[0_4px_16px_-4px_rgba(0,0,0,0.3)]",
					"backdrop-blur-lg",
				)}
			>
				<DialogHeader>
					<DialogTitle className="text-[#D4B485]">
						名额分配记录详情
					</DialogTitle>
				</DialogHeader>

				<div className="space-y-6">
					{/* 记录ID和状态 */}
					<div className="flex items-center justify-between">
						<div className="flex items-center gap-2">
							<div className="text-[#D4B485]/60">记录ID:</div>
							<div className="text-[#D4B485] font-mono">
								{record.id}
							</div>
						</div>
						<div
							className={cn(
								"rounded-full px-3 py-1",
								"text-sm font-medium",
								"border border-current/20",
								OrderStatusColors[
									record.status || "COMPLETED"
								] || "text-emerald-500/80",
							)}
						>
							{OrderStatusLabels[record.status || "COMPLETED"] ||
								"已完成"}
						</div>
					</div>

					{/* 代理商信息 */}
					<div className="space-y-4">
						{/* 来源代理商 */}
						<div className="space-y-2">
							<div className="text-[#D4B485]/60 text-sm">
								来源代理商
							</div>
							<div
								className={cn(
									"rounded-lg p-4",
									"bg-[#D4B485]/5",
									"ring-1 ring-[#D4B485]/10",
								)}
							>
								<div className="flex items-center gap-2">
									<div className="text-[#D4B485]">
										{record.fromAgentName}
									</div>
									<div
										className={cn(
											"rounded-full px-2 py-0.5 text-xs",
											"border border-current/20",
											AgentRoleColors[
												record.fromAgentRole as AgentRole
											],
										)}
									>
										{
											AgentRoleLabels[
												record.fromAgentRole as AgentRole
											]
										}
									</div>
								</div>
								<div className="text-sm text-[#D4B485]/40 mt-1">
									ID: {record.fromAgentId}
								</div>
							</div>
						</div>

						{/* 箭头 */}
						<div className="flex justify-center">
							<div className="text-[#D4B485]/20">
								<ArrowBigDown className="h-6 w-6" />
							</div>
						</div>

						{/* 目标代理商 */}
						<div className="space-y-2">
							<div className="text-[#D4B485]/60 text-sm">
								目标代理商
							</div>
							<div
								className={cn(
									"rounded-lg p-4",
									"bg-[#D4B485]/5",
									"ring-1 ring-[#D4B485]/10",
								)}
							>
								<div className="flex items-center gap-2">
									<div className="text-[#D4B485]">
										{record.toAgentName}
									</div>
									<div
										className={cn(
											"rounded-full px-2 py-0.5 text-xs",
											"border border-current/20",
											AgentRoleColors[
												record.toAgentRole as AgentRole
											],
										)}
									>
										{
											AgentRoleLabels[
												record.toAgentRole as AgentRole
											]
										}
									</div>
								</div>
								<div className="text-sm text-[#D4B485]/40 mt-1">
									ID: {record.toAgentId}
								</div>
							</div>
						</div>
					</div>

					{/* 分配信息 */}
					<div className="space-y-2">
						<div className="text-[#D4B485]/60 text-sm">
							分配信息
						</div>
						<div
							className={cn(
								"rounded-lg p-4",
								"bg-[#D4B485]/5",
								"ring-1 ring-[#D4B485]/10",
							)}
						>
							<div className="flex items-baseline gap-1">
								<span className="text-[#D4B485] text-lg font-medium">
									{record.quantity.toLocaleString()}
								</span>
								<span className="text-[#D4B485]/60">个</span>
							</div>
							{record.unitPrice && (
								<div className="text-sm text-[#D4B485]/40 mt-1">
									单价: ¥{record.unitPrice.toLocaleString()}
									/个
								</div>
							)}
							{record.amount && (
								<div className="text-sm text-[#D4B485]/40 mt-1">
									总金额: ¥{record.amount.toLocaleString()}
								</div>
							)}
							{record.remark && (
								<div className="text-sm text-[#D4B485]/40 mt-2">
									备注: {record.remark}
								</div>
							)}
						</div>
					</div>

					{/* 时间信息 */}
					<div className="space-y-2">
						<div className="text-[#D4B485]/60 text-sm">
							时间信息
						</div>
						<div
							className={cn(
								"rounded-lg p-4",
								"bg-[#D4B485]/5",
								"ring-1 ring-[#D4B485]/10",
							)}
						>
							<div className="text-[#D4B485]/80">
								创建时间:{" "}
								{format(
									new Date(record.createdAt),
									"yyyy-MM-dd HH:mm:ss",
								)}
							</div>
							{record.completedAt && (
								<div className="text-[#D4B485]/60 mt-1">
									完成时间:{" "}
									{format(
										new Date(record.completedAt),
										"yyyy-MM-dd HH:mm:ss",
									)}
								</div>
							)}
						</div>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
}
