"use client";

import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";
import { format } from "date-fns";
import { motion } from "framer-motion";
import { ChevronRight, Users } from "lucide-react";
import type { DirectAgent } from "../hooks/use-direct-agents";
import { roleNameMap } from "./AllocationModal";

interface AgentCardProps {
	agent: DirectAgent;
	onSelect: (agent: DirectAgent) => void;
}

// 状态映射配置
const statusConfig = {
	ACTIVE: {
		label: "已激活",
		color: "bg-emerald-500/10 text-emerald-500 ring-emerald-500/30",
	},
	DISABLED: {
		label: "已禁用",
		color: "bg-red-500/10 text-red-500 ring-red-500/30",
	},
	PENDING: {
		label: "待审核",
		color: "bg-amber-500/10 text-amber-500 ring-amber-500/30",
	},
};

export function AgentCard({ agent, onSelect }: AgentCardProps) {
	const remainingPercentage = Math.round(
		((agent.teamQuota - agent.usedTeamQuota) / agent.teamQuota) * 100,
	);

	// 判断是否允许分配名额 - 只有激活状态才允许
	const canAllocate = agent.status === "ACTIVE";

	// 获取状态配置
	const status = statusConfig[agent.status as keyof typeof statusConfig] || {
		label: "未知状态",
		color: "bg-gray-500/10 text-gray-500 ring-gray-500/30",
	};

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			className={cn(
				"group relative",
				"rounded-lg",
				"bg-gradient-to-br from-[#1E2023]/80 to-[#1A1C1E]/90",
				"border border-[#D4B485]/10",
				"shadow-sm",
				"transition-all duration-300",
				"hover:shadow-[0_8px_24px_-4px_rgba(212,180,133,0.12)]",
				"hover:border-[#D4B485]/20",
			)}
		>
			{/* 背景装饰 */}
			<div className="absolute inset-0 rounded-lg bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.02)_50%,transparent_75%)] opacity-0 transition-opacity duration-500 group-hover:opacity-100" />

			<div className="relative flex items-center gap-6 p-4">
				{/* 代理商信息 */}
				<div className="flex-1">
					<div className="flex items-center gap-3">
						<div
							className={cn(
								"flex h-10 w-10 items-center justify-center rounded-lg",
								"bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
								"ring-1 ring-[#D4B485]/20",
								"shadow-[0_4px_12px_-2px_rgba(212,180,133,0.12)]",
								"transition-transform duration-300 group-hover:scale-110",
							)}
						>
							<Users className="h-5 w-5 text-[#D4B485]" />
						</div>
						<div>
							<div className="flex items-center gap-2">
								<div className="text-base font-medium text-[#D4B485]">
									{agent.name}
								</div>
								<div
									className={cn(
										"rounded-full px-2 py-0.5 text-xs",
										"bg-[#D4B485]/5 text-[#D4B485]",
										"ring-1 ring-[#D4B485]/20",
									)}
								>
									{roleNameMap[agent.role]}
								</div>
							</div>
						</div>
					</div>
				</div>

				{/* 名额信息 */}
				<div className="flex-1">
					<div className="flex items-baseline gap-1">
						<div className="text-lg font-medium text-[#D4B485]">
							{agent.teamQuota.toLocaleString()}
						</div>
						<div className="text-sm text-[#D4B485]/60">个名额</div>
					</div>
					<div className="mt-2 h-1.5 w-full overflow-hidden rounded-full bg-[#D4B485]/10">
						<div
							className={cn(
								"h-full rounded-full transition-all duration-500",
								remainingPercentage > 50
									? "bg-emerald-500"
									: remainingPercentage > 20
										? "bg-amber-500"
										: "bg-red-500",
							)}
							style={{ width: `${remainingPercentage}%` }}
						/>
					</div>
					<div className="mt-1 flex items-center justify-between text-xs">
						<span className="text-[#D4B485]/60">
							已用: {agent.usedTeamQuota.toLocaleString()}
						</span>
						<span className="text-[#D4B485]/60">
							剩余:{" "}
							{(
								agent.teamQuota - agent.usedTeamQuota
							).toLocaleString()}
						</span>
					</div>
				</div>

				{/* 团队信息 */}
				<div className="flex-1">
					<div className="flex items-baseline gap-1">
						<div className="text-lg font-medium text-[#D4B485]">
							{agent.teamSize.toLocaleString()}
						</div>
						<div className="text-sm text-[#D4B485]/60">
							个直属下级
						</div>
					</div>
					<div className="mt-1 text-xs text-[#D4B485]/60">
						本月接收 {agent.monthReceived.toLocaleString()} 个名额
					</div>
				</div>

				{/* 状态列 - 新增 */}
				<div className="flex-1">
					<div className="flex items-center justify-start">
						<span
							className={cn(
								"rounded-full px-3 py-1 text-sm",
								"ring-1",
								status.color,
							)}
						>
							{status.label}
						</span>
					</div>
				</div>

				{/* 加入时间 */}
				<div className="flex-1">
					<div className="text-sm text-[#D4B485]/80">
						{format(
							new Date(agent.createdAt),
							"yyyy-MM-dd HH:mm:ss",
						)}
					</div>
				</div>

				{/* 操作按钮 */}
				<Button
					variant="ghost"
					size="sm"
					className={cn(
						"flex items-center gap-1.5 px-4",
						"bg-gradient-to-r from-[#D4B485]/10 to-[#B08968]/10",
						"text-[#D4B485] font-medium",
						"border border-[#D4B485]/20",
						"hover:bg-gradient-to-r hover:from-[#D4B485]/20 hover:to-[#B08968]/20",
						"hover:border-[#D4B485]/30",
						"hover:shadow-[0_0_12px_rgba(212,180,133,0.2)]",
						"transition-all duration-300",
						!canAllocate &&
							"opacity-50 cursor-not-allowed hover:bg-none hover:border-[#D4B485]/20 hover:shadow-none",
					)}
					onClick={() => canAllocate && onSelect(agent)}
					disabled={!canAllocate}
				>
					<span>分配名额</span>
					<ChevronRight className="h-4 w-4 animate-pulse" />
				</Button>
			</div>
		</motion.div>
	);
}
