"use client";

import {
	Pagin<PERSON>,
	PaginationContent,
	PaginationEllipsis,
	PaginationItem,
	PaginationLink,
	PaginationNext,
	PaginationPrevious,
} from "@ui/components/pagination";
import { cn } from "@ui/lib";

export interface ConsumerPaginationProps {
	total: number;
	page: number;
	pageSize: number;
	onPageChange: (page: number) => void;
	className?: string;
}

export function ConsumerPagination({
	total,
	page,
	pageSize,
	onPageChange,
	className,
}: ConsumerPaginationProps) {
	const totalPages = Math.ceil(total / pageSize);
	const showEllipsis = totalPages > 7;

	const getPageNumbers = () => {
		const pages: number[] = [];
		if (showEllipsis) {
			if (page <= 4) {
				// 当前页在前面
				for (let i = 1; i <= 5; i++) {
					pages.push(i);
				}
			} else if (page >= totalPages - 3) {
				// 当前页在后面
				for (let i = totalPages - 4; i <= totalPages; i++) {
					pages.push(i);
				}
			} else {
				// 当前页在中间
				for (let i = page - 2; i <= page + 2; i++) {
					pages.push(i);
				}
			}
		} else {
			for (let i = 1; i <= totalPages; i++) {
				pages.push(i);
			}
		}
		return pages;
	};

	return (
		<Pagination className={className}>
			<PaginationContent
				className={cn(
					"p-2 rounded-lg",
					"bg-[#1D1F23]",
					"border border-[#D4B485]/30",
					"backdrop-blur-xl",
					"shadow-[0_8px_32px_-4px_rgba(0,0,0,0.3)]",
					"relative",
					"before:absolute before:inset-0",
					"before:rounded-lg",
					"before:bg-gradient-to-r",
					"before:from-[#E5C9A5]/5 before:via-[#D4B485]/5 before:to-[#B39065]/5",
					"before:opacity-0",
					"hover:before:opacity-100",
					"before:transition-opacity before:duration-500",
				)}
			>
				<PaginationItem>
					<PaginationPrevious
						onClick={() => onPageChange(page - 1)}
						className={cn(
							"bg-gradient-to-br from-[#1A1C1E] via-[#1E2023] to-[#23252A]",
							"border-[#D4B485]/30 text-[#E5C9A5]",
							"hover:border-[#D4B485]/50 hover:bg-[#D4B485]/20",
							"transition-all duration-500",
							page === 1 && "pointer-events-none opacity-50",
						)}
					/>
				</PaginationItem>

				{showEllipsis && page > 4 && (
					<>
						<PaginationItem>
							<PaginationLink
								onClick={() => onPageChange(1)}
								className={cn(
									"bg-gradient-to-br from-[#1A1C1E] via-[#1E2023] to-[#23252A]",
									"border-[#D4B485]/30 text-[#E5C9A5]",
									"hover:border-[#D4B485]/50 hover:bg-[#D4B485]/20",
									"transition-all duration-500",
								)}
							>
								1
							</PaginationLink>
						</PaginationItem>
						<PaginationItem>
							<PaginationEllipsis className="text-[#D4B485]" />
						</PaginationItem>
					</>
				)}

				{getPageNumbers().map((pageNumber) => (
					<PaginationItem key={pageNumber}>
						<PaginationLink
							onClick={() => onPageChange(pageNumber)}
							isActive={page === pageNumber}
							className={cn(
								"bg-gradient-to-br from-[#1A1C1E] via-[#1E2023] to-[#23252A]",
								"border-[#D4B485]/30 text-[#E5C9A5]",
								"hover:border-[#D4B485]/50 hover:bg-[#D4B485]/20",
								"transition-all duration-500",
								page === pageNumber &&
									"border-[#D4B485] bg-[#D4B485]/20",
							)}
						>
							{pageNumber}
						</PaginationLink>
					</PaginationItem>
				))}

				{showEllipsis && page < totalPages - 3 && (
					<>
						<PaginationItem>
							<PaginationEllipsis className="text-[#D4B485]" />
						</PaginationItem>
						<PaginationItem>
							<PaginationLink
								onClick={() => onPageChange(totalPages)}
								className={cn(
									"bg-gradient-to-br from-[#1A1C1E] via-[#1E2023] to-[#23252A]",
									"border-[#D4B485]/30 text-[#E5C9A5]",
									"hover:border-[#D4B485]/50 hover:bg-[#D4B485]/20",
									"transition-all duration-500",
								)}
							>
								{totalPages}
							</PaginationLink>
						</PaginationItem>
					</>
				)}

				<PaginationItem>
					<PaginationNext
						onClick={() => onPageChange(page + 1)}
						className={cn(
							"bg-gradient-to-br from-[#1A1C1E] via-[#1E2023] to-[#23252A]",
							"border-[#D4B485]/30 text-[#E5C9A5]",
							"hover:border-[#D4B485]/50 hover:bg-[#D4B485]/20",
							"transition-all duration-500",
							page === totalPages &&
								"pointer-events-none opacity-50",
						)}
					/>
				</PaginationItem>
			</PaginationContent>
		</Pagination>
	);
}
