"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Badge } from "@ui/components/badge";
import { Card, CardContent } from "@ui/components/card";
import { cn } from "@ui/lib";
import { format } from "date-fns";
import {
	ClockIcon,
	LayersIcon,
	MailIcon,
	PhoneIcon,
	UserIcon,
} from "lucide-react";

export interface ConsumerCardProps {
	id: string;
	name: string;
	phone: string;
	email: string;
	status: string;
	currentPlan: {
		id: string;
		name: string;
		code: string;
		level: string;
		computingPower: number;
		isActive: boolean;
	} | null;
	createdAt: string;
	className?: string;
	onClick?: () => void;
}

export function ConsumerCard({
	name,
	phone,
	email,
	status,
	currentPlan,
	createdAt,
	className,
	onClick,
}: ConsumerCardProps) {
	// 获取状态标签样式
	const getStatusBadgeStyle = (status: string) => {
		switch (status) {
			case "ACTIVE":
				return "bg-emerald-500/20 text-emerald-300 hover:bg-emerald-500/30 border border-emerald-500/30";
			case "DISABLED":
				return "bg-rose-500/20 text-rose-300 hover:bg-rose-500/30 border border-rose-500/30";
			case "PENDING":
				return "bg-amber-500/20 text-amber-300 hover:bg-amber-500/30 border border-amber-500/30";
			default:
				return "bg-[#D4B485]/20 text-[#E5C9A5] hover:bg-[#D4B485]/30 border border-[#D4B485]/30";
		}
	};

	// 获取套餐等级标签样式
	const getPlanLevelBadgeStyle = (level: string) => {
		switch (level) {
			case "TRIAL":
				return "bg-[#D4B485]/20 text-[#E5C9A5] hover:bg-[#D4B485]/30 border border-[#D4B485]/30";
			case "BASIC":
				return "bg-blue-500/20 text-blue-300 hover:bg-blue-500/30 border border-blue-500/30";
			case "ENHANCED":
				return "bg-purple-500/20 text-purple-300 hover:bg-purple-500/30 border border-purple-500/30";
			case "ENTERPRISE":
				return "bg-amber-500/20 text-amber-300 hover:bg-amber-500/30 border border-amber-500/30";
			case "PREMIUM":
				return "bg-rose-500/20 text-rose-300 hover:bg-rose-500/30 border border-rose-500/30";
			default:
				return "bg-[#D4B485]/20 text-[#E5C9A5] hover:bg-[#D4B485]/30 border border-[#D4B485]/30";
		}
	};

	return (
		<Card
			className={cn(
				"group",
				"border-[#D4B485]/30",
				"bg-[#1D1F23]",
				"transition-all duration-500",
				"hover:border-[#D4B485]/50",
				"relative",
				"before:absolute before:inset-0",
				"before:rounded-lg",
				"before:bg-gradient-to-r",
				"before:from-[#E5C9A5]/5 before:via-[#D4B485]/5 before:to-[#B39065]/5",
				"before:opacity-0",
				"hover:before:opacity-100",
				"before:transition-opacity before:duration-500",
				"shadow-[0_8px_32px_-4px_rgba(0,0,0,0.3)]",
				"backdrop-blur-xl",
				"cursor-pointer",
				className,
			)}
			onClick={onClick}
		>
			<CardContent className="p-6">
				<div className="flex items-start justify-between">
					<div className="flex items-start gap-4">
						<div
							className={cn(
								"flex h-10 w-10 items-center justify-center rounded-full",
								"bg-gradient-to-br from-[#1A1C1E] via-[#1E2023] to-[#23252A]",
								"border border-[#D4B485]/30",
								"shadow-[0_4px_12px_-2px_rgba(212,180,133,0.12)]",
								"group-hover:border-[#D4B485]/50",
								"transition-all duration-500",
							)}
						>
							<UserIcon className="h-5 w-5 text-[#E5C9A5]" />
						</div>
						<div className="space-y-1">
							<div className="flex items-center gap-2">
								<h3
									className={cn(
										"font-semibold text-lg",
										zywhFont.className,
										"leading-none",
										"tracking-[0.05em]",
									)}
									style={{
										background:
											"linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.85) 50%, rgba(229,201,165,0.8) 100%)",
										WebkitBackgroundClip: "text",
										WebkitTextFillColor: "transparent",
										backgroundClip: "text",
										textShadow:
											"0 2px 4px rgba(0,0,0,0.3), 0 0 20px rgba(255,255,255,0.1)",
										filter: "contrast(1.2) brightness(1.1)",
									}}
								>
									{name}
								</h3>
								<Badge
									className={cn(
										"font-medium",
										getStatusBadgeStyle(status),
									)}
								>
									{status === "ACTIVE"
										? "正常"
										: status === "DISABLED"
											? "已禁用"
											: status === "PENDING"
												? "待审核"
												: status}
								</Badge>
							</div>
							<div className="flex items-center gap-4 text-sm text-[#D4B485]">
								<div className="flex items-center gap-1">
									<PhoneIcon className="h-3 w-3" />
									<span>{phone}</span>
								</div>
								<div className="flex items-center gap-1">
									<MailIcon className="h-3 w-3" />
									<span>{email}</span>
								</div>
								<div className="flex items-center gap-1">
									<ClockIcon className="h-3 w-3" />
									<span>
										{format(
											new Date(createdAt),
											"yyyy-MM-dd HH:mm",
										)}
									</span>
								</div>
							</div>
						</div>
					</div>

					<div className="flex items-center gap-2">
						{currentPlan ? (
							<div className="flex items-center gap-2">
								<Badge
									className={cn(
										"font-medium",
										getPlanLevelBadgeStyle(
											currentPlan.level,
										),
									)}
								>
									{currentPlan.name}
								</Badge>
								<div className="flex items-center gap-1 text-sm text-[#D4B485]">
									<LayersIcon className="h-3 w-3" />
									<span>
										{currentPlan.computingPower.toLocaleString()}
										点
									</span>
								</div>
							</div>
						) : (
							<Badge className="font-medium bg-[#D4B485]/20 text-[#E5C9A5] border border-[#D4B485]/30">
								未开通
							</Badge>
						)}
					</div>
				</div>
			</CardContent>
		</Card>
	);
}
