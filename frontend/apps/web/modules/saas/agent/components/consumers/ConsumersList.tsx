"use client";

import { qxygFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { apiClient } from "@shared/lib/api-client";
import { cn } from "@ui/lib";
import { Loader2Icon } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { toast } from "sonner";
import { ConsumerCard } from "./components/ConsumersCard";
import { ConsumerPagination } from "./components/ConsumersPagination";
import { ConsumerSearch } from "./components/ConsumersSearch";

interface Consumer {
	id: string;
	name: string;
	phone: string;
	email: string;
	status: string;
	currentPlan: {
		id: string;
		name: string;
		code: string;
		level: string;
		computingPower: number;
		isActive: boolean;
	} | null;
	createdAt: string;
}

interface ConsumerListResponse {
	code: number;
	data?: {
		total: number;
		items: Consumer[];
	};
	error?: string;
	message?: string;
}

export function ConsumerList() {
	const [loading, setLoading] = useState(true);
	const [consumers, setConsumers] = useState<Consumer[]>([]);
	const [total, setTotal] = useState(0);
	const [page, setPage] = useState(1);
	const [pageSize] = useState(10);
	const [searchParams, setSearchParams] = useState<{
		keyword?: string;
		status?: string;
		planLevel?: string;
	}>({});
	const [_selectedConsumerId, setSelectedConsumerId] = useState<string>();
	const [_detailOpen, setDetailOpen] = useState(false);

	// 获取客户列表
	const fetchConsumers = useCallback(async () => {
		try {
			setLoading(true);
			const query: Record<string, string> = {
				page: String(page),
				pageSize: String(pageSize),
			};

			// 添加搜索参数
			if (searchParams.keyword) {
				query.keyword = searchParams.keyword;
			}
			if (searchParams.status) {
				query.status = searchParams.status;
			}
			if (searchParams.planLevel) {
				query.planLevel = searchParams.planLevel;
			}

			try {
				const res = await apiClient.v1.agent.consumers.list.$get({
					query,
				});

				// 检查响应状态
				if (res.status === 401) {
					throw new Error("登录已过期，请重新登录");
				}

				const data: ConsumerListResponse = await res.json();

				if (data.code === 200 && data.data) {
					setConsumers(data.data.items);
					setTotal(data.data.total);
				} else {
					throw new Error(data.message || "获取客户列表失败");
				}
			} catch (fetchError) {
				// 捕获网络错误
				if (
					fetchError instanceof TypeError &&
					fetchError.message === "Failed to fetch"
				) {
					// 网络错误，可能是CORS或服务器未启动
					throw new Error(
						"无法连接到服务器，请检查网络连接或联系管理员",
					);
				}
				throw fetchError;
			}
		} catch (error) {
			toast.error("获取失败", {
				description:
					error instanceof Error ? error.message : "未知错误",
			});
		} finally {
			setLoading(false);
		}
	}, [page, pageSize, searchParams]);

	useEffect(() => {
		fetchConsumers();
	}, [fetchConsumers, page, searchParams]);

	const handleSearch = useCallback(
		(params: { keyword?: string; status?: string; planLevel?: string }) => {
			setSearchParams(params);
			setPage(1); // 重置页码
		},
		[searchParams],
	);

	const handlePageChange = useCallback(
		(newPage: number) => {
			setPage(newPage);
		},
		[page],
	);

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<h1
					className={cn(
						"text-2xl font-semibold",
						"text-[#E5C9A5]",
						qxygFont.className,
						"leading-none",
						"tracking-[0.05em]",
						"px-1",
						"drop-shadow-[0_2px_4px_rgba(229,201,165,0.3)]",
					)}
				>
					客户管理
				</h1>
			</div>

			<ConsumerSearch onSearch={handleSearch} />

			{loading ? (
				<div
					className={cn(
						"p-6 rounded-lg",
						"bg-[#1D1F23]",
						"border border-[#D4B485]/30",
						"backdrop-blur-xl",
						"group",
						"hover:border-[#D4B485]/50",
						"transition-all duration-500",
						"relative",
						"before:absolute before:inset-0",
						"before:rounded-lg",
						"before:bg-gradient-to-r",
						"before:from-[#E5C9A5]/5 before:via-[#D4B485]/5 before:to-[#B39065]/5",
						"before:opacity-0",
						"hover:before:opacity-100",
						"before:transition-opacity before:duration-500",
						"shadow-[0_8px_32px_-4px_rgba(0,0,0,0.3)]",
					)}
				>
					<div className="flex items-center justify-center gap-2 text-sm text-[#D4B485] py-8">
						<Loader2Icon className="h-4 w-4 animate-spin" />
						<span>加载中...</span>
					</div>
				</div>
			) : consumers.length === 0 ? (
				<div
					className={cn(
						"p-6 rounded-lg",
						"bg-[#1D1F23]",
						"border border-[#D4B485]/30",
						"backdrop-blur-xl",
						"group",
						"hover:border-[#D4B485]/50",
						"transition-all duration-500",
						"relative",
						"before:absolute before:inset-0",
						"before:rounded-lg",
						"before:bg-gradient-to-r",
						"before:from-[#E5C9A5]/5 before:via-[#D4B485]/5 before:to-[#B39065]/5",
						"before:opacity-0",
						"hover:before:opacity-100",
						"before:transition-opacity before:duration-500",
						"shadow-[0_8px_32px_-4px_rgba(0,0,0,0.3)]",
					)}
				>
					<div className="text-sm text-[#D4B485] text-center py-8">
						暂无客户数据
					</div>
				</div>
			) : (
				<div className="grid gap-4">
					{consumers.map((consumer) => (
						<ConsumerCard
							key={consumer.id}
							{...consumer}
							onClick={() => {
								setSelectedConsumerId(consumer.id);
								setDetailOpen(true);
							}}
						/>
					))}
				</div>
			)}

			{consumers.length > 0 && (
				<ConsumerPagination
					total={total}
					page={page}
					pageSize={pageSize}
					onPageChange={handlePageChange}
					className="mt-6"
				/>
			)}

			{/* <ConsumerDetailDialog
				open={detailOpen}
				onOpenChange={setDetailOpen}
				consumerId={selectedConsumerId}
			/> */}
		</div>
	);
}
