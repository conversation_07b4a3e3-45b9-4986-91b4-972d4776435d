"use client";

import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { cn } from "@ui/lib";
import { SearchIcon } from "lucide-react";
import { useCallback, useState } from "react";

export interface ConsumerSearchProps {
	onSearch: (params: {
		keyword?: string;
		status?: string;
		planLevel?: string;
	}) => void;
	className?: string;
}

export function ConsumerSearch({ onSearch, className }: ConsumerSearchProps) {
	const [keyword, setKeyword] = useState("");
	const [status, setStatus] = useState<string | undefined>(undefined);
	const [planLevel, setPlanLevel] = useState<string | undefined>(undefined);

	const handleSearch = useCallback(() => {
		onSearch({
			keyword: keyword || undefined,
			status,
			planLevel,
		});
	}, [keyword, status, planLevel, onSearch]);

	const handleReset = useCallback(() => {
		setKeyword("");
		setStatus(undefined);
		setPlanLevel(undefined);
		onSearch({});
	}, [onSearch]);

	return (
		<div
			className={cn(
				"flex items-center gap-4",
				"p-4 rounded-lg",
				"bg-[#1D1F23]",
				"border border-[#D4B485]/30",
				"shadow-[0_8px_32px_-4px_rgba(0,0,0,0.3)]",
				"relative",
				className,
			)}
		>
			<Input
				placeholder="搜索客户名称/邮箱/手机号"
				value={keyword}
				onChange={(e) => setKeyword(e.target.value)}
				className={cn(
					"w-64",
					"bg-gradient-to-br from-[#1A1C1E] via-[#1E2023] to-[#23252A]",
					"border-[#D4B485]/30",
					"text-[#E5C9A5]",
					"placeholder:text-[#D4B485]/50",
					"focus:border-[#D4B485]/50",
					"focus:ring-[#D4B485]/20",
					"transition-all duration-500",
				)}
			/>
			<Select
				value={status}
				onValueChange={(value) =>
					setStatus(value === "ALL_STATUS" ? undefined : value)
				}
			>
				<SelectTrigger
					className={cn(
						"w-40",
						"bg-[#1A1C1E]",
						"border-[#D4B485]/20",
						"text-[#D4B485]",
						"focus:ring-[#D4B485]/40",
					)}
				>
					<SelectValue placeholder="账号状态" />
				</SelectTrigger>
				<SelectContent
					className={cn(
						"bg-[#1A1C1E]",
						"border-[#D4B485]/20",
						"text-[#D4B485]",
					)}
				>
					<SelectItem value="ALL_STATUS">全部状态</SelectItem>
					<SelectItem value="ACTIVE">正常</SelectItem>
					<SelectItem value="DISABLED">已禁用</SelectItem>
					<SelectItem value="PENDING">待审核</SelectItem>
				</SelectContent>
			</Select>
			<Select
				value={planLevel}
				onValueChange={(value) =>
					setPlanLevel(value === "ALL_PLANS" ? undefined : value)
				}
			>
				<SelectTrigger
					className={cn(
						"w-40",
						"bg-[#1A1C1E]",
						"border-[#D4B485]/20",
						"text-[#D4B485]",
						"focus:ring-[#D4B485]/40",
					)}
				>
					<SelectValue placeholder="套餐等级" />
				</SelectTrigger>
				<SelectContent
					className={cn(
						"bg-[#1A1C1E]",
						"border-[#D4B485]/20",
						"text-[#D4B485]",
					)}
				>
					<SelectItem value="ALL_PLANS">全部等级</SelectItem>
					<SelectItem value="TRIAL">试用版</SelectItem>
					<SelectItem value="BASIC">基础版</SelectItem>
					<SelectItem value="ENHANCED">增强版</SelectItem>
					<SelectItem value="ENTERPRISE">企业版</SelectItem>
					<SelectItem value="PREMIUM">尊享版</SelectItem>
				</SelectContent>
			</Select>
			<Button
				variant="outline"
				onClick={handleSearch}
				className={cn(
					"border-[#D4B485]/30 text-[#E5C9A5]",
					"hover:border-[#D4B485]/50 hover:bg-[#D4B485]/20",
					"transition-all duration-500",
				)}
			>
				<SearchIcon className="h-4 w-4 mr-2" />
				搜索
			</Button>
			<Button
				variant="ghost"
				onClick={handleReset}
				className={cn(
					"text-[#D4B485]",
					"hover:bg-[#D4B485]/20",
					"transition-all duration-500",
				)}
			>
				重置
			</Button>
		</div>
	);
}
