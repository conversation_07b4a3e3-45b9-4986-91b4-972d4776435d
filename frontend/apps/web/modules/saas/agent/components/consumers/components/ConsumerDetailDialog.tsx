"use client";

import { apiClient } from "@shared/lib/api-client";
import { Badge } from "@ui/components/badge";
import { cn } from "@ui/lib";
import { format } from "date-fns";
import { AnimatePresence, motion } from "framer-motion";
import { Loader2Icon, XIcon } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { toast } from "sonner";

interface ConsumerDetail {
	id: string;
	name: string;
	phone: string;
	email: string;
	status: string;
	computingPower: number;
	currentPlan: {
		id: string;
		name: string;
		code: string;
		level: string;
		computingPower: number;
		isActive: boolean;
	} | null;
	createdAt: string;
	orders: Array<{
		id: string;
		orderNo: string;
		amount: number;
		status: string;
		createdAt: string;
	}>;
	activations: Array<{
		id: string;
		planName: string;
		status: string;
		startAt: string;
		expireAt: string;
	}>;
}

interface ConsumerDetailResponse {
	code: number;
	data?: ConsumerDetail;
	error?: string;
	message?: string;
}

interface ConsumerDetailDialogProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	consumerId?: string;
}

export function ConsumerDetailDialog({
	open,
	onOpenChange,
	consumerId,
}: ConsumerDetailDialogProps) {
	const [loading, setLoading] = useState(false);
	const [detail, setDetail] = useState<ConsumerDetail | null>(null);

	const fetchDetail = useCallback(async () => {
		if (!consumerId) {
			return;
		}

		try {
			setLoading(true);
			const res = await apiClient.v1.agent.consumers.detail[":id"].$get({
				param: { id: consumerId },
			});
			const data: ConsumerDetailResponse = await res.json();

			if (data.code === 200 && data.data) {
				setDetail(data.data);
			} else {
				throw new Error(data.message || "获取客户详情失败");
			}
		} catch (error) {
			toast.error("获取详情失败", {
				description:
					error instanceof Error ? error.message : "未知错误",
			});
		} finally {
			setLoading(false);
		}
	}, [consumerId]);

	useEffect(() => {
		if (open && consumerId) {
			fetchDetail();
		} else {
			setDetail(null);
		}
	}, [open, consumerId, fetchDetail]);

	// 获取状态标签样式
	const getStatusBadgeStyle = (status: string) => {
		switch (status) {
			case "ACTIVE":
				return "bg-emerald-500/20 text-emerald-300 hover:bg-emerald-500/30 border border-emerald-500/30";
			case "DISABLED":
				return "bg-rose-500/20 text-rose-300 hover:bg-rose-500/30 border border-rose-500/30";
			case "PENDING":
				return "bg-amber-500/20 text-amber-300 hover:bg-amber-500/30 border border-amber-500/30";
			default:
				return "bg-[#D4B485]/20 text-[#E5C9A5] hover:bg-[#D4B485]/30 border border-[#D4B485]/30";
		}
	};

	return (
		<AnimatePresence>
			{open && (
				<motion.div
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					exit={{ opacity: 0 }}
					className="fixed inset-0 z-50 flex items-center justify-center bg-[#1E2023]/80 p-4 backdrop-blur-sm"
				>
					<motion.div
						initial={{ opacity: 0, scale: 0.95 }}
						animate={{ opacity: 1, scale: 1 }}
						exit={{ opacity: 0, scale: 0.95 }}
						className={cn(
							"relative w-full max-w-2xl overflow-hidden rounded-lg",
							"bg-gradient-to-br from-[#1A1C1E] via-[#1E2023] to-[#23252A]",
							"border border-[#D4B485]/30",
							"shadow-[0_8px_32px_-4px_rgba(0,0,0,0.3)]",
							"backdrop-blur-xl",
						)}
					>
						{/* 关闭按钮 */}
						<button
							type="button"
							onClick={() => onOpenChange(false)}
							className={cn(
								"absolute right-4 top-4 rounded-full p-2",
								"text-[#D4B485]/40 hover:text-[#D4B485]/80",
								"transition-colors duration-200",
								"z-20",
							)}
						>
							<XIcon className="h-5 w-5" />
						</button>

						{/* 装饰背景 */}
						<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
						<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />

						{/* 标题 */}
						<div
							className={cn(
								"sticky top-0 z-10",
								"border-b border-[#D4B485]/20",
								"bg-[#1D1F23]",
								"px-6 py-4",
							)}
						>
							<h2 className="text-lg font-medium text-[#E5C9A5]">
								客户详情
							</h2>
						</div>

						{/* 内容区域 */}
						<div
							className={cn(
								"max-h-[calc(85vh-8rem)]",
								"overflow-y-auto",
								"px-6 py-4",
								"scrollbar-thin",
								"scrollbar-track-[#1A1C1E]",
								"scrollbar-thumb-[#D4B485]/10",
								"hover:scrollbar-thumb-[#D4B485]/20",
							)}
						>
							{loading ? (
								<div className="flex items-center justify-center py-8">
									<Loader2Icon className="h-6 w-6 animate-spin text-[#D4B485]" />
								</div>
							) : detail ? (
								<div className="space-y-6">
									<div className="space-y-2">
										<div className="flex items-center justify-between">
											<h3 className="text-lg font-medium text-[#E5C9A5]">
												{detail.name}
											</h3>
											<Badge
												className={cn(
													"font-medium",
													getStatusBadgeStyle(
														detail.status,
													),
												)}
											>
												{detail.status === "ACTIVE"
													? "正常"
													: detail.status ===
															"DISABLED"
														? "已禁用"
														: "待审核"}
											</Badge>
										</div>
										<div className="text-sm text-[#D4B485] space-y-1">
											<p>手机号：{detail.phone}</p>
											<p>邮箱：{detail.email}</p>
											<p>
												注册时间：
												{format(
													new Date(detail.createdAt),
													"yyyy-MM-dd HH:mm:ss",
												)}
											</p>
											<p>
												算力值：
												{detail.computingPower.toLocaleString()}{" "}
												点
											</p>
										</div>
									</div>

									{detail.currentPlan && (
										<div className="space-y-2">
											<h4 className="font-medium text-[#E5C9A5]">
												当前套餐
											</h4>
											<div className="text-sm text-[#D4B485] space-y-1">
												<p>
													套餐名称：
													{detail.currentPlan.name}
												</p>
												<p>
													套餐等级：
													{detail.currentPlan.level}
												</p>
												<p>
													算力值：
													{detail.currentPlan.computingPower.toLocaleString()}{" "}
													点
												</p>
												<p>
													状态：
													{detail.currentPlan.isActive
														? "生效中"
														: "已失效"}
												</p>
											</div>
										</div>
									)}

									{detail.orders.length > 0 && (
										<div className="space-y-2">
											<h4 className="font-medium text-[#E5C9A5]">
												最近订单
											</h4>
											<div className="space-y-2">
												{detail.orders.map((order) => (
													<div
														key={order.id}
														className={cn(
															"text-sm text-[#D4B485] p-2 rounded",
															"border border-[#D4B485]/20",
															"bg-gradient-to-br from-[#1A1C1E] via-[#1E2023] to-[#23252A]",
															"hover:border-[#D4B485]/30",
															"transition-all duration-300",
														)}
													>
														<div className="flex items-center justify-between">
															<span>
																订单号：
																{order.orderNo}
															</span>
															<Badge
																className={cn(
																	"font-medium",
																	order.status ===
																		"PAID"
																		? "bg-emerald-500/20 text-emerald-300 border-emerald-500/30"
																		: order.status ===
																				"PENDING"
																			? "bg-amber-500/20 text-amber-300 border-amber-500/30"
																			: "bg-[#D4B485]/20 text-[#E5C9A5] border-[#D4B485]/30",
																)}
															>
																{order.status ===
																"PAID"
																	? "已支付"
																	: order.status ===
																			"PENDING"
																		? "待支付"
																		: order.status}
															</Badge>
														</div>
														<div className="mt-1 flex items-center justify-between">
															<span>
																金额：¥
																{(
																	order.amount /
																	100
																).toFixed(2)}
															</span>
															<span>
																{format(
																	new Date(
																		order.createdAt,
																	),
																	"yyyy-MM-dd HH:mm",
																)}
															</span>
														</div>
													</div>
												))}
											</div>
										</div>
									)}

									{detail.activations.length > 0 && (
										<div className="space-y-2">
											<h4 className="font-medium text-[#E5C9A5]">
												激活记录
											</h4>
											<div className="space-y-2">
												{detail.activations.map(
													(activation) => (
														<div
															key={activation.id}
															className={cn(
																"text-sm text-[#D4B485] p-2 rounded",
																"border border-[#D4B485]/20",
																"bg-gradient-to-br from-[#1A1C1E] via-[#1E2023] to-[#23252A]",
																"hover:border-[#D4B485]/30",
																"transition-all duration-300",
															)}
														>
															<div className="flex items-center justify-between">
																<span>
																	套餐：
																	{
																		activation.planName
																	}
																</span>
																<Badge
																	className={cn(
																		"font-medium",
																		activation.status ===
																			"ACTIVE"
																			? "bg-emerald-500/20 text-emerald-300 border-emerald-500/30"
																			: activation.status ===
																					"EXPIRED"
																				? "bg-rose-500/20 text-rose-300 border-rose-500/30"
																				: "bg-[#D4B485]/20 text-[#E5C9A5] border-[#D4B485]/30",
																	)}
																>
																	{activation.status ===
																	"ACTIVE"
																		? "生效中"
																		: activation.status ===
																				"EXPIRED"
																			? "已过期"
																			: activation.status}
																</Badge>
															</div>
															<div className="mt-1">
																<div>
																	生效时间：
																	{format(
																		new Date(
																			activation.startAt,
																		),
																		"yyyy-MM-dd HH:mm",
																	)}
																</div>
																<div>
																	过期时间：
																	{format(
																		new Date(
																			activation.expireAt,
																		),
																		"yyyy-MM-dd HH:mm",
																	)}
																</div>
															</div>
														</div>
													),
												)}
											</div>
										</div>
									)}
								</div>
							) : null}
						</div>
					</motion.div>
				</motion.div>
			)}
		</AnimatePresence>
	);
}
