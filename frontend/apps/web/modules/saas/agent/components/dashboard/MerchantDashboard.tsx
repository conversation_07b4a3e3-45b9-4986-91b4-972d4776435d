"use client";
import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { AgentList } from "./components/AgentList";
import { PerformanceChart } from "./components/PerformanceChart";
import { PerformanceSummary } from "./components/PerformanceSummary";
import { StatisticsCards } from "./components/StatisticsCards";
import { useDashboardStats } from "./hooks/useDashboardStats";

type Period = "7d" | "30d" | "6m" | "1y";
const periodLabels: Record<Period, string> = {
	"7d": "最近7天",
	"30d": "最近30天",
	"6m": "最近半年",
	"1y": "最近一年",
};

export function MerchantDashboard() {
	const { loading, stats, fetchStats } = useDashboardStats();
	const [period, setPeriod] = useState<Period>("30d");

	useEffect(() => {
		const now = new Date();
		const endTime = new Date(now);
		const startTime = new Date(now);

		switch (period) {
			case "7d":
				startTime.setDate(now.getDate() - 7);
				break;
			case "30d":
				startTime.setMonth(now.getMonth() - 1);
				break;
			case "6m":
				startTime.setMonth(now.getMonth() - 6);
				break;
			case "1y":
				startTime.setFullYear(now.getFullYear() - 1);
				break;
		}

		fetchStats({
			startTime: startTime.getTime(),
			endTime: endTime.getTime(),
		});
	}, [fetchStats, period]);

	if (loading || !stats) {
		return (
			<div className="flex h-screen w-full items-center justify-center text-white/80">
				数据加载中，请稍候...
			</div>
		);
	}

	return (
		<div className="w-full space-y-6 p-6">
			{/* 标题和操作栏 */}
			<div className="flex items-center justify-between">
				<h1
					className={cn("text-2xl font-bold", zywhFont.className)}
					style={{
						background:
							"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
						WebkitBackgroundClip: "text",
						WebkitTextFillColor: "transparent",
						backgroundClip: "text",
						textShadow:
							"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
					}}
				>
					招商概览
				</h1>
				<div className="flex items-center gap-4">
					<div className="flex items-center gap-2 rounded-lg bg-[#1E2023]/50 p-1">
						{Object.keys(periodLabels).map((p) => (
							<Button
								key={p}
								variant="ghost"
								onClick={() => setPeriod(p as Period)}
								className={cn(
									"h-7 px-3 text-sm font-normal",
									period === p
										? "bg-[#D4B485]/10 text-[#E5C9A5]"
										: "text-[#D4B485]/60 hover:bg-[#D4B485]/5 hover:text-[#D4B485]",
								)}
							>
								{periodLabels[p as Period]}
							</Button>
						))}
					</div>
					<Button
						variant="outline"
						className={cn(
							"h-8 px-3",
							"bg-[#1E2023]/50 border-[#D4B485]/20",
							"text-[#D4B485] hover:text-[#E5C9A5]",
							"hover:bg-[#D4B485]/10",
							"transition duration-200",
						)}
					>
						导出数据
					</Button>
				</div>
			</div>

			{/* 业绩概览卡片 */}
			<div className="grid grid-cols-3 gap-6">
				<PerformanceSummary
					summary={stats.summary}
					comparison={stats.comparison}
				/>
			</div>

			{/* 业绩趋势和代理成员 */}
			<div className="grid grid-cols-3 gap-6">
				{/* 业绩趋势 */}
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					className={cn(
						"col-span-2 rounded-xl p-6",
						"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
						"border border-[#D4B485]/20",
						"group",
						"hover:border-[#D4B485]/40",
						"transition-all duration-500",
						"relative overflow-hidden",
					)}
				>
					{/* 背景装饰 */}
					<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
					<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />

					<div className="relative">
						<h2
							className={cn(
								"text-lg font-semibold",
								zywhFont.className,
							)}
							style={{
								background:
									"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
								WebkitBackgroundClip: "text",
								WebkitTextFillColor: "transparent",
								backgroundClip: "text",
								textShadow:
									"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
							}}
						>
							业绩趋势
						</h2>
						<PerformanceChart trends={stats.trends} />
					</div>
				</motion.div>

				{/* 代理成员 */}
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					className={cn(
						"rounded-xl p-6",
						"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
						"border border-[#D4B485]/20",
						"group",
						"hover:border-[#D4B485]/40",
						"transition-all duration-500",
						"relative",
						"h-[500px]",
					)}
				>
					{/* 背景装饰 */}
					<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
					<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />

					<div className="relative h-full">
						<AgentList />
					</div>
				</motion.div>
			</div>

			{/* 底部统计卡片 */}
			<StatisticsCards summary={stats.summary} />
		</div>
	);
}
