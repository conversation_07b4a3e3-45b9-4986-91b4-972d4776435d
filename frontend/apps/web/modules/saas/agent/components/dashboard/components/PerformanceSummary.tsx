"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { Target, TrendingUp, Users } from "lucide-react";
import type { DashboardStatsData } from "../hooks/useDashboardStats";
import { AnimatedNumber } from "./AnimatedNumber";

interface SummaryCardProps {
	title: string;
	amount: number;
	change: string;
	isPositive: boolean;
	icon: typeof Users;
	compareText: string;
	delay?: number;
}

function SummaryCard({
	title,
	amount,
	change,
	isPositive,
	icon: Icon,
	compareText,
	delay = 0,
}: SummaryCardProps) {
	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			className={cn(
				"rounded-xl p-6",
				"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
				"border border-[#D4B485]/20",
				"group",
				"hover:border-[#D4B485]/40",
				"transition-all duration-500",
				"relative overflow-hidden",
			)}
		>
			{/* 背景装饰 */}
			<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
			<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />

			<div className="relative space-y-3">
				<div className="flex items-center justify-between">
					<h3 className="text-sm font-medium text-[#D4B485]/60">
						{title}
					</h3>
					<Icon className="h-4 w-4 text-[#D4B485]/40" />
				</div>

				<div>
					<AnimatedNumber
						value={amount}
						duration={2}
						delay={delay}
						formatter={(val) => val.toLocaleString()}
						className={cn(
							"text-2xl font-semibold",
							zywhFont.className,
						)}
						style={{
							background:
								"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
							WebkitBackgroundClip: "text",
							WebkitTextFillColor: "transparent",
							backgroundClip: "text",
							textShadow:
								"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
						}}
					/>
					<div className="mt-1 flex items-center gap-2 text-sm">
						<span
							className={cn(
								"font-medium",
								change === "0.0%"
									? "text-[#D4B485]/60"
									: isPositive
										? "text-emerald-500"
										: "text-rose-500",
							)}
						>
							{change === "0.0%"
								? "无变化"
								: `${isPositive ? "+" : ""}${change}`}
						</span>
						<span className="text-[#D4B485]/40">
							对比{compareText}
						</span>
					</div>
				</div>
			</div>
		</motion.div>
	);
}

interface PerformanceSummaryProps {
	summary: DashboardStatsData["summary"];
	comparison: DashboardStatsData["comparison"];
}

export function PerformanceSummary({
	summary,
	comparison,
}: PerformanceSummaryProps) {
	// 格式化变化率显示
	const formatChangeRate = (changeRate: number) => {
		// 处理特殊情况
		if (changeRate === 0) {
			return "0.0%";
		}
		if (!Number.isFinite(changeRate)) {
			return "100.0%"; // 处理从0到正数的情况
		}
		return `${Math.abs(changeRate).toFixed(1)}%`;
	};

	return (
		<>
			<SummaryCard
				title="团队规模"
				amount={summary.teamSize}
				change={formatChangeRate(comparison.teamSize.changeRate)}
				isPositive={comparison.teamSize.changeRate > 0}
				icon={Users}
				compareText="上月"
				delay={0.2}
			/>
			<SummaryCard
				title="本月业绩"
				amount={summary.monthlyRevenue / 100}
				change={formatChangeRate(comparison.monthlyRevenue.changeRate)}
				isPositive={comparison.monthlyRevenue.changeRate > 0}
				icon={Target}
				compareText="上月"
				delay={0.4}
			/>
			<SummaryCard
				title="累计业绩"
				amount={summary.totalRevenue / 100}
				change={formatChangeRate(comparison.totalRevenue.changeRate)}
				isPositive={comparison.totalRevenue.changeRate > 0}
				icon={TrendingUp}
				compareText="去年"
				delay={0.6}
			/>
		</>
	);
}
