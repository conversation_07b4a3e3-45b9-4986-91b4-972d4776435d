"use client";

import type { Agent<PERSON><PERSON> } from "@prisma/client";
import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { Avatar, AvatarFallback, AvatarImage } from "@ui/components/avatar";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { Crown, Medal, Trophy } from "lucide-react";
import { useEffect } from "react";
import { useRanking } from "../hooks/useRanking";
import { AnimatedNumber } from "./AnimatedNumber";

// 角色映射表
const ROLE_LABELS: Record<AgentRole, string> = {
	ADMIN: "管理员",
	BRANCH: "分公司",
	DIRECTOR: "联席董事",
	PARTNER: "合伙人",
	SALES: "超级个体",
};

const RankIcon = ({ rank }: { rank: number }) => {
	const iconClassName = "h-4 w-4";
	const containerClassName = cn(
		"flex items-center justify-center",
		"w-6 h-6 rounded-full",
		"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
		"border border-[#D4B485]",
	);

	switch (rank) {
		case 1:
			return (
				<div className={cn(containerClassName, "border-[#D4B485]")}>
					<Crown className={cn(iconClassName, "text-[#D4B485]")} />
				</div>
			);
		case 2:
			return (
				<div className={cn(containerClassName, "border-[#D4B485]/80")}>
					<Medal className={cn(iconClassName, "text-[#D4B485]/80")} />
				</div>
			);
		case 3:
			return (
				<div className={cn(containerClassName, "border-[#D4B485]/60")}>
					<Trophy
						className={cn(iconClassName, "text-[#D4B485]/60")}
					/>
				</div>
			);
		default:
			return (
				<div
					className={cn(
						"flex items-center justify-center",
						"w-6 h-6 rounded-full",
						"bg-[#1E2023]",
						"border border-[#D4B485]/20",
					)}
				>
					<span className="text-xs font-medium text-[#D4B485]/40">
						{rank}
					</span>
				</div>
			);
	}
};

export function AgentList() {
	const { loading, rankingData, fetchRanking } = useRanking();

	useEffect(() => {
		fetchRanking({ limit: 10 });
	}, [fetchRanking]);

	// 如果正在加载或没有数据，显示占位内容
	if (loading || !rankingData) {
		return (
			<div className="h-full flex flex-col">
				<h2
					className={cn(
						"text-base font-semibold",
						zywhFont.className,
					)}
					style={{
						background:
							"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
						WebkitBackgroundClip: "text",
						WebkitTextFillColor: "transparent",
						backgroundClip: "text",
						textShadow:
							"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
					}}
				>
					业绩排行
				</h2>
				<div className="flex-1 flex items-center justify-center text-[#D4B485]/60">
					{loading ? "加载中..." : "暂无数据"}
				</div>
			</div>
		);
	}

	return (
		<div className="h-full flex flex-col">
			<h2
				className={cn("text-base font-semibold", zywhFont.className)}
				style={{
					background:
						"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
					WebkitBackgroundClip: "text",
					WebkitTextFillColor: "transparent",
					backgroundClip: "text",
					textShadow:
						"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
				}}
			>
				业绩排行
			</h2>

			<div
				className={cn(
					"flex-1 overflow-y-auto space-y-2 pr-2 mt-3",
					// 隐藏滚动条但保持功能
					"scrollbar-none",
					"[&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]",
				)}
			>
				{rankingData.ranking.map((agent, index) => (
					<motion.div
						key={agent.id}
						initial={{ opacity: 0, x: 20 }}
						animate={{ opacity: 1, x: 0 }}
						transition={{ delay: index * 0.1 }}
						className={cn(
							"flex items-center justify-between",
							"rounded-lg p-2.5",
							"bg-gradient-to-r from-[#1E2023] to-[#1A1C1E]",
							"border border-[#D4B485]/20",
							"group",
							"hover:border-[#D4B485]/40",
							"transition-all duration-200",
							"relative",
							agent.rank <= 3 && "bg-[#1E2023]",
						)}
					>
						{/* 左侧内容 */}
						<div className="flex items-center gap-2">
							{/* 排名图标 */}
							<RankIcon rank={agent.rank} />

							{/* 头像和信息 */}
							<div className="flex items-center gap-2">
								<Avatar
									className={cn(
										"h-8 w-8 rounded-full",
										"border",
										agent.rank === 1 && "border-[#D4B485]",
										agent.rank === 2 &&
											"border-[#D4B485]/80",
										agent.rank === 3 &&
											"border-[#D4B485]/60",
										agent.rank > 3 && "border-[#D4B485]/20",
									)}
								>
									<AvatarImage
										src={agent.avatar || undefined}
										alt={agent.name}
									/>
									<AvatarFallback
										className={cn(
											"text-[#1E2023] font-semibold text-xs",
											"bg-gradient-to-br from-[#D4B485] to-[#B08968]",
										)}
									>
										{agent.name.slice(0, 1)}
									</AvatarFallback>
								</Avatar>
								<div className="min-w-0">
									<div className="flex items-center gap-1.5 truncate">
										<span
											className={cn(
												"text-sm font-medium truncate",
												agent.rank === 1 &&
													"text-[#D4B485]",
												agent.rank === 2 &&
													"text-[#D4B485]/80",
												agent.rank === 3 &&
													"text-[#D4B485]/60",
												agent.rank > 3 &&
													"text-[#D4B485]/40",
											)}
										>
											{agent.name}
										</span>
										<span
											className={cn(
												"rounded-full px-1.5 py-0.5 text-[10px]",
												"bg-[#D4B485]/10",
												"text-[#D4B485]/60",
												"whitespace-nowrap",
											)}
										>
											{ROLE_LABELS[
												agent.role as AgentRole
											] || agent.role}
										</span>
									</div>
									<div className="text-[10px] text-[#D4B485]/40">
										本月业绩
									</div>
								</div>
							</div>
						</div>

						{/* 业绩数据 */}
						<div className="text-right">
							<AnimatedNumber
								value={agent.performance / 100}
								duration={2}
								delay={index * 0.1}
								formatter={(val) => `¥${val.toLocaleString()}`}
								className={cn(
									"text-sm font-medium",
									agent.rank === 1 && "text-[#D4B485]",
									agent.rank === 2 && "text-[#D4B485]/80",
									agent.rank === 3 && "text-[#D4B485]/60",
									agent.rank > 3 && "text-[#D4B485]/40",
								)}
							/>
						</div>
					</motion.div>
				))}
			</div>
		</div>
	);
}
