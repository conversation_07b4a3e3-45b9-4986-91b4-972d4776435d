import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";

export interface TrendDataPoint {
	date: string;
	value: number;
}

export interface ComparisonData {
	current: number;
	previous: number;
	change: number;
	changeRate: number;
}

export interface DashboardStatsData {
	trends: {
		newAgents: TrendDataPoint[];
		periodRevenue: TrendDataPoint[];
		periodPlanSales: TrendDataPoint[];
	};
	summary: {
		teamSize: number;
		monthlyNewCustomers: number;
		monthlyNewAgents: number;
		activeAgents: number;
		totalCustomers: number;
		quotaStats: {
			purchased: number;
			allocated: number;
		};
		totalExpenses: number;
		totalRevenue: number;
		monthlyRevenue: number;
	};
	comparison: {
		teamSize: ComparisonData;
		monthlyRevenue: ComparisonData;
		totalRevenue: ComparisonData;
	};
	timeRange: {
		startTime: string;
		endTime: string;
	};
}

export function useDashboardStats() {
	const [loading, setLoading] = useState(true);
	const [stats, setStats] = useState<DashboardStatsData | null>(null);

	const fetchStats = useCallback(
		async (params?: { startTime?: number; endTime?: number }) => {
			setLoading(true);
			// logger.info("[Merchant][Dashboard] 开始获取仪表盘统计数据", {
			// 	params,
			// 	timestamp: new Date().toLocaleString(),
			// });

			try {
				const queryParams: Record<string, string> = {};
				if (params?.startTime) {
					queryParams.startTime = String(params.startTime);
				}
				if (params?.endTime) {
					queryParams.endTime = String(params.endTime);
				}

				const response = await apiClient.v1.agent.performance.overview[
					"dashboard-stats"
				].$get({
					query: queryParams,
				});

				const result = await response.json();

				if (response.ok && "data" in result && result.data) {
					setStats(result.data);
					// logger.info("[Merchant][Dashboard] 获取仪表盘数据成功");
				} else {
					const errorMsg = result.message || response.statusText;
					// logger.error("[Merchant][Dashboard] 获取仪表盘数据失败", {
					// 	code: result.code || response.status,
					// 	message: errorMsg,
					// 	params,
					// });
					toast.error(`获取仪表盘数据失败: ${errorMsg}`);
				}
			} catch (error) {
				const errorMessage =
					error instanceof Error ? error.message : String(error);
				// logger.error("[Merchant][Dashboard] 获取仪表盘数据异常", {
				// 	error: errorMessage,
				// 	params,
				// });
				toast.error(`获取仪表盘数据失败: ${errorMessage}`);
			} finally {
				setLoading(false);
			}
		},
		[],
	);

	return {
		loading,
		stats,
		fetchStats,
	};
}
