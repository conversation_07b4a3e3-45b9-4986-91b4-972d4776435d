import { animate } from "framer-motion";
import { useEffect, useState } from "react";

export function useAnimatedNumber(value: number, duration = 1, delay = 0) {
	const [displayValue, setDisplayValue] = useState(0);

	useEffect(() => {
		const controls = animate(0, value, {
			duration,
			delay,
			onUpdate: (val) => {
				setDisplayValue(val);
			},
			ease: "easeOut",
		});

		return () => controls.stop();
	}, [value, duration, delay]);

	return displayValue;
}
