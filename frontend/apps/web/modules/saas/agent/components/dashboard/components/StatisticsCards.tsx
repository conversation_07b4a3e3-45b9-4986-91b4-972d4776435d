"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import {
	ArrowLeftRight,
	Coins,
	TrendingUp,
	UserPlus,
	Users,
} from "lucide-react";
import type { DashboardStatsData } from "../hooks/useDashboardStats";
import { AnimatedNumber } from "./AnimatedNumber";

interface StatCardProps {
	title: string;
	value: number;
	icon: typeof TrendingUp;
	description: string;
	delay?: number;
	formatter?: (value: number) => string;
}

function StatCard({
	title,
	value,
	icon: Icon,
	description,
	delay = 0,
	formatter = (val) => val.toLocaleString(),
}: StatCardProps) {
	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ delay }}
			className={cn(
				"rounded-xl p-4",
				"bg-gradient-to-br from-[#1E2023] to-[#1A1C1E]",
				"border border-[#D4B485]/20",
				"group",
				"hover:border-[#D4B485]/40",
				"transition-all duration-500",
				"relative overflow-hidden",
			)}
		>
			{/* 背景装饰 */}
			<div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.03)_50%,transparent_75%)] bg-[length:500px_500px] animate-[gradient_20s_linear_infinite]" />
			<div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_-20%,rgba(255,255,255,0.05),transparent_70%)]" />

			<div className="relative space-y-2">
				<div className="flex items-center justify-between">
					<h3 className="text-sm font-medium text-[#D4B485]/60">
						{title}
					</h3>
					<Icon className="h-4 w-4 text-[#D4B485]/40" />
				</div>
				<AnimatedNumber
					value={value}
					duration={2}
					delay={delay}
					formatter={formatter}
					className={cn("text-2xl font-semibold", zywhFont.className)}
					style={{
						background:
							"linear-gradient(135deg, #F8D8B9 0%, #E5C9A5 35%, #D4B485 60%, #B08968 100%)",
						WebkitBackgroundClip: "text",
						WebkitTextFillColor: "transparent",
						backgroundClip: "text",
						textShadow:
							"0 2px 4px rgba(0,0,0,0.2), 0 0 20px rgba(212,180,133,0.15)",
					}}
				/>
				<p className="text-sm text-[#D4B485]/40">{description}</p>
			</div>
		</motion.div>
	);
}

interface StatisticsCardsProps {
	summary: DashboardStatsData["summary"];
}

export function StatisticsCards({ summary }: StatisticsCardsProps) {
	return (
		<div className="grid grid-cols-3 gap-4">
			<StatCard
				title="本月新增代理"
				value={summary.monthlyNewAgents}
				icon={UserPlus}
				description="本月新发展的下级代理"
				delay={0.2}
			/>
			<StatCard
				title="活跃代理"
				value={summary.activeAgents}
				icon={TrendingUp}
				description={`占总代理商 ${
					summary.teamSize > 0
						? (
								(summary.activeAgents / summary.teamSize) *
								100
							).toFixed(1)
						: 0
				}%`}
				delay={0.4}
			/>
			<StatCard
				title="名额分配"
				value={summary.quotaStats.allocated}
				icon={ArrowLeftRight}
				description={`已分配 ${summary.quotaStats.allocated} 个`}
				delay={0.6}
			/>
			<StatCard
				title="总客户数"
				value={summary.totalCustomers}
				icon={Users}
				description="历史累计绑定客户"
				delay={0.5}
			/>
			<StatCard
				title="本月新增客户"
				value={summary.monthlyNewCustomers}
				icon={UserPlus}
				description="近30天内绑定的客户"
				delay={0.3}
			/>
			<StatCard
				title="总支出"
				value={summary.totalExpenses / 100}
				icon={Coins}
				description="本月已结算金额"
				delay={0.7}
				formatter={(val) => `¥ ${val.toLocaleString()}`}
			/>
		</div>
	);
}
