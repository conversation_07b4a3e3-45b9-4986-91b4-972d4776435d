import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";

export interface RankingAgentItem {
	id: string;
	name: string;
	role: string;
	avatar?: string | null;
	performance: number; // 本月总业绩（分）
	rank: number; // 排名
	// 业绩构成明细
	breakdown: {
		agentFeeRevenue: number; // 代理费收入
		quotaMarginRevenue: number; // 名额分配收入
		planMarginRevenue: number; // 套餐销售收入
		// 实时数据（当天到现在）
		realtimeRevenue: number; // 实时收入
	};
}

export interface RankingData {
	ranking: RankingAgentItem[];
	totalCount: number;
	updateTime: string;
}

export function useRanking() {
	const [loading, setLoading] = useState(true);
	const [rankingData, setRankingData] = useState<RankingData | null>(null);

	const fetchRanking = useCallback(async (params?: { limit?: number }) => {
		setLoading(true);
		// logger.info("[Merchant][Ranking] 开始获取业绩排行榜", {
		// 	params,
		// 	timestamp: new Date().toLocaleString(),
		// });

		try {
			const queryParams: Record<string, string> = {};
			if (params?.limit) {
				queryParams.limit = String(params.limit);
			}

			const response =
				await apiClient.v1.agent.performance.overview.ranking.$get({
					query: queryParams,
				});

			const result = await response.json();

			if (response.ok && "data" in result && result.data) {
				setRankingData(result.data);
				// logger.info("[Merchant][Ranking] 获取业绩排行榜成功", {
				// 	totalCount: result.data.totalCount,
				// 	rankingCount: result.data.ranking.length,
				// });
			} else {
				const errorMsg = (result as any).message || response.statusText;
				// logger.error("[Merchant][Ranking] 获取业绩排行榜失败", {
				// 	code: result.code || response.status,
				// 	message: errorMsg,
				// 	params,
				// });
				toast.error(`获取业绩排行榜失败: ${errorMsg}`);
			}
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : String(error);
			// logger.error("[Merchant][Ranking] 获取业绩排行榜异常", {
			// 	error: errorMessage,
			// 	params,
			// });
			toast.error(`获取业绩排行榜失败: ${errorMessage}`);
		} finally {
			setLoading(false);
		}
	}, []);

	return {
		loading,
		rankingData,
		fetchRanking,
	};
}
