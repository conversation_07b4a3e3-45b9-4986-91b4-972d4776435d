"use client";
import {
	<PERSON>,
	<PERSON><PERSON>hart,
	CartesianGrid,
	ResponsiveContainer,
	Tooltip,
	XAxis,
	YAxis,
} from "recharts";
import type { DashboardStatsData } from "../hooks/useDashboardStats";

interface PerformanceChartProps {
	trends: DashboardStatsData["trends"];
}

export function PerformanceChart({ trends }: PerformanceChartProps) {
	// 解析不同格式的日期字符串
	const formatDateLabel = (dateStr: string): string => {
		// 如果是周范围格式: "2025-06-02~2025-06-08"
		if (dateStr.includes("~")) {
			const [startDate, endDate] = dateStr.split("~");
			const start = new Date(startDate);
			const end = new Date(endDate);
			const startFormatted = start.toLocaleDateString("zh-CN", {
				month: "short",
				day: "numeric",
			});
			const endFormatted = end.toLocaleDateString("zh-CN", {
				month: "short",
				day: "numeric",
			});
			return `${startFormatted}~${endFormatted}`;
		}

		// 如果是月份格式: "2025-06"
		if (dateStr.match(/^\d{4}-\d{2}$/)) {
			const [year, month] = dateStr.split("-");
			const date = new Date(
				Number.parseInt(year),
				Number.parseInt(month) - 1,
				1,
			);
			return date.toLocaleDateString("zh-CN", {
				year: "numeric",
				month: "short",
			});
		}

		// 如果是标准日期格式: "2025-06-02"
		if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
			const date = new Date(dateStr);
			return date.toLocaleDateString("zh-CN", {
				month: "short",
				day: "numeric",
			});
		}

		// 兜底处理
		return dateStr;
	};

	const chartData = trends.periodRevenue.map((revPoint, index) => {
		const newAgentsPoint = trends.newAgents[index] || { value: 0 };
		const planSalesPoint = trends.periodPlanSales[index] || { value: 0 };
		return {
			month: formatDateLabel(revPoint.date),
			当期收入: revPoint.value / 100,
			新增代理: newAgentsPoint.value,
			套餐销售额: planSalesPoint.value / 100,
		};
	});

	return (
		<div className="space-y-4">
			{/* 图表区域 */}
			<div className="h-[300px] w-full pt-4">
				<ResponsiveContainer width="100%" height="100%">
					<BarChart data={chartData}>
						<CartesianGrid
							strokeDasharray="3 3"
							stroke="#D4B48520"
							vertical={false}
						/>
						<XAxis
							dataKey="month"
							axisLine={false}
							tickLine={false}
							tick={{ fill: "#D4B485", fontSize: 12 }}
						/>
						<YAxis
							axisLine={false}
							tickLine={false}
							tick={{ fill: "#D4B485", fontSize: 12 }}
							yAxisId="left"
							tickFormatter={(value) =>
								`¥${(value / 1000).toFixed(0)}k`
							}
						/>
						<YAxis
							axisLine={false}
							tickLine={false}
							tick={{ fill: "#D4B485", fontSize: 12 }}
							yAxisId="right"
							orientation="right"
							tickFormatter={(value) => value.toLocaleString()}
						/>
						<Tooltip
							contentStyle={{
								backgroundColor: "#1E2023",
								border: "1px solid #D4B48520",
								borderRadius: "8px",
							}}
							labelStyle={{ color: "#E5C9A5" }}
							itemStyle={{ color: "#D4B485" }}
							formatter={(value: number, name: string) => [
								name === "新增代理"
									? `${value} 人`
									: `¥${value.toLocaleString()}`,
								name,
							]}
						/>
						<Bar
							name="当期收入"
							dataKey="当期收入"
							fill="#D4B485"
							yAxisId="left"
							radius={[4, 4, 0, 0]}
						/>
						<Bar
							name="新增代理"
							dataKey="新增代理"
							fill="#60A5FA"
							yAxisId="right"
							radius={[4, 4, 0, 0]}
						/>
						<Bar
							name="套餐销售额"
							dataKey="套餐销售额"
							fill="#10B981"
							yAxisId="left"
							radius={[4, 4, 0, 0]}
						/>
					</BarChart>
				</ResponsiveContainer>
			</div>
		</div>
	);
}
