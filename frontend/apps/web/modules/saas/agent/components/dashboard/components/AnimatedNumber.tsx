"use client";
import { useAnimatedNumber } from "../hooks/useAnimatedNumber";

interface AnimatedNumberProps extends React.HTMLAttributes<HTMLSpanElement> {
	value: number;
	duration?: number;
	delay?: number;
	formatter?: (value: number) => string | number;
}

export function AnimatedNumber({
	value,
	duration,
	delay,
	formatter = (value) => Math.round(value).toLocaleString(),
	...props
}: AnimatedNumberProps) {
	const displayValue = useAnimatedNumber(value, duration, delay);

	return <span {...props}>{formatter(displayValue)}</span>;
}
