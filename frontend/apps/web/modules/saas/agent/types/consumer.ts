// 消费者绑定状态枚举
export enum ConsumerBindingStatus {
	SELF = "self", // 绑定当前代理
	OTHER = "other", // 绑定其他代理
	NONE = "none", // 未绑定代理
	AGENT = "agent", // 该用户是代理商
}

// 消费者基础类型
export interface Consumer {
	id: string;
	name: string;
	phone: string;
	status: string;
	currentPlan?: {
		id: string;
		name: string;
		level: string;
		expireAt?: string;
	};
	createdAt: string;
	bindingStatus: ConsumerBindingStatus;
}
