import type { LucideIcon } from "lucide-react";

export type IconName = LucideIcon;

export enum AgentSystemType {
	ADMIN = "admin",
	MERCHANT = "merchant",
	SALES = "sales",
}

export interface MenuConfig {
	label: string;
	href: string;
	icon: IconName;
	subItems?: Omit<MenuConfig, "subItems">[];
	isCategory?: boolean;
}

export interface SystemLink {
	label: string;
	href: string;
	icon: IconName;
	gradient?: string;
}
