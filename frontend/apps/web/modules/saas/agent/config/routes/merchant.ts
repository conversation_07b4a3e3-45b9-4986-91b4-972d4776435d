import {
	BarChart3,
	Box,
	LayoutDashboard,
	MessageSquare,
	Package,
	ScrollText,
	Settings,
	ShoppingCart,
	Ticket,
	UserPlus,
	Users,
	Zap,
} from "lucide-react";
import type { MenuConfig, SystemLink } from "../types";

export const merchantMenuItems: MenuConfig[] = [
	{
		label: "控制台",
		href: "/app/agent/dashboard",
		icon: LayoutDashboard,
	},
	{
		label: "代理商功能",
		href: "",
		icon: Users,
		isCategory: true,
	},
	{
		label: "推荐代理商",
		href: "/app/agent/agents/add",
		icon: UserPlus,
	},
	{
		label: "我的代理商",
		href: "/app/agent/agents/list",
		icon: Users,
	},
	{
		label: "名额分配",
		href: "/app/agent/quota/allocation",
		icon: Ticket,
	},
	{
		label: "业绩概览",
		href: "/app/agent/performance/overview",
		icon: BarChart3,
	},
	{
		label: "销售功能",
		href: "",
		icon: ShoppingCart,
		isCategory: true,
	},
	{
		label: "客户服务",
		href: "",
		icon: Users,
		subItems: [
			{
				label: "客户管理",
				href: "/app/agent/consumers",
				icon: Users,
			},
			{
				label: "套餐激活",
				href: "/app/agent/orders/activate",
				icon: Package,
			},
		],
	},
	{
		label: "购买套餐",
		href: "/app/agent/packages",
		icon: Package,
		subItems: [
			{
				label: "套餐购买",
				href: "/app/agent/orders/purchase",
				icon: Zap,
			},
			{
				label: "套餐仓库",
				href: "/app/agent/packages/inventory",
				icon: Box,
			},
			{
				label: "订单历史",
				href: "/app/agent/orders/history",
				icon: ScrollText,
			},
		],
	},
	{
		label: "通用功能",
		href: "",
		icon: Settings,
		isCategory: true,
	},
	{
		label: "消息中心",
		href: "/app/agent/messages",
		icon: MessageSquare,
	},
	{
		label: "账户设置",
		href: "/app/agent/setting",
		icon: Settings,
	},
];

export const merchantSystemLinks: SystemLink[] = [
	{
		label: "管理后台",
		href: "/app/agent/admin/dashboard",
		icon: LayoutDashboard,
	},
	{
		label: "代理商系统",
		href: "/app/agent/dashboard",
		icon: Users,
	},
];
