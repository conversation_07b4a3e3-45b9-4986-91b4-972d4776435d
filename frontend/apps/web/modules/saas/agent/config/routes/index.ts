import type { AgentRole } from "@prisma/client";
import type { MenuConfig, SystemLink } from "../types";
import { adminMenuItems, adminSystemLinks } from "./admin";
import { merchantMenuItems, merchantSystemLinks } from "./merchant";
import { salesMenuItems, salesSystemLinks } from "./sales";

/**
 * 根据代理商角色获取对应的菜单配置
 * @param role 代理商角色
 * @returns 对应角色的菜单配置
 */
export function getMenuByRole(role?: AgentRole | null): MenuConfig[] {
	// 如果是销售角色，只返回销售菜单
	if (role === "SALES") {
		return salesMenuItems;
	}

	// 其他角色（分公司、联席董事、合伙人）返回完整菜单
	return merchantMenuItems;
}

/**
 * 获取系统导航链接
 * @param type 系统类型
 * @returns 系统导航链接
 */
export function getSystemLinks(
	type: "admin" | "merchant" | "sales",
): SystemLink[] {
	switch (type) {
		case "admin":
			return adminSystemLinks;
		case "merchant":
			return merchantSystemLinks;
		case "sales":
			return salesSystemLinks;
		default:
			return [];
	}
}

// 为方便使用，直接导出所有菜单配置
export { adminMenuItems, merchantMenuItems, salesMenuItems };
export { adminSystemLinks, merchantSystemLinks, salesSystemLinks };
