import type { MenuConfig } from "@saas/agent/config/types";
import {
	BarChart3,
	Bell,
	FileText,
	LayoutDashboard,
	MessageSquare,
	Package,
	Ticket,
	Users,
	Wallet,
} from "lucide-react";

export const adminMenuItems: MenuConfig[] = [
	{
		label: "控制台",
		href: "/app/agent/admin/dashboard",
		icon: LayoutDashboard,
	},
	{
		label: "管理功能",
		href: "",
		icon: Users,
		isCategory: true,
	},
	{
		label: "用户管理",
		href: "/app/agent/admin/users",
		icon: Users,
		subItems: [
			{
				label: "代理商管理",
				href: "/app/agent/admin/users/agents",
				icon: Users,
			},
			{
				label: "客户管理",
				href: "/app/agent/admin/users/consumers",
				icon: Users,
			},
		],
	},
	{
		label: "名额管理",
		href: "/app/agent/admin/quota",
		icon: Ticket,
		subItems: [
			{
				label: "名额分配",
				href: "/app/agent/admin/quota/allocation",
				icon: Ticket,
			},
			{
				label: "分配记录",
				href: "/app/agent/admin/quota/records",
				icon: FileText,
			},
		],
	},
	{
		label: "套餐管理",
		href: "/app/agent/admin/plans/activate",
		icon: Ticket,
	},
	{
		label: "订单管理",
		href: "",
		icon: FileText,
		subItems: [
			{
				label: "订单管理",
				href: "/app/agent/admin/orders",
				icon: FileText,
			},
			{
				label: "套餐激活记录",
				href: "/app/agent/admin/activation-records",
				icon: Package,
			},
		],
	},
	{
		label: "数据统计",
		href: "/app/agent/admin/statistics",
		icon: BarChart3,
	},
	{
		label: "财务管理",
		href: "/app/agent/admin/finance",
		icon: Wallet,
	},
	{
		label: "通用功能",
		href: "",
		icon: MessageSquare,
		isCategory: true,
	},
	{
		label: "消息中心",
		href: "/app/agent/admin/messages",
		icon: MessageSquare,
	},
	{
		label: "通知管理",
		href: "/app/agent/admin/notifications",
		icon: Bell,
	},
];

export const adminSystemLinks = [
	{
		label: "管理后台",
		href: "/app/agent/admin/dashboard",
		icon: LayoutDashboard,
	},
	{
		label: "代理商系统",
		href: "/app/agent/dashboard",
		icon: Users,
	},
];
