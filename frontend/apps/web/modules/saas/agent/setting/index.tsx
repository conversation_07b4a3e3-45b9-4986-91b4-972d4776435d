"use client";

import { zywhFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { cn } from "@ui/lib";
import { motion } from "framer-motion";
import { Settings } from "lucide-react";
import { AccountSettingForm } from "./components/AccountSettingForm";

export interface AccountSettingProps {
	className?: string;
}

export function AccountSetting({ className }: AccountSettingProps) {
	return (
		<div className={cn("min-h-screen text-white p-6", className)}>
			<div className="max-w-4xl mx-auto space-y-8">
				{/* 标题区域 */}
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ delay: 0.1 }}
					className="relative mb-8"
				>
					<div className="flex items-center gap-4">
						<div
							className={cn(
								"flex h-14 w-14 shrink-0 items-center justify-center",
								"rounded-xl bg-gradient-to-br from-[#D4B485]/10 to-[#D4B485]/5",
								"ring-1 ring-[#D4B485]/20",
								"shadow-[0_0_0_8px_rgba(212,180,133,0.03)]",
								"transform-gpu transition-transform duration-300",
								"hover:scale-105 hover:shadow-[0_0_0_10px_rgba(212,180,133,0.05)]",
							)}
						>
							<Settings className="h-7 w-7 text-[#D4B485]" />
						</div>
						<div className="space-y-1">
							<h1
								className={cn(
									"font-semibold text-3xl",
									zywhFont.className,
									"leading-none tracking-[0.05em]",
								)}
								style={{
									background:
										"linear-gradient(135deg, rgba(229,201,165,0.95) 0%, rgba(212,180,133,0.9) 50%, rgba(176,137,104,0.85) 100%)",
									WebkitBackgroundClip: "text",
									WebkitTextFillColor: "transparent",
								}}
							>
								账号设置
							</h1>
							<p className="text-sm text-[#D4B485]/60">
								管理您的基础信息和账号配置
							</p>
						</div>
					</div>
				</motion.div>

				{/* 设置表单 */}
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ delay: 0.2 }}
				>
					<AccountSettingForm />
				</motion.div>
			</div>
		</div>
	);
}

export default AccountSetting;
