import { apiClient } from "@shared/lib/api-client";
import { useCallback, useState } from "react";
import { toast } from "sonner";

// 实名认证请求参数
export interface VerifyParams {
	realName: string;
	idCardNumber: string;
}

// 实名认证响应数据
export interface VerifyResult {
	realName: string | null;
	idCardNumber: string | null;
	idCardVerified: boolean;
	verifiedAt: Date | string;
}

// API响应类型
interface ApiResponse<T> {
	code: number;
	message?: string;
	data?: T;
	error?: string;
}

export function useIdentityVerification() {
	const [loading, setLoading] = useState(false);
	const [result, setResult] = useState<VerifyResult | null>(null);

	const verify = useCallback(async (params: VerifyParams) => {
		setLoading(true);
		try {
			// 调用后端的实名认证接口
			const response =
				await apiClient.v1.agent.verify.verify_by_tencent.$post({
					json: params,
				});

			const result: ApiResponse<VerifyResult> = await response.json();

			if (response.ok && result.code === 200) {
				if (!result.data) {
					// logger.error("[Agent][Verify] 响应数据格式不正确", {
					// 	result,
					// });
					toast.error("实名认证失败", {
						description: "响应数据格式不正确",
					});
					return null;
				}
				setResult(result.data);
				toast.success("实名认证成功", {
					description: "您的身份信息已通过验证",
				});
				return result.data;
			}

			const errorMsg = result.message || result.error || "未知错误";
			// logger.error("[Agent][Verify] 实名认证失败", {
			// 	code: result.code,
			// 	message: errorMsg,
			// 	result,
			// });
			toast.error("实名认证失败", { description: errorMsg });
			return null;
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : String(error);
			// logger.error("[Agent][Verify] 实名认证异常", {
			// 	error: errorMessage,
			// 	params: {
			// 		realName: params.realName,
			// 		idCardNumber: `***${params.idCardNumber.slice(-4)}`,
			// 	},
			// });
			toast.error("实名认证请求失败", { description: errorMessage });
			return null;
		} finally {
			setLoading(false);
		}
	}, []);

	return {
		loading,
		result,
		verify,
	};
}
