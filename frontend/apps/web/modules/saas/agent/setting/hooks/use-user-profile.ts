"use client";

import { apiClient } from "@shared/lib/api-client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useMemo } from "react";
import { toast } from "sonner";
import { v4 as uuidv4 } from "uuid";

// 用户资料数据类型（基于后端API返回）
export interface UserProfile {
	id: string;
	name: string;
	phoneCountryCode: string;
	phoneNumber: string;
	phoneNumberFull: string;
	phoneNumberVerified: boolean;
	email: string;
	emailVerified: boolean;
	backupContact?: string | null;
	realName?: string | null; // 后端已脱敏，通常为null
	idCardNumber?: string | null; // 后端已脱敏，通常为null
	realNameMasked?: string | null; // 脱敏后的真名显示
	idCardNumberMasked?: string | null; // 脱敏后的身份证显示
	idCardVerified: boolean;
	verifiedAt?: string | null;
	computingPower: number;
	totalComputingPower: number;
	status: "ACTIVE" | "DISABLED" | "PENDING";
	createdAt: string;
	updatedAt: string;
	avatar?: string | null; // 头像URL
	avatarUrl?: string | null; // 头像URL (备用字段名)
}

// 更新资料请求参数
export interface UpdateProfileParams {
	name?: string;
	email?: string;
	backupContact?: string;
	realName?: string;
	idCardNumber?: string;
}

// 处理敏感信息显示
export const formatSensitiveData = (profile: UserProfile) => {
	return {
		...profile,
		// 使用后端返回的脱敏数据，无需前端再次处理
		// 手机号脱敏显示：138****8000
		phoneNumberMasked: profile.phoneNumber
			? `${profile.phoneNumber.slice(0, 3)}****${profile.phoneNumber.slice(-4)}`
			: profile.phoneNumber,
		// 邮箱脱敏显示：zh***@example.com
		emailMasked: profile.email
			? profile.email.replace(/(.{2}).*@/, "$1***@")
			: profile.email,
		// 真名和身份证号使用后端返回的脱敏字段
		realNameDisplay: profile.realNameMasked || "未填写",
		idCardNumberDisplay: profile.idCardNumberMasked || "未填写",
	};
};

// 检查字段是否可编辑
export const getFieldEditability = (profile: UserProfile) => {
	return {
		name: true, // 用户名始终可编辑
		email: !profile.emailVerified, // 已验证的邮箱不可编辑
		phoneNumber: !profile.phoneNumberVerified, // 已验证的手机号不可编辑
		backupContact: true, // 备用联系方式始终可编辑
		realName: !profile.idCardVerified, // 已实名认证后不可编辑
		idCardNumber: !profile.idCardVerified, // 已实名认证后不可编辑
	};
};

// 获取用户资料Hook
export function useUserProfile() {
	const query = useQuery({
		queryKey: ["user", "profile"],
		queryFn: async (): Promise<UserProfile> => {
			try {
				const response = await apiClient.v1.shared.user.profile.$get();
				const result = await response.json();

				if (response.ok && result.data) {
					return result.data;
				}
				const _errorCode = response.status;
				const errorMsg =
					(result as any).message ||
					response.statusText ||
					"获取用户资料失败";

				throw new Error(errorMsg);
			} catch (error) {
				const errorMessage =
					error instanceof Error ? error.message : String(error);

				throw new Error(errorMessage);
			}
		},
		staleTime: 5 * 60 * 1000, // 5分钟内不重新获取
		gcTime: 10 * 60 * 1000, // 10分钟后清理缓存
	});

	// 使用 useMemo 缓存处理后的数据，避免无限循环
	const profileWithSensitiveData = useMemo(() => {
		return query.data ? formatSensitiveData(query.data) : null;
	}, [query.data]);

	const editability = useMemo(() => {
		return query.data ? getFieldEditability(query.data) : null;
	}, [query.data]);

	return {
		...query,
		data: profileWithSensitiveData,
		editability,
		rawData: query.data, // 原始数据，用于需要完整数据的场景
	};
}

// 更新用户资料Hook
export function useUpdateProfile() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (params: UpdateProfileParams) => {
			try {
				const response = await apiClient.v1.shared.user.profile.$patch({
					json: params,
				});

				const result = await response.json();
				if (response.ok) {
					return result;
				}
				const _errorCode = response.status;
				const errorMsg =
					(result as any).message ||
					response.statusText ||
					"更新用户资料失败";

				throw new Error(errorMsg);
			} catch (error) {
				const errorMessage =
					error instanceof Error ? error.message : String(error);
				throw new Error(errorMessage);
			}
		},
		onSuccess: (_data) => {
			// 更新缓存
			queryClient.invalidateQueries({ queryKey: ["user", "profile"] });
			toast.success("用户资料更新成功");
		},
		onError: (error: Error) => {
			toast.error(error.message || "更新用户资料失败");
		},
	});
}

// 头像更新Hook
export function useUpdateAvatar() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: async (file: File) => {
			try {
				// 生成文件路径和获取文件类型
				const fileExtension = file.name.split(".").pop() || "jpg";
				const fileName = `${uuidv4().replace(/-/g, "")}.${fileExtension}`;

				// 第一步：获取签名上传URL
				const signedUrlResponse = await apiClient.v1.shared.upload[
					"get-signed-url"
				].$post({
					json: {
						feature: "avatar",
						filePath: fileName,
						fileType: file.type || "image/jpeg",
					},
				});

				const signedUrlResult = await signedUrlResponse.json();

				if (!signedUrlResponse.ok) {
					const errorMsg =
						(signedUrlResult as any).message ||
						signedUrlResponse.statusText ||
						"获取上传URL失败";
					throw new Error(errorMsg);
				}

				const successResult = signedUrlResult as {
					code: number;
					data: string;
				};
				const signedUrl = successResult.data;

				// 第二步：直接上传文件到S3
				const uploadResponse = await fetch(signedUrl, {
					method: "PUT",
					body: file,
					headers: {
						"Content-Type": file.type || "image/jpeg",
					},
				});

				if (!uploadResponse.ok) {
					throw new Error("文件上传失败");
				}

				// 第四步：更新用户头像信息
				const updateResponse =
					await apiClient.v1.shared.user.avatar.$post({
						json: { avatarUrl: fileName },
					});

				const updateResult = await updateResponse.json();

				if (!updateResponse.ok) {
					const errorMsg =
						(updateResult as any).message ||
						updateResponse.statusText ||
						"头像信息更新失败";
					throw new Error(errorMsg);
				}

				const successUpdateResult = updateResult as {
					message: string;
					data: { fullSourceUrl: string };
				};

				return successUpdateResult.data.fullSourceUrl;
			} catch (error) {
				const errorMessage =
					error instanceof Error ? error.message : String(error);
				throw new Error(errorMessage);
			}
		},
		onSuccess: () => {
			// 更新缓存
			queryClient.invalidateQueries({ queryKey: ["user", "profile"] });
			toast.success("头像更新成功");
		},
		onError: (error: Error) => {
			toast.error(error.message || "头像更新失败");
		},
	});
}

// 密码修改Hook
export function useChangePassword() {
	return useMutation({
		mutationFn: async (params: {
			currentPassword: string;
			newPassword: string;
			confirmPassword: string;
		}) => {
			try {
				const response = await apiClient.v1.shared.user[
					"change-password"
				].$post({
					json: params,
				});

				const result = await response.json();

				if (response.ok) {
					return result;
				}
				const _errorCode = response.status;
				const errorMsg =
					(result as any).message ||
					response.statusText ||
					"密码修改失败";

				throw new Error(errorMsg);
			} catch (error) {
				const errorMessage =
					error instanceof Error ? error.message : String(error);
				throw new Error(errorMessage);
			}
		},
		onSuccess: () => {
			toast.success("密码修改成功");
		},
		onError: (error: Error) => {
			toast.error(error.message || "密码修改失败");
		},
	});
}

// 通过验证码重置密码Hook
export function useResetPasswordByOTP() {
	return useMutation({
		mutationFn: async (params: {
			phoneCountryCode: string;
			phoneNumber: string;
			verificationCode: string;
			newPassword: string;
			confirmPassword: string;
		}) => {
			try {
				const response = await apiClient.v1.shared.user[
					"reset-password-by-otp"
				].$post({
					json: params,
				});

				if (response.ok) {
					const result = await response.json();
					return result;
				}
				const _errorCode = response.status;
				let errorMsg = "密码重置失败";

				try {
					const result = await response.json();
					errorMsg =
						(result as any).message ||
						(result as any).error ||
						response.statusText ||
						"密码重置失败";
				} catch (_parseError) {
					// 如果不是JSON格式，尝试获取文本
					try {
						const textResult = await response.text();
						errorMsg =
							textResult || response.statusText || "密码重置失败";
					} catch (_textError) {
						errorMsg = response.statusText || "密码重置失败";
					}
				}

				throw new Error(errorMsg);
			} catch (error) {
				const errorMessage =
					error instanceof Error ? error.message : String(error);
				throw new Error(errorMessage);
			}
		},
		onSuccess: () => {
			toast.success("密码重置成功");
		},
		onError: (error: Error) => {
			toast.error(error.message || "密码重置失败");
		},
	});
}

// 检查数据可见性的工具函数
export const checkDataVisibility = (profile: UserProfile) => {
	return {
		// 基础信息 - 始终可见
		basicInfo: true,
		// 联系方式 - 始终可见但可能脱敏
		contactInfo: true,
		// 身份信息 - 根据验证状态决定是否显示完整信息
		identityInfo: profile.idCardVerified,
		// 算力信息 - 可见但可能需要权限控制
		computingPower: true,
		// 敏感操作历史 - 需要特殊权限
		securityInfo: false,
	};
};
