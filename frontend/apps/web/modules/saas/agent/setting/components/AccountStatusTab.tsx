import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import { motion } from "framer-motion";
import { CreditCard } from "lucide-react";
import type { AccountStatusTabProps } from "../types";

// 账号状态标签
const STATUS_LABELS = {
	ACTIVE: { label: "正常", color: "text-green-400" },
	DISABLED: { label: "禁用", color: "text-red-400" },
	PENDING: { label: "待激活", color: "text-yellow-400" },
};

export function AccountStatusTab({ userData }: AccountStatusTabProps) {
	return (
		<div className="space-y-6 mt-6">
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.1 }}
			>
				<Card className="bg-gradient-to-br from-[#1E2023]/60 to-[#1A1C1E]/60 border border-[#D4B485]/20 backdrop-blur-md">
					<CardHeader>
						<CardTitle className="text-[#D4B485] flex items-center gap-2">
							<CreditCard className="h-5 w-5" />
							账号信息
						</CardTitle>
						<CardDescription className="text-[#D4B485]/60">
							查看您的账号状态和角色信息
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-6">
						{/* 账号状态 */}
						<div className="space-y-4">
							<h4 className="font-medium text-[#D4B485] border-b border-[#D4B485]/20 pb-2">
								基本状态
							</h4>
							<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
								<div className="space-y-3">
									<div className="flex justify-between items-center">
										<span className="text-[#D4B485]/60">
											账号状态
										</span>
										<span
											className={
												STATUS_LABELS[
													userData.status as keyof typeof STATUS_LABELS
												]?.color || "text-[#D4B485]"
											}
										>
											{STATUS_LABELS[
												userData.status as keyof typeof STATUS_LABELS
											]?.label || userData.status}
										</span>
									</div>
									<div className="flex justify-between items-center">
										<span className="text-[#D4B485]/60">
											注册时间
										</span>
										<span className="text-[#D4B485]">
											{new Date(
												userData.createdAt,
											).toLocaleDateString()}
										</span>
									</div>
								</div>
								<div className="space-y-3">
									<div className="flex justify-between items-center">
										<span className="text-[#D4B485]/60">
											最后更新
										</span>
										<span className="text-[#D4B485]">
											{new Date(
												userData.updatedAt,
											).toLocaleDateString()}
										</span>
									</div>
									{userData.verifiedAt && (
										<div className="flex justify-between items-center">
											<span className="text-[#D4B485]/60">
												实名认证时间
											</span>
											<span className="text-[#D4B485]">
												{new Date(
													userData.verifiedAt,
												).toLocaleDateString()}
											</span>
										</div>
									)}
								</div>
							</div>
						</div>

						<div className="border-t border-[#D4B485]/10 pt-6">
							{/* 验证信息 */}
							<div className="space-y-4 mb-6">
								<h4 className="font-medium text-[#D4B485] border-b border-[#D4B485]/20 pb-2">
									验证状态
								</h4>
								<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
									<div className="space-y-3">
										<div className="flex justify-between items-center">
											<span className="text-[#D4B485]/60">
												手机号验证
											</span>
											<span
												className={
													userData.phoneNumberVerified
														? "text-green-400"
														: "text-yellow-400"
												}
											>
												{userData.phoneNumberVerified
													? "已验证"
													: "未验证"}
											</span>
										</div>
										<div className="flex justify-between items-center">
											<span className="text-[#D4B485]/60">
												邮箱验证
											</span>
											<span
												className={
													userData.emailVerified
														? "text-green-400"
														: "text-yellow-400"
												}
											>
												{userData.emailVerified
													? "已验证"
													: "未验证"}
											</span>
										</div>
									</div>
									<div className="space-y-3">
										<div className="flex justify-between items-center">
											<span className="text-[#D4B485]/60">
												实名认证
											</span>
											<span
												className={
													userData.idCardVerified
														? "text-green-400"
														: "text-yellow-400"
												}
											>
												{userData.idCardVerified
													? "已认证"
													: "未认证"}
											</span>
										</div>
										{userData.backupContact && (
											<div className="flex justify-between items-center">
												<span className="text-[#D4B485]/60">
													备用联系方式
												</span>
												<span className="text-[#D4B485]">
													已设置
												</span>
											</div>
										)}
									</div>
								</div>
							</div>

							{/* 联系信息 */}
							<div className="space-y-4 mb-6">
								<h4 className="font-medium text-[#D4B485] border-b border-[#D4B485]/20 pb-2">
									联系信息
								</h4>
								<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
									<div className="space-y-3">
										<div className="flex justify-between items-center">
											<span className="text-[#D4B485]/60">
												手机号码
											</span>
											<span className="text-[#D4B485]">
												{userData.phoneNumberMasked ||
													userData.phoneNumber}
											</span>
										</div>
										{/* <div className="flex justify-between items-center">
											<span className="text-[#D4B485]/60">邮箱地址</span>
											<span className="text-[#D4B485]">
												{userData.emailMasked || userData.email}
											</span>
										</div> */}
										{(userData.idCardNumberMasked ||
											userData.idCardNumber) && (
											<div className="flex justify-between items-center">
												<span className="text-[#D4B485]/60">
													身份证号
												</span>
												<span className="text-[#D4B485]">
													{userData.idCardNumberMasked ||
														"未填写"}
												</span>
											</div>
										)}
									</div>
									<div className="space-y-3">
										{(userData.realNameMasked ||
											userData.realName) && (
											<div className="flex justify-between items-center">
												<span className="text-[#D4B485]/60">
													真实姓名
												</span>
												<span className="text-[#D4B485]">
													{userData.realNameMasked ||
														"未填写"}
												</span>
											</div>
										)}
									</div>
								</div>
							</div>
						</div>
					</CardContent>
				</Card>
			</motion.div>
		</div>
	);
}
