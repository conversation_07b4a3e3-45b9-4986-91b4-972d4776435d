import { Button } from "@ui/components/button";
import { Camera, RefreshCw, User } from "lucide-react";
import Image from "next/image";
import type { AvatarUploadProps } from "../types";

export function AvatarUpload({
	avatar,
	onAvatarUpload,
	onRemoveAvatar,
	isLoading,
}: AvatarUploadProps) {
	const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const file = e.target.files?.[0];
		if (file) {
			onAvatarUpload(file);
		}
	};

	return (
		<div className="flex items-center gap-6">
			<div className="relative">
				<div className="w-20 h-20 rounded-full bg-gradient-to-br from-[#D4B485]/20 to-[#D4B485]/10 border border-[#D4B485]/30 flex items-center justify-center overflow-hidden">
					{avatar ? (
						<Image
							src={avatar}
							alt="头像"
							width={80}
							height={80}
							className="w-full h-full object-cover"
						/>
					) : (
						<User className="h-8 w-8 text-[#D4B485]/60" />
					)}
				</div>
				<label className="absolute -bottom-2 -right-2 w-8 h-8 bg-[#D4B485] hover:bg-[#B08968] rounded-full flex items-center justify-center cursor-pointer transition-colors">
					<input
						type="file"
						accept="image/*"
						className="hidden"
						onChange={handleFileChange}
					/>
					{isLoading ? (
						<RefreshCw className="h-4 w-4 text-[#1A1C1E] animate-spin" />
					) : (
						<Camera className="h-4 w-4 text-[#1A1C1E]" />
					)}
				</label>
			</div>
			<div className="flex-1">
				<h4 className="font-medium text-[#D4B485] mb-1">头像设置</h4>
				<p className="text-sm text-[#D4B485]/60 mb-3">
					点击相机图标上传新头像，支持 JPG、PNG 格式，建议尺寸 200x200
					像素
				</p>
				<Button
					variant="outline"
					size="sm"
					className="bg-[#1A1C1E] border-[#D4B485]/30 text-[#D4B485] hover:bg-[#D4B485]/10"
					onClick={onRemoveAvatar}
				>
					移除头像
				</Button>
			</div>
		</div>
	);
}
