"use client";

import type { AgentRole, ConsumerRole } from "@prisma/client";
import { useSession } from "@saas/auth/hooks/use-session";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@ui/components/tabs";
import {
	AlertTriangle,
	CreditCard,
	RefreshCw,
	Shield,
	User,
} from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import {
	type UpdateProfileParams,
	useChangePassword,
	useUpdateAvatar,
	useUpdateProfile,
	useUserProfile,
} from "../hooks/use-user-profile";
import type { FormData, NotificationSettings } from "../types";

import { AccountStatusTab } from "./AccountStatusTab";
// 导入新的子组件
import { BasicInfoTab } from "./BasicInfoTab";
import { SecurityTab } from "./SecurityTab";

// 角色名称映射
const _AGENT_ROLE_LABELS: Record<AgentRole, string> = {
	ADMIN: "管理员",
	BRANCH: "分公司",
	DIRECTOR: "联席董事",
	PARTNER: "合伙人",
	SALES: "超级个体",
};

const _CONSUMER_ROLE_LABELS: Record<ConsumerRole, string> = {
	INTERNAL: "内部测试用户",
	REGULAR: "普通用户",
};

// 账号状态标签
const _STATUS_LABELS = {
	ACTIVE: { label: "正常", color: "text-green-400" },
	DISABLED: { label: "禁用", color: "text-red-400" },
	PENDING: { label: "待激活", color: "text-yellow-400" },
};

export function AccountSettingForm() {
	useSession();
	const {
		data: apiUserData,
		isLoading: loading,
		editability,
	} = useUserProfile();
	const updateProfile = useUpdateProfile();
	const updateAvatar = useUpdateAvatar();
	const _changePassword = useChangePassword();

	// 使用后端实际返回的用户数据
	const userData = apiUserData;

	// 表单数据状态
	const [formData, setFormData] = useState<FormData>({
		name: "",
		phoneNumber: "",
		email: "",
		backupContact: "",
		realName: "",
		idCardNumber: "",
		avatar: "",
	});

	// 设置状态
	const [_settings, _setSettings] = useState<NotificationSettings>({
		emailNotifications: true,
		smsNotifications: true,
		securityAlerts: true,
		marketingEmails: false,
	});

	// 同步用户数据到表单
	useEffect(() => {
		if (userData) {
			setFormData({
				name: userData.name || "",
				phoneNumber: userData.phoneNumber || "",
				email: "", // 邮箱字段已隐藏
				backupContact: userData.backupContact || "",
				// 编辑时显示脱敏数据，如果已认证则只读
				realName: userData.idCardVerified
					? userData.realNameMasked || ""
					: userData.realName || "",
				idCardNumber: userData.idCardVerified
					? userData.idCardNumberMasked || ""
					: userData.idCardNumber || "",
				avatar: userData.avatar || "", // 从用户数据获取头像URL
			});
		}
	}, [userData]);

	// 处理表单提交
	const handleSave = async () => {
		try {
			const updateData: UpdateProfileParams = {};

			// 只提交有变化且允许编辑的字段
			if (formData.name !== userData?.name && editability?.name) {
				updateData.name = formData.name;
			}
			// 邮箱字段已隐藏，不允许修改
			// if (formData.email !== userData?.email && editability?.email) {
			// 	updateData.email = formData.email;
			// }
			if (
				formData.backupContact !== userData?.backupContact &&
				editability?.backupContact
			) {
				updateData.backupContact = formData.backupContact;
			}
			// 实名信息只有在未认证时才能修改，且需要比较的是输入值而不是脱敏值
			if (editability?.realName && !userData?.idCardVerified) {
				const currentRealName = userData?.realName || "";
				if (formData.realName !== currentRealName) {
					updateData.realName = formData.realName;
				}
			}
			if (editability?.idCardNumber && !userData?.idCardVerified) {
				const currentIdCardNumber = userData?.idCardNumber || "";
				if (formData.idCardNumber !== currentIdCardNumber) {
					updateData.idCardNumber = formData.idCardNumber;
				}
			}

			if (Object.keys(updateData).length === 0) {
				toast.info("没有需要更新的内容");
				return;
			}

			await updateProfile.mutateAsync(updateData);
		} catch (_error) {}
	};

	// 处理头像上传
	const handleAvatarUpload = async (file: File) => {
		try {
			const avatarUrl = await updateAvatar.mutateAsync(file);
			// 头像上传成功后，更新表单数据
			setFormData((prev) => ({ ...prev, avatar: avatarUrl }));
		} catch (_error) {}
	};

	if (loading) {
		return (
			<div className="flex items-center justify-center py-12">
				<div className="flex items-center gap-3 text-[#D4B485]">
					<RefreshCw className="h-5 w-5 animate-spin" />
					<span>加载中...</span>
				</div>
			</div>
		);
	}

	if (!userData) {
		return (
			<div className="flex items-center justify-center py-12">
				<div className="text-center space-y-3">
					<AlertTriangle className="h-12 w-12 text-red-400 mx-auto" />
					<p className="text-[#D4B485]/60">无法加载用户数据</p>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			<Tabs defaultValue="basic" className="w-full">
				<TabsList className="grid w-full grid-cols-3 bg-[#1E2023]/30 border border-[#D4B485]/20 backdrop-blur-sm">
					<TabsTrigger
						value="basic"
						className="text-white/70 data-[state=active]:bg-[#D4B485]/20 data-[state=active]:text-[#D4B485] hover:text-white transition-colors"
					>
						<User className="h-4 w-4 mr-2" />
						基础信息
					</TabsTrigger>
					<TabsTrigger
						value="security"
						className="text-white/70 data-[state=active]:bg-[#D4B485]/20 data-[state=active]:text-[#D4B485] hover:text-white transition-colors"
					>
						<Shield className="h-4 w-4 mr-2" />
						安全设置
					</TabsTrigger>
					{/* <TabsTrigger 
						value="notifications"
						className="text-white/70 data-[state=active]:bg-[#D4B485]/20 data-[state=active]:text-[#D4B485] hover:text-white transition-colors"
					>
						<Bell className="h-4 w-4 mr-2" />
						通知设置
					</TabsTrigger> */}
					<TabsTrigger
						value="account"
						className="text-white/70 data-[state=active]:bg-[#D4B485]/20 data-[state=active]:text-[#D4B485] hover:text-white transition-colors"
					>
						<CreditCard className="h-4 w-4 mr-2" />
						账号状态
					</TabsTrigger>
				</TabsList>

				{/* 基础信息 */}
				<TabsContent value="basic">
					<BasicInfoTab
						userData={userData}
						formData={formData}
						setFormData={setFormData}
						editability={editability}
						onSave={handleSave}
						onAvatarUpload={handleAvatarUpload}
						isLoading={
							updateProfile.isPending || updateAvatar.isPending
						}
					/>
				</TabsContent>

				{/* 安全设置 */}
				<TabsContent value="security">
					<SecurityTab userData={userData} />
				</TabsContent>

				{/* 通知设置 */}
				{/* <TabsContent value="notifications">
					<NotificationsTab 
						settings={settings}
						setSettings={setSettings}
					/>
				</TabsContent> */}

				{/* 账号状态 */}
				<TabsContent value="account">
					<AccountStatusTab userData={userData} />
				</TabsContent>
			</Tabs>
		</div>
	);
}
