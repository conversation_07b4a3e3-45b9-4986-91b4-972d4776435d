import { But<PERSON> } from "@ui/components/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import { motion } from "framer-motion";
import { RefreshCw, Save, User } from "lucide-react";
import type { BasicInfoTabProps } from "../types";
import { AvatarUpload } from "./AvatarUpload";
import { IdentityVerification } from "./IdentityVerification";
import { PersonalInfoForm } from "./PersonalInfoForm";

export function BasicInfoTab({
	userData,
	formData,
	setFormData,
	onSave,
	onAvatarUpload,
	isLoading,
}: BasicInfoTabProps) {
	return (
		<div className="space-y-6 mt-6">
			{/* 个人信息 */}
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.1 }}
			>
				<Card className="bg-gradient-to-br from-[#1E2023]/60 to-[#1A1C1E]/60 border border-[#D4B485]/20 backdrop-blur-md">
					<CardHeader>
						<CardTitle className="text-[#D4B485] flex items-center gap-2">
							<User className="h-5 w-5" />
							个人信息
						</CardTitle>
						<CardDescription className="text-[#D4B485]/60">
							管理您的基本个人信息
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-6">
						{/* 头像上传 */}
						<AvatarUpload
							avatar={formData.avatar}
							onAvatarUpload={onAvatarUpload}
							onRemoveAvatar={() =>
								setFormData((prev) => ({ ...prev, avatar: "" }))
							}
							isLoading={isLoading}
						/>

						{/* 个人信息表单 */}
						<PersonalInfoForm
							userData={userData}
							formData={formData}
							setFormData={setFormData}
						/>
					</CardContent>
				</Card>
			</motion.div>

			{/* 实名认证 */}
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.2 }}
			>
				<IdentityVerification
					userData={userData}
					formData={formData}
					setFormData={setFormData}
				/>
			</motion.div>

			{/* 保存按钮 */}
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.3 }}
				className="flex justify-end"
			>
				<Button
					onClick={onSave}
					disabled={isLoading}
					className="bg-gradient-to-r from-[#D4B485] to-[#B08968] text-[#1A1C1E] hover:from-[#B08968] hover:to-[#D4B485] px-8"
				>
					{isLoading ? (
						<>
							<RefreshCw className="h-4 w-4 mr-2 animate-spin" />
							保存中...
						</>
					) : (
						<>
							<Save className="h-4 w-4 mr-2" />
							保存更改
						</>
					)}
				</Button>
			</motion.div>
		</div>
	);
}
