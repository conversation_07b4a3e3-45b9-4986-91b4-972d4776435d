import { authClient } from "@repo/auth/client";
import { But<PERSON> } from "@ui/components/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "@ui/components/dialog";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Switch } from "@ui/components/switch";
import { motion } from "framer-motion";
import { Eye, EyeOff, HelpCircle, Lock, RefreshCw } from "lucide-react";
import { useId, useState } from "react";
import { toast } from "sonner";
import { VerificationCode } from "../../../auth/components/shared/VerificationCode";
import {
	useChangePassword,
	useResetPasswordByOTP,
} from "../hooks/use-user-profile";
import type { SecurityTabProps } from "../types";

export function SecurityTab({ userData }: SecurityTabProps) {
	const [showChangePasswordForm, setShowChangePasswordForm] = useState(false);
	const [showForgotPasswordDialog, setShowForgotPasswordDialog] =
		useState(false);
	const [showPassword, setShowPassword] = useState({
		current: false,
		new: false,
		confirm: false,
	});

	// 修改密码表单数据
	const [passwordFormData, setPasswordFormData] = useState({
		currentPassword: "",
		newPassword: "",
		confirmPassword: "",
	});

	// 忘记密码表单数据
	const [forgotPasswordData, setForgotPasswordData] = useState({
		phoneCountryCode: userData.phoneCountryCode || "+86",
		phoneNumber: userData.phoneNumber || "",
		verificationCode: "",
		newPassword: "",
		confirmNewPassword: "",
	});

	const [_forgotPasswordStep, setForgotPasswordStep] =
		useState<"verify">("verify"); // 直接跳到验证码步骤
	const id = useId();

	const changePassword = useChangePassword();
	const resetPasswordByOTP = useResetPasswordByOTP();

	// 处理密码修改
	const handlePasswordChange = async () => {
		if (!passwordFormData.currentPassword.trim()) {
			toast.error("请输入当前密码");
			return;
		}
		if (!passwordFormData.newPassword.trim()) {
			toast.error("请输入新密码");
			return;
		}
		if (passwordFormData.newPassword !== passwordFormData.confirmPassword) {
			toast.error("新密码与确认密码不匹配");
			return;
		}
		if (passwordFormData.newPassword.length < 6) {
			toast.error("新密码长度不能少于6位");
			return;
		}

		const result = await changePassword.mutateAsync({
			currentPassword: passwordFormData.currentPassword,
			newPassword: passwordFormData.newPassword,
			confirmPassword: passwordFormData.confirmPassword,
		});

		if (result) {
			// 重置表单
			setPasswordFormData({
				currentPassword: "",
				newPassword: "",
				confirmPassword: "",
			});
			setShowChangePasswordForm(false);
		}
	};

	// 取消密码修改
	const handleCancelPasswordChange = () => {
		setPasswordFormData({
			currentPassword: "",
			newPassword: "",
			confirmPassword: "",
		});
		setShowChangePasswordForm(false);
	};

	// 发送验证码
	const handleSendVerificationCode = async () => {
		try {
			const response = await authClient.phoneAuth.sendOTP(
				userData.phoneCountryCode || "+86",
				userData.phoneNumber || "",
			);

			if (!response.success) {
				toast.error(response.message || "验证码发送失败");
				return;
			}

			toast.success("验证码已发送");
		} catch (_error) {
			toast.error("验证码发送失败，请稍后重试");
		}
	};

	// 验证码验证并重置密码
	const handleForgotPasswordReset = async () => {
		if (!forgotPasswordData.verificationCode.trim()) {
			toast.error("请输入验证码");
			return;
		}
		if (!forgotPasswordData.newPassword.trim()) {
			toast.error("请输入新密码");
			return;
		}
		if (
			forgotPasswordData.newPassword !==
			forgotPasswordData.confirmNewPassword
		) {
			toast.error("新密码与确认密码不匹配");
			return;
		}
		if (forgotPasswordData.newPassword.length < 6) {
			toast.error("新密码长度不能少于6位");
			return;
		}

		try {
			await resetPasswordByOTP.mutateAsync({
				phoneCountryCode: userData.phoneCountryCode || "+86",
				phoneNumber: userData.phoneNumber || "",
				verificationCode: forgotPasswordData.verificationCode,
				newPassword: forgotPasswordData.newPassword,
				confirmPassword: forgotPasswordData.confirmNewPassword,
			});

			// 重置成功后关闭对话框和重置表单
			setShowForgotPasswordDialog(false);
			resetForgotPasswordForm();
		} catch (_error) {}
	};

	// 重置忘记密码表单
	const resetForgotPasswordForm = () => {
		setForgotPasswordData({
			phoneCountryCode: userData.phoneCountryCode || "+86",
			phoneNumber: userData.phoneNumber || "",
			verificationCode: "",
			newPassword: "",
			confirmNewPassword: "",
		});
		setForgotPasswordStep("verify");
	};

	// 切换密码可见性
	const togglePasswordVisibility = (field: "current" | "new" | "confirm") => {
		setShowPassword((prev) => ({ ...prev, [field]: !prev[field] }));
	};

	return (
		<div className="space-y-6 mt-6">
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.1 }}
			>
				<Card className="bg-gradient-to-br from-[#1E2023]/60 to-[#1A1C1E]/60 border border-[#D4B485]/20 backdrop-blur-md">
					<CardHeader>
						<CardTitle className="text-[#D4B485] flex items-center gap-2">
							<Lock className="h-5 w-5" />
							密码与安全
						</CardTitle>
						<CardDescription className="text-[#D4B485]/60">
							管理您的账号安全设置
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-6">
						<div className="space-y-4">
							{/* 登录密码 */}
							<div className="flex items-center justify-between p-4 bg-[#1A1C1E]/50 rounded-lg border border-[#D4B485]/10">
								<div className="space-y-1">
									<h4 className="font-medium text-[#D4B485]">
										登录密码
									</h4>
									<p className="text-sm text-[#D4B485]/60">
										定期修改密码以提高账号安全性
									</p>
								</div>
								<Button
									variant="outline"
									size="sm"
									onClick={() =>
										setShowChangePasswordForm(
											!showChangePasswordForm,
										)
									}
									className="bg-[#1A1C1E] border-[#D4B485]/30 text-[#D4B485] hover:bg-[#D4B485]/10"
								>
									{showChangePasswordForm
										? "取消修改"
										: "修改密码"}
								</Button>
							</div>

							{/* 修改密码表单 */}
							{showChangePasswordForm && (
								<motion.div
									initial={{ opacity: 0, height: 0 }}
									animate={{ opacity: 1, height: "auto" }}
									exit={{ opacity: 0, height: 0 }}
									className="p-4 bg-[#1A1C1E]/30 rounded-lg border border-[#D4B485]/10 space-y-4"
								>
									<h5 className="font-medium text-[#D4B485] mb-3">
										修改密码
									</h5>

									<div className="grid grid-cols-1 gap-4">
										{/* 当前密码 */}
										<div className="space-y-2">
											<Label
												htmlFor={`${id}-currentPassword`}
												className="text-[#D4B485] font-medium"
											>
												当前密码 *
											</Label>
											<div className="relative">
												<Input
													id={`${id}-currentPassword`}
													type={
														showPassword.current
															? "text"
															: "password"
													}
													value={
														passwordFormData.currentPassword
													}
													onChange={(e) =>
														setPasswordFormData(
															(prev) => ({
																...prev,
																currentPassword:
																	e.target
																		.value,
															}),
														)
													}
													placeholder="请输入当前密码"
													disabled={
														changePassword.isPending
													}
													className="bg-[#1A1C1E] border-[#D4B485]/30 text-[#D4B485] placeholder:text-[#D4B485]/40 pr-10"
												/>
												<button
													type="button"
													onClick={() =>
														togglePasswordVisibility(
															"current",
														)
													}
													className="absolute right-3 top-1/2 -translate-y-1/2 text-[#D4B485]/60 hover:text-[#D4B485]"
												>
													{showPassword.current ? (
														<EyeOff className="h-4 w-4" />
													) : (
														<Eye className="h-4 w-4" />
													)}
												</button>
											</div>
											<div className="flex items-center gap-2 text-sm text-[#D4B485]/60">
												<Dialog
													open={
														showForgotPasswordDialog
													}
													onOpenChange={
														setShowForgotPasswordDialog
													}
												>
													<DialogTrigger asChild>
														<button
															type="button"
															className="text-[#D4B485] hover:text-[#D4B485]/80 underline flex items-center gap-1"
														>
															<HelpCircle className="h-3 w-3" />
															忘记当前密码？
														</button>
													</DialogTrigger>
												</Dialog>
											</div>
										</div>

										{/* 新密码 */}
										<div className="space-y-2">
											<Label
												htmlFor={`${id}-newPassword`}
												className="text-[#D4B485] font-medium"
											>
												新密码 *
											</Label>
											<div className="relative">
												<Input
													id={`${id}-newPassword`}
													type={
														showPassword.new
															? "text"
															: "password"
													}
													value={
														passwordFormData.newPassword
													}
													onChange={(e) =>
														setPasswordFormData(
															(prev) => ({
																...prev,
																newPassword:
																	e.target
																		.value,
															}),
														)
													}
													placeholder="请输入新密码（至少6位）"
													disabled={
														changePassword.isPending
													}
													className="bg-[#1A1C1E] border-[#D4B485]/30 text-[#D4B485] placeholder:text-[#D4B485]/40 pr-10"
												/>
												<button
													type="button"
													onClick={() =>
														togglePasswordVisibility(
															"new",
														)
													}
													className="absolute right-3 top-1/2 -translate-y-1/2 text-[#D4B485]/60 hover:text-[#D4B485]"
												>
													{showPassword.new ? (
														<EyeOff className="h-4 w-4" />
													) : (
														<Eye className="h-4 w-4" />
													)}
												</button>
											</div>
										</div>

										{/* 确认新密码 */}
										<div className="space-y-2">
											<Label
												htmlFor={`${id}-confirmPassword`}
												className="text-[#D4B485] font-medium"
											>
												确认新密码 *
											</Label>
											<div className="relative">
												<Input
													id={`${id}-confirmPassword`}
													type={
														showPassword.confirm
															? "text"
															: "password"
													}
													value={
														passwordFormData.confirmPassword
													}
													onChange={(e) =>
														setPasswordFormData(
															(prev) => ({
																...prev,
																confirmPassword:
																	e.target
																		.value,
															}),
														)
													}
													placeholder="请再次输入新密码"
													disabled={
														changePassword.isPending
													}
													className="bg-[#1A1C1E] border-[#D4B485]/30 text-[#D4B485] placeholder:text-[#D4B485]/40 pr-10"
												/>
												<button
													type="button"
													onClick={() =>
														togglePasswordVisibility(
															"confirm",
														)
													}
													className="absolute right-3 top-1/2 -translate-y-1/2 text-[#D4B485]/60 hover:text-[#D4B485]"
												>
													{showPassword.confirm ? (
														<EyeOff className="h-4 w-4" />
													) : (
														<Eye className="h-4 w-4" />
													)}
												</button>
											</div>
										</div>
									</div>

									{/* 操作按钮 */}
									<div className="flex justify-end gap-3 pt-2">
										<Button
											variant="outline"
											onClick={handleCancelPasswordChange}
											disabled={changePassword.isPending}
											className="bg-[#1A1C1E] border-[#D4B485]/30 text-[#D4B485] hover:bg-[#D4B485]/10"
										>
											取消
										</Button>
										<Button
											onClick={handlePasswordChange}
											disabled={
												changePassword.isPending ||
												!passwordFormData.currentPassword ||
												!passwordFormData.newPassword ||
												!passwordFormData.confirmPassword
											}
											className="bg-gradient-to-r from-[#D4B485] to-[#B08968] text-[#1A1C1E] hover:from-[#B08968] hover:to-[#D4B485] px-6"
										>
											{changePassword.isPending ? (
												<>
													<RefreshCw className="h-4 w-4 mr-2 animate-spin" />
													修改中...
												</>
											) : (
												<>
													<Lock className="h-4 w-4 mr-2" />
													确认修改
												</>
											)}
										</Button>
									</div>
								</motion.div>
							)}

							{/* 两步验证 */}
							<div className="flex items-center justify-between p-4 bg-[#1A1C1E]/50 rounded-lg border border-[#D4B485]/10">
								<div className="space-y-1">
									<h4 className="font-medium text-[#D4B485]">
										两步验证
									</h4>
									<p className="text-sm text-[#D4B485]/60">
										通过手机短信进行二次验证
									</p>
								</div>
								<Switch
									checked={userData.phoneNumberVerified}
									className="data-[state=checked]:bg-[#D4B485]"
								/>
							</div>

							{/* 登录设备管理 */}
							<div className="flex items-center justify-between p-4 bg-[#1A1C1E]/50 rounded-lg border border-[#D4B485]/10">
								<div className="space-y-1">
									<h4 className="font-medium text-[#D4B485]">
										登录设备管理
									</h4>
									<p className="text-sm text-[#D4B485]/60">
										查看和管理已登录的设备
									</p>
								</div>
								<Button
									variant="outline"
									size="sm"
									className="bg-[#1A1C1E] border-[#D4B485]/30 text-[#D4B485] hover:bg-[#D4B485]/10"
								>
									管理设备
								</Button>
							</div>
						</div>
					</CardContent>
				</Card>
			</motion.div>

			{/* 忘记密码弹窗 */}
			<Dialog
				open={showForgotPasswordDialog}
				onOpenChange={setShowForgotPasswordDialog}
			>
				<DialogContent className="bg-gradient-to-br from-[#0A0B0D] via-[#1A1C1E] to-[#0F1011] border border-yellow-200/20 text-yellow-100 max-w-lg backdrop-blur-sm">
					<DialogHeader className="text-center">
						<motion.div
							initial={{ opacity: 0, y: -10 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ duration: 0.5 }}
						>
							<DialogTitle className="bg-gradient-to-r from-[#f0f0f0] to-[#fff9ab] bg-clip-text font-extrabold text-2xl text-transparent">
								重置密码
							</DialogTitle>
							<DialogDescription className="mt-2 text-[#f8f3b0]">
								将向您的手机号{" "}
								{userData.phoneNumberMasked ||
									`${userData.phoneCountryCode || "+86"} ${userData.phoneNumber || ""}`}{" "}
								发送验证码
							</DialogDescription>
						</motion.div>
					</DialogHeader>

					<motion.div
						className="space-y-5 mt-6"
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.5, delay: 0.2 }}
					>
						{/* 显示当前手机号信息 */}
						<motion.div
							className="text-center py-4"
							initial={{ opacity: 0, y: 10 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ duration: 0.5 }}
						>
							<p className="text-yellow-200/70 text-sm mb-4">
								将向以下手机号发送验证码：
							</p>
							<p className="text-yellow-100 font-medium text-lg">
								{userData.phoneNumberMasked ||
									`${userData.phoneCountryCode || "+86"} ${userData.phoneNumber || ""}`}
							</p>
						</motion.div>

						{/* 验证码输入和密码设置 */}

						{/* 验证码输入 - 仿照LoginByCode样式 */}
						<motion.div
							initial={{ opacity: 0, y: 10 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ duration: 0.5 }}
						>
							<label
								htmlFor={`${id}-forgot-password-verification-code`}
								className="mb-2 block font-medium text-base text-yellow-100"
							>
								验证码
							</label>
							<VerificationCode
								id={`${id}-forgot-password-verification-code`}
								value={forgotPasswordData.verificationCode}
								onChange={(e) =>
									setForgotPasswordData((prev) => ({
										...prev,
										verificationCode: e.target.value,
									}))
								}
								onSendCode={handleSendVerificationCode}
								isValidPhone={true}
								disabled={resetPasswordByOTP.isPending}
							/>
						</motion.div>

						{/* 新密码设置 - 使用登录页面样式 */}
						<motion.div
							className="space-y-4"
							initial={{ opacity: 0, y: 10 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ duration: 0.5, delay: 0.2 }}
						>
							<div>
								<label
									htmlFor={`${id}-forgot-password-new-password`}
									className="block font-medium text-base text-yellow-100 mb-2"
								>
									新密码
								</label>
								<Input
									id={`${id}-forgot-password-new-password`}
									type="password"
									value={forgotPasswordData.newPassword}
									onChange={(e) =>
										setForgotPasswordData((prev) => ({
											...prev,
											newPassword: e.target.value,
										}))
									}
									placeholder="请输入新密码（至少6位）"
									disabled={resetPasswordByOTP.isPending}
									className="!placeholder-yellow-200/50 flex h-12 w-full rounded-xl border-none bg-white/5 px-6 py-3 text-gray-200 text-lg transition-all duration-300 ease-in-out hover:bg-white/10 focus:bg-white/20 focus:ring-2 focus:ring-yellow-100 disabled:opacity-50"
								/>
							</div>
							<div>
								<label
									htmlFor={`${id}-forgot-password-confirm-password`}
									className="block font-medium text-base text-yellow-100 mb-2"
								>
									确认新密码
								</label>
								<Input
									id={`${id}-forgot-password-confirm-password`}
									type="password"
									value={
										forgotPasswordData.confirmNewPassword
									}
									onChange={(e) =>
										setForgotPasswordData((prev) => ({
											...prev,
											confirmNewPassword: e.target.value,
										}))
									}
									placeholder="请再次输入新密码"
									disabled={resetPasswordByOTP.isPending}
									className="!placeholder-yellow-200/50 flex h-12 w-full rounded-xl border-none bg-white/5 px-6 py-3 text-gray-200 text-lg transition-all duration-300 ease-in-out hover:bg-white/10 focus:bg-white/20 focus:ring-2 focus:ring-yellow-100 disabled:opacity-50"
								/>
							</div>
						</motion.div>

						<motion.div
							className="flex justify-end gap-3 pt-4"
							initial={{ opacity: 0 }}
							animate={{ opacity: 1 }}
							transition={{ duration: 0.5, delay: 0.4 }}
						>
							<motion.div
								whileHover={{ scale: 1.02 }}
								whileTap={{ scale: 0.98 }}
							>
								<Button
									variant="outline"
									onClick={() =>
										setShowForgotPasswordDialog(false)
									}
									disabled={resetPasswordByOTP.isPending}
									className="h-12 px-6 rounded-xl bg-white/5 border border-yellow-200/30 text-yellow-200 hover:bg-white/10 transition-all duration-300"
								>
									取消
								</Button>
							</motion.div>
							<motion.div
								whileHover={{ scale: 1.02 }}
								whileTap={{ scale: 0.98 }}
							>
								<Button
									onClick={handleForgotPasswordReset}
									disabled={
										resetPasswordByOTP.isPending ||
										!forgotPasswordData.verificationCode ||
										!forgotPasswordData.newPassword ||
										!forgotPasswordData.confirmNewPassword
									}
									className="h-12 px-8 rounded-xl font-semibold text-lg tracking-wide transition-all duration-300 hover:opacity-90 hover:shadow-lg"
									style={{
										backgroundImage:
											resetPasswordByOTP.isPending ||
											!forgotPasswordData.verificationCode ||
											!forgotPasswordData.newPassword ||
											!forgotPasswordData.confirmNewPassword
												? "none"
												: "linear-gradient(100deg, #f0f0f0,#fff9ab)",
										backgroundColor:
											resetPasswordByOTP.isPending ||
											!forgotPasswordData.verificationCode ||
											!forgotPasswordData.newPassword ||
											!forgotPasswordData.confirmNewPassword
												? "#2c302b"
												: "transparent",
										color:
											resetPasswordByOTP.isPending ||
											!forgotPasswordData.verificationCode ||
											!forgotPasswordData.newPassword ||
											!forgotPasswordData.confirmNewPassword
												? "#a0aec0"
												: "#60533d",
										cursor:
											resetPasswordByOTP.isPending ||
											!forgotPasswordData.verificationCode ||
											!forgotPasswordData.newPassword ||
											!forgotPasswordData.confirmNewPassword
												? "not-allowed"
												: "pointer",
									}}
								>
									{resetPasswordByOTP.isPending ? (
										<div className="flex items-center space-x-2">
											<RefreshCw className="h-4 w-4 animate-spin" />
											<span>重置中...</span>
										</div>
									) : (
										"重置密码"
									)}
								</Button>
							</motion.div>
						</motion.div>
					</motion.div>
				</DialogContent>
			</Dialog>
		</div>
	);
}
