import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { CheckCircle } from "lucide-react";
import { useId } from "react";
import type { PersonalInfoFormProps } from "../types";

export function PersonalInfoForm({
	userData,
	formData,
	setFormData,
}: PersonalInfoFormProps) {
	const id = useId();
	return (
		<div className="border-t border-[#D4B485]/10 pt-6">
			<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
				<div className="space-y-2">
					<Label
						htmlFor={`${id}-name`}
						className="text-[#D4B485] font-medium"
					>
						用户名 *
					</Label>
					<Input
						id={`${id}-name`}
						value={formData.name}
						onChange={(e) =>
							setFormData((prev) => ({
								...prev,
								name: e.target.value,
							}))
						}
						placeholder="请输入用户名"
						className="bg-[#1A1C1E] border-[#D4B485]/30 text-[#D4B485] placeholder:text-[#D4B485]/40"
					/>
				</div>

				{/* 邮箱字段已隐藏 */}

				<div className="space-y-2">
					<Label
						htmlFor={`${id}-phone`}
						className="text-[#D4B485] font-medium flex items-center gap-2"
					>
						手机号码
						{userData.phoneNumberVerified && (
							<CheckCircle className="h-4 w-4 text-green-400" />
						)}
					</Label>
					<div className="flex gap-2">
						<Input
							value={userData.phoneCountryCode}
							disabled
							className="w-20 bg-[#1A1C1E]/50 border-[#D4B485]/20 text-[#D4B485]/60"
						/>
						<Input
							id={`${id}-phone`}
							value={formData.phoneNumber}
							disabled
							placeholder="手机号不可修改"
							className="flex-1 bg-[#1A1C1E]/50 border-[#D4B485]/20 text-[#D4B485]/60 cursor-not-allowed"
						/>
					</div>
				</div>

				<div className="space-y-2">
					<Label
						htmlFor={`${id}-backup`}
						className="text-[#D4B485] font-medium"
					>
						备用联系方式
					</Label>
					<Input
						id={`${id}-backup`}
						value={formData.backupContact}
						onChange={(e) =>
							setFormData((prev) => ({
								...prev,
								backupContact: e.target.value,
							}))
						}
						placeholder="微信号、QQ号等"
						className="bg-[#1A1C1E] border-[#D4B485]/30 text-[#D4B485] placeholder:text-[#D4B485]/40"
					/>
				</div>
			</div>
		</div>
	);
}
