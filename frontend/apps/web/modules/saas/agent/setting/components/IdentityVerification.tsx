import { Button } from "@ui/components/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import {
	AlertTriangle,
	CheckCircle,
	IdCard,
	RefreshCw,
	Shield,
	User,
} from "lucide-react";
import { useId, useState } from "react";
import { toast } from "sonner";
import { useIdentityVerification } from "../hooks/use-identity-verification";
import type { IdentityVerificationProps } from "../types";

export function IdentityVerification({
	userData,
	formData,
}: IdentityVerificationProps) {
	const [verifyFormData, setVerifyFormData] = useState({
		realName: formData.realName || "",
		idCardNumber: formData.idCardNumber || "",
	});
	const { verify, loading } = useIdentityVerification();
	const id = useId();

	// 表单验证
	const isFormValid =
		verifyFormData.realName.trim().length > 0 &&
		verifyFormData.idCardNumber.trim().length >= 15;

	// 身份证号格式验证
	const validateIdCard = (idCard: string) => {
		const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
		return reg.test(idCard);
	};

	// 处理认证提交
	const handleVerifySubmit = async () => {
		if (!isFormValid) {
			toast.error("请填写完整的身份信息");
			return;
		}

		if (!validateIdCard(verifyFormData.idCardNumber)) {
			toast.error("请输入正确的身份证号码");
			return;
		}

		const result = await verify({
			realName: verifyFormData.realName.trim(),
			idCardNumber: verifyFormData.idCardNumber.trim(),
		});

		if (result) {
			// 认证成功，重新加载页面以更新用户状态
			window.location.reload();
		}
	};

	// 重置表单
	const handleReset = () => {
		setVerifyFormData({
			realName: "",
			idCardNumber: "",
		});
	};

	return (
		<Card className="bg-gradient-to-br from-[#1E2023]/60 to-[#1A1C1E]/60 border border-[#D4B485]/20 backdrop-blur-md">
			<CardHeader>
				<CardTitle className="text-[#D4B485] flex items-center gap-2">
					<Shield className="h-5 w-5" />
					实名认证
					{userData.idCardVerified && (
						<CheckCircle className="h-5 w-5 text-green-400" />
					)}
				</CardTitle>
				<CardDescription className="text-[#D4B485]/60">
					{userData.idCardVerified
						? `已完成实名认证 (${userData.verifiedAt ? new Date(userData.verifiedAt).toLocaleDateString() : ""})`
						: "完成实名认证以提升账号安全性"}
				</CardDescription>
			</CardHeader>
			<CardContent className="space-y-6">
				{userData.idCardVerified ? (
					// 已认证状态 - 显示只读信息
					<div className="space-y-4">
						<div className="flex items-center gap-4 p-4 bg-gradient-to-r from-green-500/10 to-[#D4B485]/10 rounded-lg border border-green-500/20">
							<CheckCircle className="h-8 w-8 text-green-400" />
							<div>
								<p className="text-green-400 font-medium">
									实名认证已通过
								</p>
								<p className="text-[#D4B485]/60 text-sm">
									认证时间：
									{userData.verifiedAt
										? new Date(
												userData.verifiedAt,
											).toLocaleDateString()
										: ""}
								</p>
							</div>
						</div>

						<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
							<div className="space-y-2">
								<Label className="text-[#D4B485] font-medium flex items-center gap-2">
									<User className="h-4 w-4" />
									真实姓名
								</Label>
								<div className="px-3 py-2 bg-[#1A1C1E]/50 border border-[#D4B485]/20 rounded-md text-[#D4B485]">
									{userData.realNameMasked || "***"}
								</div>
							</div>
							<div className="space-y-2">
								<Label className="text-[#D4B485] font-medium flex items-center gap-2">
									<IdCard className="h-4 w-4" />
									身份证号码
								</Label>
								<div className="px-3 py-2 bg-[#1A1C1E]/50 border border-[#D4B485]/20 rounded-md text-[#D4B485] font-mono">
									{userData.idCardNumberMasked || "***"}
								</div>
							</div>
						</div>
					</div>
				) : (
					// 未认证状态 - 直接显示认证表单
					<div className="space-y-6">
						<div className="flex items-center gap-4 p-4 bg-gradient-to-r from-[#D4B485]/10 to-yellow-500/10 rounded-lg border border-[#D4B485]/20">
							<AlertTriangle className="h-8 w-8 text-[#D4B485]" />
							<div>
								<p className="text-[#D4B485] font-medium">
									需要完成实名认证
								</p>
								<p className="text-[#D4B485]/60 text-sm">
									请填写真实的身份信息，用于账号安全验证
								</p>
							</div>
						</div>

						<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
							<div className="space-y-2">
								<Label
									htmlFor={`${id}-verifyRealName`}
									className="text-[#D4B485] font-medium flex items-center gap-2"
								>
									<User className="h-4 w-4" />
									真实姓名 *
								</Label>
								<Input
									id={`${id}-verifyRealName`}
									value={verifyFormData.realName}
									onChange={(e) =>
										setVerifyFormData((prev) => ({
											...prev,
											realName: e.target.value,
										}))
									}
									placeholder="请输入真实姓名"
									disabled={loading}
									className="bg-[#1A1C1E] border-[#D4B485]/30 text-[#D4B485] placeholder:text-[#D4B485]/40 focus:border-[#D4B485] focus:ring-[#D4B485]/20"
								/>
							</div>

							<div className="space-y-2">
								<Label
									htmlFor={`${id}-verifyIdCard`}
									className="text-[#D4B485] font-medium flex items-center gap-2"
								>
									<IdCard className="h-4 w-4" />
									身份证号码 *
								</Label>
								<Input
									id={`${id}-verifyIdCard`}
									value={verifyFormData.idCardNumber}
									onChange={(e) =>
										setVerifyFormData((prev) => ({
											...prev,
											idCardNumber: e.target.value,
										}))
									}
									placeholder="请输入身份证号码"
									disabled={loading}
									maxLength={18}
									className="bg-[#1A1C1E] border-[#D4B485]/30 text-[#D4B485] placeholder:text-[#D4B485]/40 focus:border-[#D4B485] focus:ring-[#D4B485]/20"
								/>
							</div>
						</div>

						{/* 操作按钮 */}
						<div className="flex justify-center gap-3 pt-4">
							<Button
								variant="outline"
								onClick={handleReset}
								disabled={loading}
								className="bg-[#1A1C1E] border-[#D4B485]/30 text-[#D4B485] hover:bg-[#D4B485]/10"
							>
								重置
							</Button>
							<Button
								onClick={handleVerifySubmit}
								disabled={!isFormValid || loading}
								className="min-w-[160px] bg-gradient-to-r from-[#D4B485] to-[#B08968] text-[#1A1C1E] hover:from-[#B08968] hover:to-[#D4B485] px-8 disabled:opacity-50 disabled:cursor-not-allowed"
							>
								{loading ? (
									<>
										<RefreshCw className="h-4 w-4 mr-2 animate-spin" />
										认证中...
									</>
								) : (
									<>
										<Shield className="h-4 w-4 mr-2" />
										提交实名认证
									</>
								)}
							</Button>
						</div>

						{/* 提醒信息 */}
						<div className="p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
							<div className="flex items-start gap-3">
								<AlertTriangle className="h-5 w-5 text-yellow-400 mt-0.5" />
								<div>
									<h4 className="font-medium text-yellow-400 mb-1">
										隐私保护说明
									</h4>
									<ul className="text-sm text-yellow-400/80 space-y-1">
										<li>
											• 您的身份信息将通过加密技术安全存储
										</li>
										<li>
											•
											我们仅在必要时使用这些信息进行身份验证
										</li>
										<li>• 个人信息不会泄露给任何第三方</li>
										<li>
											• 认证完成后，敏感信息将自动脱敏显示
										</li>
										<li>
											•
											认证过程可能需要一点点时间，请耐心等待
										</li>
									</ul>
								</div>
							</div>
						</div>
					</div>
				)}
			</CardContent>
		</Card>
	);
}
