import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import { Switch } from "@ui/components/switch";
import { motion } from "framer-motion";
import { Bell } from "lucide-react";
import type { NotificationsTabProps } from "../types";

export function NotificationsTab({
	settings,
	setSettings,
}: NotificationsTabProps) {
	return (
		<div className="space-y-6 mt-6">
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ delay: 0.1 }}
			>
				<Card className="bg-gradient-to-br from-[#1E2023]/60 to-[#1A1C1E]/60 border border-[#D4B485]/20 backdrop-blur-md">
					<CardHeader>
						<CardTitle className="text-[#D4B485] flex items-center gap-2">
							<Bell className="h-5 w-5" />
							通知偏好
						</CardTitle>
						<CardDescription className="text-[#D4B485]/60">
							选择您希望接收的通知类型
						</CardDescription>
					</CardHeader>
					<CardContent className="space-y-6">
						<div className="space-y-4">
							<div className="flex items-center justify-between p-4 bg-[#1A1C1E]/50 rounded-lg border border-[#D4B485]/10">
								<div className="space-y-1">
									<h4 className="font-medium text-[#D4B485]">
										邮件通知
									</h4>
									<p className="text-sm text-[#D4B485]/60">
										接收重要账号活动的邮件通知
									</p>
								</div>
								<Switch
									checked={settings.emailNotifications}
									onCheckedChange={(checked) =>
										setSettings((prev) => ({
											...prev,
											emailNotifications: checked,
										}))
									}
									className="data-[state=checked]:bg-[#D4B485]"
								/>
							</div>

							<div className="flex items-center justify-between p-4 bg-[#1A1C1E]/50 rounded-lg border border-[#D4B485]/10">
								<div className="space-y-1">
									<h4 className="font-medium text-[#D4B485]">
										短信通知
									</h4>
									<p className="text-sm text-[#D4B485]/60">
										接收安全相关的短信通知
									</p>
								</div>
								<Switch
									checked={settings.smsNotifications}
									onCheckedChange={(checked) =>
										setSettings((prev) => ({
											...prev,
											smsNotifications: checked,
										}))
									}
									className="data-[state=checked]:bg-[#D4B485]"
								/>
							</div>

							<div className="flex items-center justify-between p-4 bg-[#1A1C1E]/50 rounded-lg border border-[#D4B485]/10">
								<div className="space-y-1">
									<h4 className="font-medium text-[#D4B485]">
										安全警报
									</h4>
									<p className="text-sm text-[#D4B485]/60">
										接收异常登录等安全警报
									</p>
								</div>
								<Switch
									checked={settings.securityAlerts}
									onCheckedChange={(checked) =>
										setSettings((prev) => ({
											...prev,
											securityAlerts: checked,
										}))
									}
									className="data-[state=checked]:bg-[#D4B485]"
								/>
							</div>

							<div className="flex items-center justify-between p-4 bg-[#1A1C1E]/50 rounded-lg border border-[#D4B485]/10">
								<div className="space-y-1">
									<h4 className="font-medium text-[#D4B485]">
										营销邮件
									</h4>
									<p className="text-sm text-[#D4B485]/60">
										接收产品更新和营销信息
									</p>
								</div>
								<Switch
									checked={settings.marketingEmails}
									onCheckedChange={(checked) =>
										setSettings((prev) => ({
											...prev,
											marketingEmails: checked,
										}))
									}
									className="data-[state=checked]:bg-[#D4B485]"
								/>
							</div>
						</div>
					</CardContent>
				</Card>
			</motion.div>
		</div>
	);
}
