// 基础用户数据接口（从后端返回的完整数据）
export interface UserData {
	id: string;
	name: string;
	phoneCountryCode: string;
	phoneNumber: string;
	phoneNumberFull: string;
	phoneNumberVerified: boolean;
	email: string;
	emailVerified: boolean;
	backupContact?: string | null;
	realName?: string | null;
	idCardNumber?: string | null;
	realNameMasked?: string | null;
	idCardNumberMasked?: string | null;
	idCardVerified: boolean;
	verifiedAt?: string | null;
	computingPower: number;
	totalComputingPower: number;
	status: string;
	createdAt: string;
	updatedAt: string;
	// 可能包含的其他脱敏字段
	phoneNumberMasked?: string;
	emailMasked?: string;
}

// 表单数据接口
export interface FormData {
	name: string;
	phoneNumber: string;
	email: string;
	backupContact: string;
	realName: string;
	idCardNumber: string;
	avatar: string;
}

// 通知设置接口
export interface NotificationSettings {
	emailNotifications: boolean;
	smsNotifications: boolean;
	securityAlerts: boolean;
	marketingEmails: boolean;
}

// 编辑权限接口
export interface Editability {
	name?: boolean;
	email?: boolean;
	phoneNumber?: boolean;
	backupContact?: boolean;
	realName?: boolean;
	idCardNumber?: boolean;
}

// 组件 Props 接口
export interface BasicInfoTabProps {
	userData: UserData;
	formData: FormData;
	setFormData: React.Dispatch<React.SetStateAction<FormData>>;
	editability: Editability | null;
	onSave: () => Promise<void>;
	onAvatarUpload: (file: File) => Promise<void>;
	isLoading: boolean;
}

export interface SecurityTabProps {
	userData: UserData;
}

export interface NotificationsTabProps {
	settings: NotificationSettings;
	setSettings: React.Dispatch<React.SetStateAction<NotificationSettings>>;
}

export interface AccountStatusTabProps {
	userData: UserData;
}

// 小组件 Props 接口
export interface AvatarUploadProps {
	avatar: string;
	onAvatarUpload: (file: File) => Promise<void>;
	onRemoveAvatar: () => void;
	isLoading: boolean;
}

export interface PersonalInfoFormProps {
	userData: UserData;
	formData: FormData;
	setFormData: React.Dispatch<React.SetStateAction<FormData>>;
}

export interface IdentityVerificationProps {
	userData: UserData;
	formData: FormData;
	setFormData: React.Dispatch<React.SetStateAction<FormData>>;
}
