import { formatDate, formatDistance, parseISO } from "date-fns";
import { useId } from "react";
import type { ChangelogItem } from "../types";

export function ChangelogSection({ items }: { items: ChangelogItem[] }) {
	const id = useId();
	return (
		<section id={id}>
			<div className="mx-auto grid w-full max-w-xl grid-cols-1 gap-4 text-left">
				{items?.map((item) => (
					<div
						key={item.date}
						className="rounded-xl border border-gray-700 bg-gray-800/50 backdrop-blur-sm p-6 transition-all hover:bg-gray-800/70 hover:border-[#e2fa53]/30 hover:shadow-[0_0_20px_rgba(226,250,83,0.1)]"
					>
						<small
							className="inline-block rounded-full border border-[#e2fa53]/30 bg-[#e2fa53]/10 px-3 py-1 font-semibold text-[#e2fa53] text-xs"
							title={formatDate(
								parseISO(item.date),
								"yyyy-MM-dd",
							)}
						>
							{formatDistance(parseISO(item.date), new Date(), {
								addSuffix: true,
							})}
						</small>
						<ul className="mt-4 list-disc space-y-2 pl-6 text-gray-300">
							{item.changes.map((change, j) => (
								<li
									key={`${item.date}-${j}`}
									className="marker:text-[#e2fa53]"
								>
									{change}
								</li>
							))}
						</ul>
					</div>
				))}
			</div>
		</section>
	);
}
