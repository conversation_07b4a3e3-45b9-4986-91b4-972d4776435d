"use client";

import { CardSpotlight } from "@ui/components/card-spotlight";
import { motion } from "framer-motion";

// 定义服务模式数据
const serviceModels = [
	{
		title: "AI智能体与生态共建  ",

		features: [
			"基于先进的AI智能体技术",
			"提供即用型或定制化解决方案",
			"诚邀各行业伙伴加入共创生态",
			"定义和构建面向未来的智能流量解决方案",
			"共享生态价值",
		],
	},
	{
		title: "流量策略赋能",
		features: [
			"系统化知识传递",
			"策略框架植入及实战指导",
			"AI+流量 赋能企业及个人",
			"核心策略+执行SOP+数据模型+工具",
		],
	},
	{
		title: "业务增长战略伙伴",
		features: [
			"产品体系规划：更适用于自媒体生态下的产品表达",
			"流量策略：多平台/多形态/多线条/国内+国外",
			"AI降本提效：拆解流程，精准的效率颠覆",
			"商业裂变：可持续的/指数级的增长模式",
		],
	},
];

export function ServiceModel() {
	return (
		<section className="py-20 lg:py-32 bg-white">
			<div className="container mx-auto px-4">
				<div className="text-center mb-16">
					<motion.h2
						className="text-4xl lg:text-5xl font-bold text-gray-800 mb-4"
						initial={{ opacity: 0, y: 50 }}
						whileInView={{ opacity: 1, y: 0 }}
						viewport={{ once: true, margin: "-100px" }}
						transition={{ duration: 0.8, ease: "easeOut" }}
					>
						合作模式 AI+流量
					</motion.h2>
					<motion.p
						className="text-gray-600 text-lg max-w-2xl mx-auto"
						initial={{ opacity: 0, y: 50 }}
						whileInView={{ opacity: 1, y: 0 }}
						viewport={{ once: true, margin: "-100px" }}
						transition={{
							duration: 0.8,
							ease: "easeOut",
							delay: 0.2,
						}}
					>
						需求驱动开发，AI颠覆效率， 沉淀流量资产，构建智能体系
					</motion.p>
				</div>

				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
					{serviceModels.map((model, index) => (
						<motion.div
							key={model.title}
							initial={{ opacity: 0, y: 50 }}
							whileInView={{ opacity: 1, y: 0 }}
							viewport={{ once: true }}
							transition={{ duration: 0.5, delay: index * 0.2 }}
						>
							<CardSpotlight className="h-[420px] w-full">
								<p className="text-xl font-bold relative z-20 mt-1 text-white">
									{model.title}
								</p>
								<div className="text-neutral-200 mt-3 relative z-20">
									{/* {model.description}: */}
									<ul className="list-none mt-6 space-y-5">
										{model.features.map((feature) => (
											<Step
												key={feature}
												title={feature}
											/>
										))}
									</ul>
								</div>
							</CardSpotlight>
						</motion.div>
					))}
				</div>
			</div>
		</section>
	);
}

const Step = ({ title }: { title: string }) => {
	return (
		<li className="flex gap-2 items-start">
			<CheckIcon />
			<p className="text-white">{title}</p>
		</li>
	);
};

const CheckIcon = () => {
	return (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			width="24"
			height="24"
			viewBox="0 0 24 24"
			fill="currentColor"
			className="h-4 w-4 text-primary mt-1 shrink-0"
		>
			<title>Check</title>
			<path stroke="none" d="M0 0h24v24H0z" fill="none" />
			<path
				d="M12 2c-.218 0 -.432 .002 -.642 .005l-.616 .017l-.299 .013l-.579 .034l-.553 .046c-4.785 .464 -6.732 2.411 -7.196 7.196l-.046 .553l-.034 .579c-.005 .098 -.01 .198 -.013 .299l-.017 .616l-.004 .318l-.001 .324c0 .218 .002 .432 .005 .642l.017 .616l.013 .299l.034 .579l.046 .553c.464 4.785 2.411 6.732 7.196 7.196l.553 .046l.579 .034c.098 .005 .198 .01 .299 .013l.616 .017l.642 .005l.642 -.005l.616 -.017l.299 -.013l.579 -.034l.553 -.046c4.785 -.464 6.732 -2.411 7.196 -7.196l.046 -.553l.034 -.579c.005 -.098 .01 -.198 .013 -.299l.017 -.616l.005 -.642l-.005 -.642l-.017 -.616l-.013 -.299l-.034 -.579l-.046 -.553c-.464 -4.785 -2.411 -6.732 -7.196 -7.196l-.553 -.046l-.579 -.034a28.058 28.058 0 0 0 -.299 -.013l-.616 -.017l-.318 -.004l-.324 -.001zm2.293 7.293a1 1 0 0 1 1.497 1.32l-.083 .094l-4 4a1 1 0 0 1 -1.32 .083l-.094 -.083l-2 -2a1 1 0 0 1 1.32 -1.497l.094 .083l1.293 1.292l3.293 -3.292z"
				fill="currentColor"
				strokeWidth="0"
			/>
		</svg>
	);
};
