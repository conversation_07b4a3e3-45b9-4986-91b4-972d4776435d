import { lzzmFont } from "@saas/ecosystem/avatar/components/layout/font/fonts";
import { cn } from "@ui/lib";
import type React from "react";

const CardSystem: React.FC = () => {
	return (
		<div className="grid grid-cols-1 md:grid-cols-2 justify-center items-stretch gap-8 p-4 md:p-8 bg-black">
			{/* Card 1 - Green Glow with Video */}
			<div className="relative overflow-hidden bg-gray-900 rounded-xl shadow-lg p-6 flex flex-col animate-inward-glow-green">
				{/* Content Wrapper - Now holds the video */}
				<div className="relative z-10">
					<video
						className="w-full h-auto rounded-lg max-h-120 object-cover"
						autoPlay
						loop
						muted
						playsInline
					>
						<source
							src="https://cminio.9000aigc.com/hero/digitales.mp4"
							type="video/mp4"
						/>
						Your browser does not support the video tag.
					</video>
				</div>
				<div className="absolute top-6 left-0 right-0 p-4 bg-gradient-to-b from-black/60 to-transparent pointer-events-none z-20">
					<p
						className={cn(
							"text-3xl md:text-4xl font-bold text-center",
							lzzmFont.className,
						)}
						style={{
							background:
								"linear-gradient(to bottom, #feffd9, #dcf494, #8ae086, #56c350)",
							WebkitBackgroundClip: "text",
							WebkitTextFillColor: "transparent",
							backgroundClip: "text",
							textShadow: "0 2px 4px rgba(0,0,0,0.2)",
						}}
					>
						9000AI增强版数字人
					</p>
				</div>
			</div>

			{/* Card 2 - Gold Glow with Video */}
			<div className="relative overflow-hidden bg-gray-900 rounded-xl shadow-lg p-6 flex-1 flex flex-col animate-inward-glow-gold">
				{/* Content Wrapper - Now holds the video */}
				<div className="relative z-10">
					<video
						className="w-full h-auto rounded-lg max-h-120 object-cover"
						autoPlay
						loop
						muted
						playsInline
					>
						<source
							src="https://cminio.9000aigc.com/hero/agen.mp4"
							type="video/mp4"
						/>
						Your browser does not support the video tag.
					</video>
				</div>
				<div className="absolute top-6 left-0 right-0 p-4 bg-gradient-to-b from-black/60 to-transparent pointer-events-none z-20">
					<p
						className={cn(
							"text-3xl md:text-4xl font-bold text-center",
							lzzmFont.className,
						)}
						style={{
							background:
								"linear-gradient(to bottom, #fff6d9, #f4d794, #cea561, #f0daa4)",
							WebkitBackgroundClip: "text",
							WebkitTextFillColor: "transparent",
							backgroundClip: "text",
							textShadow: "0 2px 4px rgba(0,0,0,0.2)",
						}}
					>
						9000AI合伙人专用系统
					</p>
				</div>
			</div>

			{/* Card 3 - 新增卡片 (Orange/Red Glow) */}
			<div className="relative overflow-hidden bg-gray-900 rounded-xl shadow-lg p-6 flex flex-col animate-inward-glow-orange-red">
				<div className="relative z-10 rounded-lg overflow-hidden max-h-120">
					{/* 视频元素 */}
					<video
						className="w-full h-auto object-cover"
						autoPlay
						loop
						muted
						playsInline
					>
						<source
							src="https://cminio.9000aigc.com/hero/video_splice.mp4"
							type="video/mp4"
						/>
						Your browser does not support the video tag.
					</video>
					{/* 在这里添加蒙版层 */}
					<div className="absolute inset-0 bg-black/50" />
				</div>

				<div className="absolute top-6 left-0 right-0 p-4 bg-gradient-to-b from-black/60 to-transparent pointer-events-none z-20">
					<p
						className={cn(
							"text-3xl md:text-4xl font-bold text-center",
							lzzmFont.className,
						)}
						style={{
							background:
								"linear-gradient(to bottom right, #ffe501, #fd7841, #e9522a, #e0360b)",
							WebkitBackgroundClip: "text",
							WebkitTextFillColor: "transparent",
							backgroundClip: "text",
							textShadow: "0 2px 4px rgba(0,0,0,0.2)",
						}}
					>
						AI切片剪辑
					</p>
				</div>
			</div>

			{/* Card 4 - 新增卡片 (Yellow/Blue/Purple Glow) */}
			<div className="relative overflow-hidden bg-gray-900 rounded-xl shadow-lg p-6 flex flex-col animate-inward-glow-yellow-blue-purple">
				<div className="relative z-10">
					<video
						className="w-full h-auto rounded-lg max-h-120 object-cover"
						autoPlay
						loop
						muted
						playsInline
					>
						<source
							src="https://cminio.9000aigc.com/hero/picture_text.mp4"
							type="video/mp4"
						/>
						Your browser does not support the video tag.
					</video>
				</div>
				<div className="absolute top-6 left-0 right-0 p-4 bg-gradient-to-b from-black/60 to-transparent pointer-events-none z-20">
					<p
						className={cn(
							"text-3xl md:text-4xl font-bold text-center",
							lzzmFont.className,
						)}
						style={{
							background:
								"linear-gradient(to bottom left, #fcea3c, #20a0de, #886492, #4b40be)",
							WebkitBackgroundClip: "text",
							WebkitTextFillColor: "transparent",
							backgroundClip: "text",
							textShadow: "0 2px 4px rgba(0,0,0,0.2)",
						}}
					>
						AI图文裂变
					</p>
				</div>
			</div>
		</div>
	);
};

export default CardSystem;
