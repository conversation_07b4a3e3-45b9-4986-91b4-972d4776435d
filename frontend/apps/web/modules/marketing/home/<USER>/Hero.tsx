"use client";

import { Button } from "@ui/components/button";
import { motion } from "framer-motion";
import Link from "next/link";
import { useEffect, useRef } from "react";

export function Hero() {
	const videoRef = useRef<HTMLVideoElement>(null);

	// 优化视频加载和播放
	useEffect(() => {
		const videoElement = videoRef.current;
		if (!videoElement) {
			return;
		}

		// 确保视频质量
		const handleCanPlay = () => {
			if (videoElement.videoWidth) {
			}
		};

		videoElement.addEventListener("canplay", handleCanPlay);

		// 监听视频加载错误
		const handleError = () => {};

		videoElement.addEventListener("error", handleError);

		return () => {
			videoElement.removeEventListener("canplay", handleCanPlay);
			videoElement.removeEventListener("error", handleError);
		};
	}, []);

	return (
		<section className="w-full pt-12 pb-2 md:pt-16 md:pb-4 lg:pt-20 lg:pb-6 px-4 md:px-6 lg:px-8 bg-black">
			{/* 圆角容器，宽度与页面相同，高度为视口高度的2/3 */}
			<motion.div
				className="w-full h-[66vh] rounded-3xl overflow-hidden shadow-lg border border-gray-800 relative bg-black"
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.8, ease: "easeOut" }}
			>
				{/* 视频背景 - 移除任何额外的样式和滤镜 */}
				<video
					ref={videoRef}
					className="absolute inset-0 w-full h-full"
					autoPlay
					loop
					muted
					playsInline
					preload="auto"
					style={{
						objectFit: "cover",
						width: "100%",
						height: "100%",
					}}
				>
					<source
						src="https://cminio.9000aigc.com/hero/banners.mp4"
						type="video/mp4"
					/>
					你的浏览器不支持视频标签
				</video>

				{/* 添加一个较暗的渐变覆盖层，让白色文字更易读 */}
				<div className="absolute inset-0 bg-gradient-to-t from-black/50 to-black/20 pointer-events-none" />

				{/* 文字叠加层 - 向下移动位置 */}
				<div className="absolute inset-0 flex flex-col items-center justify-center pt-36">
					<motion.div
						className="text-center px-4 max-w-4xl mx-auto"
						initial={{ opacity: 0, y: 30 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.8, delay: 0.3 }}
					>
						<motion.h1
							className="text-4xl md:text-5xl lg:text-5xl xl:text-6xl font-bold tracking-tight text-white mb-4 drop-shadow-md"
							initial={{ opacity: 0, y: 20 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ duration: 0.8, delay: 0.4 }}
						>
							引爆流量的超级智能体
						</motion.h1>
						<motion.h2
							className="text-xl md:text-2xl lg:text-2xl font-medium mb-10 text-white"
							initial={{ opacity: 0, y: 20 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ duration: 0.8, delay: 0.6 }}
						>
							让中国IP走向世界
						</motion.h2>
						<motion.div
							initial={{ opacity: 0, y: 20 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ duration: 0.8, delay: 0.8 }}
						>
							<Link href="/auth" className="inline-block">
								<Button
									size="lg"
									className="bg-white text-black hover:bg-white/90 text-base font-medium px-10 py-6 rounded-full shadow-md transition-transform hover:scale-105"
								>
									立即体验
								</Button>
							</Link>
						</motion.div>
					</motion.div>
				</div>
			</motion.div>
		</section>
	);
}
