"use client";

import { motion, useScroll, useTransform } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { useRef } from "react";

interface ToolItem {
	id: string;
	icon: string;
	title: string;
	subtitle: string;
	details?: {
		topic: string;
		wordCount: string;
		quantity: string;
		generatedContent: string[][];
	};
}

const toolItems: ToolItem[] = [
	{
		id: "tool1",
		icon: "https://cdn-static.chanxuan.com/sub-module/static-file/f/e/2faab92d79",
		title: "热点文案跟创",
		subtitle: "批量跟创热门口播文案",
		details: {
			topic: "如何用好DeepSeek",
			wordCount: "100字 20s口播",
			quantity: "3条",
			generatedContent: [
				[
					"兄弟们，出了个大事儿啊，AI领域的多位大佬共同签署了份公开",
					"信。DeepSeek作为新兴的AI工具，正改变着我们的生活和工作方",
					"式。它不仅能高效处理海量数据，还能精准分析用户需求，提供个",
					"性化的解决方案。如何用好DeepSeek？首先，深入学习其核心功",
					"能，掌握数据标注和模型训练技巧；其次，结合实际应用场景，不",
					"断优化参数配置。最后，积极参与社区交流，获取最新资讯和技术",
					"支持。掌握了这些，你就能在AI浪潮中脱颖而出，成为真正的技术",
					"大神",
				],
				[
					"各位AI爱好者的黑科技，你体验过吗？DeepSeek是一款强大的AI工",
					"具，能帮你轻松处理复杂的数据分析和模型训练。它不仅简化了开",
					"发流程，还能显著提升效率。作为年轻人，我们正处在AI爆发的时",
					"代，不跟上就容易掉队。学会使用DeepSeek，不仅能让你在职场",
					"局限性，过度依赖可能导致思维僵化。赶快行动起来，加入",
					"DeepSeek的用户行列，开启你的AI新征程吧",
				],
				[
					"今天又要给大家上干货了，而且是超级大的那种，那就是关于如何",
					"用好DeepSeek的事。DeepSeek作为最新的AI工具，正在成为年轻",
					"人的新宠。你是否觉得AI技术太高深？其实不然，DeepSeek通过简",
					"单易懂的操作界面和强大的功能，让每个人都能快速上手。无论是",
					"学习编程、优化算法，还是进行数据分析，DeepSeek都能助你一",
					"臂之力。现在，越来越多的年轻人已经开始利用它来提升自己的竞",
					"争力。别再犹豫，赶快加入他们，一起探索AI的无限可能吧",
				],
			],
		},
	},
	{
		id: "tool2",
		icon: "https://cdn-static.chanxuan.com/sub-module/static-file/0/a/c84399f797",
		title: "电商图创作助手",
		subtitle: "生成指定商品和模特的海报",
	},
	{
		id: "tool3",
		icon: "https://cdn-static.chanxuan.com/sub-module/static-file/4/2/4b5646798d",
		title: "手持商品模特图",
		subtitle: "上传商品图一键生成",
	},
	{
		id: "tool4",
		icon: "https://cdn-static.chanxuan.com/sub-module/static-file/4/b/0fc1ffbb55",
		title: "生成式数字人",
		subtitle: "打造虚拟IP形象",
	},
	{
		id: "tool5",
		icon: "https://cdn-static.chanxuan.com/sub-module/static-file/8/f/ce215669d6",
		title: "生成视频素材片段",
		subtitle: "文生视频&图生视频",
	},
	{
		id: "tool6",
		icon: "https://cdn-static.chanxuan.com/sub-module/static-file/d/5/96e4a73d36",
		title: "短视频文案改写",
		subtitle: "一键跟创爆款",
	},
];

function DetailedCardContent({ item }: { item: ToolItem }) {
	if (!item.details) {
		return null;
	}

	return (
		<div className="ai-tool-bd-content flex flex-col md:flex-row gap-6 p-5">
			<div className="ai-tool-bd-left flex-shrink-0 w-full md:w-1/3 space-y-4">
				<div className="tool-left-item bg-gray-100 p-3 rounded-lg">
					<div className="title text-xs text-gray-500 mb-1">
						视频话题
					</div>
					<div className="item-bd font-medium text-gray-800">
						{item.details.topic}
					</div>
				</div>
				<div className="tool-left-item bg-gray-100 p-3 rounded-lg">
					<div className="title text-xs text-gray-500 mb-1">
						内容字数
					</div>
					<div className="item-bd font-medium text-gray-800">
						{item.details.wordCount}
					</div>
				</div>
				<div className="tool-left-item bg-gray-100 p-3 rounded-lg">
					<div className="title text-xs text-gray-500 mb-1">
						生成数量
					</div>
					<div className="item-bd font-medium text-gray-800">
						{item.details.quantity}
					</div>
				</div>
				<button
					type="button"
					className="tool-left-item mt-4 w-full p-3 bg-blue-500 text-white rounded-lg font-medium hover:bg-blue-600 transition"
				>
					立即生成
				</button>
			</div>
			<div className="ai-tool-bd-right flex-grow space-y-3 overflow-hidden">
				{item.details.generatedContent.map(
					(contentBlock, blockIndex) => (
						<div
							key={blockIndex}
							className="tool-writer-item bg-gray-50 p-3 rounded-lg text-xs text-gray-600 leading-relaxed"
						>
							{contentBlock.map((line, lineIndex) => (
								<motion.div
									key={lineIndex}
									className="tool-writer-text"
									initial={{ opacity: 0 }}
									animate={{ opacity: 1 }}
									transition={{
										delay:
											0.2 +
											blockIndex * 0.2 +
											lineIndex * 0.05,
									}}
								>
									{line}
								</motion.div>
							))}
						</div>
					),
				)}
			</div>
		</div>
	);
}

export function TestCard() {
	const containerRef = useRef<HTMLDivElement>(null);
	const { scrollYProgress } = useScroll({
		target: containerRef,
		offset: ["start end", "end start"],
	});

	const firstCard = toolItems[0];
	const gridItems = toolItems;

	const cardScale = useTransform(scrollYProgress, [0, 0.3], [1, 0.6]);
	const cardY = useTransform(scrollYProgress, [0, 0.3], ["0%", "-20%"]);
	const cardX = useTransform(scrollYProgress, [0, 0.3], ["0%", "-35%"]);
	const cardOpacity = useTransform(scrollYProgress, [0.25, 0.4], [1, 0]);

	const gridOpacity = useTransform(scrollYProgress, [0.3, 0.5], [0, 1]);
	const gridScale = useTransform(scrollYProgress, [0.3, 0.5], [0.9, 1]);

	return (
		<section
			ref={containerRef}
			className="w-full py-16 md:py-24 lg:py-32 overflow-hidden bg-gradient-to-b from-white to-gray-50 relative min-h-[1000px]"
		>
			<motion.div
				className="aidata-mask absolute inset-0 z-0 opacity-30 md:opacity-40 pointer-events-none"
				style={{
					backgroundImage:
						"url(https://cdn-static.chanxuan.com/sub-module/static-file/2/3/eccc5a3f7d)",
					backgroundSize: "cover",
					backgroundPosition: "center right",
				}}
				initial={{ opacity: 0 }}
				animate={{ opacity: 0.4 }}
				transition={{ duration: 1 }}
			/>

			<div className="aidata-conetent max-w-7xl mx-auto px-4 md:px-6 lg:px-8 flex flex-col md:flex-row relative z-10">
				<div className="aidata-left w-full md:w-2/3 relative h-[600px] md:h-[800px]">
					<motion.div
						className="absolute top-10 left-4 md:left-6 w-[calc(100%-2rem)] md:w-[calc(100%-3rem)] lg:w-[calc(100%-4rem)] z-20 origin-top-left"
						style={{
							scale: cardScale,
							y: cardY,
							x: cardX,
							opacity: cardOpacity,
						}}
					>
						<div className="ai-tool-item bg-white/90 backdrop-blur-lg rounded-xl shadow-2xl overflow-hidden">
							<div className="ai-tool-hd p-5 flex items-center border-b border-gray-200">
								<Image
									src={firstCard.icon}
									alt={firstCard.title}
									width={48}
									height={48}
									className="tool-img rounded-lg"
								/>
								<div className="ml-4">
									<div className="tool-title text-lg font-bold text-gray-800">
										{firstCard.title}
									</div>
									<div className="tool-subtitle text-sm text-gray-600">
										{firstCard.subtitle}
									</div>
								</div>
							</div>
							<DetailedCardContent item={firstCard} />
						</div>
					</motion.div>

					<motion.div
						className="ai-tool-wrap grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6 absolute inset-0 pt-10 z-10"
						style={{
							opacity: gridOpacity,
							scale: gridScale,
						}}
					>
						{gridItems.map((tool, _index) => (
							<div
								key={tool.id}
								className="ai-tool-item-small bg-white/80 backdrop-blur-md rounded-xl shadow-lg overflow-hidden h-[120px] md:h-[150px] flex items-center p-4 md:p-5"
							>
								<Image
									src={tool.icon}
									alt={tool.title}
									width={40}
									height={40}
									className="tool-img rounded-lg flex-shrink-0"
								/>
								<div className="ml-4 overflow-hidden">
									<div className="tool-title text-base md:text-lg font-bold text-gray-800 truncate">
										{tool.title}
									</div>
									<div className="tool-subtitle text-xs md:text-sm text-gray-600 truncate">
										{tool.subtitle}
									</div>
								</div>
							</div>
						))}
					</motion.div>
				</div>

				<div className="aidata-right w-full md:w-1/3 mt-12 md:mt-40 flex flex-col justify-start items-center md:items-start md:pl-10 lg:pl-16 relative z-30">
					<motion.div
						className="aidata-title text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 md:mb-6 text-center md:text-left"
						initial={{ opacity: 0, y: 20 }}
						whileInView={{ opacity: 1, y: 0 }}
						viewport={{ once: true }}
						transition={{ duration: 0.5, delay: 0.1 }}
					>
						AI灵感工具
					</motion.div>
					<motion.div
						className="aidata-subtitle text-lg md:text-xl leading-relaxed text-gray-700 mb-8 md:mb-10 text-center md:text-left"
						initial={{ opacity: 0, y: 20 }}
						whileInView={{ opacity: 1, y: 0 }}
						viewport={{ once: true }}
						transition={{ duration: 0.5, delay: 0.2 }}
					>
						爆款一键翻拍
						<br />
						让AI承包创作全链路
					</motion.div>
					<motion.div
						initial={{ opacity: 0, y: 20 }}
						whileInView={{ opacity: 1, y: 0 }}
						viewport={{ once: true }}
						transition={{ duration: 0.5, delay: 0.3 }}
						className="text-center md:text-left"
					>
						<Link href="/ai-data-home" target="_blank">
							<button
								type="button"
								className="aidata-btn px-8 py-3 bg-gray-900 text-white font-medium rounded-full hover:bg-gray-700 transition-colors shadow-lg"
							>
								去试试
							</button>
						</Link>
					</motion.div>
				</div>
			</div>
		</section>
	);
}
