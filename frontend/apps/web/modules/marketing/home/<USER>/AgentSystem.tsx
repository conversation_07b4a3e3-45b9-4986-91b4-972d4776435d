"use client";

import { cn } from "@ui/lib";
import { AnimatePresence, motion } from "framer-motion";
import { BarChart3, Network, Package, Ticket } from "lucide-react";
import Image from "next/image";
import { useId, useState } from "react";

// 1. 定义功能数据结构
const features = [
	{
		id: "architecture",
		title: "搞流量",
		icon: Network,
		description:
			"9000AI智能体 x 行业领跑者，联合发声！我们正构建一个充满活力的超级流量生态，共享颠覆式创新。每一次联合推广，都是向市场宣告：我们正与最优秀的伙伴一起，用最前沿的方式，重塑流量格局。加入我们，就是站在浪潮之巅。",
		highlights: [
			"每个行业，只选一个战略伙伴！",
			"部分系统及智能体不外售，合伙人有专享权",
			"基于合伙人业务场景的AI+流量 定向赋能",
			"一个高壁垒、高增长、高协同的商业共同体",
		],
		imagePlaceholderText: "代理架构图表界面截图",
		imageComment: "代理商团队结构图",
		imageSrc: "/images/feature/agent/agent_architecture.webp",
	},
	{
		id: "performance",
		title: "用AI",
		icon: BarChart3,
		description: "全方位的数据追踪和奖励机制，做到参与及分享红利！",
		highlights: [
			"实时统计与数据分析，深度洞悉市场动态与增长机遇。",
			"精准衡量市场拓展成效，构建贡献导向的激励体系。",
			"透明化合作成果与价值共享，共赢市场增长新未来",
		],
		imagePlaceholderText: "业绩管理面板截图",
		imageComment: "业绩统计或收益面板",
		imageSrc: "/images/feature/agent/agent_achieve.png",
	},
	{
		id: "quota",
		title: "抓趋势",
		icon: Ticket,
		description:
			"9000AI智能体 x 行业领跑者，联合发声！我们正构建一个充满活力的超级流量生态，共享颠覆式创新。每一次联合推广，都是向市场宣告：我们正与最优秀的伙伴一起，用最前沿的方式，重塑流量格局。加入我们，就是站在浪潮之巅。",
		highlights: [
			"每个行业，只选一个战略伙伴！",
			"部分系统及智能体不外售，合伙人有专享权",
			"基于合伙人业务场景的AI+流量 定向赋能",
			"一个高壁垒、高增长、高协同的商业共同体",
		],
		imagePlaceholderText: "名额分配界面截图",
		imageComment: "名额管理或分配记录",
		imageSrc: "/images/feature/agent/agent_places.png",
	},
	{
		id: "package",
		title: "做增长",
		icon: Package,
		description: "全方位的数据追踪和奖励机制，做到参与及分享红利！",
		highlights: [
			"实时统计与数据分析，深度洞悉市场动态与增长机遇。",
			"精准衡量市场拓展成效，构建贡献导向的激励体系。",
			"透明化合作成果与价值共享，共赢市场增长新未来",
		],
		imagePlaceholderText: "套餐激活流程截图",
		imageComment: "为客户激活套餐的操作界面",
		imageSrc: "/images/feature/agent/agent_package.png",
	},
];

export function AgentSystem() {
	// 2. 添加状态记录当前激活的功能 ID
	const [activeFeatureId, setActiveFeatureId] = useState(features[0].id);
	const id = useId();

	// 3. 根据 ID 查找对应的功能数据
	const activeFeature = features.find((f) => f.id === activeFeatureId);

	return (
		<section
			id={id}
			className="relative scroll-my-20 py-12 md:py-16 lg:py-24 bg-gray-50 bg-no-repeat bg-right-top"
			style={{
				backgroundImage: "url(/images/feature/bg/bg-ones.png)",
				backgroundSize: "48%",
				backgroundPosition: "80% 5%",
			}}
		>
			<div className="container max-w-6xl relative z-10">
				<div className="text-center mb-8 md:mb-10">
					<motion.h2
						className="text-3xl md:text-4xl lg:text-5xl font-bold mb-3 md:mb-4 bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent"
						initial={{ opacity: 0, y: 50 }}
						whileInView={{ opacity: 1, y: 0 }}
						viewport={{ once: true, margin: "-100px" }}
						transition={{ duration: 0.8, ease: "easeOut" }}
					>
						9000AI合伙人专用系统
					</motion.h2>
					<motion.p
						className="text-gray-600 text-lg max-w-2xl mx-auto"
						initial={{ opacity: 0, y: 50 }}
						whileInView={{ opacity: 1, y: 0 }}
						viewport={{ once: true, margin: "-100px" }}
						transition={{
							duration: 0.8,
							ease: "easeOut",
							delay: 0.2,
						}}
					>
						智能体联合发布，深度赋能合伙人流量策略与商业模式升级，共拓蓝海市场，驱动业务指数级增长。
					</motion.p>
				</div>

				<div className="flex flex-wrap justify-center gap-3 mb-10">
					{features.map((feature) => (
						<motion.button
							key={feature.id}
							onClick={() => setActiveFeatureId(feature.id)}
							onMouseEnter={() => setActiveFeatureId(feature.id)}
							className={cn(
								"flex items-center gap-2 px-4 py-3 rounded-lg transition-all duration-300",
								"border",
								activeFeatureId === feature.id
									? "bg-white border-[#b89357] shadow-md"
									: "bg-white border-gray-200 hover:border-gray-300 hover:bg-gray-50 hover:shadow-sm",
							)}
							whileHover={{ y: -2 }}
							whileTap={{ y: 0 }}
						>
							<feature.icon
								className={cn(
									"h-5 w-5 shrink-0 transition-colors",
									activeFeatureId === feature.id
										? "text-[#b89357]"
										: "text-gray-500",
								)}
							/>
							<span
								className={cn(
									"font-medium transition-colors whitespace-nowrap",
									activeFeatureId === feature.id
										? "text-[#b89357] font-semibold"
										: "text-gray-700",
								)}
							>
								{feature.title}
							</span>
						</motion.button>
					))}
				</div>

				<AnimatePresence mode="wait">
					{activeFeature && (
						<motion.div
							key={activeFeature.id}
							initial={{ opacity: 0, y: 20 }}
							animate={{ opacity: 1, y: 0 }}
							exit={{ opacity: 0, y: -20 }}
							transition={{
								duration: 0.4,
								ease: "easeInOut",
							}}
							className="grid grid-cols-1 lg:grid-cols-5 gap-8 lg:gap-12"
						>
							<div className="lg:col-span-2 space-y-4 md:space-y-6">
								<h3 className="text-2xl font-bold text-gray-800 lg:hidden">
									{activeFeature.title}
								</h3>

								<p className="text-gray-700 text-base leading-relaxed">
									{activeFeature.description}
								</p>

								<ul className="space-y-2 md:space-y-3 text-gray-600">
									{activeFeature.highlights.map(
										(highlight, index) => (
											<li
												key={index}
												className="flex items-start"
											>
												<span className="mr-2 mt-1 text-primary font-bold text-sm">
													•
												</span>
												<span>{highlight}</span>
											</li>
										),
									)}
								</ul>
							</div>

							<div
								className="lg:col-span-3 relative h-64 sm:h-80 md:h-96 overflow-hidden rounded-xl
                                        border border-white/20 bg-white/10 backdrop-blur-lg
                                        shadow-2xl shadow-gray-400/35
                                        before:absolute before:inset-0 before:bg-gradient-to-br before:from-white/30 before:to-white/10 before:pointer-events-none"
							>
								<Image
									src={activeFeature.imageSrc}
									alt={activeFeature.imagePlaceholderText}
									fill
									className="object-contain p-1 sm:p-1 md:p-2 z-10 relative rounded-xl"
									sizes="(max-width: 1024px) 90vw, 60vw"
									priority={
										activeFeatureId === activeFeature.id
									}
								/>
							</div>
						</motion.div>
					)}
				</AnimatePresence>
			</div>
		</section>
	);
}
