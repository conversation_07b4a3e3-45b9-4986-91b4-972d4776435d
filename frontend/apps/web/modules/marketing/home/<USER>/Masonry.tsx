import { motion, useInView, type Variants } from "framer-motion";
import Image from "next/image";
import type React from "react";
import { useEffect, useMemo, useRef, useState } from "react";

// Removed import "./Masonry.css";

interface MasonryItem {
	id: string | number;
	height: number;
	image: string;
	// Add any additional properties if needed
}

interface GridItem extends MasonryItem {
	x: number;
	y: number;
	width: number;
	height: number; // This represents the scaled height
}

interface MasonryProps {
	data: MasonryItem[];
}

const containerVariants: Variants = {
	hidden: { opacity: 0 },
	visible: {
		opacity: 1,
		transition: {
			staggerChildren: 0.1,
		},
	},
};

const itemVariants: Variants = {
	hidden: { opacity: 0, scale: 0.8, y: 50 },
	visible: {
		opacity: 1,
		scale: 1,
		y: 0,
		transition: {
			type: "spring",
			stiffness: 100,
			damping: 12,
		},
	},
};

const Masonry: React.FC<MasonryProps> = ({ data }) => {
	const containerRef = useRef<HTMLDivElement>(null);
	const isInView = useInView(containerRef, { once: true, amount: 0.2 });
	const [shouldRender, setShouldRender] = useState(false);

	useEffect(() => {
		if (isInView) {
			setShouldRender(true);
		}
	}, [isInView]);

	// 强制重新计算布局的函数
	useEffect(() => {
		if (shouldRender && ref.current) {
			// 使用 requestAnimationFrame 确保在下一帧重新计算
			const rafId = requestAnimationFrame(() => {
				if (ref.current) {
					const rect = ref.current.getBoundingClientRect();
					console.log("Force recalculate - Container rect:", rect);
					setWidth(rect.width);
				}
			});
			return () => cancelAnimationFrame(rafId);
		}
	}, [shouldRender]);

	const [columns, setColumns] = useState<number>(2);
	const ref = useRef<HTMLDivElement>(null);
	const [width, setWidth] = useState<number>(0);

	useEffect(() => {
		const updateColumns = () => {
			if (window.matchMedia("(min-width: 1500px)").matches) {
				setColumns(5);
			} else if (window.matchMedia("(min-width: 1000px)").matches) {
				setColumns(4);
			} else if (window.matchMedia("(min-width: 600px)").matches) {
				setColumns(3);
			} else {
				setColumns(1); // Breakpoint for mobile devices
			}
		};

		updateColumns();
		window.addEventListener("resize", updateColumns);
		return () => window.removeEventListener("resize", updateColumns);
	}, []);

	useEffect(() => {
		const handleResize = () => {
			if (ref.current) {
				const newWidth = ref.current.offsetWidth;
				console.log("Container width:", newWidth); // 调试日志
				setWidth(newWidth);
			}
		};

		// 延迟执行以确保DOM完全渲染
		const timer = setTimeout(() => {
			handleResize(); // Set initial width
		}, 100);

		window.addEventListener("resize", handleResize);
		return () => {
			clearTimeout(timer);
			window.removeEventListener("resize", handleResize);
		};
	}, [shouldRender]); // 依赖shouldRender以确保在组件完全渲染后计算宽度

	const [heights, gridItems] = useMemo<[number[], GridItem[]]>(() => {
		// 如果宽度为0，返回空数组避免布局错误
		if (width <= 0) {
			return [new Array(columns).fill(0), []];
		}

		const heights = new Array(columns).fill(0);
		const gridItems = data.map((child) => {
			const column = heights.indexOf(Math.min(...heights));
			const itemWidth = width / columns;
			const x = itemWidth * column;
			const itemHeight = child.height / 2;
			const y = heights[column];
			heights[column] += itemHeight;

			console.log(`Item ${child.id}: width=${itemWidth}, x=${x}, y=${y}`); // 调试日志

			return {
				...child,
				x,
				y,
				width: itemWidth,
				height: itemHeight,
			};
		});
		return [heights, gridItems];
	}, [columns, data, width]);

	if (!shouldRender) {
		return (
			<div
				ref={containerRef}
				className="w-full h-[500px]" // Placeholder height
			/>
		);
	}

	// 如果宽度还是0，显示加载状态
	if (width <= 0) {
		return (
			<motion.div
				ref={containerRef}
				initial={{ opacity: 0 }}
				animate={{ opacity: 1 }}
				transition={{ duration: 0.5 }}
				className="w-full"
			>
				<div
					ref={ref}
					className="relative w-full h-[500px] flex items-center justify-center"
				>
					<div className="text-gray-500">正在加载案例展示...</div>
				</div>
			</motion.div>
		);
	}

	return (
		<motion.div
			ref={containerRef}
			initial={{ opacity: 0 }}
			animate={{ opacity: 1 }}
			transition={{ duration: 0.5 }}
			className="w-full"
		>
			<motion.div
				ref={ref}
				className="relative w-full h-full"
				style={{ height: Math.max(...heights) }}
				variants={containerVariants}
				initial="hidden"
				animate={isInView ? "visible" : "hidden"}
			>
				{gridItems.map((item: GridItem) => (
					<motion.div
						key={item.id}
						className="absolute p-[10px]"
						style={{
							left: item.x,
							top: item.y,
							width: item.width,
							height: item.height,
						}}
						variants={itemVariants}
					>
						<div className="relative w-full h-full overflow-hidden rounded-[10px] shadow-[0px_10px_50px_-10px_rgba(0,0,0,0.2)] transition-transform duration-300 ease-linear hover:scale-110">
							<Image
								src={item.image}
								alt={`案例图片 ${item.id}`}
								fill
								className="object-cover"
								sizes="(max-width: 600px) 100vw, (max-width: 1000px) 33vw, (max-width: 1500px) 25vw, 20vw"
								priority={Number(item.id) <= 3} // 前3张图片优先加载
							/>
						</div>
					</motion.div>
				))}
			</motion.div>
		</motion.div>
	);
};

export default Masonry;
