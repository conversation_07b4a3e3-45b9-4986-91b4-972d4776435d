"use client"; // 声明为客户端组件

import { type ReactNode, useEffect, useState } from "react";

interface DelayedComponentProps {
	children: ReactNode; // 接收要延迟渲染的子组件
	delay?: number; // 延迟时间（毫秒），默认为 2000
}

export function DelayedComponent({
	children,
	delay = 2000,
}: DelayedComponentProps) {
	const [isVisible, setIsVisible] = useState(false);

	useEffect(() => {
		// 设置一个计时器，在指定延迟后更新状态
		const timer = setTimeout(() => {
			setIsVisible(true);
		}, delay);

		// 清理函数：当组件卸载时清除计时器，防止内存泄漏
		return () => clearTimeout(timer);
	}, [delay]); // 依赖项数组包含 delay，如果 delay 变化会重新设置计时器

	// 如果 isVisible 是 true，则渲染子组件，否则渲染 null (不渲染任何东西)
	return isVisible ? children : null;
}
