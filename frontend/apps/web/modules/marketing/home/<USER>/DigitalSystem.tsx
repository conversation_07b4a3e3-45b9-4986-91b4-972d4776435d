"use client";

import { cn } from "@ui/lib";
import { AnimatePresence, motion } from "framer-motion";
import { Bot, Clapperboard, Send, Sparkles, UserSquare2 } from "lucide-react"; // 导入 Send 图标
import Image from "next/image";
import { useId, useState } from "react";

// 1. 定义数字人系统功能数据
const features = [
	{
		id: "avatar_clone",
		title: "数字人形象克隆",
		icon: UserSquare2,
		description:
			"即刻拥有您的 AI 数字人！在线录制或上传视频，灵活选择纯形象或口型声音同步的说话化身。AI 驱动，效果逼真，轻松开启虚拟形象新时代。",
		highlights: [
			"灵活素材来源：在线录制或本地上传",
			"多样克隆模式：支持闭口形象或带声音的说话形象",
			"声音同步克隆：说话形象自动提取并克隆音色",
		],
		imagePlaceholderText: "数字人克隆界面截图",
		imageComment: "建议使用展示数字人列表或克隆进度的界面截图",
		imageSrc: "/images/feature/digital/digital_human.png",
	},
	{
		id: "voice_clone",
		title: "个性化声音克隆",
		icon: Sparkles,
		description:
			"为您的数字人注入灵魂之声！无论是上传音频克隆专属音色，还是选用支持多语言的 AI 智能配音，都能轻松实现。声音由您掌控，表达无限可能。",
		highlights: [
			"声音克隆：上传音频，定制独一无二的音色",
			"AI 配音：内置多语言智能配音，即刻发声",
			"灵活应用：克隆声音或 AI 声音均可驱动数字人",
		],
		imagePlaceholderText: "声音克隆界面截图",
		imageComment: "建议使用声音克隆管理或试听的界面截图",
		imageSrc: "/images/feature/digital/digital_voice.png",
	},
	{
		id: "video_creation",
		title: "AI视频内容生成",
		icon: Clapperboard,
		description:
			"告别脚本烦恼，引爆内容流量！系统实时追踪全网热点，AI 一键生成爆款文案脚本。选定您的数字人，即刻生成高质量播报视频，让您的内容创作领先一步。",
		highlights: [
			"AI 热点文案：实时追踪，一键生成爆款脚本",
			"文本驱动视频：智能合成高质量数字人播报",
			"高效内容创作：从热点到视频，流程极简",
		],
		imagePlaceholderText: "视频生成界面截图",
		imageComment: "建议使用视频编辑或生成任务列表的界面截图",
		imageSrc: "/images/feature/digital/hot_write.png",
	},
	{
		id: "resource_management",
		title: "数字资产管理",
		icon: Bot, // 用 Bot 代表数字资产
		description:
			"统一管理您创建的所有数字人形象、声音模型和生成的视频内容。方便查找、预览、编辑和复用您的数字资产。",
		highlights: [
			"集中管理数字人、声音和视频",
			"便捷的搜索与筛选功能",
			"资产复用，提升创作效率",
		],
		imagePlaceholderText: "数字资产管理界面截图",
		imageComment: "建议使用管理数字人列表或视频列表的界面截图",
		imageSrc: "/images/feature/digital/human_control.png",
	},
	{
		id: "publishing",
		title: "多平台一键发布",
		icon: Send, // 使用 Send 图标
		description:
			"创作完成，即刻触达全球！支持一键授权并发布视频到抖音、小红书、视频号、TikTok、YouTube 等主流平台，最大化您的内容影响力。",
		highlights: [
			"国内外主流平台覆盖（抖音、小红书、视频号、TikTok、YouTube等）",
			"一键授权，告别繁琐登录",
			"内容同步分发，高效触达",
		],
		imagePlaceholderText: "发布平台选择界面截图",
		imageComment: "建议使用展示多平台授权或发布确认的界面截图",
		imageSrc: "/images/feature/digital/platform_auth.png",
	},
];

export function DigitalSystem() {
	const [activeFeatureId, setActiveFeatureId] = useState(features[0].id);
	const activeFeature = features.find((f) => f.id === activeFeatureId);
	const id = useId();

	return (
		<section
			id={id}
			className="relative scroll-my-20 py-12 md:py-16 lg:py-24 bg-gray-50 bg-no-repeat"
			style={{
				backgroundImage: "url(/images/feature/bg/bg-twos.png)",
				backgroundSize: "48%",
				backgroundPosition: "80% 5%",
			}}
		>
			<div className="container max-w-6xl relative z-10">
				{/* 顶部标题区域 */}
				<div className="text-center mb-8 md:mb-10">
					<motion.h2
						className="text-3xl md:text-4xl lg:text-5xl font-bold mb-3 md:mb-4 bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent"
						initial={{ opacity: 0, y: 50 }}
						whileInView={{ opacity: 1, y: 0 }}
						viewport={{ once: true, margin: "-100px" }}
						transition={{ duration: 0.8, ease: "easeOut" }}
					>
						增强版数字人系统
					</motion.h2>
					<motion.p
						className="text-gray-600 text-lg max-w-2xl mx-auto"
						initial={{ opacity: 0, y: 50 }}
						whileInView={{ opacity: 1, y: 0 }}
						viewport={{ once: true, margin: "-100px" }}
						transition={{
							duration: 0.8,
							ease: "easeOut",
							delay: 0.2,
						}}
					>
						为您的品牌打造一套完整的数字化系统，让品牌在数字时代更具竞争力
					</motion.p>
				</div>

				{/* 顶部标签栏 */}
				<div className="flex flex-wrap justify-center gap-3 mb-10">
					{features.map((feature) => (
						<motion.button
							key={feature.id}
							onClick={() => setActiveFeatureId(feature.id)}
							onMouseEnter={() => setActiveFeatureId(feature.id)}
							className={cn(
								"flex items-center gap-2 px-4 py-3 rounded-lg transition-all duration-300",
								"border",
								activeFeatureId === feature.id
									? "bg-white border-[#64b160] shadow-md"
									: "bg-white border-gray-200 hover:border-gray-300 hover:bg-gray-50 hover:shadow-sm",
							)}
							whileHover={{ y: -2 }}
							whileTap={{ y: 0 }}
						>
							<feature.icon
								className={cn(
									"h-5 w-5 shrink-0 transition-colors",
									activeFeatureId === feature.id
										? "text-[#64b160]"
										: "text-gray-500",
								)}
							/>
							<span
								className={cn(
									"font-medium transition-colors whitespace-nowrap",
									activeFeatureId === feature.id
										? "text-[#64b160] font-semibold"
										: "text-gray-700",
								)}
							>
								{feature.title}
							</span>
						</motion.button>
					))}
				</div>

				{/* 主体内容区域 */}
				<AnimatePresence mode="wait">
					{activeFeature && (
						<motion.div
							key={activeFeature.id}
							initial={{ opacity: 0, y: 20 }}
							animate={{ opacity: 1, y: 0 }}
							exit={{ opacity: 0, y: -20 }}
							transition={{
								duration: 0.4,
								ease: "easeInOut",
							}}
							className="grid grid-cols-1 lg:grid-cols-5 gap-8 lg:gap-12"
						>
							{/* 左侧：文字介绍部分 */}
							<div className="lg:col-span-2 space-y-4 md:space-y-6">
								{/* 功能标题（在手机视图中显示） */}
								<h3 className="text-2xl font-bold text-gray-800 lg:hidden">
									{activeFeature.title}
								</h3>

								{/* 功能描述 */}
								<p className="text-gray-700 text-base leading-relaxed">
									{activeFeature.description}
								</p>
								{/* 功能亮点列表 */}
								<ul className="space-y-2 md:space-y-3 text-gray-600">
									{activeFeature.highlights.map(
										(highlight, index) => (
											<li
												key={index}
												className="flex items-start"
											>
												<span className="mr-2 mt-1 text-[#b89357] font-bold text-sm">
													•
												</span>
												<span>{highlight}</span>
											</li>
										),
									)}
								</ul>
							</div>

							{/* 右侧：图片展示部分 */}
							<div
								className="lg:col-span-3 relative h-64 sm:h-80 md:h-96 overflow-hidden rounded-xl
										border border-white/20 bg-white/10 backdrop-blur-lg
										shadow-2xl shadow-gray-400/35
										before:absolute before:inset-0 before:bg-gradient-to-br before:from-white/30 before:to-white/10 before:pointer-events-none"
							>
								<Image
									src={activeFeature.imageSrc}
									alt={activeFeature.imagePlaceholderText}
									fill
									className="object-contain p-3 sm:p-4 md:p-6 z-10 relative rounded-xl"
									sizes="(max-width: 1024px) 90vw, 60vw"
									priority={
										activeFeatureId === activeFeature.id
									}
								/>
							</div>
						</motion.div>
					)}
				</AnimatePresence>
			</div>
		</section>
	);
}
