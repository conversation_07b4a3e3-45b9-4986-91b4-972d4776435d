"use client";

import { motion } from "framer-motion";
import { ArrowRight } from "lucide-react";
import { useEffect, useState } from "react";

// 定义卡片数据
const cards = [
	{
		id: 1,
		title: "IP 打造",
		description:
			"解决账号难起量、视频文案难出彩等痛点。基于大数据分析，智能生成爆款文案，打造个性化IP人设，让您的账号快速破圈。AI助力内容创作，让平庸账号也能出爆款。",
		image: "/images/feature/solution/IP.png",
		gradient: "from-[#FFF7ED] to-[#e8f468]",
	},
	{
		id: 2,
		title: "带货运营",
		description:
			"突破行业热点捕捉难、创作灵感枯竭、内容产出效率低等瓶颈。AI实时监测行业动态，智能发现爆款选品，自动生成带货文案和短视频，让您的带货效率提升10倍。",
		image: "/images/feature/solution/bring_goods.png",
		gradient: "from-[#F8FFCA] to-[#0CA809]",
	},
	{
		id: 3,
		title: "视频切片",
		description:
			"一键将长视频智能切片，自动识别精彩片段，生成短视频素材。支持多平台规格适配，让您的内容快速裂变，实现多平台矩阵式运营，提升内容传播效果。",
		image: "/images/feature/solution/video_slice.jpg",
		gradient: "from-[#CAFFCF] to-[#09A8A8]",
	},
	{
		id: 4,
		title: "本地生活",
		description:
			"深度服务本地商家，打造区域影响力。智能生成店铺宣传视频、商品展示图文，多维度数据分析助力商家精准营销，让本地生意更上一层楼。",
		image: "/images/feature/solution/local_life.png",
		gradient: "from-[#ffe7bf] to-[#bb8c60]",
	},
];

export function Solution() {
	const [expandedId, setExpandedId] = useState(1);
	const [isMobile, setIsMobile] = useState(false);

	// 检测是否为移动设备
	useEffect(() => {
		const checkMobile = () => {
			setIsMobile(window.innerWidth < 768);
		};

		// 初始检查
		checkMobile();

		// 监听窗口大小变化
		window.addEventListener("resize", checkMobile);
		return () => window.removeEventListener("resize", checkMobile);
	}, []);

	return (
		<section className="py-12 md:py-16 lg:py-24 bg-white">
			<div className="container mx-auto px-4 max-w-7xl">
				<div className="text-center mb-10 md:mb-16">
					<motion.h2
						className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-800 mb-3 md:mb-4"
						initial={{ opacity: 0, y: 50 }}
						whileInView={{ opacity: 1, y: 0 }}
						viewport={{ once: true, margin: "-100px" }}
						transition={{ duration: 0.8, ease: "easeOut" }}
					>
						解决方案
					</motion.h2>
					<motion.p
						className="text-gray-600 text-lg max-w-2xl mx-auto"
						initial={{ opacity: 0, y: 50 }}
						whileInView={{ opacity: 1, y: 0 }}
						viewport={{ once: true, margin: "-100px" }}
						transition={{
							duration: 0.8,
							ease: "easeOut",
							delay: 0.2,
						}}
					>
						为您的品牌提供全方位的智能营销解决方案，让品牌营销更高效、更智能
					</motion.p>
				</div>

				{/* 移动端垂直布局 */}
				{isMobile ? (
					<div className="space-y-4">
						{cards.map((card) => (
							<motion.div
								key={card.id}
								className={`relative overflow-hidden rounded-2xl shadow-lg ${
									expandedId === card.id
										? "h-[420px]"
										: "h-[120px]"
								}`}
								initial={false}
								animate={{
									height: expandedId === card.id ? 420 : 120,
								}}
								onClick={() =>
									setExpandedId(
										card.id === expandedId ? 0 : card.id,
									)
								}
								transition={{
									duration: 0.6,
									ease: [0.4, 0, 0.2, 1],
								}}
							>
								<div
									className="absolute inset-0 bg-cover bg-center"
									style={{
										backgroundImage: `url(${card.image})`,
									}}
								/>
								<div
									className="absolute inset-0"
									style={{
										background:
											expandedId === card.id
												? "linear-gradient(to top, rgba(0,0,0,0.6) 0%, rgba(0,0,0,0.2) 100%)"
												: `linear-gradient(to top, ${card.gradient.split(" ")[1]}, ${card.gradient.split(" ")[3]})`,
									}}
								/>

								{/* 标题始终显示 */}
								<div className="absolute top-0 left-0 right-0 p-4">
									<h3 className="text-white text-xl font-bold">
										{card.title}
									</h3>
								</div>

								{/* 展开内容 */}
								{expandedId === card.id && (
									<motion.div
										className="absolute bottom-0 left-0 right-0 p-4 pt-0"
										initial={{ opacity: 0, y: 20 }}
										animate={{ opacity: 1, y: 0 }}
										exit={{ opacity: 0, y: 20 }}
										transition={{ duration: 0.5 }}
									>
										<motion.p className="text-white/90 text-sm mb-4 mt-8">
											{card.description}
										</motion.p>
										<motion.button
											className="flex items-center gap-2 px-4 py-2 rounded-full bg-white/10 hover:bg-white/20 backdrop-blur-sm border border-white/20 text-white text-sm transition-colors"
											whileTap={{ scale: 0.95 }}
										>
											解决方案
											<ArrowRight className="w-3 h-3" />
										</motion.button>
									</motion.div>
								)}
							</motion.div>
						))}
					</div>
				) : (
					/* 桌面端横向布局 */
					<div className="flex gap-6 h-[500px] w-full">
						{cards.map((card) => (
							<motion.div
								key={card.id}
								className={`relative rounded-3xl overflow-hidden cursor-pointer shadow-2xl
									${expandedId === card.id ? "flex-[3]" : "flex-[0.5]"}
								`}
								initial={false}
								animate={{
									flex: expandedId === card.id ? 3 : 0.5,
								}}
								onHoverStart={() => setExpandedId(card.id)}
								transition={{
									duration: 0.8,
									ease: [0.4, 0, 0.2, 1],
								}}
							>
								<div
									className="absolute inset-0 bg-cover bg-center transition-transform duration-500"
									style={{
										backgroundImage: `url(${card.image})`,
									}}
								/>
								<div
									className={
										"absolute inset-0 transition-opacity duration-500"
									}
									style={{
										background:
											expandedId === card.id
												? "linear-gradient(to top, rgba(0,0,0,0.9) 0%, rgba(0,0,0,0.4) 100%)"
												: `linear-gradient(to top, ${card.gradient.split(" ")[1]}, ${card.gradient.split(" ")[3]})`,
									}}
								/>
								{expandedId === card.id && (
									<motion.div
										className="absolute bottom-0 left-0 right-0 p-8"
										initial={{ opacity: 0, y: 20 }}
										animate={{ opacity: 1, y: 0 }}
										exit={{ opacity: 0, y: 20 }}
										transition={{ duration: 0.5 }}
									>
										<div className="flex items-start justify-between gap-8">
											<div className="flex-1">
												<motion.h3 className="text-white text-2xl lg:text-3xl font-bold mb-4">
													{card.title}
												</motion.h3>
												<motion.p className="text-white/90 text-base lg:text-lg leading-relaxed">
													{card.description}
												</motion.p>
											</div>
											<motion.button
												className="flex items-center gap-2 px-6 py-3 rounded-full bg-white/10 hover:bg-white/20 backdrop-blur-sm border border-white/20 text-white transition-colors"
												whileHover={{ scale: 1.05 }}
												whileTap={{ scale: 0.95 }}
											>
												解决方案
												<ArrowRight className="w-4 h-4" />
											</motion.button>
										</div>
									</motion.div>
								)}
							</motion.div>
						))}
					</div>
				)}
			</div>
		</section>
	);
}
