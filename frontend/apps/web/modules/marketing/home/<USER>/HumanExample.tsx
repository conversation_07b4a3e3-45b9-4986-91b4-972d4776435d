"use client"; // 因为 Masonry 使用了 hooks，这个组件也需要是客户端组件

import { motion } from "framer-motion";
import { lazy, Suspense, useId } from "react";

// 懒加载 Masonry 组件
const Masonry = lazy(() => import("./Masonry"));

// 你提供的数据 - 修改为6张，调整高度差异，并使用本地图片
const data = [
	{ id: 1, image: "/images/feature/example/one.webp", height: 900 },
	{ id: 2, image: "/images/feature/example/three.webp", height: 700 },
	{ id: 3, image: "/images/feature/example/four.webp", height: 450 },
	{ id: 4, image: "/images/feature/example/seven.webp", height: 850 },
	{ id: 5, image: "/images/feature/example/six.webp", height: 800 }, // Assuming six.webp for id 5
	{ id: 6, image: "/images/feature/example/two.webp", height: 480 }, // Assuming seven.webp for id 6
];

export function HumanExample() {
	const id = useId();
	return (
		<section id={id} className="py-16 lg:py-24 bg-white">
			<div className="container mx-auto px-4">
				<motion.div
					className="text-center mb-12"
					initial={{ opacity: 0, y: 50 }}
					whileInView={{ opacity: 1, y: 0 }}
					viewport={{ once: true, margin: "-100px" }}
					transition={{ duration: 0.8, ease: "easeOut" }}
				>
					<motion.h2
						className="text-3xl font-bold tracking-tight md:text-4xl lg:text-5xl mb-4 text-gray-800"
						initial={{ opacity: 0, y: 50 }}
						whileInView={{ opacity: 1, y: 0 }}
						viewport={{ once: true, margin: "-100px" }}
						transition={{
							duration: 0.8,
							ease: "easeOut",
							delay: 0.2,
						}}
					>
						来看看我们的案例
					</motion.h2>
					<motion.p
						className="text-lg text-gray-600 max-w-3xl mx-auto"
						initial={{ opacity: 0, y: 50 }}
						whileInView={{ opacity: 1, y: 0 }}
						viewport={{ once: true, margin: "-100px" }}
						transition={{
							duration: 0.8,
							ease: "easeOut",
							delay: 0.4,
						}}
					>
						精心挑选的作品展示
					</motion.p>
				</motion.div>
				<Suspense
					fallback={
						<div className="w-full h-[500px] flex items-center justify-center">
							<div className="animate-pulse bg-muted rounded-lg w-full h-full" />
						</div>
					}
				>
					<Masonry data={data} />
				</Suspense>
			</div>
		</section>
	);
}
