"use client";

import { LocaleLink, useLocalePathname } from "@i18n/routing";
import { config } from "@repo/config";
import { useSession } from "@saas/auth/hooks/use-session";
import { AgentLogo } from "@shared/components/AgentLogo";
import { But<PERSON> } from "@ui/components/button";
import {
	Sheet,
	SheetContent,
	SheetTitle,
	SheetTrigger,
} from "@ui/components/sheet";
import { cn } from "@ui/lib";
import { Menu as MenuIcon } from "lucide-react";
import NextLink from "next/link";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { useDebounceCallback } from "usehooks-ts";

export function NavBar() {
	const t = useTranslations();
	const { user } = useSession();
	const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
	const localePathname = useLocalePathname();
	const [_isTop, setIsTop] = useState(true);

	const debouncedScrollHandler = useDebounceCallback(
		() => {
			setIsTop(window.scrollY <= 10);
		},
		150,
		{
			maxWait: 150,
		},
	);

	useEffect(() => {
		window.addEventListener("scroll", debouncedScrollHandler);
		debouncedScrollHandler();
		return () => {
			window.removeEventListener("scroll", debouncedScrollHandler);
		};
	}, [debouncedScrollHandler]);

	useEffect(() => {
		setMobileMenuOpen(false);
	}, []);

	const _isDocsPage = localePathname.startsWith("/docs");

	const menuItems: {
		label: string;
		href: string;
	}[] = [
		{
			label: t("common.menu.home"),
			href: "/",
		},
		{
			label: t("common.menu.productUpdates"),
			href: "/product-updates/",
		},
		{
			label: t("common.menu.changelog"),
			href: "/changelog",
		},
		...(config.contactForm.enabled
			? [
					{
						label: t("common.menu.contact"),
						href: "/contact",
					},
				]
			: []),
	];

	const isMenuItemActive = (href: string) => localePathname.startsWith(href);

	return (
		<nav
			className={cn(
				"fixed top-0 left-0 z-50 w-full transition-shadow duration-200",
				"bg-black/90 shadow-md backdrop-blur-md",
			)}
			data-test="navigation"
		>
			<div className="container">
				<div
					className={cn(
						"flex items-center justify-stretch gap-6 transition-[padding] duration-200",
						"py-2",
					)}
				>
					<div className="flex flex-1 justify-start">
						<LocaleLink
							href="/"
							className="block hover:no-underline active:no-underline"
						>
							<AgentLogo className="scale-75 origin-left" />
						</LocaleLink>
					</div>

					<div className="hidden flex-1 items-center justify-center lg:flex">
						{menuItems.map((menuItem) => (
							<LocaleLink
								key={menuItem.href}
								href={menuItem.href}
								className={cn(
									"block px-3 py-2 font-medium text-gray-300 text-sm",
									isMenuItemActive(menuItem.href)
										? "font-bold text-white"
										: "hover:text-white",
								)}
							>
								{menuItem.label}
							</LocaleLink>
						))}
					</div>

					<div className="flex flex-1 items-center justify-end gap-3">
						{/* 						
						{config.i18n.enabled && <LocaleSwitch />} */}

						<Sheet
							open={mobileMenuOpen}
							onOpenChange={(open) => setMobileMenuOpen(open)}
						>
							<SheetTrigger asChild>
								<Button
									className="lg:hidden text-white border-gray-700 hover:bg-gray-800 hover:text-white"
									size="icon"
									variant="outline"
									aria-label="Menu"
								>
									<MenuIcon className="size-4" />
								</Button>
							</SheetTrigger>
							<SheetContent
								className="w-[280px] bg-black border-l border-gray-800"
								side="right"
							>
								<SheetTitle className="text-lg font-semibold mb-4 sr-only">
									导航菜单
								</SheetTitle>
								<div className="pt-6 flex flex-col items-start justify-center">
									{menuItems.map((menuItem) => (
										<LocaleLink
											key={menuItem.href}
											href={menuItem.href}
											className={cn(
												"block px-3 py-2 font-medium text-base text-gray-300",
												isMenuItemActive(menuItem.href)
													? "font-bold text-white"
													: "hover:text-white",
											)}
										>
											{menuItem.label}
										</LocaleLink>
									))}

									<NextLink
										key={user ? "system" : "login"}
										href="/auth"
										className="mt-4 block px-3 py-2 text-base font-medium text-white hover:text-gray-300"
										prefetch={!user}
									>
										{user
											? "选择系统"
											: t("common.menu.login")}
									</NextLink>
								</div>
							</SheetContent>
						</Sheet>

						{user ? (
							<Button
								key="system"
								className="hidden lg:flex bg-white text-black hover:bg-white/90 font-semibold"
								asChild
							>
								<NextLink href="/auth">选择系统</NextLink>
							</Button>
						) : (
							<Button
								key="login"
								className="hidden lg:flex bg-white text-black hover:bg-white/90 font-semibold"
								asChild
							>
								<NextLink href="/auth">
									{t("common.menu.login")}
								</NextLink>
							</Button>
						)}
					</div>
				</div>
			</div>
		</nav>
	);
}
