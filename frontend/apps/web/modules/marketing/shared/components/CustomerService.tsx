"use client";

import { AnimatePresence, motion } from "framer-motion";
import { MessageCircle, X } from "lucide-react";
import Image from "next/image";
import { useState } from "react";

export function CustomerService() {
	const [showQRCode, setShowQRCode] = useState(false);

	return (
		<motion.div
			className="fixed bottom-8 right-8 z-50 flex flex-col items-center gap-2"
			initial={{ opacity: 0, scale: 0.5 }}
			animate={{ opacity: 1, scale: 1 }}
			transition={{
				type: "spring",
				stiffness: 260,
				damping: 20,
			}}
		>
			<AnimatePresence>
				{showQRCode && (
					<motion.div
						initial={{ opacity: 0, y: 20, scale: 0.95 }}
						animate={{ opacity: 1, y: 0, scale: 1 }}
						exit={{ opacity: 0, y: 20, scale: 0.95 }}
						transition={{ duration: 0.2 }}
						className="absolute bottom-[calc(100%+1rem)] right-0 w-[280px] rounded-xl bg-card shadow-lg border border-border p-4"
					>
						<button
							type="button"
							onClick={() => setShowQRCode(false)}
							className="absolute right-2 top-2 p-1 rounded-lg hover:bg-foreground/10 text-foreground/60"
						>
							<X className="h-4 w-4" />
						</button>
						<div className="flex flex-col items-center gap-3">
							<p className="text-sm font-medium">扫码添加客服</p>
							<div className="relative w-[200px] h-[200px] bg-white rounded-lg p-2">
								<Image
									src="https://eco.9000aigc.com/static/QRCode/%E5%AE%A2%E6%9C%8D%E4%BA%8C%E7%BB%B4%E7%A0%81.png"
									alt="客服微信二维码"
									fill
									className="object-contain rounded-lg"
								/>
							</div>
						</div>
					</motion.div>
				)}
			</AnimatePresence>

			<button
				type="button"
				className="group flex flex-col items-center gap-1 transition-transform hover:scale-105"
				onClick={() => setShowQRCode((prev) => !prev)}
			>
				<div className="flex h-14 w-14 items-center justify-center rounded-full bg-[black] shadow-lg transition-shadow hover:shadow-[#c6c365]/25">
					<MessageCircle className="h-6 w-6 text-white" />
				</div>
				<span className="text-sm text-foreground/50">联系客服</span>
			</button>
		</motion.div>
	);
}
