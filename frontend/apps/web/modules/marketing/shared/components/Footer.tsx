import { LocaleLink } from "@i18n/routing";
import { AgentLogo } from "@shared/components/AgentLogo";
import { beianConfig, getFullCopyrightText } from "@shared/lib/beian-config";
import Image from "next/image";

export function Footer() {
	return (
		<footer className="border-t py-4 md:py-8 text-foreground/60 text-xs md:text-sm">
			<div className="container grid grid-cols-1 gap-4 md:gap-8 lg:grid-cols-5 lg:gap-8">
				<div className="flex flex-col md:flex-row items-start gap-4 md:gap-12 lg:col-span-2">
					<div className="flex flex-col items-start gap-2 md:gap-3">
						<AgentLogo className="scale-75 md:scale-90 origin-left -mt-3 md:-mt-4" />
						<p className="text-sm md:text-base font-semibold text-foreground/80">
							引爆流量的超级智能体
						</p>
					</div>
					<div className="flex flex-col items-start gap-1 pt-0 md:pt-2 text-xs md:text-sm">
						<p>公司名称：九千仿脑科技有限公司</p>
						<p>联系邮箱：<EMAIL></p>
						<p>联系电话：+86 ***********</p>
					</div>
				</div>

				<div className="lg:col-span-2 grid grid-cols-3 gap-4 md:gap-8">
					<div className="flex flex-col gap-1 md:gap-2">
						<p className="font-semibold text-foreground/80 mb-1 md:mb-2 text-xs md:text-sm">
							合作咨询
						</p>
						<p className="hover:text-foreground cursor-pointer text-xs md:text-sm">
							商务合作
						</p>
					</div>

					<div className="flex flex-col gap-1 md:gap-2">
						<p className="font-semibold text-foreground/80 mb-1 md:mb-2 text-xs md:text-sm">
							推荐产品
						</p>
						<LocaleLink
							href="/app/ecosystem/avatar/dashboard"
							className="block hover:text-foreground text-xs md:text-sm"
						>
							9000AI增强版数字人
						</LocaleLink>
						<LocaleLink
							href="/app/agent"
							className="block hover:text-foreground text-xs md:text-sm"
						>
							9000AI合伙人专用系统
						</LocaleLink>
						<LocaleLink
							href="/"
							className="block hover:text-foreground text-xs md:text-sm"
						>
							AI切片剪辑
						</LocaleLink>
						<LocaleLink
							href="/"
							className="block hover:text-foreground text-xs md:text-sm"
						>
							AI图文裂变
						</LocaleLink>
					</div>

					<div className="flex flex-col gap-1 md:gap-2">
						<p className="font-semibold text-foreground/80 mb-1 md:mb-2 text-xs md:text-sm">
							联系我们
						</p>
						<LocaleLink
							href="/contact"
							className="block hover:text-foreground text-xs md:text-sm"
						>
							联系我们
						</LocaleLink>
					</div>
				</div>

				<div className="flex flex-col items-center lg:items-end gap-2 md:gap-3 lg:col-span-1 mt-4 md:mt-0">
					<div className="flex items-center gap-1 md:gap-2 text-sm md:text-base font-semibold text-foreground/80">
						<Image
							src="https://cminio.9000aigc.com/static/marketing/images/wechat.webp"
							alt="微信图标"
							width={16}
							height={16}
							className="inline-block md:w-[20px] md:h-[20px]"
						/>
						<span className="text-xs md:text-sm">欢迎联系客服</span>
					</div>
					<Image
						src="https://eco.9000aigc.com/static/QRCode/%E5%AE%A2%E6%9C%8D%E4%BA%8C%E7%BB%B4%E7%A0%81.png"
						alt="客服微信二维码"
						width={120}
						height={120}
						className="rounded-md border border-foreground/10 w-[110px] h-[110px] md:w-[140px] md:h-[140px]"
					/>
				</div>
			</div>

			{/* 备案信息区域 */}
			<div className="border-t border-foreground/10 mt-4 md:mt-8 pt-4 md:pt-6">
				<div className="container text-center text-xs text-foreground/50">
					<div className="flex flex-col md:flex-row items-center justify-center gap-2 md:gap-6">
						{/* ICP备案号 */}
						{beianConfig.display.showIcp && (
							<a
								href={beianConfig.icp.url}
								target="_blank"
								rel="noopener noreferrer"
								className="hover:text-foreground/70 transition-colors duration-200"
							>
								{beianConfig.icp.number}
							</a>
						)}
					</div>

					{/* 版权信息 */}
					{beianConfig.display.showCopyright && (
						<div className="mt-2 text-foreground/40">
							{getFullCopyrightText()}
						</div>
					)}
				</div>
			</div>
		</footer>
	);
}
