"use client";

import { MDXContent } from "@content-collections/mdx/react";
import { mdxComponents } from "../utils/mdx-components";

export function PostContent({ content }: { content: string }) {
	return (
		<div className="prose prose-invert mx-auto mt-6 max-w-2xl text-white">
			<MDXContent
				code={content}
				components={{
					...mdxComponents,
					p: (props) => <p {...props} className="text-white" />,
					h1: (props) => <h1 {...props} className="text-white" />,
					h2: (props) => <h2 {...props} className="text-white" />,
					h3: (props) => <h3 {...props} className="text-white" />,
					h4: (props) => <h4 {...props} className="text-white" />,
					h5: (props) => <h5 {...props} className="text-white" />,
					h6: (props) => <h6 {...props} className="text-white" />,
					ul: (props) => <ul {...props} className="text-white" />,
					ol: (props) => <ol {...props} className="text-white" />,
					li: (props) => <li {...props} className="text-white" />,
				}}
			/>
		</div>
	);
}
