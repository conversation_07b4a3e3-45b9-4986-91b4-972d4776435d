"use client";

import { LocaleLink } from "@i18n/routing";
import Image from "next/image";

export function PostListItem({ post }: { post: any }) {
	const { title, excerpt, image, date, path, tags } = post;

	return (
		<div className="rounded-2xl border-2 border-green-500/40 bg-gradient-to-br from-black/40 to-green-950/40 p-6 shadow-lg backdrop-blur-sm transition-all duration-300 hover:border-green-400 hover:shadow-green-500/30 hover:shadow-lg hover:scale-[1.02]">
			{image && (
				<div className="-mx-4 -mt-4 relative mb-4 aspect-[16/9] overflow-hidden rounded-xl">
					<Image
						src={image}
						alt={title}
						fill
						sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
						className="object-cover object-center"
					/>
					<LocaleLink
						href={`/product-updates/${path}`}
						className="absolute inset-0"
					/>
				</div>
			)}

			{tags && (
				<div className="mb-2 flex flex-wrap gap-2">
					{tags.map((tag: any) => (
						<span
							key={tag}
							className="font-semibold text-green-400 text-xs uppercase tracking-wider"
						>
							#{tag}
						</span>
					))}
				</div>
			)}

			<LocaleLink
				href={`/product-updates/${path}`}
				className="font-semibold text-xl text-green-100 hover:text-green-300 transition-colors duration-200"
			>
				{title}
			</LocaleLink>
			{excerpt && <p className="mt-2 text-green-200/70">{excerpt}</p>}

			<div className="mt-4 flex items-center justify-between">
				<p className="text-sm text-green-400/50">
					{Intl.DateTimeFormat("en-US").format(new Date(date))}
				</p>
				<LocaleLink
					href={`/product-updates/${path}`}
					className="px-4 py-2 bg-[#bcff2f] text-black rounded-md hover:bg-green-700 transition-colors duration-200"
				>
					查看详情
				</LocaleLink>
			</div>
		</div>
	);
}
