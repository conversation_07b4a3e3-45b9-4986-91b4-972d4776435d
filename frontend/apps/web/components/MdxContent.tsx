/**
 * This component is used to render MDX content that is pre-processed by content-collections.
 * The `content` prop is expected to be a string of HTML that has already been sanitized.
 *
 * We are using `dangerouslySetInnerHTML` here because the content is trusted and pre-rendered.
 * This is a deliberate choice to avoid re-parsing the HTML on the client side.
 */
interface MdxContentProps {
	content: string;
	className?: string;
}

export function MdxContent({ content, className }: MdxContentProps) {
	return (
		<div
			className={className}
			// biome-ignore lint/security/noDangerouslySetInnerHtml: The body is already sanitized by content-collections
			dangerouslySetInnerHTML={{ __html: content }}
		/>
	);
}
