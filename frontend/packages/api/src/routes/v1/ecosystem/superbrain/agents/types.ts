/**
 * 智能体管理模块类型定义
 */

import type { DifyAppType as PrismaDifyAppType } from "@prisma/client";

// API层使用的Dify应用类型（小写格式）
export type DifyAppType = "chatbot" | "agent" | "workflow" | "text_gen";

// Prisma枚举映射
export const APP_TYPE_MAP: Record<DifyAppType, PrismaDifyAppType> = {
	chatbot: "CHAT_BOT",
	agent: "AGENT",
	workflow: "WORKFLOW",
	text_gen: "TEXT_GEN",
};

// 反向映射
export const APP_TYPE_REVERSE_MAP: Record<PrismaDifyAppType, DifyAppType> = {
	CHAT_BOT: "chatbot",
	AGENT: "agent",
	WORKFLOW: "workflow",
	TEXT_GEN: "text_gen",
};

// 智能体创建输入
export interface CreateAgentInput {
	name: string;
	description?: string;
	apiKey: string;
	appId?: string;
	appType: DifyAppType;
	displayName: string;
	avatar?: string;
	tags: string[];
	category?: string;
	introduction?: string;
	isPublic?: boolean;
}

// 智能体更新输入
export interface UpdateAgentInput {
	name?: string;
	description?: string;
	displayName?: string;
	avatar?: string;
	tags?: string[];
	category?: string;
	introduction?: string;
	isActive?: boolean;
	isPublic?: boolean;
}

// 智能体响应
export interface AgentResponse {
	id: string;
	name: string;
	workflowCode: string | null;
	description: string | null;
	displayName: string;
	avatar: string | null;
	tags: string[];
	category: string | null;
	introduction: string | null;
	appType: DifyAppType;
	usageCount: number;
	createdAt: string;
	updatedAt: string;
}