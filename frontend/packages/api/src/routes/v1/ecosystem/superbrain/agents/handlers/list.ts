import { db } from "@repo/database";
import { logger } from "@repo/logs";
import type { Context } from "hono";
import { HTTPException } from "hono/http-exception";

import type { SuperBrainVariables } from "../../types";
import type { AgentResponse } from "../types";
import { APP_TYPE_REVERSE_MAP } from "../types";

/**
 * 获取 Dify 应用列表处理器
 * 全局访问模式 - 显示所有未删除的 Dify 应用
 */
export const listAgents = async (c: Context<{ Variables: SuperBrainVariables }>) => {
	const consumer = c.get("consumer");
	const user = c.get("user");
	const query = c.req.query();
	// 手动验证查询参数
	const validatedQuery = {
		page: parseInt(query.page || "1"),
		limit: Math.min(parseInt(query.limit || "20"), 100),
		category: query.category,
		appType: query.appType as any,
		search: query.search,
	};
	const requestId = Math.random().toString(36).substring(7);

	try {
		logger.info("[SuperBrain] 获取 Dify 应用列表", {
			requestId,
			consumerId: consumer.id,
			userId: user.id,
			query: validatedQuery,
		});

		// 构建查询条件 - 显示所有 Dify 应用
		const where: any = {
			deletedAt: null, // 只显示未删除的应用
			isActive: true,
			isPublic: true,
		};

		// 添加筛选条件
		if (validatedQuery.category) {
			where.category = validatedQuery.category;
		}
		if (validatedQuery.appType) {
			where.appType = validatedQuery.appType;
		}
		
		if (validatedQuery.search) {
			where.AND = [
				where.AND || {},
				{
					OR: [
						{ name: { contains: validatedQuery.search, mode: "insensitive" } },
						{ displayName: { contains: validatedQuery.search, mode: "insensitive" } },
						{ description: { contains: validatedQuery.search, mode: "insensitive" } },
					],
				},
			];
		}

		// 计算分页
		const skip = (validatedQuery.page - 1) * validatedQuery.limit;

		// 查询数据
		const [agents, total] = await Promise.all([
			db.difyAppConfig.findMany({
				where,
				orderBy: [
					{ usageCount: "desc" }, // 按使用次数降序（热门应用优先）
					{ createdAt: "desc" }, // 按创建时间降序（最新应用其次）
				],
				skip,
				take: validatedQuery.limit,
				include: {
					creator: {
						select: {
							id: true,
							name: true,
							avatar: true,
						},
					},
				},
			}),
			db.difyAppConfig.count({ where }),
		]);

		logger.info("[SuperBrain] Dify 应用列表查询成功", {
			requestId,
			consumerId: consumer.id,
			total,
			count: agents.length,
		});

		// 构造响应数据
		const data: AgentResponse[] = agents.map((agent) => ({
			id: agent.id,
			name: agent.name,
			description: agent.description,
			displayName: agent.displayName,
			avatar: agent.avatar,
			tags: agent.tags,
			category: agent.category,
			introduction: agent.introduction,
			appType: APP_TYPE_REVERSE_MAP[agent.appType],
			isActive: agent.isActive,
			isPublic: agent.isPublic,
			usageCount: agent.usageCount,
			createdAt: agent.createdAt.toISOString(),
			updatedAt: agent.updatedAt.toISOString(),
		}));

		const totalPages = Math.ceil(total / validatedQuery.limit);

		return c.json({
			success: true,
			data,
			pagination: {
				page: validatedQuery.page,
				limit: validatedQuery.limit,
				total,
				totalPages,
				hasNext: validatedQuery.page < totalPages,
				hasPrev: validatedQuery.page > 1,
			},
		});
	} catch (error) {
		logger.error("[SuperBrain] 获取 Dify 应用列表失败", {
			requestId,
			consumerId: consumer.id,
			error: error instanceof Error ? error.message : String(error),
			stack: error instanceof Error ? error.stack : undefined,
		});

		if (error instanceof HTTPException) {
			throw error;
		}
		
		throw new HTTPException(500, { message: "获取 Dify 应用列表失败" });
	}
};