// 超级大脑模块数据库表结构

// Dify应用类型枚举
enum DifyAppType {
    CHAT_BOT      @map("chatbot")      // 聊天机器人
    AGENT         @map("agent")        // 智能体
    WORKFLOW      @map("workflow")     // 工作流
    TEXT_GEN      @map("text_gen")     // 文本生成
}

// 超级大脑任务状态枚举
enum SuperBrainTaskStatus {
    PENDING       @map("PENDING")        // 等待处理
    PROCESSING    @map("PROCESSING")     // 处理中
    COMPLETED     @map("COMPLETED")      // 已完成
    FAILED        @map("FAILED")         // 失败
    CANCELLED     @map("CANCELLED")      // 已取消
}

// 角色IP状态枚举
enum CharacterIPStatus {
    ACTIVE        @map("ACTIVE")        // 活跃
    DEVELOPING    @map("DEVELOPING")    // 开发中
    ARCHIVED      @map("ARCHIVED")      // 已归档
}



// 选题状态枚举
enum TopicStatus {
    DRAFT       @map("DRAFT")       // 草稿
    CONFIRMED   @map("CONFIRMED")   // 已确认
    REJECTED    @map("REJECTED")    // 已拒绝
    ARCHIVED    @map("ARCHIVED")    // 已归档
}

// Dify应用配置表
model DifyAppConfig {
    id              String      @id @default(cuid())
    name            String      // 应用名称（我们定义）
    description     String?     // 应用描述（我们定义）

    // Dify配置（后端使用，不对外暴露）
    apiKey          String      // Dify API密钥
    appId           String?     // Dify应用ID（可选）
    appType         DifyAppType // 应用类型

    // 对外使用的无厂商信息工作流编号（用于前端渲染与路由判断）
    workflowCode    String?     @unique @map("workflow_code")

    // 展示配置（我们定义）
    displayName     String      // 显示名称
    avatar          String?     // 应用头像
    tags            String[]    // 标签数组
    category        String?     // 分类
    introduction    String?     // 介绍文本

    // 状态控制
    isActive        Boolean     @default(true)  // 是否启用
    isPublic        Boolean     @default(false) // 是否公开

    // 创建者信息
    createdBy       String      // 创建者用户ID
    creator         User        @relation(fields: [createdBy], references: [id])

    // 使用统计
    usageCount      Int         @default(0) // 使用次数

    // 关联关系
    tasks           SuperBrainTask[]

    // 系统字段
    createdAt       DateTime    @default(now())
    updatedAt       DateTime    @updatedAt
    deletedAt       DateTime?   // 软删除
    remark          String?     // 备注

    @@index([appType])
    @@index([createdBy])
    @@index([isActive])
    @@index([category])
    @@index([tags])
    @@map("dify_app_config")
    FinalContent FinalContent[]
    ContentScript ContentScript[]
    OpeningContent OpeningContent[]
}

// 超级大脑任务表
model SuperBrainTask {
    id                  String                @id @default(cuid())
    name                String                // 任务名称
    
    // 关联信息
    consumerId          String                @map("consumer_id")
    consumer            Consumer              @relation(fields: [consumerId], references: [id], onDelete: Cascade)
    
    difyAppId           String                @map("dify_app_id")
    difyApp             DifyAppConfig         @relation(fields: [difyAppId], references: [id])
    
    // 任务内容
    inputContent        String                // 输入内容
    outputContent       String?               // 输出内容
    
    // 任务状态
    status              SuperBrainTaskStatus  @default(PENDING)
    progress            Float                 @default(0) // 进度(0-100)
    
    // 错误处理
    errorMessage        String?               // 错误信息
    
    // Dify API相关
    difyConversationId  String?               // Dify对话ID
    difyMessageId       String?               // Dify消息ID
    rawRequest          Json?                 // 原始请求数据
    rawResponse         Json?                 // 原始响应数据
    
    // 算力消耗
    computingPowerCost  Int                   @default(50) @map("computing_power_cost")
    tokenCount          Int                   @default(0) // 消耗token数
    
    // 关联算力记录
    usageId             String?               @map("usage_id")
    usage               ComputingPowerRecord? @relation(fields: [usageId], references: [id], onDelete: SetNull)
    
    // 关联选题
    topicSelections     TopicSelection[]
    
    // 时间相关
    startTime           DateTime?             // 任务开始时间
    completionTime      DateTime?             // 任务完成时间
    
    // 系统字段
    createdAt           DateTime              @default(now())
    updatedAt           DateTime              @updatedAt
    deletedAt           DateTime?             // 软删除
    remark              String?               // 备注
    
    @@index([consumerId])
    @@index([difyAppId])
    @@index([status])
    @@index([createdAt])
    @@index([usageId])
    @@map("superbrain_task")
}

// 角色IP定位表
model CharacterIPProfile {
    id                  String              @id @default(cuid())
    
    // 关联消费者
    consumerId          String              @map("consumer_id")
    consumer            Consumer            @relation(fields: [consumerId], references: [id], onDelete: Cascade)
    
    // IP定位信息
    ipName              String?             // IP名称（个人品牌名/企业品牌名等）
    ipType              String?             // IP类型（个人品牌/企业品牌/知识IP/内容创作者等）
    
    // 核心内容
    coreConcept         Json                @default("{}") // 核心理念（结构化）
    superQuestion       Json                @default("{}") // 超级问题（结构化的核心价值问题--行业画像）
    questionVersion     Int                 @default(1) // 问题版本号
    
    // IP画像
    targetAudience      Json?               // 目标受众画像
    valueProposition    String?             // 价值主张
    brandStory          String?             // 品牌故事
    contentStyle        Json?               // 内容风格定义
    
    // 标签和关键词
    keywords            String[]            @default([]) // 关键词列表
    tags                String[]            @default([]) // 标签列表
    
    // 状态
    status              CharacterIPStatus   @default(ACTIVE)
    
    // 系统字段
    createdAt           DateTime            @default(now())
    updatedAt           DateTime            @updatedAt
    deletedAt           DateTime?           // 软删除
    remark              String?             // 备注
    
    @@unique([consumerId, ipName]) // 确保同一用户的IP名称唯一
    @@index([consumerId])
    @@index([status])
    @@index([ipType])
    @@index([tags])
    @@map("character_ip_profile")
}

// 选题表
model TopicSelection {
    id                  String              @id @default(cuid())
    title               String              // 选题标题
    
    // 关联信息
    consumerId          String              @map("consumer_id")
    consumer            Consumer            @relation(fields: [consumerId], references: [id], onDelete: Cascade)
    
    // 由该选题生成的脚本集合
    scripts             ContentScript[]
    
    taskId              String?             @map("task_id") // 关联AI任务
    task                SuperBrainTask?     @relation(fields: [taskId], references: [id])
    
    // 选题内容（结构化）- 包含所有选题相关数据
    topicContent        Json                // 结构化选题内容（包含分类、标签、关键词、优先级等所有信息）
    contentVersion      Int                 @default(1) // 内容版本
    
    // 确认状态
    status              TopicStatus         @default(DRAFT)
    confirmedAt         DateTime?           // 确认时间
    
    // 关联
    finalContents       FinalContent[]      // 关联的成品文案
    
    // 系统字段
    createdAt           DateTime            @default(now())
    updatedAt           DateTime            @updatedAt
    deletedAt           DateTime?           // 软删除
    remark              String?             // 备注
    
    @@index([consumerId])
    @@index([taskId])
    @@index([status])
    @@map("topic_selection")
}

// 脚本表
model ContentScript {
    id                  String              @id @default(cuid())
    title               String              // 脚本标题
    
    // 脚本内容（结构化）- 包含所有脚本相关数据
    scriptContent       Json                // 结构化拆解后的脚本内容（包含分类、标签等所有信息）
    scriptVersion       Int                 @default(1) // 脚本版本
    
    // 使用统计
    usageCount          Int                 @default(0) // 使用次数
    
    // 关联
    topicId             String?             @map("topic_id") // 源选题
    topic               TopicSelection?     @relation(fields: [topicId], references: [id])
    finalContents       FinalContent[]      // 关联的成品文案
    openings            OpeningContent[]    // 关联的黄金三秒开场

    // 生成来源（Dify 应用）
    difyAppId           String?             @map("dify_app_id")
    difyApp             DifyAppConfig?      @relation(fields: [difyAppId], references: [id], onDelete: SetNull)
    
    // 系统字段
    createdAt           DateTime            @default(now())
    updatedAt           DateTime            @updatedAt
    deletedAt           DateTime?           // 软删除
    remark              String?             // 备注
    
    @@index([topicId])
    @@index([difyAppId])
    @@map("content_script")
}

// 成品文案表
model FinalContent {
    id                  String              @id @default(cuid())
    title               String              // 文案标题
    
    // 关联信息
    consumerId          String              @map("consumer_id")
    consumer            Consumer            @relation(fields: [consumerId], references: [id], onDelete: Cascade)
    
    scriptId            String?             @map("script_id") // 关联脚本
    script              ContentScript?      @relation(fields: [scriptId], references: [id])
    
    topicId             String?             @map("topic_id") // 关联选题
    topic               TopicSelection?     @relation(fields: [topicId], references: [id])
    
    // 文案内容 - 包含所有文案相关数据
    content             String              // 主要文案内容

    // 生成来源（Dify 应用）
    difyAppId           String?             @map("dify_app_id")
    difyApp             DifyAppConfig?      @relation(fields: [difyAppId], references: [id], onDelete: SetNull)
    
    // 系统字段
    createdAt           DateTime            @default(now())
    updatedAt           DateTime            @updatedAt
    deletedAt           DateTime?           // 软删除
    remark              String?             // 备注
    
    @@index([consumerId])
    @@index([scriptId])
    @@index([topicId])
    @@index([difyAppId])
    @@map("final_content")
}

// 黄金三秒开场表（由中央文案生成工作流产出，关联脚本与来源应用）
model OpeningContent {
    id                  String              @id @default(cuid())

    // 关联脚本
    scriptId            String              @map("script_id")
    script              ContentScript       @relation(fields: [scriptId], references: [id], onDelete: Cascade)

    // 开场内容
    content             String              // 黄金三秒开场文本

    // 来源工作流（Dify 应用）
    difyAppId           String?             @map("dify_app_id")
    difyApp             DifyAppConfig?      @relation(fields: [difyAppId], references: [id], onDelete: SetNull)

    // 系统字段
    createdAt           DateTime            @default(now())
    updatedAt           DateTime            @updatedAt
    deletedAt           DateTime?           // 软删除
    remark              String?             // 备注

    @@index([scriptId])
    @@index([difyAppId])
    @@index([createdAt])
    @@map("opening_content")
}