#!/bin/bash
set -e

# 函数：创建用户（如果不存在）
create_user() {
    local user=$1
    local password=$2
    echo "Creating user $user..."
    psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
        DO \$\$
        BEGIN
            IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = '$user') THEN
                CREATE USER $user WITH PASSWORD '$password';
            END IF;
        END
        \$\$;
EOSQL
}

# 函数：创建数据库（如果不存在）
create_database() {
    local dbname=$1
    local owner=$2
    echo "Creating database $dbname..."
    psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
        SELECT 'CREATE DATABASE $dbname OWNER $owner'
        WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = '$dbname')\gexec
EOSQL
}

# 函数：设置数据库权限
set_permissions() {
    local dbname=$1
    local user=$2
    echo "Setting permissions for $user on $dbname..."
    psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
        GRANT ALL PRIVILEGES ON DATABASE $dbname TO $user;
EOSQL
}

# 主逻辑
main() {
    # 默认用户和数据库（来自 .env）
    local default_user="$POSTGRES_USER"
    local default_password="$POSTGRES_PASSWORD"
    local default_db="$POSTGRES_DB"

    echo "Database initialization completed successfully!"
}

# 执行主逻辑
main 